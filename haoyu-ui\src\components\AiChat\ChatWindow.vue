<template>
  <div class="chat-window">
    <div class="chat-header">
      <span>AI客服助手</span>
      <!-- <el-button type="text" @click="testApi" style="margin-right: 10px;">测试API</el-button> -->
      <el-button type="text" @click="$emit('close')" icon="el-icon-close"></el-button>
    </div>

    <div class="chat-body" ref="messageContainer">
      <div v-for="(msg, index) in messages" :key="index" class="message" :class="msg.type === 'user' ? 'message-user' : 'message-ai'">
        <el-avatar
          :size="30"
          :icon="msg.type === 'user' ? 'el-icon-user' : 'el-icon-service'"
          class="avatar">
        </el-avatar>
        <div class="message-content">
          <p>{{ msg.content }}</p>
          <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
        </div>
      </div>
    </div>

    <div class="chat-footer">
      <el-input
        v-model="inputMessage"
        placeholder="请输入消息..."
        type="textarea"
        :rows="2"
        resize="none"
        @keyup.enter.native.exact="handleSend"
        @keyup.ctrl.enter.native="handleSend"
      >
      </el-input>
      <el-button
        type="primary"
        @click="handleSend"
        :disabled="!inputMessage.trim() || sending"
        :loading="sending"
      >
        发送
      </el-button>
    </div>
  </div>
</template>

<script>
import { Button, Input, Avatar } from 'element-ui'
import { chatService } from '@/api/aiChat'  // 确保正确引入

export default {
  name: 'ChatWindow',
  components: {
    ElButton: Button,
    ElInput: Input,
    ElAvatar: Avatar
  },
  data() {
    return {
      inputMessage: '',
      sending: false,
      messages: []  // 将消息列表移到组件内部管理
    }
  },
  methods: {
    async testApi() {
      try {
        console.log('测试API调用')
        const response = await chatService.sendMessage('测试消息')
        console.log('API测试成功:', response)
        if (response && response.answer) {
          this.$message.success(`API测试成功，回复: ${response.answer}`)
        } else {
          throw new Error('API响应格式不正确')
        }
      } catch (error) {
        console.error('API测试失败:', error)
        this.$message.error(`API测试失败: ${error.message}`)
      }
    },
    async handleSend(e) {
      // 如果是回车键触发且不是在shift+enter，阻止默认换行
      if (e && e.type === 'keyup' && !e.ctrlKey) {
        e.preventDefault()
      }

      // 如果正在发送或消息为空，不处理
      if (this.sending || !this.inputMessage.trim()) {
        return
      }

      const message = this.inputMessage.trim()
      this.sending = true

      try {
        // 添加用户消息到界面
        this.messages.push({
          content: message,
          type: 'user',
          timestamp: new Date().getTime()
        })

        // 清空输入框
        this.inputMessage = ''

        // 调用API
        const response = await chatService.sendMessage(message)
        console.log('API响应:', response)

        // 添加AI回复到界面
        if (response && response.answer) {
          this.messages.push({
            content: response.answer,
            type: 'ai',
            timestamp: new Date().getTime()
          })
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        this.messages.push({
          content: '抱歉，我遇到了一些问题，请稍后再试。',
          type: 'ai',
          timestamp: new Date().getTime()
        })
      } finally {
        this.sending = false
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messageContainer
        container.scrollTop = container.scrollHeight
      })
    }
  },
  watch: {
    messages: {
      handler() {
        this.scrollToBottom()
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.chat-window {
  position: fixed;
  width: 400px;
  height: 600px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  display: flex;
  flex-direction: column;
  pointer-events: auto;
}

.chat-header {
  padding: 10px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #409EFF;
  color: white;
  border-radius: 8px 8px 0 0;
}

.chat-header .el-button {
  color: white;
  font-size: 20px;
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f5f7fa;
}

.chat-footer {
  padding: 10px 20px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: flex-end;
  gap: 10px;
  background-color: white;
}

.message {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 20px;
}

.message-user {
  flex-direction: row-reverse;
}

.message-content {
  max-width: 70%;
  padding: 12px;
  border-radius: 8px;
  position: relative;
  word-break: break-all;
}

.message-content p {
  margin: 0;
  line-height: 1.5;
}

.message-ai .message-content {
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-radius: 0 8px 8px 8px;
  margin-left: 8px;
}

.message-user .message-content {
  background: #409EFF;
  color: white;
  border-radius: 8px 0 8px 8px;
  margin-right: 8px;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}

.message-user .message-time {
  text-align: right;
  color: rgba(255,255,255,0.8);
}

:deep(.el-textarea__inner) {
  resize: none;
  border-radius: 4px;
}

.el-button {
  height: 70px;
  padding: 0 20px;
}

.avatar {
  flex-shrink: 0;
}

.message-ai .avatar {
  background-color: #409EFF;
  color: white;
}

.message-user .avatar {
  background-color: #67c23a;
}

.message-wrapper:last-child {
  margin-bottom: 0;
}
</style>

