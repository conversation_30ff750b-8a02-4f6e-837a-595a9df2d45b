// src/store/modules/SettingConnect.js
const state = {
  selectedNodes: [],
  BaseStationNode: [],
  companyId: null,
  selectedNodeID: null,
  dialogConfirmed: false, // 用于标识弹窗是否已经确认，如果触发了那就初始化组件
  configNodeInfo: null  // 新增：用于存储组态设置需要的完整节点信息
}

const mutations = {
  // selectedNodes 相关 mutations
  ADD_NODE(state, node) {
    if (!state.selectedNodes.some(n => n.id === node.id)) {
      state.selectedNodes.push(node)
    }
  },
  CLEAR_NODES(state) {
    state.selectedNodes = []
  },
  REMOVE_NODE(state, index) {
    state.selectedNodes.splice(index, 1)
  },

  // BaseStationNode 相关 mutations
  ADD_BASE_STATION_NODE(state, node) {
    state.BaseStationNode.push(node)
  },
  CLEAR_BASE_STATION_NODES(state) {
    state.BaseStationNode = []
  },
  REMOVE_BASE_STATION_NODE(state, index) {
    state.BaseStationNode.splice(index, 1)
  },
  CLEAR_BASE_STATION_NODE(state) {
    state.BaseStationNode = []
  },
  // companyId 相关 mutations
  SET_COMPANY_ID(state, id) {
    state.companyId = id
  },
  SET_SELECTED_NODE_ID(state, id) {
    state.selectedNodeID = id
  },
  // 弹窗是否点击确定
  SET_DIALOG_CONFIRMED(state, confirmed) {
    state.dialogConfirmed = confirmed
  },
   // 新增：设置组态设置节点信息
   SET_CONFIG_NODE_INFO(state, nodeInfo) {
    state.configNodeInfo = nodeInfo
  },
  // 新增：清除组态设置节点信息
  CLEAR_CONFIG_NODE_INFO(state) {
    state.configNodeInfo = null
  }
}

const actions = {
  // selectedNodes 相关 actions
  addNode({ commit }, node) {
    commit('ADD_NODE', node)
  },
  clearNodes({ commit }) {
    commit('CLEAR_NODES')
  },
  removeNode({ commit }, index) {
    commit('REMOVE_NODE', index)
  },

  // BaseStationNode 相关 actions
  addBaseStationNode({ commit }, node) {
    commit('ADD_BASE_STATION_NODE', node)
  },
  clearBaseStationNodes({ commit }) {
    commit('CLEAR_BASE_STATION_NODES')
  },
  removeBaseStationNode({ commit }, index) {
    commit('REMOVE_BASE_STATION_NODE', index)
  },
  clearBaseStationNode({ commit }) {
    commit('CLEAR_BASE_STATION_NODE')
  },
  // companyId 相关 actions
  setCompanyId({ commit }, id) {
    commit('SET_COMPANY_ID', id)
  },
  // 树节点选择
  setSelectedNodeID({ commit }, id) {
    commit('SET_SELECTED_NODE_ID', id)
  },
  // 用于查看是否点击了弹窗确定
  confirmDialog({ commit }) {
    commit('SET_DIALOG_CONFIRMED', true)
  },
  resetDialogConfirmed({ commit }) {
    commit('SET_DIALOG_CONFIRMED', false)
  },
  // 新增：设置组态设置节点信息
  setConfigNodeInfo({ commit }, nodeInfo) {
    commit('SET_CONFIG_NODE_INFO', nodeInfo)
  },
  // 新增：清除组态设置节点信息
  clearConfigNodeInfo({ commit }) {
    commit('CLEAR_CONFIG_NODE_INFO')
  }
}

const getters = {
  // selectedNodes 相关 getters
  selectedNodes: state => state.selectedNodes,

  // BaseStationNode 相关 getters
  baseStationNode: state => state.BaseStationNode,

  // companyId getter
  companyId: state => state.companyId,
  selectedNodeID: state => state.selectedNodeID,

  // dialogConfirmed的
  dialogConfirmed: (state) => state.dialogConfirmed,

  // 新增：获取组态设置节点信息
  configNodeInfo: state => state.configNodeInfo
}

export default {
  namespaced: true, // 启用命名空间
  state,
  mutations,
  actions,
  getters
}

