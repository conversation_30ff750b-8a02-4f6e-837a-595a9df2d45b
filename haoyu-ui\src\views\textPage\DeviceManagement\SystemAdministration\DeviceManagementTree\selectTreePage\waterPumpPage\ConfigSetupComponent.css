.config-setup-component {
  height: 100%;
  width: 100%;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  margin-bottom: 10px;
  color: #333;
}

.el-form-item {
  margin-bottom: 10px;
}

.el-button {
  margin-right: 10px;
}

/* 内容区域分为左右两部分 */
.content {
  display: flex;
  height: calc(94% - 40px); /* 减去标题的高度 */
}

.left1-section {
  flex: 1; /* 左边占1/4 */
  padding: 5px;
  border-radius: 6px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  height: 100%; /* 固定高度，可根据需要调整 */
  width: 100%;  /* 固定宽度，可根据需要调整 */
  overflow-y: auto; /* 超过高度时出现垂直滚动条 */
  overflow-x: auto; /* 超过宽度时出现水平滚动条 */
}


.right-section {
  flex: 3; /* 右边占3/4 */
  padding: 5px;
  border-radius: 6px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-button {
  background-color: #4d4c47;
  color: white;
  border: none;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
}

.back-button:hover {
  background-color: #0056b3;
}


.back-button:hover {
  background-color: #0056b3;
}

.action-buttons {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  padding: 10px;
  margin-top: 10px;
  background-color: #f0f0f0;
  border-top: 1px solid #ccc;
}

.action-buttons .el-button {
  margin-right: 10px; /* 设置按钮之间的间距 */
}



.el-menu--collapse {
  width: 100%;
}
