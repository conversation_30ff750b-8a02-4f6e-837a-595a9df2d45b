<template>
  <div class="centermap">
    <div class="maptitle">
      <div v-if="levelHistory.length" class="back-button" @click="goBack">
        <span>返回上一级</span>
      </div>
      <div class="title-center">
        <div class="zuo" />
        <span class="titletext">{{ levelHistory.length ? '区域设备' : '公司列表' }}</span>
        <div class="you" />
      </div>
    </div>
    <div class="mapwrap">
      <dv-border-box-13 ref="border" :key="borderKey">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="company-grid">
          <div class="grid-content">
            <div
              v-for="item in paginatedItems"
              :key="item.id"
              class="company-card"
              :class="{ active: currentSelected === item.id }"
              :style="item.backgroundStyle"
              @click="handleSelect(item)"
            >
              <span class="company-name">{{ item.title }}</span>
              <div v-if="item.statusCount" class="status-info">
                <div class="status-item">
                  <span class="status-dot connected"></span>
                  <span class="status-count">{{ item.statusCount.connected }}</span>
                </div>
                <div class="status-item">
                  <span class="status-dot danger"></span>
                  <span class="status-count">{{ item.statusCount.danger }}</span>
                </div>
                <div class="status-item">
                  <span class="status-dot disconnected"></span>
                  <span class="status-count">{{ item.statusCount.disconnected }}</span>
                </div>
              </div>
              <div v-if="item.status" class="device-status">
                <span class="status-dot" :class="item.status"></span>
                <span class="status-text" :class="item.status">{{ getStatusText(item.status) }}</span>
              </div>
            </div>
          </div>
          <!-- 网格视图的分页组件 -->
          <div class="pagination-container">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[12, 24, 36, 48]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="currentLevelItems.length"
            >
            </el-pagination>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="company-list">
          <el-table
            :data="paginatedItems"
            style="width: 100%"
            @row-click="handleSelect"
            :row-class-name="getRowClassName"
          >
            <el-table-column prop="title" label="名称" min-width="200">
              <template slot-scope="scope">
                <div class="table-cell-content">
                  <i class="el-icon-office-building" v-if="!scope.row.treeIcon"></i>
                  <i class="el-icon-cpu" v-else></i>
                  <span class="title-text">{{ scope.row.title }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="状态信息" min-width="300" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.statusCount" class="table-status-info">
                  <div class="status-item">
                    <el-tooltip content="在线设备" placement="top">
                      <div class="status-wrapper">
                        <span class="status-dot connected"></span>
                        <span class="status-count connected">{{ scope.row.statusCount.connected }}</span>
                        <span class="status-label">在线</span>
                      </div>
                    </el-tooltip>
                  </div>
                  <div class="status-item">
                    <el-tooltip content="报警设备" placement="top">
                      <div class="status-wrapper">
                        <span class="status-dot danger"></span>
                        <span class="status-count danger">{{ scope.row.statusCount.danger }}</span>
                        <span class="status-label">报警</span>
                      </div>
                    </el-tooltip>
                  </div>
                  <div class="status-item">
                    <el-tooltip content="离线设备" placement="top">
                      <div class="status-wrapper">
                        <span class="status-dot disconnected"></span>
                        <span class="status-count disconnected">{{ scope.row.statusCount.disconnected }}</span>
                        <span class="status-label">离线</span>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div v-else-if="scope.row.status" class="table-device-status">
                  <el-tag 
                    :type="getStatusType(scope.row.status)" 
                    size="small"
                    effect="dark"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click.stop="handleSelect(scope.row)"
                >
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="currentLevelItems.length"
            >
            </el-pagination>
          </div>
        </div>
      </dv-border-box-13>
    </div>
  </div>
</template>

<script>
import { getBaseStationDeviceList } from '@/api/haoyu-system/OnlineManagement/BaseStationTree'
// import { getMachinesByCompany } from '@/api/haoyu-system/BigScreen/BigScreen'
import { getNextLevel, getAllCompany,getNextLevelImformation, geDeviceMeasureDefine, getfactoryImage, getDeviceImageList } from '@/views/textPage/BigScreen/api/apiBigscreen'

export default {
  props: {
    viewMode: {
      type: String,
      default: 'grid'
    }
  },
  data() {
    return {
      borderKey: 0,
      currentSelected: '',
      levelHistory: [],
      currentLevelItems: [],
      currentTreeData: null,
      currentLevelStats: {
        connected: 0,
        danger: 0,
        disconnected: 0
      },
      currentPage: 1,
      pageSize: 10,
    }
  },
  computed: {
    paginatedItems() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.currentLevelItems.slice(start, end);
    }
  },
  mounted() {
    this.getCompanyList()
  },
  methods: {
    // 计算并发送统计信息
    updateStatistics(items) {
      const stats = {
        connected: 0,
        danger: 0,
        disconnected: 0
      }

      const countDeviceStatus = (node) => {
        if (node.treeIcon === 'waterPump') {
          if (node.status === 'connected') stats.connected++
          else if (node.status === 'danger') stats.danger++
          else if (node.status === 'disconnected') stats.disconnected++
        }
        if (node.children && node.children.length > 0) {
          node.children.forEach(countDeviceStatus)
        }
      }

      // 遍历所有项目及其子项
      items.forEach(item => {
        if (item.children) {
          item.children.forEach(countDeviceStatus)
        }
      })

      this.currentLevelStats = stats
      // 发送统计数据到父组件
      this.$emit('statistics-update', {
        onlineDevices: stats.connected,
        alarmDevices: stats.danger,
        healthIndex: Math.round((stats.connected / (stats.connected + stats.danger + stats.disconnected)) * 100) || 0
      })
    },

    // 获取公司列表
    getCompanyList() {
      this.levelHistory = []
      this.currentLevelItems = []
      this.currentPage = 1; // 重置分页
      
      getAllCompany().then(res => {
        if (res.data && res.data.length > 0) {
          // 先创建基本的列表项
          this.currentLevelItems = res.data.map(item => ({
            id: item.id,
            title: item.title,
            backgroundStyle: {
              backgroundImage: 'none',
              backgroundColor: 'rgb(22, 49, 96)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }
          }))
          
          // 为每个项目获取背景图片和统计信息
          Promise.all(this.currentLevelItems.map(item => 
            getNextLevelImformation(item.id)
              .then(infoRes => {
                if (infoRes.code === 200 && infoRes.data) {
                  item.children = infoRes.data.children || []
                }
                return getfactoryImage(item.id)
              })
              .then(imgRes => {
                if (imgRes.code === 200 && imgRes.data && imgRes.data.imgUrl) {
                  item.backgroundStyle.backgroundImage = `url(${imgRes.data.imgUrl})`
                }
              })
              .catch(error => {
                console.error('获取数据失败:', error)
              })
          )).then(() => {
            // 所有数据加载完成后更新统计
            this.updateStatistics(this.currentLevelItems)
          })
        }
      }).catch(error => {
        console.error('获取公司列表失败:', error)
      })
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        connected: '已连接',
        danger: '警告',
        disconnected: '未连接'
      }
      return statusMap[status] || status
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'connected': 'success',
        'danger': 'danger',
        'disconnected': 'info'
      }
      return typeMap[status] || 'info'
    },

    // 统一的选择处理方法
    handleSelect(item) {
      this.currentSelected = item.id
      this.currentPage = 1; // 重置分页

      getNextLevelImformation(item.id).then(res => {
        if (res.code === 200 && res.data) {
          // 检查是否是水泵类型
          if (res.data.treeIcon === 'waterPump') {
            const row = {
              id: res.data.id,
              deviceName: res.data.title,
              status: res.data.status,
              mongodbId: res.data.mongodbId,
              children: res.data.children
            }
            this.$emit('switchToGeneralPicture', row)
            return
          }

          // 如果有子节点，显示子节点
          if (res.data.children && res.data.children.length > 0) {
            // 保存当前层级数据到历史记录
            this.levelHistory.push({
              items: this.currentLevelItems,
              selected: this.currentSelected,
              treeData: this.currentTreeData,
              stats: this.currentLevelStats
            })

            // 处理子节点数据
            const processedChildren = res.data.children.map(child => {
              const baseItem = {
                id: child.id,
                title: child.title,
                treeIcon: child.treeIcon,
                children: child.children,
                backgroundStyle: {
                  backgroundImage: 'none',
                  backgroundColor: 'rgb(22, 49, 96)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat'
                }
              }

              // 如果是设备节点，直接添加状态
              if (child.treeIcon === 'waterPump') {
                baseItem.status = child.status
              } else {
                // 如果是区域节点，添加状态统计
                const statusCount = {
                  connected: 0,
                  danger: 0,
                  disconnected: 0
                }

                const countDeviceStatus = (node) => {
                  if (node.treeIcon === 'waterPump') {
                    if (node.status === 'connected') statusCount.connected++
                    else if (node.status === 'danger') statusCount.danger++
                    else if (node.status === 'disconnected') statusCount.disconnected++
                  }
                  if (node.children && node.children.length > 0) {
                    node.children.forEach(countDeviceStatus)
                  }
                }

                if (child.children && child.children.length > 0) {
                  child.children.forEach(countDeviceStatus)
                }

                baseItem.statusCount = statusCount
              }

              return baseItem
            })

            // 更新当前层级数据
            this.currentLevelItems = processedChildren
            
            // 更新统计信息
            this.updateStatistics([res.data])

            // 获取背景图片
            this.currentLevelItems.forEach(item => {
              if (item.treeIcon === 'waterPump') {
                getDeviceImageList(item.id).then(imgRes => {
                  if (imgRes.data && imgRes.data.deviceImageUrl) {
                    item.backgroundStyle = {
                      ...item.backgroundStyle,
                      backgroundImage: `url(${imgRes.data.deviceImageUrl})`,
                      backgroundColor: 'transparent'
                    }
                  }
                }).catch(() => {
                  item.backgroundStyle = {
                    ...item.backgroundStyle,
                    backgroundImage: 'none',
                    backgroundColor: 'rgb(22, 49, 96)'
                  }
                })
              } else {
                getfactoryImage(item.id).then(imgRes => {
                  if (imgRes.code === 200 && imgRes.data && imgRes.data.imgUrl) {
                    item.backgroundStyle = {
                      ...item.backgroundStyle,
                      backgroundImage: `url(${imgRes.data.imgUrl})`,
                      backgroundColor: 'transparent'
                    }
                  }
                }).catch(error => {
                  console.error('获取工厂图片失败:', error)
                  item.backgroundStyle = {
                    ...item.backgroundStyle,
                    backgroundImage: 'none',
                    backgroundColor: 'rgb(22, 49, 96)'
                  }
                })
              }
            })

            this.currentTreeData = res.data
          }
        }
      }).catch(error => {
        console.error('获取下一级数据失败:', error)
      })
    },

    // 返回上一级
    goBack() {
      this.currentPage = 1; // 重置分页
      if (this.levelHistory.length > 0) {
        const previousLevel = this.levelHistory.pop()
        this.currentLevelItems = previousLevel.items
        this.currentSelected = previousLevel.selected
        this.currentTreeData = previousLevel.treeData
        // 恢复上一级的统计数据
        this.currentLevelStats = previousLevel.stats
        this.$emit('statistics-update', {
          onlineDevices: previousLevel.stats.connected,
          alarmDevices: previousLevel.stats.danger,
          healthIndex: Math.round((previousLevel.stats.connected / 
            (previousLevel.stats.connected + previousLevel.stats.danger + previousLevel.stats.disconnected)) * 100) || 0
        })
      }
    },

    // 获取行的类名
    getRowClassName({ row }) {
      return row.id === this.currentSelected ? 'row-active' : ''
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; // 重置到第一页
    },
    
    handleCurrentChange(val) {
      this.currentPage = val;
    },
  }
}
</script>

<style lang="scss" scoped>
.dv-border-box-13 {
  width: 100%; /* 让组件宽度占满父容器 */
  height: 100%; /* 让组件高度占满父容器 */
}
/*  deep 穿透展现表格滚动条 */
::v-deep .el-table {
  padding: 0 !important;
  border-right: none !important;
  // 表单元格高度
  th.el-table__cell div {
    height: 1.43vw;
    line-height: 1.43vw;
  }
  // 表格单元格高度
  // td.el-table__cell div {
  //   height: 1.2vw;
  //   line-height: 1.2vw;
  // }
  // 高亮行背景颜色
  .el-table__body tr:hover > td {
    background-color: rgb(148, 193, 245) !important;
  }
  // 表格背景颜色
  tr {
    background-color: rgba(#fff, 0);
  }
  // 单元格样式
  .el-table__cell {
    padding: 0.2vh !important;
    border: none;
  }

  // 表头背景色和字体样式
  .el-table__header-wrapper,
  th.el-table__cell {
    background-color: rgba(#fff, 0.04);
    font-family: MicrosoftYaHeiUI-Bold, MicrosoftYaHeiUI;
    font-weight: bold;
    color: #ffffff;
  }

  // 表格主体的滚动条样式
  .el-table__body-wrapper {
    height: 100%;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      z-index: 11;
      display: normal;
    }
  }
}

// 表格背景色
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: none !important;
  background-color: rgba(#ffffff, 0.04) !important;
}

// 表格边框
::v-deep .el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: none !important;
}

// 表格底部边框
::v-deep .el-table::before {
  height: 0 !important;
}

// 表格头部边框
::v-deep .el-table th.el-table__cell.is-leaf,
.el-table td.el-table__cell {
  border-bottom: none !important;
}

// 表格空状态背景色
::v-deep .el-table__empty-block {
  background-color: rgba(#ffffff, 0.04) !important;
}

.mapwrap {
  height: 548px;
  width: 100%;
  box-sizing: border-box;
  padding: 0;
  transition: background-image 0.3s ease;

  .inner-border {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.0);
    border-radius: 10px;
    overflow: hidden;
  }
}

// 网格视图样式
.company-grid {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  
  .grid-content {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    padding: 25px;
    overflow-y: auto;
    min-height: 0;
    
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e0e0e0;
      border-radius: 3px;
      
      &:hover {
        background: #d0d0d0;
      }
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 3px;
    }
  }
}

// 列表视图样式
.company-list {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  
  ::v-deep .el-table {
    flex: 1;
    min-height: 0;
    background-color: transparent;
    
    &::before {
      display: none;
    }
    
    .el-table__inner-wrapper {
      height: 100%;
    }
    
    .el-table__header-wrapper {
      background-color: #f5f7fa !important;
      th.el-table__cell {
        background-color: #f5f7fa !important;
        color: #606266;
        font-weight: 600;
      }
    }
    
    .el-table__body-wrapper {
      height: calc(100% - 48px) !important;
      background-color: transparent;
    }
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px;
  background: #ffffff;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e4e7ed;
}

.company-card {
  height: 200px;
  background: rgb(22, 49, 96);
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 20px;
  position: relative;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-3px);
    border-color: #409EFF;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &.active {
    border-color: #409EFF;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
  }

  .company-name {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    word-break: break-all;
    line-height: 1.4;
    position: relative;
    z-index: 2;
    margin-bottom: 15px;
    padding: 8px 16px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(1px);
    // filter: drop-shadow(0 0 10px rgba(255, 255, 255, 1)) 
    //         drop-shadow(0 0 15px rgba(255, 255, 255, 0.95))
    //         drop-shadow(0 0 20px rgba(255, 255, 255, 0.9))
    //         drop-shadow(0 0 25px rgba(255, 255, 255, 0.85));
  }

  .status-info {
    display: flex;
    gap: 10px;
    position: relative;
    z-index: 2;
    padding: 6px 12px;
    border-radius: 4px;
    // filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.9)) 
    //         drop-shadow(0 0 10px rgba(255, 255, 255, 0.7));
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(4px);

    .status-item {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        
        &.connected {
          background-color: #67C23A;
        }
        
        &.danger {
          background-color: #F56C6C;
        }
        
        &.disconnected {
          background-color: #909399;
        }
      }
      
      .status-count {
        color: #303133;
        font-size: 14px;
        font-weight: 500;
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.9)) 
                drop-shadow(0 0 8px rgba(255, 255, 255, 0.7));
      }
    }
  }

  .device-status {
    position: relative;
    z-index: 2;
    padding: 4px 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);

      &.connected {
        background-color: #67C23A;
      }
      
      &.danger {
        background-color: #F56C6C;
      }
      
      &.disconnected {
        background-color: #909399;
      }
    }

    .status-text {
      font-size: 14px;
      font-weight: 500;
      filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.9)) 
              drop-shadow(0 0 8px rgba(255, 255, 255, 0.7));

      &.connected {
        color: #67C23A;
      }
      
      &.danger {
        color: #F56C6C;
      }
      
      &.disconnected {
        color: #909399;
      }
    }
  }
}

.centermap {
  margin-bottom: 2vh;
  background: #ffffff;

  .maptitle {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 10px 20px;
    box-sizing: border-box;
    position: relative;
    background: #ffffff;
    border-bottom: 1px solid #e4e7ed;

    .title-center {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .titletext {
        font-size: 28px;
        font-weight: 900;
        letter-spacing: 6px;
        background: linear-gradient(
          92deg,
          #409EFF 0%,
          #66b1ff 48.8525390625%,
          #409EFF 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 10px;
      }

      .zuo,
      .you {
        background-size: 100% 100%;
        width: 29px;
        height: 20px;
        margin-top: 8px;
      }

      .zuo {
        background: url("../../../../assets/img/xiezuo.png") no-repeat;
      }

      .you {
        background: url("../../../../assets/img/xieyou.png") no-repeat;
      }
    }

    .back-button {
      height: 35px;
      min-width: 120px;
      background: #ffffff;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      margin-right: 20px;

      span {
        color: #606266;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        
        &::before {
          content: '←';
          margin-right: 8px;
          font-size: 16px;
        }
      }

      &:hover {
        color: #409EFF;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }

  .mapwrap {
    background: #ffffff;
  }
}

.table-cell-content {
  display: flex;
  align-items: center;
  gap: 12px;
  
  i {
    font-size: 20px;
    color: #409EFF;
    transition: all 0.3s ease;
  }
  
  .title-text {
    color: #303133;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
  }
}

.table-status-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  
  .status-item {
    .status-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 12px;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #e4e7ed;
      border-radius: 15px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409EFF;
        background: #ecf5ff;
      }
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
        
        &.connected {
          background-color: #67C23A;
        }
        
        &.danger {
          background-color: #F56C6C;
        }
        
        &.disconnected {
          background-color: #909399;
        }
      }
      
      .status-count {
        font-weight: 600;
        font-size: 14px;
        
        &.connected {
          color: #67C23A;
        }
        
        &.danger {
          color: #F56C6C;
        }
        
        &.disconnected {
          color: #909399;
        }
      }
      
      .status-label {
        font-size: 12px;
        color: #606266;
      }
    }
  }
}

.pagination-container {
  padding: 15px;
  background: #ffffff;
  border-top: 1px solid #e4e7ed;
  
  ::v-deep .el-pagination {
    .btn-prev,
    .btn-next,
    .el-pager li {
      background: #ffffff;
      border: 1px solid #dcdfe6;
      color: #606266;
      transition: all 0.3s ease;
      
      &:hover {
        color: #409EFF;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
      
      &.active {
        background: #409EFF;
        color: #ffffff;
        border-color: #409EFF;
        font-weight: 600;
      }
    }
    
    .el-pagination__total,
    .el-pagination__sizes {
      color: #606266;
      
      .el-input__inner {
        background: #ffffff;
        border: 1px solid #dcdfe6;
        color: #606266;
        
        &:hover,
        &:focus {
          border-color: #409EFF;
        }
      }
    }
  }
}

.company-list {
  display: flex;
  flex-direction: column;
  
  .el-table {
    flex: 1;
  }
}
</style>
