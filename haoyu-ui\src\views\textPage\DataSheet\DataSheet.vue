<template>
  <div ref="tableContainer" style="height: 100%" @contextmenu.prevent="onContextmenu">
    <el-table
      ref="dataTable"
      :data="paginatedData"
      style="width: 100%; padding: 0; overflow-x: hidden;"
      height="90%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
      />
      <el-table-column
        prop="collectorTime"
        label="采集时间"
        min-width="150"
      />
      <el-table-column
        prop="value"
        label="有效值"
        :formatter="formatValue"
      />
<!--       <el-table-column
        prop="deviceId"
        label="设备id"
      /> -->
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 30, 40,50]"
      style="margin-top: 0px; width: 100%;"
      :page-size="pageSize"
      :current-page="currentPage"
      :total="total"
      layout="sizes,prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      small
      :disabled="isDisabledPagination"
    />
    <el-dialog :visible.sync="showTimeRangeDialog" title="选择时间范围" width="450px" class="centered-dialog">
      <el-date-picker
        v-model="timeRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"

      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="showTimeRangeDialog = false">取消</el-button>
        <el-button type="primary" @click="applyTimeFilter">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getData } from './getdata'  // 使用命名导入
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      form: {
        sampleTime: '',
        sampleValue: '',
/*         remark: '', */
        instrumentSerialNumber: ''
      },
      tableData: [],
      selectedRows: [],
      currentPage: 1,
      pageSize: 10,
      total: 0, // 后端返回的总数
      selectedRowId: null ,
      contextMenuData: [
        {
          label: '编辑',
          icon: 'el-icon-edit',
          divided: true,
          onClick: () => {
            this.handleEdit()
          },
        },
        {
          label: '数据导出',
          icon: 'el-icon-printer',
          divided: true,
          children: [
            {
              label: '导出为PDF',
              onClick: () => {
                  console.log('PDF');

              }
            },
            {
              label: '导出为Excel',
              onClick: () => {
                  console.log('Excel');

              }
            }
          ],
        },
        {
          label: '时间范围',
          icon: 'el-icon-time',
          divided: true,
          onClick: () => {
            this.showTimeRangeDialog = true;
          }
        },
        {
          label: '重置',
          icon: 'el-icon-refresh-right',
          onClick: async ()=> {
            this.timeRange = []; // 清空时间范围选择
            await this.fetchData(); // 重新获取数据
          }
        }
      ],
      showTimeRangeDialog: false,
      timeRange: [],
      filteredData: []
    }
  },
  created() {
    this.filteredData = this.tableData;
  },
  computed: {
    ...mapGetters('tree', ['selectedNode']),
    currentNodeId() {
      const selectedNode = this.$store.state.tree.selectedTreeNode;
      return selectedNode?.treeicon === 'measureDef' ? selectedNode?.id : null;
    },
    paginatedData() {
      /* const start = (this.currentPage - 1) * this.pageSize
      const end = this.currentPage * this.pageSize
      return this.filteredData.slice(start, end); */
      // return this.tableData.slice(start, end)
      return this.filteredData;
    },
    isDisabledPagination() {
      const currentNode = this.$store.state.tree.selectedTreeNode;
      return ['waterPump', 'company', 'grading','bearing','value'].includes(currentNode?.treeicon);
    },
  },
  watch: {
    currentNodeId: {
      immediate: true,
      async handler(newId) {
        // console.log(this.$store.state.tree.selectedTreeNode);
        if (newId && newId != 0) {
          try {
            //获取分页数据
            const data = await getData(newId, this.currentPage, this.pageSize)
            this.tableData = data.rows
            this.filteredData = data.rows
            this.total = data.total; // 设置分页总数
            if (data && data.rows){
              // 默认选中第一行
              if (data.rows && data.rows.length > 0) {
                this.$nextTick(() => {
                  // 使用 el-table 的 toggleRowSelection 方法选中第一行
                  const firstRow = this.$refs.dataTable.toggleRowSelection(data.rows[0], true)

                  // 存储选中行的数据
                  const { id, measurementId: point_id, collectorTime: time_point } = data.rows[0]
                  if (this.selectedRowId !== data.rows[0].id) {
                    this.updateStoreData(id, point_id, time_point);
                    this.selectedRowId = data.rows[0].id;  // 记录当前已选中的行
                  }
                })
              }
            }
          } catch (error) {
            console.error('获取数据失败:', error)
          }
        }
      }
    },
  },
  mounted(){
    if (!this.timeRange || this.timeRange.length !== 2) {
      this.filteredData = this.tableData;
    }
  },
  methods: {
    // 添加更新store的方法
    async updateStoreData(deviceId, pointId, timePoint) {
      try {
        // console.log('准备更新 Store 数据:', { deviceId, pointId, timePoint });
        await Promise.all([
          this.$store.dispatch('dataStore/setDeviceId', deviceId),
          this.$store.dispatch('dataStore/setPointId', pointId),
          this.$store.dispatch('dataStore/setTimePoint', timePoint)
        ])

        this.$nextTick(() => {
          console.log('Store 更新后:', {
            deviceId: this.$store.state.dataStore.device_id,
            pointId: this.$store.state.dataStore.point_id,
            timePoint: this.$store.state.dataStore.time_point
          });
        });
      } catch (error) {
        console.error('更新Store失败:', error)
      }
    },

    // 修改选择变化处理方法
    handleSelectionChange(val) {
      this.selectedRows = val

      // 更新所有选中的行数据到 store（用于多时域波形图）
      const selectedRowsData = val.map(row =>({
        device_id : row.id,
        point_id : row.measurementId,
        time_point: row.collectorTime
      }))
      this.$store.dispatch('dataStore/setSelectedRowsData', selectedRowsData);

      // 如果有选中的数据，直接更新 store
      if (val && val.length > 0) {
        const { id, measurementId: point_id, collectorTime: time_point } = val[val.length - 1];
        this.updateStoreData(id, point_id, time_point);
      }else {
        // 如果没有选中的行，可以选择清空相关的 Vuex 数据
        this.updateStoreData(null, null, null); // 清空或重置相关数据
      }
    },
    submitForm() {
      this.tableData.push({ ...this.form })
      this.resetForm()
    },
    resetForm() {
      this.$refs.form.resetFields()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    onContextmenu(event) {
        this.$contextmenu({
            items: this.contextMenuData,
            event, // 鼠标事件信息
            zIndex: 5, // 菜单样式 z-index
            minWidth: 150 // 主菜单最小宽度
        })
        return false
    },
    handleEdit() {
      console.log('编辑被点击')
    },
    applyTimeFilter() {
      if (this.timeRange && this.timeRange.length === 2) {
        const [start, end] = this.timeRange;
        this.filteredData = this.tableData.filter(item => {
          const collectorTime = new Date(item.collectorTime);
          return collectorTime >= start && collectorTime <= end;
        });
        this.total = this.filteredData.length; // 更新分页总数
        this.currentPage = 1; // 重置为第一页
      } else {
        this.filteredData = this.tableData;
        this.total = this.tableData.length;
      }
      this.showTimeRangeDialog = false;
    },
    async fetchData() {
      try {
        // 使用当前页和每页条数来请求后端数据
        const data = await getData(this.currentNodeId, this.currentPage, this.pageSize);
        this.tableData = data.rows
        this.filteredData = data.rows
        this.total = data.total; // 更新总数
        // console.log('请求数据返回：', data);  // 查看返回的数据
        this.$nextTick(() => {
          // 强制视图更新
          this.currentPage = this.currentPage; // 触发分页重新渲染
        });
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },
    formatValue(row, column, cellValue) {
      const num = Number(cellValue);
      if (!isNaN(num)) {
        return num.toFixed(4);
      }
      return cellValue;
    }
  }
}
</script>

<style scoped>
.demo-form-inline .el-form-item {
  margin-right: 20px;
}

.custom-table >>> .el-table__cell {
  padding: 0 !important;
}
.centered-dialog {
    position: absolute;
    display: flex;
    align-items: center;
    bottom: 20%;
    right: 10%;
}

</style>
