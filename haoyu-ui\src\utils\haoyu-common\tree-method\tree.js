// src/utils.js
export function getIcon(props) {
  const { treeIcon, status } = props.dataRef
  let iconClass = treeIcon

  // 判断icon状态
  switch (status) {
    // 预警状态
    case 'warning':
      iconClass = `${treeIcon}-warningValue`
      break
    // 危险状态
    case 'danger':
      iconClass = `${treeIcon}-dangerValue`
      break
    // 连接状态
    case 'connected':
      iconClass = `${treeIcon}-connectedData`
      break
    // 未连接状态
    case 'disconnected':
      iconClass = `${treeIcon}-disconnectedData`
      break
    default:
      iconClass = treeIcon
  }

  // 返回icon的DOM元素
  return <svg-icon icon-class={iconClass} style='width: 1.5em; height: 1.5em;' />
}
