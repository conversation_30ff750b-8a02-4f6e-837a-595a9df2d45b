<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="处理报警"
    width="1600px"
    center
    custom-class="alarm-process-dialog"
    :close-on-click-modal="false"
  >
    <div v-if="alarmData" class="alarm-process-content">
      <!-- 报警基本信息 -->
      <div class="alarm-basic-info">
        <h3>报警信息</h3>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="设备名称" :span="2">{{ alarmData.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="测点名称">{{ alarmData.measurementName }}</el-descriptions-item>
          <el-descriptions-item label="报警级别">
            <el-tag :type="getAlarmLevelTag(alarmData.warnLevel)" effect="dark">
              {{ getAlarmLevelText(alarmData.warnLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="报警时间">{{ alarmData.warnTime }}</el-descriptions-item>
          <el-descriptions-item label="持续时间">{{ formatDuration(alarmData.warnTime) }}</el-descriptions-item>
          <el-descriptions-item label="报警消息" :span="2">{{ alarmData.warnMsg }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 处理表单 -->
      <div class="alarm-process-form">
        <h3>处理信息</h3>
        <el-form ref="processForm" :model="processForm" :rules="rules" label-width="100px" size="small">

          <el-form-item label="处理类型" prop="processType">
            <el-select v-model="processForm.processType" placeholder="请选择处理类型" style="width: 100%;">
              <el-option label="现场排查" value="fieldInspection"></el-option>
              <el-option label="远程调整" value="remoteAdjustment"></el-option>
              <el-option label="设备维修" value="equipmentRepair"></el-option>
              <el-option label="更换部件" value="partReplacement"></el-option>
              <el-option label="软件调整" value="softwareAdjustment"></el-option>
              <el-option label="其他" value="other"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="处理说明" prop="processDescription">
            <vue-editor v-model="processForm.processDescription" :editorToolbar="customToolbar"></vue-editor>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="submitProcess">提交处理</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { VueEditor } from 'vue2-editor';
import { getAlarmSubList } from './api/AlaSub'; // 导入API方法

export default {
  name: 'AlarmProcessDialog',
  components: {
    VueEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    alarmItem: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      alarmData: null,
      customToolbar: [
        ['bold', 'italic', 'underline', 'strike'],        // 文字格式
        ['blockquote', 'code-block'],                     // 引用和代码块
        [{ 'header': 1 }, { 'header': 2 }],               // 标题
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],     // 列表
        [{ 'script': 'sub'}, { 'script': 'super' }],      // 上标/下标
        [{ 'indent': '-1'}, { 'indent': '+1' }],          // 缩进
        [{ 'direction': 'rtl' }],                         // 文字方向
        [{ 'size': ['small', false, 'large', 'huge'] }],  // 字体大小
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],        // 标题大小
        [{ 'color': [] }, { 'background': [] }],          // 字体颜色和背景色
        [{ 'font': [] }],                                 // 字体系列
        [{ 'align': [] }],                                // 对齐方式
        ['clean'],                                        // 清除格式
        ['link', 'image', 'video']                        // 链接、图片和视频
      ],
      toolbarTips: [
        { selector: '.ql-bold', title: '加粗' },
        { selector: '.ql-italic', title: '斜体' },
        { selector: '.ql-underline', title: '下划线' },
        { selector: '.ql-strike', title: '删除线' },
        { selector: '.ql-blockquote', title: '引用' },
        { selector: '.ql-code-block', title: '代码块' },
        { selector: '.ql-header[value="1"]', title: '标题1' },
        { selector: '.ql-header[value="2"]', title: '标题2' },
        { selector: '.ql-list[value="ordered"]', title: '有序列表' },
        { selector: '.ql-list[value="bullet"]', title: '无序列表' },
        { selector: '.ql-script[value="sub"]', title: '下标' },
        { selector: '.ql-script[value="super"]', title: '上标' },
        { selector: '.ql-indent[value="-1"]', title: '减少缩进' },
        { selector: '.ql-indent[value="+1"]', title: '增加缩进' },
        { selector: '.ql-direction', title: '文字方向' },
        { selector: '.ql-size', title: '字体大小' },
        { selector: '.ql-header.ql-picker', title: '标题格式' },
        { selector: '.ql-color', title: '字体颜色' },
        { selector: '.ql-background', title: '背景颜色' },
        { selector: '.ql-font', title: '字体' },
        { selector: '.ql-align', title: '对齐方式' },
        { selector: '.ql-clean', title: '清除格式' },
        { selector: '.ql-link', title: '插入链接' },
        { selector: '.ql-image', title: '插入图片' },
        { selector: '.ql-video', title: '插入视频' }
      ],
      processForm: {
        processType: '',
        processDescription: ''
      },
      rules: {
        processType: [
          { required: true, message: '请选择处理类型', trigger: 'change' }
        ],
        processDescription: [
          { required: true, message: '请输入处理说明', trigger: 'blur' },
          { min: 5, max: 2000, message: '长度在 5 到 2000 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    alarmItem: {
      handler(val) {
        this.alarmData = val;
      },
      immediate: true,
      deep: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
      if (!val) {
        // 重置表单
        if (this.$refs.processForm) {
          this.$refs.processForm.resetFields();
        }
      }
    }
  },
  methods: {
    closeDialog() {
      this.dialogVisible = false;
    },
    submitProcess() {
      this.$refs.processForm.validate(valid => {
        if (!valid) {
          return false;
        }

        // 构建处理结果数据，按照指定格式提交
        const processData = {
          warnId: this.alarmData.id,  // 报警记录id
          hurtType: this.processForm.processType,  // 报警类型
          hurtMsg: this.processForm.processDescription  // 报警的详细信息(富文本内容)
        };

        // 调用实际API进行网络请求
        getAlarmSubList(processData).then(res => {
          if (res.code === 200) {
            // 合并处理信息并传给父组件
            const processedAlarm = {
              ...this.alarmData,
              isSolved: 1,  // 使用isSolved替代isProcessed
              confirmTime: new Date().toLocaleString(),
              duration: this.formatDuration(this.alarmData.warnTime),
              processInfo: processData
            };

            this.$emit('process-success', processedAlarm);
            this.$message.success('报警处理成功');
            this.closeDialog();
          } else {
            // this.$message.error(res.msg || '处理失败，请重试');
          }
        }).catch(error => {
          console.error('处理报警出错:', error);
          // this.$message.error('网络请求出错，请重试');
        });
      });
    },
    getAlarmLevelTag(level) {
      const levelMap = {
        '1': 'warning',  // 一级报警 - 黄色
        '2': 'warning',  // 二级报警 - 橙色
        '3': 'danger'    // 三级报警 - 红色
      };
      return levelMap[level] || 'info';
    },
    getAlarmLevelText(level) {
      const levelMap = {
        '1': '一级报警',
        '2': '二级报警',
        '3': '三级报警'
      };
      return levelMap[level] || '未知级别';
    },
    formatDuration(startTimeStr) {
      // 计算从报警开始到现在的持续时间
      if (!startTimeStr) return '-';

      const startTime = new Date(startTimeStr);
      const now = new Date();
      const diffMs = now - startTime;

      // 转换为天、小时、分钟
      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      // 格式化输出
      let result = '';
      if (days > 0) result += `${days}天`;
      if (hours > 0) result += `${hours}小时`;
      result += `${minutes}分钟`;

      return result;
    }
  },
  mounted() {
    // 添加工具栏悬浮提示功能
    this.$nextTick(() => {
      // 等待编辑器完全渲染后再添加提示
      setTimeout(() => {
        this.toolbarTips.forEach(tip => {
          try {
            const elements = document.querySelectorAll(tip.selector);
            elements.forEach(element => {
              element.setAttribute('title', tip.title);
            });
          } catch (e) {
            console.error('设置工具提示失败:', e);
          }
        });
      }, 500);
    });
  }
}
</script>

<style lang="scss" scoped>
.alarm-process-content {
  padding: 0 10px;
}

.alarm-basic-info, .alarm-process-form {
  margin-bottom: 20px;

  h3 {
    margin: 10px 0 15px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-left: 4px solid #409EFF;
  }
}

:deep(.el-descriptions-item__label) {
  background-color: #f5f7fa;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-tag) {
  font-size: 12px;
}
</style>

<style>
.alarm-process-dialog {
  margin: 0 auto !important;
}

.alarm-process-dialog .el-dialog {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.alarm-process-dialog .el-dialog__body {
  padding: 15px 20px;
  max-height: 80vh;
  overflow-y: auto;
  flex: 1;
}

.alarm-process-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

.alarm-process-dialog .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #EBEEF5;
}

/* 富文本编辑器样式 */
.alarm-process-dialog .quill-editor {
  line-height: 1.5;
  margin-bottom: 10px;
}

.alarm-process-dialog .ql-container {
  min-height: 120px;
  max-height: 250px;
  overflow-y: auto;
}

.alarm-process-dialog .ql-toolbar {
  background-color: #f8f8f8;
  border-radius: 4px 4px 0 0;
}

.alarm-process-dialog .ql-editor {
  font-size: 14px;
  background-color: #fff;
}

/* 两个编辑器高度差异化 */
.alarm-process-form .el-form-item:nth-child(4) .ql-container {
  min-height: 150px;
}

.alarm-process-form .el-form-item:nth-child(5) .ql-container {
  min-height: 100px;
}
</style>
