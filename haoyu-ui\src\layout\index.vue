<template>
  <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
        <right-panel>
        <settings />
      </right-panel>
    </div>

    <!-- 报警对话框 -->
    <alarm-dialog
      :visible.sync="alarmDialogVisible"
      :alertTitle="'测点报警'"
      @close="handleAlarmClose"
      @resolve="handleAlarmResolve"
      ref="alarmDialog"
    />
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import variables from '@/assets/styles/variables.scss'
import WebSocketService from '@/utils/alarmSocket/websocket'
import AlarmDialog from '@/components/AlarmDialog/alarmdialog'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    AlarmDialog
  },
  data() {
    return {
      alarmDialogVisible: false,
      currentAlarmMessage: ''
    }
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    // 处理报警消息
    handleAlarmMessage(message) {
      console.log('在布局组件中处理报警消息:', message);
      this.currentAlarmMessage = message;

      // 直接显示对话框，不再调用addMessage方法
      this.alarmDialogVisible = true;
    },
    // 关闭报警对话框
    handleAlarmClose() {
      this.alarmDialogVisible = false;
    },
    // 处理报警解决
    handleAlarmResolve() {
      console.log('报警已解决');
      this.alarmDialogVisible = false;
    }
  },
  mounted() {
    // 注册WebSocket报警通知回调
    if (WebSocketService) {
      WebSocketService.onAlarmNotification((alarmData) => {
        console.log('收到报警通知:', alarmData);
        // 显示报警对话框
        this.handleAlarmMessage(alarmData);
      });
    } else {
      console.error('WebSocket服务未初始化');
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mixin.scss";
  @import "~@/assets/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }
</style>
