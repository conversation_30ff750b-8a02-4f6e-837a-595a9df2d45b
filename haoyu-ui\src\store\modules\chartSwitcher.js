const state = {
  activeChart: 'TrendWaveformSpectrumChart'
}

const mutations = {
  SET_ACTIVE_CHART(state, chartType) {
    state.activeChart = chartType
  }
}

const actions = {
  setActiveChart({ commit }, chartType) {
    commit('SET_ACTIVE_CHART', chartType)
  },
  updateChart({ commit }, chartType) {
    commit('SET_ACTIVE_CHART', chartType)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
