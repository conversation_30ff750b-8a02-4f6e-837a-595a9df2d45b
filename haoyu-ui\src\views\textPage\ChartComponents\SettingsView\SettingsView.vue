<template>
  <div class="settings-page">
    <!-- 头部文本 -->
    <div class="header_text">部件描述</div>
    <!-- 中间内容 -->
    <div class="settings-content">
      <a-tree
        show-icon
        show-line
        :tree-data="treeData"
        :default-expand-all="true"
        class="custom-tree"
        @select="handleSelect"
      >
        <!-- 定义树节点的标题模板 -->
        <template #title="{ title }">
          <span>
            <svg-icon icon-class="copy" />
            {{ title }}
          </span>
        </template>
      </a-tree>
    </div>
    <!-- 底部操作 -->
    <div class="settings-operate">
      <el-popover
        v-model="showNewDeviceOptions"
        placement="bottom"
        trigger="click"
      >
        <!-- 新建设备类型 -->
        <DeviceTypeButtons @select="selectDeviceType" />

        <el-button slot="reference" type="primary">新建</el-button>
      </el-popover>
      <el-popover
        v-model="showDeleteConfirm"
        placement="top"
        trigger="click"
      >
        <p style="margin: 5px;">确定要删除这个设备吗？</p>
        <div style="text-align: right; margin: 0;">
          <el-button size="mini" type="text" @click="showDeleteConfirm = false">取消</el-button>
          <el-button size="mini" type="text" style="margin-left: 5px;" @click="confirmDelete">确定</el-button>
        </div>
        <el-button slot="reference" type="danger" :disabled="!isDeviceSelected">删除</el-button>
      </el-popover>
      <el-button type="warning" :disabled="!isDeviceSelected" @click="editDevice">编辑</el-button>
      <el-button type="success" :disabled="!isLinkMeasurementPointEnabled" @click="linkMeasurementPoint">关联测点</el-button>
    </div>
    <!-- 新建设备对话框 -->
    <el-dialog
      :visible.sync="newDeviceDialogVisible"
      :title="newDeviceTitle"
      width="50em"
      @close="newDeviceDialogVisible = false"
    >
      <el-form ref="newDeviceForm" :model="newDeviceForm" :rules="rules" label-width="100px">
        <el-form-item label="描述" prop="name" required>
          <el-input v-model="newDeviceForm.name" :placeholder="newDeviceplaceholder" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="newDeviceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm('newDeviceForm')">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DeviceTypeButtons from './DeviceTypeButtons/DeviceTypeButtons.vue'

export default {
  components: {
    DeviceTypeButtons
  },
  data() {
    return {
      // 页面树的初始数据
      treeData: [
        {
          title: '轴承',
          key: '0-0',
          treeIcon: 'settings-view',
          children: [
            { title: '风机', key: '0-0-0', treeIcon: 'detection-mode' },
            { title: '塔筒', key: '0-0-1', treeIcon: 'health-report' }
          ]
        }
      ],
      // 必填项
      rules: {
        name: [
          { required: true, message: '描述是必填项', trigger: 'blur' }
        ]
      },
      newDeviceForm: {
        name: ''
      },
      selectedKey: null,
      showNewDeviceOptions: false,
      newDeviceDialogVisible: false,
      showDeleteConfirm: false,
      newDeviceName: '',
      newDeviceParentKey: '',
      newDeviceTitle: '新建设备',
      newDeviceplaceholder: '请输入新设备名称'
    }
  },
  computed: {
    isDeviceSelected() {
      return this.selectedKey !== null
    },
    isLinkMeasurementPointEnabled() {
      return this.treeData.length > 0
    }
  },
  methods: {
    // 获取节点图标
    getIcon(props) {
      const iconClass = props.dataRef.treeIcon
      return <svg-icon icon-class={iconClass} />
    },
    // 监听节点点击
    handleSelect(selectedKeys, { node }) {
      if (selectedKeys.length === 0) {
        this.selectedKey = null
        this.newDeviceParentKey = ''
      } else {
        this.selectedKey = selectedKeys[0]
        this.newDeviceParentKey = node.dataRef.key
      }
    },
    // 确认删除设备
    confirmDelete() {
      const deleteNode = (nodes, keyToDelete) => {
        return nodes.filter(node => {
          if (node.key === keyToDelete) {
            return false
          }
          if (node.children) {
            node.children = deleteNode(node.children, keyToDelete)
          }
          return true
        })
      }

      this.treeData = deleteNode(this.treeData, this.selectedKey)
      this.selectedKey = null
      this.newDeviceParentKey = ''
      this.showDeleteConfirm = false
      this.$message.success('设备删除成功')
    },
    // 监听浮窗选择定义
    selectDeviceType(deviceType) {
      this.newDeviceTitle = `新建${deviceType}定义`
      this.newDeviceplaceholder = `请输入${deviceType}名称`
      this.newDeviceForm.name = deviceType
      this.newDeviceName = this.newDeviceForm.name
      this.newDeviceDialogVisible = true
      this.showNewDeviceOptions = false
    },
    // 新建确定
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.createNewDevice()
        } else {
          return false
        }
      })
    },
    // 新建设备定义
    createNewDevice() {
      if (this.newDeviceName) {
        const newDevice = {
          title: this.newDeviceName,
          key: `${this.newDeviceParentKey}-${new Date().getTime()}`,
          treeIcon: 'device'
        }
        const addNewDevice = (nodes, parentKey) => {
          for (const node of nodes) {
            if (node.key === parentKey) {
              if (!node.children) {
                this.$set(node, 'children', [])
              }
              node.children.push(newDevice)
              break
            }
            if (node.children) {
              addNewDevice(node.children, parentKey)
            }
          }
        }
        addNewDevice(this.treeData, this.newDeviceParentKey)
        this.newDeviceDialogVisible = false
      }
    },
    // 编辑设备
    editDevice() {
      // 实现编辑设备逻辑
    },
    // 关联测点
    linkMeasurementPoint() {
      // 实现关联测点逻辑
    }
  }
}
</script>

<style scoped>
@import './SettingsView.css'
</style>
