<!-- 多参量图表主组件 -->
<template>
  <div class="multi-parameter-chart" :class="{ resizing: isResizing }">
    <!-- 左侧面板：波形图和频谱图 -->
    <div v-if="!hideLeftPanel" class="left-panel" :style="{ width: leftPanelWidth + 'px' }" :class="{ 'fullscreen': fullscreenChart }">
      <div class="waveform-container" :class="{ 'fullscreen': fullscreenChart === 'waveform' }">
        <waveform-chart
          ref="waveformChart"
          :waveform-data="chartData.waveform"
          :time-range="timeRange"
          :title="currentPointName"
          @zoom="handleWaveformZoom"
          @fullscreen="handleFullScreen"
        />
      </div>
      <!-- 波形图和频谱图之间的分隔条 -->
      <div v-if="!fullscreenChart" class="resizer horizontal" @mousedown="startResize('waveform', $event)"></div>
      <div class="spectrum-container" :class="{ 'fullscreen': fullscreenChart === 'spectrum' }">
        <spectrum-chart
          ref="spectrumChart"
          :spectrum-data="chartData.spectrum"
          :freq-range="freqRange"
          :title="currentPointName"
          @zoom="handleSpectrumZoom"
          @fullscreen="handleFullScreen"
        />
      </div>
    </div>

    <!-- 非全屏时显示其他面板 -->
    <template v-if="!fullscreenChart">
      <!-- 左右面板之间的分隔条 -->
      <div v-if="!hideLeftPanel" class="resizer vertical" @mousedown="startResize('left', $event)"></div>

      <!-- 中间面板：趋势图组 -->
      <div class="middle-panel" :style="{ width: middlePanelWidth + 'px' }">
        <trend-chart-group
          :charts-data="chartData.trendData"
          :measure-def-point-map="measureDefPointMap"
          :points-info="selectedDefinitions"
          :selected-indicators="selectedIndicators"
          @zoom="handleTrendZoom"
          @dataPointClick="handleDataPointClick"
          @toggleOtherCharts="handleToggleOtherCharts"
        />
      </div>

      <!-- 中间和右侧面板之间的分隔条 -->
      <div class="resizer vertical" @mousedown="startResize('right', $event)"></div>

      <!-- 右侧控制面板 -->
      <div class="control-panel" :style="{ width: controlPanelWidth + 'px' }">
        <div class="panel-content">
          <div class="time-range">
            <time-range-selector
              :initial-range="timeRange"
              @change="handleTimeRangeChange"
            />
          </div>
          <div class="measure-points">
            <measure-point-tree
              ref="measurePointTree"
              v-if="isWaterPump"
              :device-id="deviceId"
              :initial-selected="selectedPoints"
              :selected-definitions.sync="selectedDefinitions"
              @change="handlePointsChange"
              @update:measureDefPointMap="measureDefPointMap = $event"
            />
          </div>
          <div class="indicators">
            <indicator-selector
              :initial-selected="selectedIndicators"
              :measure-point-ids="selectedPoints.map(point => point.definitionId).join(',')"
              :time-range="timeRange"
              @change="handleIndicatorChange"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import WaveformChart from './components/LeftPanel/WaveformChart.vue'
import SpectrumChart from './components/LeftPanel/SpectrumChart.vue'
import TrendChartGroup from './components/RightPanel/TrendChartGroup.vue'
import TimeRangeSelector from './components/ControlPanel/TimeRangeSelector.vue'
import MeasurePointTree from './components/ControlPanel/MeasurePointTree.vue'
import IndicatorSelector from './components/ControlPanel/IndicatorSelector.vue'
import request from '@/utils/request'
import { getMeasurePointManagement,getData,getMoreInfo } from './js/deviceApi'
import { getParams } from './utils/getParams'

export default {
  name: 'MultiParameterChart',

  components: {
    WaveformChart,
    SpectrumChart,
    TrendChartGroup,
    TimeRangeSelector,
    MeasurePointTree,
    IndicatorSelector
  },

  data() {
    return {
      currentQuery: null,  // 当前查询参数
      timeRange: {
        start: new Date(),
        end: new Date()
      },
      freqRange: {
        start: null,
        end: null
      },
      selectedPoints: [],
      selectedDefinitions: [], // 添加选中的测量定义数组
      selectedIndicators: ['accPeak', 'accPpeak'],
      chartData: {
        waveform: [],
        spectrum: [],
        trendData:[]
      },
      deviceId: '',
      leftPanelWidth: 400,
      middlePanelWidth: 400,
      controlPanelWidth: 300,
      waveformHeight: 200,
      isResizing: false,
      currentResizer: null,
      startX: 0,
      startY: 0,
      startWidth: 0,
      startHeight: 0,
      rafId: null,
      fullscreenChart: null, // 'waveform' | 'spectrum' | null
      measureDefPointMap: {},  // 添加映射关系
      hideLeftPanel: false  // 控制左边面板的显示/隐藏
    }
  },

  computed: {
    ...mapState('tree', ['selectedTreeNode']),
    ...mapState('chartSwitcher', ['activeChart']),

    isWaterPump() {
      console.log('当前节点信息:', this.selectedTreeNode)
      this.deviceId = this.selectedTreeNode.id
      if(this.selectedTreeNode.treeicon === 'waterPump' || this.selectedTreeNode.treeicon === 'waterpump'){
        return true
      }else if(this.selectedTreeNode.treeicon === 'bearing'){
        getMeasurePointManagement(this.selectedTreeNode.id).then(res => {
          this.device = res.data.deviceId
        })
        return true
      }else if(this.selectedTreeNode.treeicon === 'measureDef'){
        getData(this.selectedTreeNode.id).then(res => {
          if(res.rows.length > 0){
            this.deviceId = res.rows[0].deviceId
          }else{
            console.log('未找到波形数据')
          }
        })
        return true
      }

    },
    isActive() {
      return this.activeChart === 'MultiParameterChart'
    },
    currentPointName() {
      if (!this.selectedPoints || this.selectedPoints.length === 0) {
        return ''
      }

      // 仅返回测量定义的完整名称
      return this.selectedPoints[0].name
    }
  },

  methods: {
    // 处理窗口大小变化
    handleResize() {
      // 使用 requestAnimationFrame 来优化性能
      if (this.rafId) {
        cancelAnimationFrame(this.rafId);
      }
      this.rafId = requestAnimationFrame(() => {
        // 触发所有图表的重新渲染
        this.$refs.waveformChart && this.$refs.waveformChart.handleResize();
        this.$refs.spectrumChart && this.$refs.spectrumChart.handleResize();
      });
    },

    // 开始调整大小
    startResize(resizer, event) {
      this.isResizing = true;
      this.currentResizer = resizer;
      this.startX = event.clientX;
      this.startY = event.clientY;

      // 记录开始时的尺寸
      switch (resizer) {
        case 'left':
          this.startWidth = this.leftPanelWidth;
          break;
        case 'right':
          this.startWidth = this.controlPanelWidth;
          break;
        case 'waveform':
          this.startHeight = this.waveformHeight;
          break;
      }

      document.addEventListener('mousemove', this.handleResizing);
      document.addEventListener('mouseup', this.stopResize);
    },

    // 处理调整大小过程
    handleResizing(event) {
      if (!this.isResizing) return;

      const diffX = event.clientX - this.startX;
      const diffY = event.clientY - this.startY;

      switch (this.currentResizer) {
        case 'left':
          this.leftPanelWidth = Math.max(300, Math.min(600, this.startWidth + diffX));
          break;
        case 'right':
          this.controlPanelWidth = Math.max(200, Math.min(500, this.startWidth - diffX));
          break;
        case 'waveform':
          this.waveformHeight = Math.max(100, Math.min(window.innerHeight - 200, this.startHeight + diffY));
          break;
      }

      // 触发图表重新渲染
      this.handleResize();
    },

    // 停止调整大小
    stopResize() {
      this.isResizing = false;
      document.removeEventListener('mousemove', this.handleResizing);
      document.removeEventListener('mouseup', this.stopResize);
    },

    // 初始化数据
    async initData() {
      console.log('初始化数据')
      // 设置默认时间范围为最近1小时
      const end = new Date()
      const start = new Date(end.getTime() - 60 * 60 * 1000)
      this.timeRange = { start, end }

      // 获取初始数据
      // await // this.fetchData()
    },

    // 处理波形图缩放
    handleWaveformZoom(range) {
      // 只更新波形图内部的显示区间，不动 this.timeRange
      if (this.$refs.waveformChart && this.$refs.waveformChart.updateTimeRange) {
        this.$refs.waveformChart.updateTimeRange({
          start: new Date(range.start),
          end: new Date(range.end)
        });
      }
      // 不再修改 this.timeRange
    },

    // 处理频谱图缩放
    handleSpectrumZoom(range) {
      this.freqRange = range
    },

    // 处理趋势图缩放
    handleTrendZoom(range) {
      // 只更新趋势图内部的显示区间，不动 this.timeRange
      if (this.$refs.trendChartGroup && this.$refs.trendChartGroup.updateTimeRange) {
        this.$refs.trendChartGroup.updateTimeRange({
          start: new Date(range.start),
          end: new Date(range.end)
        });
      }
      // 不再修改 this.timeRange
    },

    // 处理时间范围变更
    handleTimeRangeChange(range) {
      this.timeRange = {
        start: new Date(range.start),
        end: new Date(range.end)
      }
      // this.fetchData()
    },

    // 处理测点选择变更
    handlePointsChange(points) {
      this.selectedPoints = points;
      this.selectedDefinitions = points; // 同时更新selectedDefinitions
      console.log('更新选中的测点和定义:', points);
    },

    // 处理指标选择变更
    handleIndicatorChange(data) {
      console.log('接收到的数据:', data)
      // data 可能是接口返回的完整数据，也可能是指标数组
      // 如果 data 是数组，更新 selectedIndicators，否则更新 chartData
      if (Array.isArray(data)) {
        this.selectedIndicators = data;
      } else {
        this.chartData = data;
      }
    },

    handleFullScreen(chartType) {
      if (this.fullscreenChart === chartType) {
        this.fullscreenChart = null;
      } else {
        this.fullscreenChart = chartType;
      }

      // 重新调整图表大小
      this.$nextTick(() => {
        // 等待 DOM 更新完成后重新初始化图表
        setTimeout(() => {
          if (chartType === 'waveform' || this.fullscreenChart === null) {
            this.$refs.waveformChart && this.$refs.waveformChart.initChart();
            this.$refs.waveformChart && this.$refs.waveformChart.updateChart(this.chartData.waveform);
          }
          if (chartType === 'spectrum' || this.fullscreenChart === null) {
            this.$refs.spectrumChart && this.$refs.spectrumChart.initChart();
            this.$refs.spectrumChart && this.$refs.spectrumChart.updateChart(this.chartData.spectrum);
          }
          // 触发窗口 resize 事件以确保图表大小正确
          window.dispatchEvent(new Event('resize'));
        }, 100);
      });
    },

    // 获取测量定义数据并请求图表数据
    async getMeasurementDefinition(pointId, measureDefId, dataPoint) {
      try {
        const response = await request.get(`/measurePoint/measurePointManagement/definition/${pointId}`);
        if (response.code === 200 && response.data) {
          // 在测量定义列表中找到对应的定义
          const measureDef = response.data.measurementDefinitionList.find(def => def.id === Number(measureDefId));
          if (measureDef) {
            // 构建参数，确保转换为数字类型
            const fs = [
              Number(measureDef.upperLimitFrequency),
              Number(measureDef.numberOfSpectralLines)
            ];

            // 构建波形图参数
            const waveformParams = getParams(
              1,  // ftype: 波形图
              dataPoint.recordId,  // 使用 recordId 替换 deviceId
              measureDef.factoryManagementId,  // point_id
              dataPoint.timePoint,  // time_point
              measureDef.timeSignalUnitId,  // timeSignalUnitId
              fs  // fs
            );

            // 构建频谱图参数
            const spectrumParams = getParams(
              2,  // ftype: 频谱图
              dataPoint.recordId,  // 使用 recordId 替换 deviceId
              measureDef.factoryManagementId,  // point_id
              dataPoint.timePoint,  // time_point
              measureDef.timeSignalUnitId,  // timeSignalUnitId
              fs  // fs
            );

            console.log('波形图参数:', waveformParams);
            console.log('频谱图参数:', spectrumParams);

            // 请求波形图数据
            const waveformResponse = await request.post('/python/render/post/waveform_graph', waveformParams);
            if (waveformResponse.code === 200) {
              // 直接使用返回的 x 和 y 数组
              const { x, y } = waveformResponse.data;
              this.chartData.waveform = x.map((xVal, index) => [xVal, y[index]]);

              // 不再更新全局的timeRange，只更新波形图的局部时间范围
              if (x.length > 0) {
                // 使用局部变量，不影响全局timeRange
                const waveformTimeRange = {
                  start: x[0],
                  end: x[x.length - 1]
                };
                // 如果需要，可以将波形图的时间范围传递给波形图组件
                if (this.$refs.waveformChart) {
                  this.$refs.waveformChart.updateTimeRange(waveformTimeRange);
                }
              }
            }

            // 请求频谱图数据
            const spectrumResponse = await request.post('/python/render/post/waveform_graph', spectrumParams);
            if (spectrumResponse.code === 200) {
              // 直接使用返回的 x 和 y 数组
              const { x, y } = spectrumResponse.data;
              this.chartData.spectrum = x.map((xVal, index) => [xVal, y[index]]);

              // 更新频率范围
              if (x.length > 0) {
                this.freqRange = {
                  start: x[0],
                  end: x[x.length - 1]
                };
              }
            }
          } else {
            console.warn('未找到对应的测量定义');
          }
        }
      } catch (error) {
        console.error('获取测量定义数据失败:', error);
      }
    },

    handleDataPointClick(dataPoint) {
      console.log('数据点点击:', dataPoint);
      // 通过测点ID获取测量定义数据并请求图表数据
      this.getMeasurementDefinition(dataPoint.pointId, dataPoint.measureDefId, dataPoint);
    },

    // 处理切换其他图表显示/隐藏
    handleToggleOtherCharts(shouldHide) {
      this.hideLeftPanel = shouldHide;

      // 当隐藏左边面板时，可以触发中间面板的重新渲染以适应新的宽度
      if (shouldHide) {
        // 隐藏左边面板，中间面板可以占用更多空间
        this.$nextTick(() => {
          // 触发趋势图的重新渲染
          window.dispatchEvent(new Event('resize'));
        });
      } else {
        // 显示左边面板，恢复原有布局
        this.$nextTick(() => {
          // 重新初始化左边面板的图表
          if (this.$refs.waveformChart) {
            this.$refs.waveformChart.initChart();
            this.$refs.waveformChart.updateChart(this.chartData.waveform);
          }
          if (this.$refs.spectrumChart) {
            this.$refs.spectrumChart.initChart();
            this.$refs.spectrumChart.updateChart(this.chartData.spectrum);
          }
          // 触发所有图表的重新渲染
          window.dispatchEvent(new Event('resize'));
        });
      }
    },
  },

  created() {
    if (this.isActive && this.isWaterPump) {
      console.log('组件创建时初始化数据')
      this.initData()
    }
  },

  mounted() {
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }
    document.removeEventListener('mousemove', this.handleResizing);
    document.removeEventListener('mouseup', this.stopResize);
  }
}
</script>

<style lang="scss" scoped>
.multi-parameter-chart {
  display: flex;
  height: 100%;
  width: 100%;
  background: #fff;
  position: relative;

  .left-panel {
    display: flex;
    flex-direction: column;
    min-width: 300px;

    &.fullscreen {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100% !important;
      z-index: 1000;
    }

    .waveform-container,
    .spectrum-container {
      flex: 1;
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;
      position: relative;

      &.fullscreen {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1001;
        border-radius: 0;
      }
    }

    // 移除第一个容器的上边距和最后一个容器的下边距
    .waveform-container {
      margin: 10px 10px 0 10px;

      &.fullscreen {
        margin: 0;
      }
    }

    .spectrum-container {
      margin: 0 10px 10px 10px;

      &.fullscreen {
        margin: 0;
      }
    }
  }

  .middle-panel {
    flex: 1;
    margin: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    min-width: 300px;
  }

  .resizer {
    background-color: #e8e8e8;
    transition: background-color 0.3s;
    z-index: 1;

    &:hover,
    &:active {
      background-color: #409EFF;
    }

    &.vertical {
      width: 4px;
      cursor: col-resize;
      margin: 10px 0;
    }

    &.horizontal {
      height: 4px;
      cursor: row-resize;
      margin: 0;
    }
  }

  .control-panel {
    min-width: 200px;
    max-width: 500px;
    background: #f5f7fa;
    border-left: 1px solid #ddd;
    transition: width 0.1s ease;
    display: flex;
    flex-direction: column;

    .panel-content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;

      .time-range {
        margin-bottom: 20px;
        padding: 5px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .measure-points,
      .indicators {
        margin-bottom: 20px;
        padding: 15px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .measure-points {
        overflow: visible;
      }

      // 移除最后一个卡片的下边距
      .indicators {
        margin-bottom: 0;
      }
    }
  }
}

// 添加用户选择限制，防止拖动时选中文本
.multi-parameter-chart.resizing {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  * {
    pointer-events: none;
  }

  .resizer {
    pointer-events: auto;
  }
}
</style>
