<template>
  <div class="detailed-container">
    <h2>采集站--设备关联</h2>
    <div class="node-list-container">
      <p class="section-title">检测设备</p>
      <ul class="node-list">
        <li v-for="(node, index) in selectedNodes" :key="index" class="node-item">
          <span>{{ node.title }}</span>
          <el-button type="danger" icon="el-icon-minus" class="node-buttom" size="mini" @click="removeNode(index, node)" />
        </li>
      </ul>
    </div>
    <el-tabs v-model="activeTab" type="card" class="custom-tabs">
      <el-tab-pane label="振动通道" name="vibration">
        <div class="tab-content">
          <el-table :data="vibrationChannels" style="width: 100%" border>
            <el-table-column
              v-for="(column, index) in tableTitle"
              :key="index"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.type === 'dropdown' ? 150 : 100"
            >
              <!-- 通用 slot-scope 用于处理所有类型 -->
              <template slot-scope="scope">
                <!-- 输入框类型 -->
                <el-input
                  v-if="column.type === 'input'"
                  v-model="scope.row[column.prop]"
                  size="small"
                />

                <!-- 复选框类型 -->
                <el-checkbox
                  v-else-if="column.type === 'checkbox'"
                  v-model="scope.row[column.prop]"
                  :true-label="1"
                  :false-label="0"
                />

                <!-- 下拉框类型 -->
                <el-select
                  v-else-if="column.type === 'dropdown'"
                  v-model="scope.row[column.prop]"
                  clearable
                  size="small"
                  placeholder="请选择"
                >
                  <!-- 暂时只用于关联测点的下拉框数据 -->
                  <el-option
                    v-for="(id, title) in PointsOptions"
                    :key="id"
                    :label="title"
                    :value="id"
                  />
                  <!-- <el-option
                    v-for="(id, title) in filterOptions(PointsOptions)"
                    :key="id"
                    :label="title"
                    :value="id"
                  /> -->
                </el-select>

                <!-- 默认显示类型 -->
                <span v-else>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import {
  getBaseStationDeviceList,
  getDeviceMeasurePointList,
  getCollectorTypeList,
  getBaseStationTableList,
  getBaseStationParamList
} from '@/api/haoyu-system/OnlineManagement/BaseStationTree'

export default {
  name: 'RelevancyDetailed',
  data() {
    return {
      activeTab: 'vibration', // 默认显示“振动通道”标签
      companyId: null, // 公司ID
      nextLevelNodes: [], // 存放下一级节点的列表
      // 公司的列表，到时候重新获取一边在这里
      companies: [],
      // 获取表格数据列表
      vibrationChannels: [],
      // 获取表格元素
      tableTitle: [],
      // 关联测点下拉框数据
      PointsOptions: []
    }
  },
  computed: {
    ...mapGetters('SettingConnect', ['selectedNodes', 'selectedNodeID', 'dialogConfirmed']) // 使用命名空间 'SettingConnect' 的 mapGetters
  },
  watch: {
    selectedNodes(newVal, oldVal) {
      this.getSelect()
      this.updateNextLevelNodes()
    },
    // 监听基站id如果更改就执行以下代码
    selectedNodeID(newVal, oldVal) {
      console.log('selectedNodeID', newVal)
      // 重新初始化数据
      this.initializeData()
    },
    
    dialogConfirmed(newVal, oldVal) {
      if (newVal && !oldVal) {
        // 当 dialogConfirmed 变为 true 时，执行逻辑
        this.initializeData() // 或其他需要的操作
        this.$store.dispatch('SettingConnect/resetDialogConfirmed') // 重置状态
      }
    }

    // 监听下拉框选项
    // vibrationChannels: {
    //   handler(newVal) {
    //     this.handleSelectChange()
    //   },
    //   deep: true
    // }
  },
  created() {
    // console.log('created', this.$store.state.SettingConnect.companyId)
    // this.companyId = this.$store.state.SettingConnect.companyId
    // this.getSelect()
    this.initializeData()
  },
  methods: {
    ...mapActions('SettingConnect', {
      removeNodeFromStore: 'removeNode', // 将 Vuex 中的 removeNode action 映射为 removeNodeFromStore 方法
      clearNodes: 'clearNodes'
    }),
    // 初始化数据
    initializeData() {
      this.companyId = this.$store.state.SettingConnect.companyId
      this.getSelect()
      getBaseStationParamList(this.selectedNodeID).then(res => {
        // 重新清空vuex里面的数据防止上次取消弹窗的时候未清空
        this.clearNodes()

        const deviceIds = res.data.device_moitor.split(',').filter(id => id.trim() !== '') // 过滤掉空字符串
        const deviceNames = res.data.device_moitor_name.split(',').filter(name => name.trim() !== '')
        // 构建节点对象
        const storeNodes = deviceIds.map((id, index) => ({
          title: deviceNames[index] || '', // 使用设备名称，如果没有名称则为空
          key: '', // 设为默认值为空
          id: Number(id)
        }))
        // 初始化关联测点下拉框

        this.getPointsOptions(storeNodes)

        // 将节点存储到 Vuex
        storeNodes.forEach(node => {
          this.$store.dispatch('SettingConnect/addNode', node)
        })
      })
      // 初始化默认下拉选项
      getCollectorTypeList(this.selectedNodeID).then(res => {
        this.stationTypes = res.rows
        // 获取指定基站的表格数据
        getBaseStationTableList(this.selectedNodeID).then(res => {
          console.log('获取初始数据', res)
          this.tableTitle = res.table[0].table_title
          this.vibrationChannels = res.table[0].table_value
        })
        // console.log('编辑模式下获取采集类型初始数据')
      })
    },
    // 获取关联测点下拉框属性
    getPointsOptions(node) {
      const ids = node.map(n => n.id) // 根据你的节点数据结构，修改 `node.id` 以匹配实际 ID 字段
      if (ids.length !== 0) {
        getDeviceMeasurePointList(ids).then(res => {
          this.PointsOptions = res.data
        }).catch(error => {
          console.error('获取设备测点列表失败:', error)
        })
      } else {
        console.log('没有可用的关联测点')
      }
    },
    // 获取下拉框数据
    getSelect() {
      getBaseStationDeviceList(this.companyId).then(res => {
        this.companies = res.data
      }).catch(error => {
        console.error('获取基站设备列表失败:', error)
      })
    },
    // 删除监测的设备
    removeNode(index, node) {
      this.$confirm('您确定要删除这个节点吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.removeNodeFromStore(index)
        // 清空下拉框的选择
        // this.clearSelectedOptions(node)
      })
    },
    // 根据 selectedNodes 找到下一级节点
    updateNextLevelNodes() {
      // 提取选中节点的 ID
      const ids = this.selectedNodes.map(node => node.id) // 根据你的节点数据结构，修改 `node.id` 以匹配实际 ID 字段
      if (ids.length !== 0) {
        getDeviceMeasurePointList(ids).then(res => {
          this.PointsOptions = res.data
        }).catch(error => {
          console.error('获取设备测点列表失败:', error)
        })
      } else {
        console.log('没有可用的关联测点')
      }
    }

    // // 处理下拉框选项变化
    // handleSelectChange(selectedValue) {
    //   // 更新 selectedOptions 数组
    //   this.selectedOptions = this.vibrationChannels
    //     .map(channel => channel.selectedOption)
    //     .filter(option => option !== undefined && option !== null)
    // },
    // // 过滤下拉框选项
    // filterOptions(options) {
    //   // 过滤掉已被选择的选项
    //   return Object.entries(options).filter(([id, title]) => {
    //     return !this.selectedOptions.includes(Number(id))
    //   }).map(([id, title]) => ({
    //     id: Number(id),
    //     title
    //   }))
    // }

  }
}
</script>

<style scoped>
.detailed-container {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #007bff; /* 标题颜色，选择亮蓝色 */
  font-size: 18px; /* 标题字体大小 */
  font-weight: bold; /* 标题加粗 */
  margin-bottom: 15px; /* 标题和表格之间的距离 */
  border-bottom: 2px solid #007bff; /* 标题下方的分隔线 */
  padding-bottom: 8px;
}

.node-list-container {
  width: 100%;
  height: 15%; /* 保持容器高度为 10% */
  background-color: #f9f9f9; /* 背景颜色 */
  border: 1px solid #ddd; /* 边框样式 */
  border-radius: 4px; /* 边框圆角 */
  padding: 10px; /* 内边距 */
  margin-bottom: 20px; /* 和下方内容的距离 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微的阴影 */
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px; /* 标题和列表之间的距离 */
}

.node-list {
  display: flex; /* 使用 Flexbox 布局 */
  flex-wrap: wrap; /* 当空间不足时换行 */
  gap: 10px; /* 项目之间的间距 */
  list-style: none; /* 去除默认的列表样式 */
  padding: 0; /* 去除默认的内边距 */
  margin: 0; /* 去除默认的外边距 */
}

.node-item {
  display: flex;
  height: 50%;
  align-items: center; /* 垂直居中对齐 */
  padding: 5px 10px; /* 内边距 */
  border: 1px solid #ddd; /* 边框样式 */
  border-radius: 4px; /* 边框圆角 */
  background-color: #ffffff; /* 背景颜色 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微的阴影 */
}

.node-item span {
  margin-right: 10px; /* 文本和按钮之间的间距 */
  font-size: 14px; /* 字体大小 */
  color: #333; /* 字体颜色 */
}

.node-buttom {
  padding: 5px;
  font-size: small;
}

.el-button {
  margin: 0; /* 移除按钮默认外边距 */
}

.custom-tabs .el-tabs__header {
  background-color: #f1f1f1;
  border-bottom: 2px solid #e0e0e0;
}

.tab-content {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  width: 100%;
}

.el-table {
  width: 100%;
  background-color: #ffffff;
}

.el-table th {
  background-color: #f9f9f9;
  color: #333;
}

::v-deep .el-tabs__content {
  width: 100%; /* 当内容超出时启用水平滚动条 */
}
</style>
