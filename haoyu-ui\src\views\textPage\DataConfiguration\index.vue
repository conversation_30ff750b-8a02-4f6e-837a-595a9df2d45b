<template>
  <div style="height: 100%">
    <div class="container1">
      <div ref="topContainer" class="half-height">
        <WorkTree ref="workTree" />
      </div>
      <div class="half-height other-area">
        <DataSheet />
      </div>
    </div>
  </div>
</template>

<script>
// 引入 Tree 组件
import WorkTree from '../WorkTree/WorkTree.vue'
import DataSheet from '../DataSheet/DataSheet.vue'
export default {
  components: {
    WorkTree,
    DataSheet
  }
}
</script>

<style scoped>
@import './index.css'
</style>
