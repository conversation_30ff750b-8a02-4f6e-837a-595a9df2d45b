<template>
  <div class="config-setup-component">
    <!-- <el-button class="back-button" size="mini" style="margin-left: 5px;" @click="groupCopy">组态复制</el-button> -->
    <div class="content">
      <!-- 左侧部分 -->
      <div class="left1-section" @click="handleTreeContainerClick">
        <h3 :style="{ color: 'red', fontWeight: 'bold' }">{{ nodeData.title || currentTitle }}</h3>

        <el-tree
          ref="ConfigSetupTree"
          :data="treeData"
          :default-expand-all="true"
          :draggable="true"
          :props="defaultProps"
          :render-content="renderContent"
          node-key="id"
          highlight-current
          @node-click="selectNode"
          @node-drop="handleDrop"
        ></el-tree>

      </div>

      <!-- 右侧部分 -->
      <div class="right-section">
        <!-- 轴承专用组件 -->
        <bearing-dialog
          v-if="dialogType === 'bearing'"
          style="height: 100%;"
          :visible="showBearingDialog"
          :default-value="defaultValue"
          :data-list="bearingDataList"
          :edit-model="editConfiguration"
          :bearing-description-data="bearingDescriptionData"
          @confirm="handleBearingConfirm"
          @cancel="handleBearingCancel"
          @handle-current-change="TableCurrentChange"
          @submit-bearing-info="tableBearingSubmit"
        />

        <!-- 平行轴系 -->
        <ParallelAxisComponent
          v-if="dialogType === 'parallelShafting' || dialogType === 'noDeleteParallelShafting'"
          :key="componentKey"
          :form-data="componentData"
          :device-id="currentDevicesId"
          :table-data-tree="treeData"
          :selected-node="selectedConfiNode"
          :parent-node="parentNodeMsg"
          :edit-model="editConfiguration"
          @submit="handleSubmitGearbox"
        />

        <!-- 行星轴系 -->
        <PlanetaryAxisComponent
          v-if="dialogType === 'planetaryShaftSys' || dialogType === 'noDeletePlanetaryShaftSys'"
          :form-data="componentData"
          :device-id="currentDevicesId"
          :selected-node="selectedConfiNode"
          :edit-model="editConfiguration"
          :table-data-tree="treeData"
          @submit="handleSubmitGearbox"
        />

      </div>
    </div>
    <!-- 操作按钮容器 -->
    <div class="action-buttons">
      <!-- 新建按钮及下拉菜单 -->
      <el-dropdown placement="top" trigger="click" class="dropdown-link">
        <el-button class="el-dropdown-link" size="mini" type="primary">
          新建<i class="el-icon-arrow-up el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <!-- 轴 -->
          <el-dropdown-item @click.native="handleMenuSelect('轴', 'axis')">
            <svg-icon icon-class="axis" />
            轴
          </el-dropdown-item>

          <!-- 齿轮箱 -->
          <el-dropdown-item>
            <el-dropdown ref="subDropdown" trigger="click" placement="right-start" :show-timeout="0">
              <span @mouseenter="() => {$refs.subDropdown.show()}" @click.stop>
                <svg-icon icon-class="gearbox" />
                齿轮箱
                <i class="el-icon-arrow-right el-icon--right" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <!-- 行星轴系 -->
                <el-dropdown-item>
                  <el-dropdown trigger="hover" placement="right-start" :show-timeout="0">
                    <span>
                      <svg-icon icon-class="planetaryShaftSys" />
                      行星轴系
                      <i class="el-icon-arrow-right el-icon--right" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="handleMenuSelect('行星架输入', 'noDeletePlanetaryShaftSys', '1-1')">
                        <svg-icon icon-class="planetaryShaftSys" />
                        行星架输入
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="handleMenuSelect('太阳轮输入', 'noDeletePlanetaryShaftSys', '1-2')">
                        <svg-icon icon-class="planetaryShaftSys" />
                        太阳轮输入
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </el-dropdown-item>

                <!-- 平行轴系 -->
                <el-dropdown-item>
                  <el-dropdown trigger="hover" placement="right-start" :show-timeout="0">
                    <span>
                      <svg-icon icon-class="parallelShafting" />
                      平行轴系
                      <i class="el-icon-arrow-right el-icon--right" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="handleMenuSelect('一级平行', 'parallelShafting', '2-1')">
                        <svg-icon icon-class="parallelShafting" />
                        一级平行
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="handleMenuSelect('二级平行', 'parallelShafting', '2-2')">
                        <svg-icon icon-class="parallelShafting" />
                        二级平行
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="handleMenuSelect('三级平行', 'parallelShafting', '2-3')">
                        <svg-icon icon-class="parallelShafting" />
                        三级平行
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="handleMenuSelect('四级平行', 'parallelShafting', '2-4')">
                        <svg-icon icon-class="parallelShafting" />
                        四级平行
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-dropdown-item>

          <!-- 轴承 -->
          <el-dropdown-item :disabled="isDisabled" @click.native="handleMenuSelect('轴承', 'bearing')">
            <svg-icon icon-class="bearing" />
            轴承
          </el-dropdown-item>

          <!-- 叶片 -->
          <el-dropdown-item :disabled="isDisabled" @click.native="handleMenuSelect('叶片', 'blade')">
            <svg-icon icon-class="blade" />
            叶片
          </el-dropdown-item>

          <!-- 塔筒 -->
          <el-dropdown-item @click.native="handleMenuSelect('塔筒', 'towers')">
            <svg-icon icon-class="towers" />
            塔筒
          </el-dropdown-item>

          <!-- 发电机 -->
          <el-dropdown-item @click.native="handleMenuSelect('发电机', 'dynamo')">
            <svg-icon icon-class="dynamo" />
            发电机
          </el-dropdown-item>

          <!-- 电机 -->
          <el-dropdown-item @click.native="handleMenuSelect('电机', 'motor')">
            <svg-icon icon-class="motor" />
            电机
          </el-dropdown-item>

          <!-- 泵 -->
          <el-dropdown-item @click.native="handleMenuSelect('泵', 'Pump')">
            <svg-icon icon-class="Pump" />
            泵
          </el-dropdown-item>

          <!-- 风机 -->
          <el-dropdown-item @click.native="handleMenuSelect('风机', 'fans')">
            <svg-icon icon-class="fans" />
            风机
          </el-dropdown-item>

          <!-- 皮带和皮带轮 -->
          <el-dropdown-item @click.native="handleMenuSelect('皮带和皮带轮', 'pulleys')">
            <svg-icon icon-class="pulleys" />
            皮带和皮带轮
          </el-dropdown-item>

          <!-- 联轴器 -->
          <el-dropdown-item @click.native="handleMenuSelect('联轴器', 'coupling')">
            <svg-icon icon-class="coupling" />
            联轴器
          </el-dropdown-item>

          <!-- 辊子 -->
          <el-dropdown-item @click.native="handleMenuSelect('辊子', 'roller')">
            <svg-icon icon-class="roller" />
            辊子
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!-- 其他按钮 -->
      <el-button type="warning" size="mini" :disabled="selectedConfiNodeKey === null" @click="handleEdit">
        编辑
      </el-button>
      <el-button type="danger" size="mini" :disabled="noDelete" @click="handleDelete">
        删除
      </el-button>
      <el-button type="success" size="mini" @click="handleAssociate">
        关联测点
      </el-button>
      <el-button size="mini" @click="groupCopy">
        组态复制
      </el-button>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      title="组态复制"
      :visible.sync="groupCopyDialogVisible"
      width="40%"
      :close-on-click-modal="false"
      height="300px"
      @close="closeDialog"
    >
      <!-- 设备树 -->
      <div style="max-height: 30vh; min-height: 30vh; max-width: 100%; overflow: auto;">
        <el-tree
          ref="deviceTree"
          :data="deviceTreeData"
          :show-checkbox="false"
          node-key="id"
          :props="defaultProps"
          highlight-current
          :render-content="renderTreeNode"
          @check="handleDeviceTreeCheck"
        />
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="confirmGroupCopy">确认</el-button>
      </span>
    </el-dialog>
    <!-- 输入框弹窗组件 -->
    <input-dialog
      :visible="showGenericDialog"
      :title="dialogTitle"
      :dialog-data-list="dialogDataList"
      :tree-icon="dialogType"
      :default-value="dialogDefaultValue"
      :table-data-tree="treeData"
      :selected-node="selectedConfiNode"
      :edit-model="editConfiguration"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
    />

    <!-- 叶片专用弹窗 -->
    <create-blade-dialog
      v-if="dialogType === 'blade'"
      :visible="showCreateBladeDialog"
      :blade-data="bladeData"
      :selected-node="selectedConfiNode"
      :edit-mode="editConfiguration"
      @confirm="handleDialogConfirm"
      @cancel="handleCreateBladeCancel"
    />  

    <!-- 关联测点弹窗 -->
    <el-dialog
      title="关联测点"
      :visible.sync="showRelationDialog"
      width="80%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :modal-append-to-body="false"
      custom-class="draggable-dialog"
      v-dialogDrag
    >
      <relation-page 
        v-if="showRelationDialog"
        :current-device="currentNode"
        @close="handleRelationClose"
      />
    </el-dialog>
  </div>
</template>

<script>
import InputDialog from './InputDialog.vue'
import BearingDialog from './BearingDialog.vue'
import CreateBladeDialog from './CreateBladeDialog.vue'
import { getMenuItems } from './ConfigSetupData'
import ParallelAxisComponent from './ParallelAxisComponent.vue'
import PlanetaryAxisComponent from './PlanetaryAxisComponent.vue'
import {
  getGroupComponent,
  addGroupComponent,
  deleteGroupComponent,
  getGroupComponentInfo,
  updateGroupComponent,
  getGroupComponentInfoByCode,
  copyGroupComponent,
  batchUpdateGroupComponent,
  batchUpdateGroupComponentList
} from '@/api/haoyu-system/SystemAdministration/Configuration.js'
import {
  listDeviceManagement,
} from '@/api/haoyu-system/deviceManagement/deviceManagement.js'
import { mapState } from 'vuex'
import RelationPage from '../relationPage/index.vue'
export default {
  components: {
    InputDialog,
    BearingDialog,
    CreateBladeDialog,
    ParallelAxisComponent,
    PlanetaryAxisComponent,
    RelationPage
  },
  props: {
    nodeData: {
      type: Object,
      required: false,
      default: () => ({
        id: null,
        title: ''
      })
    }
  },
  data() {
    return {
      bladeData: {}, // 叶片的数据
      isDisabled: true,
      bearingDescriptionData: [], // 轴承描述的弹窗数据
      waterPumpIds: [], // 批量组态复制存储所选择的设备数组(存储所选设备id)
      deviceTreeData: [], // 设备树
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      groupCopyDialogVisible: false, // 控制组态复制弹窗显示
      parentNodeMsg: null, // 父级信息
      nodeHashTable: [], // 哈希表
      noDelete: true,
      menuItems: getMenuItems(this.handleMenuSelect), // 引入菜单
      componentData: null, // 用来组织传入组件结构
      editConfiguration: false, // 组态编辑状态
      dialogVisible: false, // 弹窗
      dialogTitle: '', // 弹窗名称
      dialogDataList: [], // 传给输入框或者其他弹窗的需要用到的数据，也就是选中的节点数据
      dialogDefaultValue: '',
      // 轴承弹窗信息
      showGenericDialog: false,
      showBearingDialog: false,
      dialogType: '',
      defaultValue: '',
      // 叶片弹窗属性
      showCreateBladeDialog: false,
      currentTitle: '', // 当前设备名称
      selectedConfiNode: null, // 组态树选中的节点
      selectedConfiNodeKey: null, // 记录当前选中的组态节点的 key
      treeData: [], // 组态树
      showDropdown: false, // 控制下拉菜单的显示与隐藏
      bearingDataList: [],
      // 用于存储轴承弹窗表格选中的数据
      currentSelect: [],
      // 传入的设备id
      currentDevicesId: null,
      // 提交表单的信息
      newNodeMsg: {
        id: '',
        parentId: '', // 设备id
        deviceName: '轴', // 设备名称
        treeIcon: 'axis', // 图标
        mongodbId: '', // 轴承id（如果新建的不是轴承就传null）
        inputSpeed: '', // 转速
        table: {} // 预留给有配置的部件
      },
      componentKey: 0, // 添加组件key
      showRelationDialog: false, // 添加关联测点弹窗控制变量
    }
  },
  watch: {
    // 监听传入的 nodeData 变化
    nodeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.id) {  // 添加空值检查
          this.currentNode = newValue
          console.log('this.currentNode', this.currentNode)
          this.currentDevicesId = newValue.id
          console.log('nodeData12312321312321', newValue)
          if (this.selectedConfiNode !== null) {
            this.selectedConfiNode.id = null
            this.Reset()
          }
          this.getGroupComponentList()
        }
      },
      immediate: true,  // 添加 immediate: true
      deep: true
    },
    // 监听 selectedConfiNode 变化
    selectedConfiNode: {
      handler(newValue) {
        if (!newValue) {
          // 如果 selectedConfiNode 是空的
          this.noDelete = true
        } else if (newValue.treeIcon.startsWith('noDelete')) {
          // 如果 treeIcon 以 'noDelete' 开头
          this.noDelete = true
        } else {
          // 如果 treeIcon 是 'parallelShafting'
          this.noDelete = false
        }
      },
      immediate: true, // 立即执行
      deep: true // 深度监听 selectedConfiNode 的变化
    }
  },
  computed:{
    ...mapState('SettingConnect', ['configNodeInfo'])
  },
  mounted(){
    console.log('组态设置节点信息:', this.configNodeInfo)
    // 验证从诊断分析树传来的节点信息
    if (this.configNodeInfo) {
      // 验证节点信息的合法性
      if (this.configNodeInfo.id && this.configNodeInfo.title) {
        // 更新当前设备ID
        this.currentDevicesId = this.configNodeInfo.id
        this.currentTitle = this.configNodeInfo.title
        // 获取组态树
        this.getGroupComponentList()
      } else {
        this.$message.error('节点信息不完整，请重新选择节点')
      }
    }
    // 确保组件挂载时也能获取数据
    if (this.nodeData && this.nodeData.id) {
      this.currentDevicesId = this.nodeData.id
      this.getGroupComponentList()
    }
  },
  created() {
    // 获取设备树所有信息
    listDeviceManagement().then(res => {
      this.deviceTreeData = res.data
    })
    this.newNodeMsg.parentId = this.currentDevicesId
  },
  methods: {
    // 自定义渲染节点内容
    renderContent(h, { node, data, store }) {
      return h(
        'span',
        { style: { display: 'flex', alignItems: 'center' }},
        [
          this.getIcon({ dataRef: data }),
          h('span', { style: { marginLeft: '8px' }}, data.title)
        ]
      )
    },
    // 处理拖拽事件
    handleDrop(draggingNode, dropNode, dropType, ev) {
      const dragPos = draggingNode.level
      const dropPos = dropNode.level

      // 检查是否在同一级
      if (dragPos !== dropPos && draggingNode.data.id !== dropNode.data.id) {
        this.$message.error('只能够在同级目录下移动组态部件')
        return // 阻止移动操作
      }

      // 不能移动到同级节点的内部
      if (dropType !== 'inner') {
        this.$message.error('不能将组态部件移动到同级组态部件的内部')
        return // 阻止移动操作
      }

      // 弹出确认框
      this.$confirm(
        `确定将节点 "${draggingNode.label}" 移动到节点 "${dropNode.label}" ${dropType === 'before' ? '之前' : dropType === 'after' ? '之后' : '之内'} 吗？`,
        '确认移动',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 更新逻辑...
        batchUpdateGroupComponentList(this.treeData).then(() => {
          this.$message.success('更新成功')
          this.getGroupComponentList()
        })
      }).catch(() => {
        // 用户点击取消，不进行任何操作
        console.log('用户取消了移动操作')
      })
    },
    // 设备树选中之后触发的方法
    handleDeviceTreeCheck(checkedNodes, checkedKeys) {
      // 每次处理前清空数组，避免数据累积
      this.waterPumpIds = []
      // 遍历选中的节点，检查 treeIcon 是否为 waterPump
      checkedNodes.forEach(node => {
        if (node.treeIcon === 'waterPump') {
          this.waterPumpIds.push(node.id) // 如果 treeIcon 是 waterPump，就把 ID 加入数组
        }
      })
    },
    // 组态复制
    groupCopy() {
      this.groupCopyDialogVisible = true
    },
    // 关闭弹窗
    closeDialog() {
      // 清空 waterPumpIds 数组
      this.waterPumpIds = []
      // 重置 el-tree 选中状态
      this.$refs.deviceTree.setCheckedKeys([]) // 清空树的选中状态
      this.groupCopyDialogVisible = false
    },
    // 获取选中的节点并确认
    confirmGroupCopy() {
      // 先弹出确认对话框
      this.$confirm('确定要进行组态复制吗？组态复制会覆盖原有的组态！', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击了"确定"后执行复制操作
        const waterPumpIdsString = this.waterPumpIds.join(',') // 转化成字符串提交后端

        // 执行组态复制提交
        copyGroupComponent(this.currentDevicesId, waterPumpIdsString).then(() => {
          this.$message.success('组态复制成功')
          this.closeDialog() // 复制成功后关闭弹窗
        }).catch(() => {
          this.$message.error('组态复制失败')
        })
      }).catch(() => {})
    },
    renderTreeNode(h, { node, data }) {
      // 使用 getIcon 方法获取图标
      const icon = this.getIcon({ dataRef: data })

      return h('div', { style: { display: 'flex', alignItems: 'center' }}, [
        data.treeIcon === 'waterPump'
          ? h('div', { style: { display: 'flex', justifyContent: 'center', alignItems: 'center' }}, [
            h('el-checkbox', {
              props: {
                value: node.checked // 使用节点的选中状态
              },
              on: {
                input: (val) => {
                  node.setChecked(val) // 手动设置节点选中状态
                  // 手动触发 @check 事件
                  this.handleDeviceTreeCheck(this.$refs.deviceTree.getCheckedNodes(), this.$refs.deviceTree.getCheckedKeys())
                }
              }
            })
          ])
          : null,
        // 渲染动态图标
        icon,
        // 显示节点标题
        h('span', { style: { marginLeft: '8px' }}, data.title)
      ])
    },
    // 行星轴系提交成功
    handleSubmitGearbox() {
      this.getGroupComponentList()
      if (this.editConfiguration) {
        this.Reset()
      }
      this.$message.success('提交成功')
    },
    // 多级菜单触发自定义方法
    handleCommand(item) {
      if (item === undefined) {
        console.warn(`未找到对应的 method,command: ${item.index}`)
      } else if (item.method) {
        item.method() // 直接调用传入的 `item` 对象的 `method`
      } else {
        console.warn(`未找到对应的 method,command: ${item.index}`)
      }
    },
    // 查询设备组态树
    getGroupComponentList() {
      getGroupComponent(this.currentDevicesId).then(res => {
        // console.log(this.currentDevicesId);

        // 将树数据转换为可以使用的树结构
        this.treeData = this.convertToTreeData(res.rows)
        // 通过树数据构建哈希表，不影响原始树结构, key：子节点，value：父节点，子节点查找父节点哈希表
        this.nodeHashTable = this.buildNodeHashTable(this.treeData)
      })
    },
    // 构建哈希表的方法：子节点 id -> 父节点信息
    buildNodeHashTable(treeData) {
      const hashTable = {}
      // 递归遍历树结构，构建哈希表
      function traverse(node) {
        if (node.children) {
          node.children.forEach(child => {
            // 每个子节点的 id 对应父节点的所有信息
            hashTable[child.id] = node
            traverse(child) // 继续处理子节点
          })
        }
      }
      // 从根节点开始遍历整棵树
      treeData.forEach(rootNode => traverse(rootNode))
      return hashTable // 返回构建好的哈希表
    },
    // 将后端返回的数据递归转换为 el-tree 所需的格式
    convertToTreeData(rows) {
      return rows.map((item) => {
        return {
          id: item.id,
          parentId: item.parentId,
          deviceName: item.deviceName,
          treeRank: item.treeRank,
          // 优先使用 tableData 中的转速字段
          inputSpeed: item.table && item.table.tableData
            ? (item.table.tableData.inputShaftSpeed != null
                ? item.table.tableData.inputShaftSpeed
                : (item.table.tableData.speed != null
                    ? item.table.tableData.speed
                    : item.inputSpeed))
            : item.inputSpeed,
          title: item.deviceName, // 设备名称作为节点文本
          key: item.deviceKey, // 使用 deviceKey 作为节点的 key
          treeIcon: item.treeIcon, // 使用 treeIcon 作为图标
          mongodbId: item.mongodbId,
          table: item.table,

          children: item.children ? this.convertToTreeData(item.children) : null // 如果有子节点，递归调用自己
        }
      })
    },
    // 树节点点击事件用于判断什么时候让树无法选中
    handleNodeClick(data, node, component) {
      if (this.selectedConfiNodeKey === node.key) {
        this.selectedConfiNodeKey = null
        this.selectedConfiNode = null
        this.updateMenuItems()
      }
    },
    // 选中
    handleMenuSelect(title, treeIcon, cogsMenu) {
      this.editConfiguration = false;
      this.dialogType = treeIcon;
      this.dialogTitle = title;
      this.defaultValue = title;

      // 重构
      this.componentData = {
        treeIcon: treeIcon,
        label: title,
        defaultValue: title
      };

      const menuActions = {
        'bearing': () => {
          this.showBearingDialog = true
        },
        'blade': () => {
          this.showCreateBladeDialog = true
        },
        // 行星轴系
        'noDeletePlanetaryShaftSys': () => {
          this.componentData = {
            gearboxType: treeIcon,
            planetaryType: cogsMenu,
            table: {
              gearboxType: treeIcon,
              planetaryType: cogsMenu,
              tableData: {
                speed: 500, // 行星架转速 500 RPM
                gearbox: '行星齿轮箱',
                planetaryGearCount: '8', // 行星轮个数 8
                planetaryGearTeeth: '5', // 行星轮齿数 5
                ringGearTeeth: '100', // 齿圈齿数 100
                sunGearTeeth: '6', // 太阳轮齿数 6
                inputShaftSpeed: 500,
                inputShaftType: 'carrier',
                meshFrequencyFormulas: {
                  sunPlanetMesh: 'fp_s = n_c * Z_s / 60', // 太阳轮-行星轮啮合频率
                  ringPlanetMesh: 'fp_r = n_c * Z_r / 60', // 内齿圈-行星轮啮合频率
                  singleSunPlanetMesh: 'fp_s_single = n_c * Z_s / (60 * N)', // 单个行星轮与太阳轮的啮合频率
                  singleRingPlanetMesh: 'fp_r_single = n_c / 60', // 单个行星轮与内齿圈的啮合频率
                  sunPassFreq: 'f_s_pass = n_c * N / 60', // 太阳轮上某一点通过的行星轮的频率
                  ringPassFreq: 'f_r_pass = n_c * N / 60', // 内齿圈某一点通过的行星轮的频率
                  carrierRotateFreq: 'f_c = n_c / 60', // 行星架的转动频率
                  description: `
                    其中：
                    fp_s - 太阳轮-行星轮啮合频率
                    fp_r - 内齿圈-行星轮啮合频率
                    fp_s_single - 单个行星轮与太阳轮的啮合频率
                    fp_r_single - 单个行星轮与内齿圈的啮合频率
                    f_s_pass - 太阳轮上某一点通过的行星轮的频率
                    f_r_pass - 内齿圈某一点通过的行星轮的频率
                    f_c - 行星架的转动频率
                    n_c - 行星架转速
                    Z_s - 太阳轮齿数
                    Z_r - 内齿圈齿数
                    N - 行星轮个数
                  `
                }
              }
            }
          }
          const gearboxActions = {
            '1-1': () => {
              // 行星架输入
              this.componentData.table.tableData = {
                speed: 500, // 行星架转速 500 RPM
                gearboxType: '行星架输入',
                planetaryGearCount: '8', // 行星轮个数 8
                planetaryGearTeeth: '5', // 行星轮齿数 5
                ringGearTeeth: '100', // 齿圈齿数 100
                sunGearTeeth: '6', // 太阳轮齿数 6
                inputShaftSpeed: 500,
                inputShaftType: 'carrier',
                meshFrequencyFormulas: {
                  // 行星齿转频N2
                  planetaryGearFreq: 'N2 = (n_c/60) * (Z_r + Z_s) / Z_p',
                  // 行星齿啮合转频
                  planetaryMeshFreq: 'f_mesh_p = N2 * Z_p',
                  // 太阳轮转频N3
                  sunGearFreq: 'N3 = n_c * (1 + Z_r/Z_s) / 60',
                  // 齿圈故障通过频率
                  ringPassFreq: 'f_r_pass = n_c * N / 60',
                  // 啮合频率
                  meshFreq: 'f_mesh = n_c * Z_s / 60',
                  // 行星架转频
                  carrierRotateFreq: 'f_c = n_c / 60',
                  description: `
                    其中：
                    N2 - 行星轮转频
                    f_mesh_p - 行星轮啮合频率
                    N3 - 太阳轮转频
                    f_r_pass - 齿圈故障通过频率
                    f_mesh - 啮合频率
                    f_c - 行星架转频
                    n_c - 行星架转速(rpm)
                    Z_s - 太阳轮齿数
                    Z_r - 内齿圈齿数
                    Z_p - 行星轮齿数
                    N - 行星轮个数
                  `
                }
              };
            },
            '1-2': () => {
              // 太阳轮输入
              this.componentData.table.tableData = {
                speed: 300, // 太阳轮转速 300 RPM
                gearboxType: '太阳轮输入',
                gearbox: '行星齿轮箱',
                planetaryGearCount: '4', // 行星轮个数 4
                planetaryGearTeeth: '55', // 行星轮齿数 55
                ringGearTeeth: '95', // 齿圈齿数 95
                sunGearTeeth: '10', // 太阳轮齿数 10
                inputShaftSpeed: 300,
                inputShaftType: 'sun',
                meshFrequencyFormulas: {
                  // 行星齿转频N2
                  planetaryGearFreq: 'N2 = (n_s/60) * Z_s / Z_p',
                  // 行星齿啮合转频
                  planetaryMeshFreq: 'f_mesh_p = N2 * Z_p',
                  // 行星架转频N1
                  carrierFreq: 'N1 = n_s / (60 * (1 + Z_r/Z_s))',
                  // 齿圈故障通过频率
                  ringPassFreq: 'f_r_pass = N1 * N',
                  // 啮合频率
                  meshFreq: 'f_mesh = n_s * Z_s / 60',
                  // 太阳轮转频
                  sunRotateFreq: 'f_s = n_s / 60',
                  description: `
                    其中：
                    N2 - 行星轮转频
                    f_mesh_p - 行星轮啮合频率
                    N1 - 行星架转频
                    f_r_pass - 齿圈故障通过频率
                    f_mesh - 啮合频率
                    f_s - 太阳轮转频
                    n_s - 太阳轮转速(rpm)
                    Z_s - 太阳轮齿数
                    Z_r - 内齿圈齿数
                    Z_p - 行星轮齿数
                    N - 行星轮个数
                  `
                }
              };
            }
          }
          if (gearboxActions[cogsMenu]) {
            gearboxActions[cogsMenu]() // 调用对应的处理函数
          } else {
            console.log(`未找到 ${cogsMenu} 对应的处理项`)
          }
        },

        // 平行轴系
        'parallelShafting': () => {
          // 每次选择时重置 componentKey 来强制刷新组件
          this.componentKey += 1;
          this.handleParallelShafting(cogsMenu);
        }
      }

      if (menuActions[treeIcon]) {
        menuActions[treeIcon]();
      } else {
        // 默认处理逻辑(输入框)
        this.showGenericDialog = true;
      }
    },
    // 确定操作
    handleDialogConfirm(value) {
      this.newNodeMsg.id = this.selectedConfiNode ? this.selectedConfiNode.id : ''
      this.newNodeMsg.deviceName = value.title
      this.newNodeMsg.treeIcon = value.treeIcon
      this.newNodeMsg.inputSpeed = value.inputSpeed
      this.newNodeMsg.treeRank = value.treeRank
      if (value.table !== null) {
        this.newNodeMsg.table = value.table
      }
      if (this.editConfiguration) {
        this.newNodeMsg.mongodbId = value.mongodbId
        // 编辑
        this.updateNode(this.newNodeMsg)
      } else {
        // 新建
        this.addNode(this.newNodeMsg)
      }
      this.showGenericDialog = false
    },
    // 取消操作
    handleDialogCancel() {
      this.showGenericDialog = false
    },
    // 返回
    handleBack() {
      this.$emit('back')
    },
    // 获取图表信息
    getIcon(props) {
      const { treeIcon, status } = props.dataRef
      let iconClass = treeIcon
      // 判断icon状态
      switch (status) {
        // 预警状态
        case 'warning':
          iconClass = `${treeIcon}-warningValue`
          break
        // 危险状态
        case 'danger':
          iconClass = `${treeIcon}-dangerValue`
          break
        // 连接状态
        case 'connected':
          iconClass = `${treeIcon}-connectedData`
          break
        // 未连接状态
        case 'disconnected':
          iconClass = `${treeIcon}-disconnectedData`
          break
        default:
          iconClass = treeIcon
      }
      // 返回icon的DOM元素
      return <svg-icon icon-class={iconClass} style='width: 1.5em; height: 1.5em;'/>
    },
    // 选中的节点信息
    selectNode(data, node, component) {
      if (data) {
        this.selectedConfiNodeKey = node.key
        this.selectedConfiNode = node.data
        this.newNodeMsg.id = this.selectedConfiNode.id
        this.getGroupComponentListMsg(this.selectedConfiNode)
        this.updateMenuItems()
      } else {
        this.selectedConfiNodeKey = null
        this.newNodeMsg.id = null
      }
    },
    updateMenuItems() {
      if (this.selectedConfiNode && this.selectedConfiNode.treeIcon) {
        if (this.selectedConfiNode.treeIcon === 'axis' || this.selectedConfiNode.treeIcon === 'noDeleteAxis') {
          this.isDisabled = false // "轴"节点时，轴承和叶片菜单项可用
        } else {
          this.isDisabled = true // 非"轴"节点时，轴承和叶片菜单项禁用
        }
      } else {
        this.isDisabled = true // 如果没有选中节点或节点无 treeIcon，则禁用相关菜单项
      }
    },
    // 获取组态对象信息
    getGroupComponentListMsg(node) {
      getGroupComponentInfo(node.id).then(res => {
        // 将 API 返回的所有必要属性都赋值给 selectedConfiNode
        if (res.data) {
          this.selectedConfiNode = {
            ...this.selectedConfiNode,
            ...res.data,
            table: res.data.table
          }
          // 更新对话框数据
          this.dialogDataList = this.selectedConfiNode
        }
      })
    },
    // 更新组态信息
    updateNode(updateNode) {
      const node = {
        id: updateNode.id || '',
        parentId: updateNode.parentId,
        deviceName: updateNode.deviceName,
        treeIcon: updateNode.treeIcon,
        treeRank: updateNode.treeRank,
        inputSpeed: updateNode.inputSpeed,
        mongodbId: updateNode.mongodbId,
        table: updateNode.table
      }
      updateGroupComponent(node).then(res => {
        this.getGroupComponentList()
        this.$message.success('更新成功')
        this.Reset()
      })
    },
    // 添加节点
    addNode(newNode) {
      let treeRank = ''
      if (!this.selectedConfiNode) {
        treeRank = this.treeData.length
        newNode.parentId = null // 父节点设置为空，表示根节点
      } else {
        newNode.parentId = this.selectedConfiNode.id
        treeRank = this.selectedConfiNode.children ? this.selectedConfiNode.children.length : 0
      }
      const node = {
        id: newNode.id || '',
        treeRank: treeRank,
        parentId: this.currentDevicesId,
        deviceName: newNode.deviceName,
        treeIcon: newNode.treeIcon,
        mongodbId: newNode.mongodbId,
        inputSpeed: newNode.inputSpeed,
        table: newNode.table
      }
      addGroupComponent(node).then(res => {
        this.getGroupComponentList()
        this.$message.success('提交成功')
        this.Reset()
      })
    },
    // 提交添加轴承描述的信息
    tableBearingSubmit(val) {
      if (this.editConfiguration) {
        batchUpdateGroupComponent(val).then(res => {
          this.$message.success('更新成功')
          this.Reset()
        })
      } else {
        this.newNodeMsg.table = val
        this.newNodeMsg.deviceName = val.description
        this.newNodeMsg.treeIcon = 'bearing'
        this.newNodeMsg.id = this.selectedConfiNode.id
        this.newNodeMsg.mongodbId = val.id
        this.addNode(this.newNodeMsg)
      }
    },
    // 点击编辑
    handleEdit() {
      this.componentKey += 1;

      // 深拷贝数据确保响应式更新
      this.componentData = JSON.parse(JSON.stringify(this.selectedConfiNode));

      this.dialogTitle = this.selectedConfiNode.title
      this.defaultValue = this.selectedConfiNode.title
      this.editConfiguration = true
      const menuActions = {
        'bearing': () => {
          this.dialogType = this.selectedConfiNode.treeIcon
          this.bearingDescriptionData = this.selectedConfiNode
          this.showBearingDialog = true
        },
        'blade': () => {
          this.dialogType = this.selectedConfiNode.treeIcon
          getGroupComponentInfo(this.selectedConfiNode.id).then(res => {
            this.bladeData = res.data.table
          })
          this.showCreateBladeDialog = true
        },
        // 行星轴系
        'noDeletePlanetaryShaftSys': () => {
          this.dialogType = this.selectedConfiNode.treeIcon
          const parentNodeId = this.nodeHashTable[this.selectedConfiNode.id]
          getGroupComponentInfoByCode(parentNodeId.id).then(res => {
            this.editConfiguration = true
            this.componentData = res.data.table
            this.parentNodeMsg = res.data
          })
        },
        // 平行轴系
        'noDeleteParallelShafting': () => {
          this.dialogType = this.selectedConfiNode.treeIcon
          const nodeHashTable = this.nodeHashTable
          const selectedNode = this.selectedConfiNode
          const firstParentNode = nodeHashTable[selectedNode.id]
          const secondParentNode = nodeHashTable[firstParentNode.id]
          this.componentData = secondParentNode
          this.parseAndInsertData()
        },
        'noDeleteAxis': () => {
          const nodeHashTable = this.nodeHashTable
          const selectedNode = this.selectedConfiNode
          const firstParentNode = nodeHashTable[selectedNode.id]
          this.dialogType = firstParentNode.table.gearboxType
          this.componentData = firstParentNode
          if (this.componentData.table.treeIcon === 'noDeleteParallelShafting' ||
          this.componentData.table.treeIcon === 'parallelShafting') {
            this.parseAndInsertData()
          }
        },
        'gearbox': () => {
          this.componentData = this.selectedConfiNode
          this.dialogType = this.selectedConfiNode.table.gearboxType
          this.parseAndInsertData()
        }
      }
      if (menuActions[this.selectedConfiNode.treeIcon]) {
        menuActions[this.selectedConfiNode.treeIcon]() // 调用对应的处理函数
      } else {
        this.dialogType = this.selectedConfiNode.treeIcon
        this.dialogDataList = this.selectedConfiNode
        this.showGenericDialog = true
      }
    },
    // 删除选中树节点
    handleDelete() {
      deleteGroupComponent(this.currentDevicesId, this.selectedConfiNode.id).then(() => {
        this.getGroupComponentList()
        this.$message.success('删除成功')
        this.selectedConfiNode = null
        this.Reset()
      })
    },

    handleAssociate() {
      this.showRelationDialog = true
    },

    // 关闭关联测点弹窗
    handleRelationClose() {
      this.showRelationDialog = false
    },
    // 轴承弹窗的确认取消
    handleBearingConfirm(value) {
      this.$message(`轴承弹窗确认: ${value}`)
      this.showBearingDialog = false
    },
    handleBearingCancel() {
      this.Reset()
    },
    // 轴承表格点击切换数据
    TableCurrentChange(val) {
      this.currentSelect = val
    },
    // 叶片弹窗的确认方法
    handleCreateBladeCancel() {
      this.Reset()
      this.showCreateBladeDialog = false
    },
    // 重置
    Reset() {
      this.parentNodeMsg = null
      this.editConfiguration = false
      this.newNodeMsg = {
        id: '',
        parentId: this.currentDevicesId || '', // 设备id
        deviceName: '', // 设备名称
        treeIcon: '', // 图标
        mongodbId: '', // 轴承id（如果新建的不是轴承就传null）
        table: {} // 预留给有配置的部件
      }
    },
    handleTreeContainerClick(event) {
      // 检查点击事件是否直接发生在树节点上
      if (!event.target.closest('.el-tree-node')) {
        // 如果点击的不是树节点，则取消所有节点的选中状态
        this.$refs.ConfigSetupTree.setCurrentKey(null)
        this.selectedConfiNode = null
        this.selectedConfiNodeKey = null
        this.updateMenuItems()
      }
    },
    // 添加 toRoman 方法
    toRoman(num) {
      const romanNumerals = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']
      return romanNumerals[num - 1] || '' // 支持前十个罗马数字，注意这里改为 num - 1
    },

    // 生成平行轴系数据
    generateParallelShaftingData(level) {
      const data = [];
      data.type = '平行轴系'
      // 第一根轴 - 只有驱动轮
      data.push({
        speed: '1000',
        description: '', // 第一根轴没有从动轮
        axis: '',
        teethCount: '',
        mainDescription: 'I转轴驱动轮',
        mainAxis: 'I转轴',
        mainTeethCount: '13',
        editableDescription: false,
        editableAxis: false,
        editableTeethCount: false,
        editableMainDescription: false,
        editableMainAxis: false,
        editableMainTeethCount: false,
        editableSpeed: false,
        index: 0,
        inputSpeed: 1000 // 增加inputSpeed字段，作为实际计算的转速
      });

      // 固定第一级输入转速
      const firstSpeed = 1000;
      let currentSpeed = firstSpeed;
      
      // 中间的轴 - 每根轴都有从动轮和驱动轮
      for(let i = 1; i < level; i++) {
        // 计算当前级的转速 = 上一级转速 × (上一级驱动轮齿数 / 当前从动轮齿数)
        // 从第二级开始，使用固定齿数进行计算：驱动轮13齿，从动轮26齿
        currentSpeed = (currentSpeed * 13) / 26;
        
        // 当前轴的从动轮
        data.push({
          speed: currentSpeed.toFixed(2),
          description: `${this.toRoman(i + 1)}转轴从动轮`,
          axis: `${this.toRoman(i + 1)}转轴`,
          teethCount: '26',
          mainDescription: `${this.toRoman(i + 1)}转轴驱动轮`,
          mainAxis: `${this.toRoman(i + 1)}转轴`,
          mainTeethCount: '13',
          editableDescription: false,
          editableAxis: false,
          editableTeethCount: false,
          editableMainDescription: false,
          editableMainAxis: false,
          editableMainTeethCount: false,
          editableSpeed: false,
          index: i * 2 - 1,
          inputSpeed: currentSpeed // 设置当前轴的转速
        });
      }

      // 最后一根轴 - 只有从动轮
      if (level > 0) {
        // 计算最后一级的转速 = 上一级转速 × (上一级驱动轮齿数 / 当前从动轮齿数)
        const lastSpeed = (currentSpeed * 13) / 26;
        
        data.push({
          speed: lastSpeed.toFixed(2),
          description: `${this.toRoman(level + 1)}转轴从动轮`,
          axis: `${this.toRoman(level + 1)}转轴`,
          teethCount: '26',
          mainDescription: '', // 最后一根轴没有驱动轮
          mainAxis: '', // 确保最后一根轴的主动轮轴为空
          mainTeethCount: '', // 确保最后一根轴的主动轮齿数为空
          editableDescription: false,
          editableAxis: false,
          editableTeethCount: false,
          editableMainDescription: false,
          editableMainAxis: false,
          editableMainTeethCount: false,
          editableSpeed: false,
          index: level * 2 - 1,
          inputSpeed: lastSpeed // 设置最后一根轴的转速
        });
      }

      return data;
    },

    // 平行轴系处理函数
    handleParallelShafting(cogsMenu) {
      this.componentData = this.componentData || {}; // 确保 componentData 存在
      this.componentData.gearbox = '齿轮箱';

      // 从菜单编号中获取级数
      const level = parseInt(cogsMenu.split('-')[1]);
      if (!isNaN(level) && level >= 1 && level <= 4) {
        // 重置 tableData，确保每次都是新的数组
        this.componentData.tableData = [];
        // 生成新的数据并计算转速
        this.componentData.tableData = this.generateParallelShaftingData(level);
        
        // 添加总传动比计算
        let totalRatio = 1;
        for (let i = 0; i < this.componentData.tableData.length - 1; i++) {
          const currentGear = this.componentData.tableData[i];
          const nextGear = this.componentData.tableData[i + 1];
          
          if (currentGear.mainTeethCount && nextGear.teethCount) {
            totalRatio *= Number(nextGear.teethCount) / Number(currentGear.mainTeethCount);
          }
        }
        
        // 存储计算得到的总传动比
        this.componentData.totalRatio = totalRatio.toFixed(2);
        
        console.log('生成的平行轴系数据:', this.componentData.tableData);
        console.log('总传动比:', this.componentData.totalRatio);
      } else {
        console.error('无效的齿轮箱级数');
      }
    },
  },
  directives: {
    dialogDrag: {
      bind(el, binding, vnode) {
        const bindDrag = () => {
          const dialogHeaderEl = el.querySelector('.el-dialog__header')
          const dragDom = el.querySelector('.el-dialog')
          if (!dialogHeaderEl || !dragDom) return
          
          dialogHeaderEl.style.cursor = 'move'
          
          // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
          const sty = window.getComputedStyle(dragDom, null)
          const moveDown = (e) => {
            // 鼠标按下，计算当前元素距离可视区的距离
            const disX = e.clientX - dialogHeaderEl.offsetLeft
            const disY = e.clientY - dialogHeaderEl.offsetTop
            
            // 获取到的值带px 正则匹配替换
            let styL, styT
            
            // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
            if (sty.left.includes('%')) {
              styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100)
              styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100)
            } else {
              styL = +sty.left.replace(/\px/g, '') || 0
              styT = +sty.top.replace(/\px/g, '') || 0
            }
            
            document.onmousemove = function(e) {
              // 通过事件委托，计算移动的距离
              const l = e.clientX - disX
              const t = e.clientY - disY
              
              // 移动当前元素
              dragDom.style.left = `${l + styL}px`
              dragDom.style.top = `${t + styT}px`
              
              // 将此时的位置传出去
              // binding.value({x:e.pageX,y:e.pageY})
            }
            
            document.onmouseup = function(e) {
              document.onmousemove = null
              document.onmouseup = null
            }
          }
          
          dialogHeaderEl.onmousedown = moveDown
        }

        // 延迟执行，确保DOM已经渲染
        setTimeout(() => {
          bindDrag()
        }, 100)
      },
      
      update(el, binding, vnode) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header')
        const dragDom = el.querySelector('.el-dialog')
        if (!dialogHeaderEl || !dragDom) return
        
        dialogHeaderEl.style.cursor = 'move'
        
        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = window.getComputedStyle(dragDom, null)
        const moveDown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX - dialogHeaderEl.offsetLeft
          const disY = e.clientY - dialogHeaderEl.offsetTop
          
          // 获取到的值带px 正则匹配替换
          let styL, styT
          
          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
          if (sty.left.includes('%')) {
            styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100)
            styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100)
          } else {
            styL = +sty.left.replace(/\px/g, '') || 0
            styT = +sty.top.replace(/\px/g, '') || 0
          }
          
          document.onmousemove = function(e) {
            // 通过事件委托，计算移动的距离
            const l = e.clientX - disX
            const t = e.clientY - disY
            
            // 移动当前元素
            dragDom.style.left = `${l + styL}px`
            dragDom.style.top = `${t + styT}px`
          }
          
          document.onmouseup = function(e) {
            document.onmousemove = null
            document.onmouseup = null
          }
        }
        
        dialogHeaderEl.onmousedown = moveDown
      }
    }
  },
}
</script>

<style scoped>
@import './ConfigSetupComponent.css';

.draggable-dialog {
  position: relative;
}

.draggable-dialog .el-dialog {
  margin: 0 auto 50px !important;
  position: absolute;
  top: 15vh;
  left: 10vw;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.draggable-dialog .el-dialog:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.18);
}

.draggable-dialog .el-dialog__header {
  cursor: move;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  user-select: none;
}

.draggable-dialog .el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.draggable-dialog .el-dialog__headerbtn {
  top: 16px;
  right: 20px;
  font-size: 18px;
}

.draggable-dialog .el-dialog__headerbtn:hover .el-dialog__close {
  color: #409EFF;
  transform: rotate(90deg);
  transition: all 0.3s ease;
}

.draggable-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.draggable-dialog .el-dialog__body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.draggable-dialog .el-dialog__body::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.draggable-dialog .el-dialog__body::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 表格样式优化 */
.draggable-dialog .el-table {
  border-radius: 4px;
  overflow: hidden;
}

.draggable-dialog .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  padding: 12px 0;
}

.draggable-dialog .el-table td {
  padding: 10px 0;
}

/* 分页器样式优化 */
.draggable-dialog .el-pagination {
  padding: 15px 0;
  text-align: right;
}

/* 下拉框样式优化 */
.draggable-dialog .el-select {
  width: 100%;
}

.draggable-dialog .el-select:hover .el-input__inner {
  border-color: #c0c4cc;
}

.draggable-dialog .el-select .el-input__inner:focus {
  border-color: #409EFF;
}

/* 搜索框样式优化 */
.draggable-dialog .el-input__inner {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.draggable-dialog .el-input__inner:hover {
  border-color: #c0c4cc;
}

.draggable-dialog .el-input__inner:focus {
  border-color: #409EFF;
}
</style>
