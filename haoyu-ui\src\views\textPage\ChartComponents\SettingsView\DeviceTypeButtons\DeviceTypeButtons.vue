<template>
  <div class="deviceTypeButtons">
    <span class="section-title">部件类型</span>
    <el-button
      v-for="type in deviceTypes"
      :key="type"
      type="text"
      class="device-button"
      @click="handleSelect(type)"
    >
      {{ type }}
    </el-button>
  </div>
</template>

<script>
import { deviceTypes } from './deviceTypes'

export default {
  data() {
    return {
      deviceTypes
    }
  },
  methods: {
    handleSelect(type) {
      this.$emit('select', type)
      // 使用防抖处理确保元素状态更新
      setTimeout(() => {
        // 处理可能导致aria-hidden错误的逻辑
      }, 0)
    }
  }
}
</script>
<style scoped>
@import './DeviceTypeButtons.css'
</style>
