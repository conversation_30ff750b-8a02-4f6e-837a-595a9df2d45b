<template>
  <div class="collection-tree">
    <!-- 按钮组 -->
    <div class="button-group">
      <el-button type="primary" size="mini" @click="openDialog('add')">
        <i class="el-icon-plus" /> 新增
      </el-button>
      <el-button type="default" :disabled="!canEdit || !selectedNodeID" size="mini" @click="openDialog('edit')">
        <i class="el-icon-edit" /> 编辑
      </el-button>
      <el-button type="danger" size="mini" @click="handleDelete">
        <i class="el-icon-delete" /> 删除
      </el-button>
    </div>

    <!-- 搜索框及按钮 -->
    <div class="search-container">
      <el-input
        v-model="searchQuery"
        placeholder="搜索站点"
        suffix-icon="el-icon-search"
        style="width: 300px;"
        @keyup.enter.native="handleSearch"
      />
      <el-button
        type="default"
        icon="el-icon-refresh"
        :disabled="!searchQuery"
        class="reset-button"
        @click="handleReset"
      />
    </div>

    <!-- 弹窗 -->
    <AddEditDialog
      :visible.sync="isDialogVisible"
      :mode="dialogMode"
      @submit="handleDialogSubmit"
      @close="handleDialogClose"
    />

    <!-- 公司选择器 -->
    <a-select
      v-model="selectedCompany"
      placeholder="选择公司"
      style="width: 100%; margin-bottom: 20px;"
      @change="handleCompanyChange"
    >
      <a-select-option
        v-for="company in companyList"
        :key="company.id"
        :value="company.mainKey"
      >
        {{ company.title }}
      </a-select-option>
    </a-select>
    <!-- 树组件 -->
    <div class="BaseStationTree-tree-container" :style="{ height: selectedNode ? '50%' : '80%' }">
      <a-tree
        :tree-data="treeBaseStationList"
        default-expand-all
        @select="handleSelect"
      />
    </div>

    <!-- 点击站点后显示的设备选择器及相关树 -->
    <div v-if="selectedNode" style="height: 50%;">
      <div v-if="selectedDevice" class="BaseStationTree-tree-container" style="margin-top: 20px; height: 50%;">
        <span>设备监测</span>
        <a-tree
          :tree-data="deviceTreeData"
          default-expand-all
          @select="handleDeviceNodeClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import AddEditDialog from './AddEditDialog.vue'
import {
  getCompaniesList,
  getBaseStationList,
  getBaseStationDeviceList
} from '@/api/haoyu-system/OnlineManagement/BaseStationTree'
import { delBaseStation } from '@/api/haoyu-system/SystemAdministration/SystemAdministration.js'
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'BaseStationTree',
  components: {
    AddEditDialog
  },
  data() {
    return {
      // 是否可以编辑
      canEdit: false,
      // 公司ID
      companyId: 1,
      dialogMode: 'add', // 'add' 或 'edit'
      isDialogVisible: false,
      selectedCompany: '', // 默认选项
      searchQuery: '', // 搜索框的输入
      selectedNode: null, // 选中的站点
      selectedDevice: null, // 选中的设备
      // 获取下拉框数据
      companyList: [],
      selectedStation: null, // 选中的基站显示
      // 公司下的基站数据
      treeBaseStationList: [],
      devices: ['设备1', '设备2', '设备3'], // 模拟设备数据
      deviceTreeData: [] // 设备对应的树数据
    }
  },
  computed: {
    ...mapGetters('SettingConnect', ['selectedNodeID', 'dialogConfirmed']) // 获取选中的节点 ID
  },
  watch: {
    selectedNode() {
      // 当选中站点时，自动选择第一个设备
      if (this.devices.length) {
        this.selectedDevice = this.devices[0]
        // console.log('selectedDevice:', this.selectedDevice)
      }
    },
    dialogConfirmed(newVal, oldVal) {
      if (newVal && !oldVal) {
        // 当 dialogConfirmed 变为 true 时，执行逻辑
        this.getCompanies() // 或其他需要的操作
        this.$store.dispatch('SettingConnect/resetDialogConfirmed') // 重置状态
      }
    }
  },
  created() {
    // console.log('dialogConfirmed', this.$store.state.SettingConnect.confirmDialog)
    this.initializeGetCompanies()
  },
  methods: {
    ...mapActions('SettingConnect', ['setSelectedNodeID']), // 映射 setSelectedNodeID action

    initializeGetCompanies() {
      getCompaniesList().then(res => {
        this.companyList = res.data
        this.selectedCompany = this.companyList[0].mainKey
        // this.$store.dispatch('SettingConnect/setCompanyId', selectedCompanyId.id)
        // 默认选择
        this.handleCompanyChange(this.selectedCompany)
      })
    },
    // 获取列表
    getCompanies() {
      getCompaniesList().then(res => {
        this.companyList = res.data
        this.handleCompanyChange(this.selectedCompany)
      })
    },
    // 存储节点
    handleDeviceNodeClick(selectedKeys, info) {
      // 确保 info.node 存在且有标题
      if (info.node && info.node.title) {
        // 存储需要保存的设备节点
        const storeNodes = []

        // 递归函数，用于遍历节点并存储目标设备节点
        const storeDevices = (node, shouldContinue) => {
          // console.log('点击的设备:', node)
          // 如果当前节点是设备节点，存储当前节点
          if (node && node.treeIcon === 'waterPump') {
            // console.log('点击的设备:', node)
            // console.log('Found device:', node.dataRef.type)
            // 检查当前节点是否已经被存储
            if (!this.$store.state.SettingConnect.selectedNodes.some(n => n.id === node.id)) {
              // 存储节点信息
              if (node.title && node.key) {
                storeNodes.push({ title: node.title, key: node.key, id: node.id })
              }
            } else {
              // 提示节点已存在
              this.$message.error(`${node.title} 已经存在！`, '警告')
            }
            // 发现设备后不再向下查找
            shouldContinue = false
          }

          // 检查当前节点是否有子节点
          if (shouldContinue && node.children && node.children.length > 0) {
            // 遍历子节点
            node.children.forEach(child => {
              // 递归调用，存储子节点中的设备
              // storeDevices(child, shouldContinue)
            })
          }
        }

        // 递归函数，存储点击节点及其所有祖先中的设备节点
        const storeAncestorsAndDevices = (node) => {
          // 遍历当前节点及其所有子节点
          storeDevices(node.dataRef, true)
        }

        // 开始存储点击节点及其所有祖先中的设备节点
        storeAncestorsAndDevices(info.node)

        // 将节点存储到 Vuex
        storeNodes.forEach(n => {
          this.$store.dispatch('SettingConnect/addNode', n)
        })
      }
    },
    // 打开弹窗
    openDialog(mode) {
      this.dialogMode = mode
      this.isDialogVisible = true
    },
    handleDelete() {
      this.$confirm('确认删除基站?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 确认删除
        delBaseStation(this.selectedNodeID)
          .then(() => {
            this.$message.success('删除成功')
            this.setSelectedNodeID(null)
            this.getCompanies()
          })
          .catch(() => {
            this.$message.error('删除失败请联系管理员')
          })
      }, () => {
        // 用户取消删除，不做任何操作
      })
    },
    // 切换不同公司下的基站
    handleCompanyChange(value) {
      // console.log(this.companyId)
      // console.log(value)
      this.searchQuery = '' // 更改公司时重置搜索查询
      // 查找选中的公司对象
      const selectedCompany = this.companyList.find(company => company.mainKey === value)
      this.companyId = selectedCompany.id
      // console.log(this.companyId)
      this.$emit('company-changed', this.companyId) // 给父组件传递信息更改公司下拉框
      this.selectedNode = null
      this.$store.dispatch('SettingConnect/setCompanyId', this.companyId)
      // console.log(this.companyId)
      if (selectedCompany) {
        getBaseStationList(selectedCompany.id).then(res => {
          this.treeBaseStationList = this.convertToTreeData(res.data)
          // console.log('转换后的树数据:', this.treeBaseStationList)
        })
      }
    },
    // 转换数据为树形结构
    convertToTreeData(data) {
      return data.map(item => ({
        ...item,
        title: item.deviceCoding || item.title, // 使用 deviceCoding 作为 title 的备选
        key: item.id.toString() // 确保 key 是字符串
      }))
    },
    // 选择树之后切换组件
    handleSelect(selectedKeys, info) {
      this.canEdit = !!(selectedKeys && selectedKeys.length === 1)

      console.log('Selected node info:', info) // 添加这行来查看完整的 info 对象

      let nodeData
      if (info.node && info.node.dataRef) {
        nodeData = info.node.dataRef
      } else if (info.node && info.node.$options) {
        // 处理 Vue 组件情况
        nodeData = info.node.$props || info.node.$attrs
      } else {
        console.error('无法获取节点数据:', info.node)
        return
      }

      if (nodeData && nodeData.id) {
        this.setSelectedNodeID(nodeData.id)

        console.log('节点数据:', nodeData) // 添加这行来查看处理后的节点数据

        // 使用 deviceCoding 作为 title
        const title = nodeData.deviceCoding || nodeData.title

        if (title) {
          const emitData = {
            ...nodeData,
            title: title
          }
          this.$emit('node-click', emitData)
          this.selectedNode = emitData // 保存选中的站点

          // 初始化一个空数组
          const newDeviceTreeData = []

          getBaseStationDeviceList(this.companyId).then(res => {
            // 将获取到的数据压入空数组
            newDeviceTreeData.push(res.data)
            // 更新 this.deviceTreeData
            this.deviceTreeData = newDeviceTreeData
          }).catch(error => {
            console.error('获取基站设备列表失败:', error)
          })
        } else {
          console.warn('节点缺少 title 和 deviceCoding:', nodeData)
        }
      } else {
        console.error('无效的节点数据:', nodeData)
      }
    },
    // 搜索
    handleSearch() {
      console.log('搜索查询:', this.searchQuery)
    },
    // 重置
    handleReset() {
      this.searchQuery = ''
    },
    // 弹窗确定
    handleDialogSubmit(form) {
      // 用于下拉框的方法后续如果提交有什么问题可以尝试恢复
      // this.handleCompanyChange(this.selectedCompany)
      // 处理提交的数据
      this.isDialogVisible = false
    },
    handleDialogClose() {
      this.isDialogVisible = false
    }
  }
}
</script>

<style scoped>
.collection-tree {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  background-color: #f5f5f5; /* 明亮背景色 */
  border-radius: 8px; /* 圆角边框 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
  gap:10px;
}

.button-group {
  display: flex;
  flex-wrap: wrap; /* 允许按钮换行 */
  gap: 10px; /* 按钮间距 */
  justify-content: flex-start; /* 按钮左对齐 */
/*   border: 1px solid red; */
}

.button-group el-button {
  min-width: 80px; /* 设置按钮的最小宽度 */
  max-width: 100%; /* 设置按钮的最大宽度 */
}

.search-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-input {
  flex: 1; /* 使输入框占满剩余空间 */
}

.reset-button {
  cursor: pointer;
  padding: 0 10px;
  height: 36px;
}

.BaseStationTree-tree-container {
  max-height: 50%; /* 限制树组件的最大高度 */
  overflow-y: auto; /* 当内容超出最大高度时显示纵向滚动条 */
  background-color: #ffffff; /* 白色背景色 */
  /* bottom: 10px; */
  padding: 5px;
  border-radius: 8px; /* 圆角边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
}
</style>
