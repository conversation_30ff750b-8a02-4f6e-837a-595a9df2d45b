import request from '@/utils/request_forweb3d'

export function UploadFile(data) {
    return request({
        url: '/fileContent/fileManagement/upload/', // 上传接口地址
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: data, // 传入 FormData 格式的数据
    });
}


export function GetImgObjList(type, pageSize, pageNum) {
    // 构建查询字符串
    const queryString = `?pageSize=${pageSize}&pageNum=${pageNum}`;
    // 拼接完整的URL
    const url = `/fileContent/fileManagement/list/${type}${queryString}`;

    return request({
        url: url,
        method: 'get',
    });
}

export function DownloadImgObjList(id, signal) {
    return request({
        url: `/fileContent/fileManagement/download/local/${id}`,
        method: 'get',
        responseType: 'blob', // 确保响应被处理为二进制数据
        timeout: 60000, // 设置超时时间为1分钟
        signal: signal // 添加AbortController的signal参数
    });
}

export function getDemoModelList() {
    return request({
        url: `/fileRelation/relation/model/list`,
        method: 'get'
    })
}

export function uploadImageToURL(file) {
    const formData = new FormData();
    formData.append('file', file);

    return request({
        url: '/common/upload',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: formData
    });
}

export function DeleteItem(id) {
    return request({
        url: `/fileContent/fileManagement/${id}`,
        method: 'delete',
    });
}

export function ChunkFile(data) {
    return request({
        url: `/fileContent/fileManagement/file/chunk`,
        method: 'post',
        data: data,
    });
}

export function MergeFile(data) {
    return request({
        url: `/fileContent/fileManagement/file/merge`,
        method: 'post',
        data: data
    });
}

export function CreateRelationImformation(data) {
    return request({
        url: `/fileRelation/relation`,
        method: 'post',
        data: data
    });
}

// /获取文件联系
export function GetRelationList(id) {
    const url = `/fileRelation/relation/${id}`;
    return request({
        url: url,
        method: 'get',
    });
}

//根据设备id更新模型
export function UpLoadRelationImformation(params) {
    const url = `/fileRelation/relation/deviceId`;
    return request({
        url: url,
        method: 'put',
        data: params
    });
}

//通过设备id获取文件联系
export function GetOrNewDateDeviceRelation(deviceId) {
    const url = `/fileRelation/relation/deviceId/${deviceId}`;
    return request({
        url: url,
        method: 'get',
    });
}

export function DeleteDeviceRelation(deviceId) {
    const url = `/fileRelation/relation/deviceId/${deviceId}`;
    return request({
        url: url,
        method: 'delete',
    });
}

//获取模型位置信息
export function getModelPosition(deviceId) {
    const url = `/fileContent/fileManagement/list/obj`;
    return request({
        url: url,
        method: 'get',
        params: {
            pageSize: 1,
            pageNum: 1,
            id: deviceId
        }
    });
}

//获取模型位置信息
export function getAllModelPosition(idsss) {
    const url = `/fileContent/fileManagement/list/batches/obj`;
    return request({
        url: url,
        method: 'get',
        params: {
            ids: idsss
        }
    });
}


//更新模型位置信息
export function UpdateModelPosition(params) {
    return request({
        url: '/fileContent/fileManagement',
        method: 'put',
        data: params // 直接传递整个对象作为请求体
    });
}


export function UpdateBackground(type, file, deviceId) {
    const formData = new FormData();
    formData.append('img', file);
    formData.append('deviceId', deviceId);

    return request({
        url: `/fileRelation/relation/upload/${type}`,
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: formData
    });
}

export function GetBackgroundPhoto(deviceId) {
    const url = `/fileRelation/relation/deviceId/forColor/${deviceId}`;
    return request({
        url: url,
        method: 'get',
    });
}

export function UpdateBackgroundColor(params) {
    return request({
        url: '/fileRelation/relation/deviceId',
        method: 'put',
        data: params // 直接传递整个对象作为请求体
    });
}