.parent-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;
    background: #fff;
    position: relative;
}

.top-section {
    margin-top: 0;
    display: flex;
    height: 75%;
    gap: 20px;
    margin-bottom: 20px;
}

.image-section {
    flex: 5;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.bottom-section {
    height: 25%;
    display: flex;
    flex-direction: column;
}

h3 {
    color: #333;
    font-size: 16px;
    margin: 0 0 8px 0;
    padding-bottom: 4px;
    border-bottom: 2px solid #f0f0f0;
}

.trend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.trend-header h3 {
    margin: 0;
    padding: 0;
    border: none;
}

.trend-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.reset-btn {
    padding: 7px 12px;
}

.toolbar {
    position: absolute;
    top: 12px;
    left: 12px;
    transform: none;
    width: auto;
    height: 28px;
    display: inline-flex;
    gap: 6px;
    padding: 3px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    z-index: 100;
    border-radius: 4px;
    backdrop-filter: blur(8px);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
    display: flex;
    align-items: center;
}

.toolbar-button {
    height: 22px;
    min-width: 22px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: #606266;
    white-space: nowrap;
}

.toolbar-button:hover {
    transform: translateY(-1px);
    color: #409EFF;
    border-color: #a3d0ff;
    background-color: #ecf5ff;
}

.toolbar-button [class^="el-icon-"] {
    font-size: 12px;
    margin: 0;
}

.view-switch {
    transform: scale(0.8);
    margin: 0 2px;
}

.view-switch :deep(.el-switch__label) {
    color: #606266;
    font-size: 12px;
}

.view-switch :deep(.el-switch__core) {
    width: 36px !important;
    height: 18px;
    border-radius: 9px;
    background-color: #dcdfe6;
}

.view-switch :deep(.el-switch__core:after) {
    width: 14px;
    height: 14px;
    top: 1px;
}

.view-switch :deep(.el-switch.is-checked .el-switch__core::after) {
    margin-left: -15px;
}

.view-switch :deep(.el-switch.is-checked .el-switch__core) {
    background-color: #409EFF;
    border-color: #409EFF;
}


/* 主按钮样式 */

.toolbar-button.el-button--primary {
    background-color: #409EFF;
    border-color: #409EFF;
    color: white;
    padding: 0 12px;
}

.toolbar-button.el-button--primary:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
    color: white;
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.canvas-container,
.viewer-container {
    flex: 1;
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.canvas-container {
    background: #f5f5f5;
    border: none;
}


/* 自定义表格样式 */

.custom-table {
    background: #fff;
}

.custom-table :deep(.el-table__header-wrapper th) {
    background: #f5f7fa;
    color: #606266;
    font-weight: 500;
}

.custom-table :deep(.el-table__row:hover) {
    background: #f5f7fa;
}

.custom-table :deep(.el-table__row td) {
    padding: 8px 0;
}

.web3d-container {
    width: 100%;
    height: 100%;
    background: #fff;
}

.viewer-container :deep(.obj-viewer) {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
}

.image-section h3 {
    margin: 0 0 8px 0;
    padding-bottom: 6px;
    height: 30px;
}

.trend-chart-container {
    flex: 1;
    width: 100%;
    height: calc(100% - 20px);
}

.gauge-container {
    flex: 1;
    width: 100%;
    min-height: 200px;
}

.alarm-records {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 250px;
    /* 设小度 */
}

.alarm-records h3 {
    margin-bottom: 12px;
}

.alarm-list {
    flex: 1;
    overflow: hidden;
}


/* 自定义表格样式 */

.alarm-list :deep(.el-table) {
    font-size: 12px;
}

.alarm-list :deep(.el-table th) {
    padding: 8px 0;
    font-weight: 500;
}

.alarm-list :deep(.el-table td) {
    padding: 4px 0;
}

.alarm-list :deep(.el-tag) {
    margin: 0;
}


/* 调整仪表盘容器高度 */

.gauge-container {
    height: 180px;
    min-height: 180px;
}

.device-name-button {
    padding: 8px 16px;
    background: transparent;
    /* 改为透明背景 */
    border: none;
    /* 移除框 */
    color: #1890ff;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
    margin-bottom: 16px;
    transition: all 0.3s;
}

.device-name-button:hover {
    background: rgba(24, 144, 255, 0.1);
    /* 悬停时显示半透明背景 */
    color: #40a9ff;
    /* 悬停时文字颜色变浅 */
}

.measure-definitions {
    /* 原有样式保持不变 */
}

.definition-item {
    cursor: pointer;
    padding: 5px;
    margin: 2px 0;
    transition: background-color 0.3s;
}

.definition-item:hover {
    background-color: #f5f5f5;
}

.no-data-tip {
    text-align: center;
    color: #67C23A;
    padding: 30px 0;
    font-size: 14px;
    background: #f8f8f8;
    border-radius: 4px;
    margin-top: 10px;
}