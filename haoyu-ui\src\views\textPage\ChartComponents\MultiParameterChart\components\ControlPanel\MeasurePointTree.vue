<!-- 测点选择树组件 -->
<template>
  <div class="measure-point-tree">
    <!-- <div class="section-title">测点选择</div> -->
    <div class="tree-container">
      <el-tree
        ref="tree"
        :data="treeData"
        show-checkbox
        node-key="id"
        :props="defaultProps"
        @check="handleCheck"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <i :class="getNodeIcon(data)"></i>
            <span>{{ node.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'MeasurePointTree',

  props: {
    // 设备ID
    deviceId: {
      type: [String, Number],
      required: true
    },
    // 初始选中的节点
    initialSelected: {
      type: Array,
      default: () => []
    }
  },

  watch: {
    deviceId: {
      handler(newId) {
        if (newId) {
          this.treeData = []  // 清空原有数据
          this.fetchMeasurePoints()  // 重新获取测点数据
        }
      },
      immediate: true
    }
  },

  data() {
    return {
      treeData: [],
      expandedKeys: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      measureDefToPointMap: {}  // 添加映射关系对象
    }
  },

  methods: {
    // 获取节点图标
    getNodeIcon(data) {
      // 根据节点类型返回不同的图标类名
      if (data.type === 'measurePoint') {
        return 'el-icon-location'  // 测点使用位置图标
      } else if (data.type === 'definition') {
        return 'el-icon-data-line'  // 测量定义使用数据图标
      }
      return ''
    },

    // 获取测点列表
    async fetchMeasurePoints() {
      try {
        // 1. 首先获取测点列表
        const response = await request.get(`/deviceManagement/factorymanagement/deviceMeasurePoint/list/${this.deviceId}`)
        console.log('测点列表响应:', response)

        if (response.code === 200 && response.data) {
          console.log('测点映射数据:', response.data)

          // 2. 将数据转换为数组并排序
          const sortedPoints = Object.entries(response.data)
            .map(([pointName, pointId]) => ({
              pointName,
              pointId: pointId
            }))
            .sort((a, b) => Number(a.pointId) - Number(b.pointId))

          // 3. 处理每个测点
          const points = []
          for (const point of sortedPoints) {
            if (point.pointId) {
              try {
                // 4. 获取每个测点的测量定义
                const defResponse = await request.get(`/measurePoint/measurePointManagement/definition/${point.pointId}`)
                console.log(`测点 ${point.pointId} 的测量定义:`, defResponse)

                if (defResponse.code === 200 && defResponse.data) {
                  // 5. 构建测点节点
                  const pointNode = {
                    id: point.pointId,
                    name: point.pointName,
                    type: 'measurePoint',
                    children: (defResponse.data.measurementDefinitionList || []).map(def => ({
                      id: `${point.pointId}-${def.id}`,
                      name: `${def.measureDefineName} (${def.lowerLimitFrequency}-${def.upperLimitFrequency}HZ) (${def.numberOfSpectralLines}线)`,
                      type: 'definition',
                      pointId: point.pointId,
                      definitionId: def.id,
                      pointName: point.pointName
                    }))
                  }
                  points.push(pointNode)
                }
              } catch (error) {
                console.error(`获取测点 ${point.pointId} 的测量定义失败:`, error)
              }
            }
          }

          // 6. 设置树形数据
          this.treeData = points

          // 7. 设置默认展开的节点
          this.expandedKeys = points.map(point => point.id)

          console.log('构建的树形数据:', this.treeData)

          // 处理树数据，构建映射关系
          this.processMeasurePointTree(this.treeData)
        } else {
          console.warn('获取测点列表失败:', response.msg)
          this.treeData = []
        }
      } catch (error) {
        console.error('获取测点列表失败:', error)
        this.treeData = []
      }
    },

    // 处理节点选中状态变更
    handleCheck(data, { checkedNodes }) {
      // 只发送测量定义节点的数据
      const selectedDefinitions = checkedNodes.filter(node => node.type === 'definition')
        .map(node => ({
          pointId: node.pointId,
          definitionId: node.definitionId,
          name: `${node.pointName} - ${node.name}`,  // 修改名称格式
          pointName: node.pointName  // 添加测点名称
        }))

      console.log('选中的测量定义:', selectedDefinitions);

      // 更新父组件的选中状态
      this.$emit('change', selectedDefinitions);

      // 同时更新 selected-definitions
      this.$emit('update:selected-definitions', selectedDefinitions);
    },

    // 处理树数据，构建映射关系
    processMeasurePointTree(treeData) {
      treeData.forEach(point => {
        if (point.children) {
          point.children.forEach(measureDef => {
            // 使用 definitionId 作为键，因为这是实际的测量定义ID
            this.measureDefToPointMap[measureDef.definitionId] = point.id;
          });
        }
      });
      console.log('构建的映射关系:', this.measureDefToPointMap);
      // 向父组件发送映射关系
      this.$emit('update:measureDefPointMap', this.measureDefToPointMap);
    },

    // 在数据加载完成后调用处理方法
    async loadTreeData() {
      try {
        const response = await this.fetchTreeData();
        if (response && response.data) {
          this.treeData = response.data;
          this.processMeasurePointTree(this.treeData);
        }
      } catch (error) {
        console.error('加载树形数据失败:', error);
      }
    }
  },

  async created() {
    await this.fetchMeasurePoints()

    // 设置初始选中的节点
    if (this.initialSelected.length > 0) {
      this.$nextTick(() => {
        const keys = this.initialSelected.map(item => `${item.pointId}-${item.definitionId}`)
        this.$refs.tree.setCheckedKeys(keys)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.measure-point-tree {
  height: 250px;
  display: flex;
  flex-direction: column;

  .section-title {
    font-weight: bold;
    margin-bottom: 8px;
    flex-shrink: 0;
  }

  .tree-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;

    :deep(.el-tree) {
      background: transparent;
    }

    :deep(.el-tree-node__content) {
      height: 28px;
    }

    :deep(.el-tree-node__children) {
      overflow: visible;
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 13px;
  padding-right: 8px;

  i {
    margin-right: 8px;
    font-size: 16px;
    color: #606266;
  }

  span {
    flex: 1;
  }
}

// 自定义滚动条样式
.tree-container::-webkit-scrollbar {
  width: 6px;
}

.tree-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
</style>
