.container {
  display: flex;
  flex-direction: column;
  height: 91vh; /* 设置整个容器的高度为视口高度 */
  border: 2px solid #dcdcdc; /* 边框颜色 */
  border-radius: 8px 0 0 8px; /* 左边圆角，右边无圆角 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  padding: 1px; /* 内部填充 */
  background-color: #fff; /* 背景颜色 */
}
.left-sidebar {
  width: 200px; /* 设置左侧区域的宽度 */
  background-color: #e0e0e0; /* 设置左侧区域的背景颜色 */
  border-right: 2px solid #dcdcdc; /* 添加右边框，与主容器区分 */
  border-radius: 0; /* 去除圆角，以适应容器 */
}
.half-height {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto; /* 超出高度时显示滚动条 */
}

.other-area {
  border-top: 2px solid #dcdcdc; /* 添加顶部边框 */
  background-color: #f0f0f0; /* 设置不同的背景颜色以示区别 */
  border-radius: 0; /* 去除圆角，以适应容器 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加底部阴影效果 */
}

/* 自定义滚动条样式 */
.half-height::-webkit-scrollbar {
  width: 8px; /* 设置滚动条宽度 */
  height: 8px; /* 设置滚动条高度 */
}

.half-height::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 设置滚动条颜色 */
  border-radius: 4px; /* 设置滚动条圆角 */
}

.half-height::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1); /* 设置滚动条轨道颜色 */
}
.resizer {
  width: 8px;
  background: #ccc;
  cursor: ew-resize;
  height: 100%;
}

