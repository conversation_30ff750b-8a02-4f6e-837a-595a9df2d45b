import { v4 as uuidv4 } from 'uuid';
import { ChunkFile, MergeFile } from '@/api/haoyu-system/web3d_api/web_3dapi.js';

/**
 * 分片上传 OBJ 数据
 * @param {string} objData - OBJ 文件内容
 * @param {string} name - 文件名称
 * @returns {Promise<string>} - 返回上传成功后的文件ID
 */
export async function chunkAndMergeFile(objData, name) {
    // 增加分片大小，减少分片数量
    const CHUNK_SIZE = 20 * 1024 * 1024; // 20MB 每片
    const uuid = uuidv4(); // 生成唯一的 UUID
    const objBlob = new Blob([objData]);
    const totalChunks = Math.ceil(objBlob.size / CHUNK_SIZE);

    // 生成唯一标识符 UUID，去掉 '-' 并转换为小写
    const processedUuid = uuid.replace(/-/g, '').toLowerCase();
    console.log("uuid", processedUuid, "总分片数", totalChunks);

    // 改为顺序上传，避免同时发送太多请求
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const start = chunkIndex * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, objBlob.size);
        const chunk = objBlob.slice(start, end);

        const formData = new FormData();
        formData.append('file', chunk);
        formData.append('chunkIndex', chunkIndex);
        // 确保使用模型本身的名称作为文件名
        const fileName = name || "mesh.obj";
        formData.append('filename', fileName);
        formData.append('fileType', "obj");
        formData.append('uuid', processedUuid);

        // 重试逻辑
        let success = false;
        let attempts = 0;
        const maxRetries = 3;

        while (!success && attempts < maxRetries) {
            try {
                // console.log(`上传第 ${chunkIndex + 1}/${totalChunks} 分片`);
                await ChunkFile(formData);
                success = true;
                // console.log(`第 ${chunkIndex + 1}/${totalChunks} 分片上传成功`);
            } catch (error) {
                attempts++;
                // console.error(`第 ${chunkIndex + 1}/${totalChunks} 分片上传失败 (尝试 ${attempts}/${maxRetries}):`, error);

                if (attempts >= maxRetries) {
                    throw new Error(`分片 ${chunkIndex + 1} 上传失败，已重试 ${maxRetries} 次`);
                }

                // 等待一段时间后重试
                const retryDelay = 2000 * attempts; // 逐渐增加等待时间
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }

    console.log("所有分片上传完成，准备合并文件");

    // 创建合并请求数据对象，确保使用相同的文件名
    const mergeData = {
        uuid: processedUuid,
        filename: name || "mesh.obj", // 使用传入的模型名称
        id: "",
        totalChunks: totalChunks,
        fileType: "obj",
        fileLocationInfo: JSON.stringify({ "location": [1, 2, 1] })
    };

    // 调用 MergeFile 合并文件
    let mergeAttempts = 0;
    const maxMergeRetries = 3;

    while (mergeAttempts < maxMergeRetries) {
        try {
            console.log("开始合并文件...", mergeData);
            const response = await MergeFile(mergeData);
            // console.log("文件合并成功", response.data);
            return response.data;
        } catch (error) {
            mergeAttempts++;
            console.error(`文件合并失败 (尝试 ${mergeAttempts}/${maxMergeRetries}):`, error);

            if (mergeAttempts >= maxMergeRetries) {
                throw new Error("文件合并失败，已达到最大重试次数");
            }

            // 等待更长时间再重试合并
            await new Promise(resolve => setTimeout(resolve, 3000 * mergeAttempts));
        }
    }
}