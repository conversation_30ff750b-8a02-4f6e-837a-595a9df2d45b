import request from '@/utils/request'

// 上传公司图片
export function uploadCompanyImage(data) {
  return request({
    url: '/factory/factoryImage/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取公司图片
export function getCompanyImage(id) {
  return request({
    url: `/factory/factoryImage/factory/${id}`,
    method: 'get'
  })
}

// 获取分级图片
export function getGradingImage(id) {
  return request({
    url: `/factory/factoryImage/factory/${id}`,
    method: 'get'
  })
}
