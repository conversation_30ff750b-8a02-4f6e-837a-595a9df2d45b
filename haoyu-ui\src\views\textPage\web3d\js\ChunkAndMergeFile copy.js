import { v4 as uuidv4 } from 'uuid';
import { ChunkFile, MergeFile } from '@/api/haoyu-system/web3d_api/web_3dapi.js';

/**
 * 分片上传 OBJ 数据
 * @param {string} objData - OBJ 文件内容
 */
export async function chunkAndMergeFile(objData) {
    const CHUNK_SIZE = 1 * 1024 * 1024; // 1MB 每片
    const uuid = uuidv4(); // 生成唯一的 UUID
    const objBlob = new Blob([objData]);
    const totalChunks = Math.ceil(objBlob.size / CHUNK_SIZE);

    console.log("分成", totalChunks);

    // 生成唯一标识符 UUID，去掉 '-' 并转换为小写
    const processedUuid = uuid.replace(/-/g, '').toLowerCase();
    console.log("uuid", processedUuid);

    // 定义一个并发上传的 Promise 数组
    const uploadPromises = [];

    // 分片上传
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const start = chunkIndex * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, objBlob.size);
        const chunk = objBlob.slice(start, end);

        const formData = new FormData();
        formData.append('file', chunk);
        formData.append('chunkIndex', chunkIndex);

        if (objData.name === undefined) {
            formData.append('filename', "mesh.obj");
        } else {
            formData.append('filename', objData.name);
        }

        formData.append('uuid', processedUuid); // 将 UUID 添加到表单数据中

        const uploadPromise = (async() => {
            let success = false;
            let attempts = 0;
            const maxRetries = 3;

            while (!success && attempts < maxRetries) {
                try {
                    console.log(`上传第 ${chunkIndex} 片`);
                    await ChunkFile(formData);
                    success = true;
                } catch (error) {
                    attempts++;
                    if (attempts >= maxRetries) {
                        throw new Error(`分片 ${chunkIndex} 上传失败`);
                    }
                    await new Promise(res => setTimeout(res, 1000));
                }
            }
        })();

        uploadPromises.push(uploadPromise);
    }

    try {
        await Promise.all(uploadPromises);
    } catch (error) {
        console.error("上传分片时发生错误", error);
        throw new Error("分片上传失败");
    }

    // 上传成功后合并文件
    const mergeFormData = new FormData();
    mergeFormData.append('uuid', processedUuid);
    if (objData.name === undefined) {
        mergeFormData.append('filename', "mesh.obj");
    } else {
        mergeFormData.append('filename', objData.name);
    }
    mergeFormData.append('id', "");
    mergeFormData.append('totalChunks', totalChunks);
    mergeFormData.append('fileLocationInfo', JSON.stringify({ "location": [1, 2, 1] }));

    // 将 FormData 转换为 JSON 对象
    const jsonObject = {};
    mergeFormData.forEach((value, key) => {
        jsonObject[key] = value;
    });

    // 将 JSON 对象转换为字符串
    const jsonString = JSON.stringify(jsonObject);
    console.log("jsonString", jsonString);

    // 调用 MergeFile 合并文件
    await MergeFile(jsonString);
    console.log('分片合并成功');
}