// chartKeyboardNavigation.js
/* 
* @description: 为图表添加键盘导航功能,按下左右键可以切换数据点
*/
export default class ChartKeyboardNavigation {
  constructor(chartInstance, options = {}) {
    this.chartInstance = chartInstance;
    this.data = options.data || [];
    this.xAxisData = options.xAxisData || [];
    this.currentPointIndex = 0;
    this.onPointChange = options.onPointChange || (() => {});
    this.keydownHandler = this.handleKeyPress.bind(this);
    this.isMouseInChart = false;
    this.chartElement = options.chartElement;
    this.useArrayFormat = Array.isArray(this.data[0]); // 检查是否使用二维数组格式
    // 添加鼠标进入离开事件监听
    if (this.chartElement) {
      this.mouseenterHandler = () => { this.isMouseInChart = true; };
      this.mouseleaveHandler = () => { this.isMouseInChart = false; };
    }
  }

  init() {
    window.addEventListener('keydown', this.keydownHandler);
    if (this.chartElement) {
      this.chartElement.addEventListener('mouseenter', this.mouseenterHandler);
      this.chartElement.addEventListener('mouseleave', this.mouseleaveHandler);
    }
  }

  destroy() {
    window.removeEventListener('keydown', this.keydownHandler);
    if (this.chartElement) {
      this.chartElement.removeEventListener('mouseenter', this.mouseenterHandler);
      this.chartElement.removeEventListener('mouseleave', this.mouseleaveHandler);
    }
  }

  updateData(data, xAxisData) {
    this.data = data;
    this.xAxisData = xAxisData;
    this.currentPointIndex = 0;
  }

  handleKeyPress(event) {
    if (!this.data || this.data.length === 0 || !this.isMouseInChart) return;
    
    if (event.key === 'ArrowLeft') {
      this.currentPointIndex = Math.max(0, this.currentPointIndex - 1);
      this.highlightPoint(this.currentPointIndex);
    } else if (event.key === 'ArrowRight') {
      this.currentPointIndex = Math.min(this.data.length - 1, this.currentPointIndex + 1);
      this.highlightPoint(this.currentPointIndex);
    }
  }

  highlightPoint(index) {
    if (!this.data[index]) return;
    
    const point = this.data[index];
    const xValue = point[0];
    const yValue = point[1];
    
    // 显示 tooltip
    this.chartInstance.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: index
    });
    // 更新十字指示器
    this.chartInstance.dispatchAction({
      type: 'updateAxisPointer',
      seriesIndex: 0,
      dataIndex: index
    });
    // 调用回调函数
    this.onPointChange({
      xValue,
      yValue,
      index
    });
  }
}
