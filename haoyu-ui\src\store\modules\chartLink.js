const state = {
  baseFrequency: null
}

const mutations = {
  SET_BASE_FREQUENCY(state, frequency) {
    state.baseFrequency = frequency
  }
}

const actions = {
  setBaseFrequency({ commit }, frequency) {
    console.log('[Vuex Action - chartLink/setBaseFrequency] 准备提交 SET_BASE_FREQUENCY，值为:', frequency, '类型:', typeof frequency);
    commit('SET_BASE_FREQUENCY', frequency)
  },
}
const getters = {
  baseFrequency: state => state.baseFrequency
}
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}