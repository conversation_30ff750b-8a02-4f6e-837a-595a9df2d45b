<template>
  <div >
    <h1>{{ msg }}</h1>
    <div class="controls">
      <!-- 添加颜色选择器 -->
      <label for="colorPicker">选择模型颜色:</label>
      <input type="color" id="colorPicker" v-model="color" @input="changeColor"/>
    </div>

    <div>
      <input type="file" @change="uploadImage" />
      <button @click="processImage">替换模型</button>
      <a :href="objFileUrl" download="mesh.obj" v-if="objFileUrl">下载 OBJ 文件</a>

      <div ref="objViewer" class="obj-viewer" style="width: 600px; height: 400px;"></div>
      <button @click="rotateModel">旋转模型 90 度</button>

    </div>
  </div>
</template>

<script>
import * as THREE from 'three';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js'; // 用于加载 HDR 环境贴图
import { 
  UploadFile, 
  GetImgObjList, 
  DownloadImgObjList, 
  DeleteItem, 
  getModelPosition,
  GetOrNewDateDeviceRelation 
} from '@/api/haoyu-system/web3d_api/web_3dapi.js';
import axios from 'axios';

export default {
  name: 'HelloWorld',

  props: {
    msg: String
  },
  data() {
    return {
      color: '#ff0000',
      model: null,
      scene: null,
      renderer: null,
      camera: null,
      controls: null,
      ObjUrl: '',
      pageSize: 1,
      pageNum: 1,
      selectedFile: null,
      objFileUrl: null,
      canCreate: true,
      download_list: null,
      scale: 1,
      data: {
        id: null // 需要从父组件传入
      }
    };
  },
  mounted() {
    this.initObjViewer();
  },
  methods: {
    rotateModel() {
      if (this.model) {
        // 将模型绕Y轴旋转 90 度
        this.model.rotation.x -= Math.PI / 2; // 90度的弧度值为 PI / 2
      }
    },

    resetScene() {
      // 清空场景中的所有对象
      while(this.scene.children.length > 0){
        const object = this.scene.children[0];
        this.scene.remove(object);
        if (object.geometry) object.geometry.dispose(); // 释放几何体内存
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach((material) => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      }

      // 重新初始化灯光和地面
      this.initLightingAndGround();
    },

    initLightingAndGround() {
      // 重新添加灯光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
      this.scene.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(5, 10, 7.5);
      directionalLight.castShadow = true;
      this.scene.add(directionalLight);

      // 重新添加地面
      const planeGeometry = new THREE.PlaneGeometry(100, 100);
      const planeMaterial = new THREE.MeshStandardMaterial({ color: 0x808080, roughness: 0.5 });
      const plane = new THREE.Mesh(planeGeometry, planeMaterial);
      plane.rotation.x = -Math.PI / 2;
      plane.position.y = -1;
      plane.receiveShadow = true;
      this.scene.add(plane);
    },

    // 模型
    uploadImage(event) {
        this.selectedFile = event.target.files[0];
      },

      async processImage() {
        const formData = new FormData();
        formData.append('image', this.selectedFile);

        formData.append('locationInfo', 'wzhenniubi'); // 添加此行
        try {
          const response = await axios.post('http://192.168.31.35:8000/process-image-to-obj/', formData, {
            responseType: 'json', // 修改为接收 JSON 数据
          });

          // 解析 JSON 响应以获取 obj_id
          const responseData = response.data;
          this.objFileUrl = responseData.file_url;
          this.objId = responseData.obj_id; // 保存 obj_id 到数据属性

          // 在控制台中打印 obj_id
          console.log("obj_id:", this.objId);
          this.DownloadAndLoading(this.objId);

          // 创建一个临时的 URL 供下载
          // 注意：这里不需要创建 Blob URL，因为我们直接从服务器获了文件 URL
        } catch (error) {
          console.error('处理图片时出错:', error);
        }
      },
      //

      initObjViewer() {
        const container = this.$refs.objViewer;
        if (!container) {
          console.error('容器元素未找到');
          return false;
        }

        try {
          // Setting up the scene
          this.scene = new THREE.Scene();
          this.scene.background = new THREE.Color(0xEEEEEE);

          // Setting up the camera
          this.camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
          this.camera.position.set(0, 2, 5);

          // Setting up the renderer
          this.renderer = new THREE.WebGLRenderer({ antialias: true });
          this.renderer.setSize(container.offsetWidth, container.offsetHeight);
          this.renderer.shadowMap.enabled = true; // Enable shadows
          container.appendChild(this.renderer.domElement);

          // Add light
          const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
          this.scene.add(ambientLight);

          const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
          directionalLight.position.set(5, 10, 7.5);
          directionalLight.castShadow = true;
          this.scene.add(directionalLight);

          // Add ground and set isGround property to true
          const planeGeometry = new THREE.PlaneGeometry(100, 100);
          const planeMaterial = new THREE.MeshStandardMaterial({ color: 0x808080, roughness: 0.5 });
          const plane = new THREE.Mesh(planeGeometry, planeMaterial);
          plane.rotation.x = -Math.PI / 2;
          plane.position.y = -1;
          plane.receiveShadow = true;
          plane.isGround = true; // 设置 isGround 标识
          this.scene.add(plane);

          // Add OrbitControls
          this.controls = new OrbitControls(this.camera, this.renderer.domElement);
          this.controls.enableDamping = true;
          this.controls.dampingFactor = 0.05;

          // Render loop
          const animate = () => {
            requestAnimationFrame(animate);
            this.controls.update();
            this.renderer.render(this.scene, this.camera);
          };
          animate();

          // Handle window resize
          window.addEventListener('resize', this.onWindowResize);
          
          // 初始化完成后加载模型
          this.$nextTick(() => {
            // Load model
            this.fetchImageList();
          });
          
          return true;
        } catch (error) {
          console.error('初始化场景失败:', error);
          this.scene = null;
          this.camera = null;
          this.renderer = null;
          this.controls = null;
          return false;
        }
      },

    fetchImageList() {
      if (!this.scene) {
        console.error('场景未初始化，无法加载模型列表');
        // 延迟重试
        setTimeout(() => {
          if (this.scene) {
            this.fetchImageList();
          } else {
            console.error('场景初始化超时，放弃加载模型');
          }
        }, 1000);
        return;
      }
      
      console.log("this.deviceId", this.data.id)
      if (!this.data.id) {
        console.error('设备ID未提供，无法加载模型列表');
        return;
      }
      
      GetOrNewDateDeviceRelation(this.data.id).then((response) => {
        console.log("response", response.data)
        if(response.data.error) {
          console.log("没有文件联系以新建")
          return;
        }
        console.log("不能新建")
        this.canCreate = false
        this.download_list = response.data;
        console.log("download_list", this.download_list)

        if (!this.download_list || Object.keys(this.download_list).length === 0) {
          console.log('没有可加载的模型');
          return;
        }

        Object.keys(this.download_list).forEach((key) => {
          const value = this.download_list[key];
          console.log("value", value)
          console.log("key", key)
          this.DownloadAndLoading(key);
        });
      }).catch(error => {
        console.error('获取模型关联信息失败:', error);
      });
    },

    DownloadAndLoading(id) {
      if (!this.scene) {
        console.error('场景未初始化，无法加载模型');
        return;
      }
      
      if (!id) {
        console.error('模型ID无效');
        return;
      }
      
      DownloadImgObjList(id)
        .then((response) => {
          if (!response) {
            throw new Error('下载模型数据为空');
          }
          
          const blob = new Blob([response], { type: 'obj/binary' });
          const url = URL.createObjectURL(blob);
          this.ObjUrl = url;
          
          return this.loadModel(this.ObjUrl, id);
        })
        .catch((error) => {
          console.error('Error downloading OBJ:', error);
          // this.$message.error('OBJ download failed');
        });
    },

    clearModels() {
      // 遍历场景中的所有对象，删除所有不是地面的对象
      const objectsToRemove = [];

      this.scene.traverse((child) => {
        if (child.isMesh && !child.isGround) {
          objectsToRemove.push(child); // 将需要删除的对象添加到数组中
        }
      });

      // 执行删除
      objectsToRemove.forEach((object) => {
        this.scene.remove(object);
        object.geometry.dispose(); // 释放几何体内存
        if (object.material) {
          // 果存在多种材质，需要分别释放内存
          if (Array.isArray(object.material)) {
            object.material.forEach((material) => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });

      this.model = null; // 确保模型引用被重置
    },


    loadModel(ObjUrl, id) {
      if (!ObjUrl) {
        console.error('无效的模型URL');
        return Promise.reject(new Error('无效的模型URL'));
      }

      if (!this.scene) {
        console.error('场景未初始化，无法加载模型');
        // 尝试重新初始化场景
        const initialized = this.initObjViewer();
        if (!initialized) {
          return Promise.reject(new Error('场景初始化失败'));
        }
        
        // 即使初始化成功，也再次检查场景
        if (!this.scene) {
          return Promise.reject(new Error('场景在初始化后仍不可用'));
        }
      }

      // 清除现有模型
      if (this.model) {
        this.clearModels();
      }

      const loader = new OBJLoader();
      
      return getModelPosition(id).then((response) => {
        if (!response.rows || !response.rows[0] || !response.rows[0].fileModelInfo) {
          throw new Error('模型位置信息无效');
        }

        // 在加载前最后一次确认场景状态
        if (!this.scene) {
          throw new Error('场景丢失，无法加载模型');
        }

        console.log("Position_response", response.rows[0].fileModelInfo);
        const modelInfo = response.rows[0].fileModelInfo[0];
        
        // 解析缩放值
        const scale = parseFloat(modelInfo.scale) || 1;
        
        // 解析位置字符串 "(0,0,0)" -> {x: 0, y: 0, z: 0}
        const positionMatch = modelInfo.position.match(/\((.*?)\)/);
        const position = { x: 0, y: 0, z: 0 };
        if (positionMatch) {
          const [x, y, z] = positionMatch[1].split(',').map(Number);
          position.x = x || 0;
          position.y = y || 0;
          position.z = z || 0;
        }
        
        // 更精确的颜色和透明度解析
        const color = modelInfo.color || 'rgba(255,255,255,1)';
        const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        let opacity = 1;
        if (rgbaMatch) {
          opacity = parseFloat(rgbaMatch[4]);
        }
        
        return new Promise((resolve, reject) => {
          loader.load(
            ObjUrl,
            (object) => {
              try {
                // 应用缩放
                object.scale.set(scale, scale, scale);
                this.scale = scale;

                // 应用位置
                object.position.set(position.x, position.y, position.z);

                // 创建材质时确保正确设置透明度
                const material = new THREE.MeshStandardMaterial({
                  color: color,
                  roughness: 0.3,
                  metalness: 0.8,
                  transparent: true,
                  opacity: opacity,
                  depthWrite: opacity === 1
                });

                object.traverse((child) => {
                  if (child.isMesh) {
                    child.material = material;
                    child.material.needsUpdate = true;
                    child.castShadow = true;
                    child.receiveShadow = true;
                  }
                });

                this.model = object;
                if (!this.scene) {
                  console.error('场景未初始化，无法添加模型');
                  this.initObjViewer(); // 尝试重新初始化场景
                  
                  // 再次检查
                  if (!this.scene) {
                    reject(new Error('场景未初始化，无法添加模型'));
                    return;
                  }
                }
                this.scene.add(object);
                this.opacity = opacity;
                
                resolve(object);
              } catch (error) {
                reject(error);
              }
            },
            (xhr) => {
              console.log((xhr.loaded / xhr.total) * 100 + '% loaded');
            },
            (error) => {
              console.error('Failed to load model:', error);
              reject(error);
            }
          );
        });
      }).catch(error => {
        console.error('加载模型失败:', error);
        // 使用默认设置加载模型
        return new Promise((resolve, reject) => {
          loader.load(
            ObjUrl,
            (object) => {
              try {
                object.scale.set(1, 1, 1);
                object.position.set(0, 0, 0);
                const material = new THREE.MeshStandardMaterial({
                  color: this.color,
                  roughness: 0.3,
                  metalness: 0.8,
                });
                object.traverse((child) => {
                  if (child.isMesh) {
                    child.material = material;
                    child.castShadow = true;
                    child.receiveShadow = true;
                  }
                });
                this.model = object;
                if (!this.scene) {
                  console.error('场景未初始化，无法添加模型');
                  this.initObjViewer(); // 尝试重新初始化场景
                  
                  // 再次检查
                  if (!this.scene) {
                    reject(new Error('场景未初始化，无法添加模型'));
                    return;
                  }
                }
                this.scene.add(object);
                resolve(object);
              } catch (error) {
                reject(error);
              }
            },
            null,
            reject
          );
        });
      });
    },

    changeColor() {
      if (!this.model) {
        console.warn('模型未加载，无法更改颜色');
        return;
      }
      try {
        this.model.traverse((child) => {
          if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => {
                if (material) {
                  material.color.set(this.color);
                  material.needsUpdate = true;
                }
              });
            } else {
              child.material.color.set(this.color);
              child.material.needsUpdate = true;
            }
          }
        });
        // 强制渲染更新
        if (this.renderer && this.scene && this.camera) {
          this.renderer.render(this.scene, this.camera);
        }
      } catch (error) {
        console.error('更改颜色时发生错误:', error);
      }
    },

    onWindowResize() {
      const container = this.$refs.objViewer;
      this.camera.aspect = container.offsetWidth / container.offsetHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(container.offsetWidth, container.offsetHeight);
    },

    async mergeAndUploadModels_split(event) {
      // ... 其他代码 ...
      
      this.models.forEach((model, index) => {
        model.updateMatrixWorld(true);
        model.traverse((child) => {
          if (child.isMesh) {
            meshes.push(child);
            
            // 获取颜色和透明度
            const color = child.material.color;
            // 确保获取正确的透明度值
            const opacity = child.material.opacity !== undefined ? child.material.opacity : 1;
            // 使用精确的数值转换
            const rgba = `rgba(${Math.round(color.r * 255)},${Math.round(color.g * 255)},${Math.round(color.b * 255)},${opacity.toFixed(2)})`;
            
            const modelInfo = {
              name: `mesh_${index}.obj`,
              color: rgba,
              scale: model.scale.x.toString(),
              position: `(${model.position.x},${model.position.y},${model.position.z})`
            };
            console.log('Uploading model with opacity:', opacity, 'rgba:', rgba);
            modelInfos.push(modelInfo);
          }
        });
      });
    },

    changeOpacity(newOpacity) {
      this.opacity = Number(newOpacity);
      if (this.model) {
        this.model.traverse((child) => {
          if (child.isMesh) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => {
                material.transparent = true;
                material.opacity = this.opacity;
                material.depthWrite = this.opacity === 1;
                material.needsUpdate = true;
              });
            } else {
              child.material.transparent = true;
              child.material.opacity = this.opacity;
              child.material.depthWrite = this.opacity === 1;
              child.material.needsUpdate = true;
            }
          }
        });
        this.renderer.render(this.scene, this.camera);
        console.log('Opacity changed to:', this.opacity);
      }
    },

    // 获取当前模型位置信息
    getModelPosition() {
      if (!this.model) return null;
      return {
        position: this.model.position.clone(),
        scale: this.model.scale.clone(),
        rotation: this.model.rotation.clone()
      };
    },

    // 设置模型位置信息
    setModelPosition(positionInfo) {
      if (!this.model || !positionInfo) return;
      this.model.position.copy(positionInfo.position);
      this.model.scale.copy(positionInfo.scale);
      this.model.rotation.copy(positionInfo.rotation);
    },

    initScene() {
      // 初始化场景
      this.scene = new THREE.Scene();
      
      // 如果有背景纹理，使用纹理，否则使用纯色
      if (this.backgroundTexture) {
        const geometry = new THREE.SphereGeometry(500, 60, 40);
        geometry.scale(-1, 1, 1);
        const material = new THREE.MeshBasicMaterial({
          map: this.backgroundTexture
        });
        this.backgroundMesh = new THREE.Mesh(geometry, material);
        this.scene.add(this.backgroundMesh);
      } else {
        this.scene.background = new THREE.Color(this.backgroundColor);
      }
      
      // 初始化地面
      this.initGround();
    },

    initGround() {
      // 解析地面颜色的透明度
      const groundColorStr = this.groundColor;
      let groundOpacity = 1;
      if (groundColorStr.startsWith('rgba')) {
        const matches = groundColorStr.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        if (matches) {
          groundOpacity = parseFloat(matches[4]);
        }
      }

      // 只在透明度不为0时创建地面
      if (groundOpacity >= 0.2) {
        const planeGeometry = new THREE.PlaneGeometry(100, 100);
        const planeMaterial = new THREE.MeshBasicMaterial({
          color: new THREE.Color(this.groundColor),
          side: THREE.DoubleSide,
          transparent: groundOpacity < 1,
          opacity: groundOpacity
        });

        if (this.groundTexture) {
          planeMaterial.map = this.groundTexture;
        }

        const plane = new THREE.Mesh(planeGeometry, planeMaterial);
        plane.rotation.x = -Math.PI / 2;
        plane.position.y = -1;
        plane.receiveShadow = true;
        plane.isGround = true;
        this.scene.add(plane);
      }
    },

    async loadBackgroundSettings() {
      try {
        const response = await GetBackgroundPhoto({ deviceId: this.deviceId });
        if (response.code === 200) {
          const { background, ground } = response.data;
          
          // 加载背景
          if (background) {
            this.setBackgroundFromUrl(background);
          }
          
          // 加载地面纹理
          if (ground) {
            this.loadGroundTexture(ground);
          }
        }
      } catch (error) {
        console.error('加载背景设置失败:', error);
      }
    },

    setBackgroundFromUrl(url) {
      const loader = new THREE.TextureLoader();
      loader.setCrossOrigin('Anonymous');
      loader.load(
        url,
        (texture) => {
          if (this.backgroundMesh) {
            this.scene.remove(this.backgroundMesh);
            this.backgroundMesh.geometry.dispose();
            this.backgroundMesh.material.dispose();
            this.backgroundMesh = null;
          }

          const geometry = new THREE.SphereGeometry(500, 60, 40);
          geometry.scale(-1, 1, 1);
          const material = new THREE.MeshBasicMaterial({
            map: texture
          });
          this.backgroundMesh = new THREE.Mesh(geometry, material);
          this.scene.add(this.backgroundMesh);
          this.scene.background = null;
        },
        undefined,
        (error) => {
          console.error('加载背景纹理失败:', error);
        }
      );
    },

    loadGroundTexture(url) {
      const loader = new THREE.TextureLoader();
      loader.load(
        url,
        (texture) => {
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.repeat.set(10, 10);

          this.scene.traverse((object) => {
            if (object.isGround) {
              const currentColor = object.material.color;
              object.material = new THREE.MeshBasicMaterial({
                map: texture,
                color: currentColor,
                side: THREE.DoubleSide
              });
              object.material.needsUpdate = true;
            }
          });
        },
        undefined,
        (error) => {
          console.error('加载地面纹理失败:', error);
        }
      );
    }
}

};
</script>

<style scoped>
h1 {
  margin: 20px 0;
}
.controls {
  margin-bottom: 20px;
}
.hello {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.obj-viewer {
  border: 1px solid #ccc;
  width: 600px;
  height: 400px;
}
</style>
