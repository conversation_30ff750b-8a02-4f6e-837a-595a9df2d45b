import request from '@/utils/request'
const state = {
    cachedData: {} // 用于缓存请求结果
};

const mutations = {
    SET_MEASURE_DATA(state, { select_id, data }) {
        // 将请求数据缓存到 state
        state.cachedData[select_id] = data;
    }
};

const actions = {
    async fetchMeasureData({ commit, state }, select_id) {
        if (!state.pendingRequests) {
            state.pendingRequests = {};
        }

        // 如果请求已经在进行，等待请求完成
        if (state.pendingRequests[select_id]) {
            return state.pendingRequests[select_id];
        }

        // 如果缓存中已经有该节点的数据，直接返回
        if (state.cachedData[select_id]) {
            return state.cachedData[select_id];
        }

        // 发起请求并将其存储到 pendingRequests 中
        const requestPromise = request({
            url: `/measureDefinition/measureDefinition/${select_id}`,
            method: 'get'
        }).then(result => {
            console.log("datatatatatataw", result.data)
            commit('SET_MEASURE_DATA', { select_id, data: result.data });
            return result.data;
        }).finally(() => {
            // 请求完成后，移除 pendingRequests 中的该请求
            delete state.pendingRequests[select_id];
        });

        state.pendingRequests[select_id] = requestPromise;

        return requestPromise;
    }
};

const getters = {
    getMeasureData: (state) => (select_id) => {
        return state.cachedData[select_id] || null;
    }
};

export default {
    namespaced: true, // 启用命名空间
    state,
    mutations,
    actions,
    getters
};