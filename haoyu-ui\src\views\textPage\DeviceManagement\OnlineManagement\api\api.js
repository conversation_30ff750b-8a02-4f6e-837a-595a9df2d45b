/*
    在线管理
*/
import request from '@/utils/request'

// 更改设备线数和频率
export const changeDeviceLineNumberAndFrequency = (data) => {
    return request({
        url: '/deviceManagements/deviceManagements',
        method: 'put',
        data: {
            id: data.id,  // 设备id
            collectTime: data.collectTime, // 采集时间
            gap: data.gap, // 采样间隔
            waveGap: data.waveGap, // 波形间隔
        }
    })
}

/* 获取波形时间间隔、采集间隔、采集秒数 */
export const getWaveformTimeInterval = (id) => {
    return request({
        url: `/deviceManagements/deviceManagements/second/${id}`,
        method: 'get',
    })
}
// 获取采样间隔
export const getSamplingInterval = (id) => {
    return request({
        url: `/deviceManagements/deviceManagements/commonGap`,
        method: 'get',
    })
}

// 获取波形间隔
export const getWaveformInterval = (id) => {
    return request({
        url: `/deviceManagements/deviceManagements/waveGap`,
        method: 'get',
    })
}