import request from '@/utils/request'

// 新建公司
export function addCompaniesList(data) {
  return request({
    url: '/deviceManagement/factorymanagement/insert/newCompany',
    method: 'post',
    data: data
  })
}

// 新建厂区/设备（获取mysql_id）
export function addFactory(data) {
  return request({
    url: '/deviceManagement/factorymanagement/insert/newFactory',
    method: 'post',
    data: data
  })
}

// 删除公司/厂区/设备/测点
export function delTreeNode(ids) {
  return request({
    url: '/deviceManagement/factorymanagement/' + ids,
    method: 'delete'
  })
}

// 删除基站
export function delBaseStation(ids) {
  return request({
    url: '/deviceManagements/deviceManagements/' + ids,
    method: 'delete'
  })
}

// 新增/更新指定设备配置数据
export function deviceConfigData(data) {
  return request({
    url: '/dynamic-entity/device_value/save',
    method: 'post',
    data: data
  })
}

// 批量新建设备/测点
export function batchAddDevice(data) {
  return request({
    url: '/deviceManagement/factorymanagement/add/lot',
    method: 'post',
    data: data
  })
}

// 批量更新设备/测点
export function batchUpdateDevice(data) {
  return request({
    url: '/deviceManagement/factorymanagement/add/lot',
    method: 'post',
    data: data
  })
}

// 新建测点表单提交
export function addDevicePoint(data) {
  return request({
    url: '/measurePoint/measurePointManagement',
    method: 'post',
    data: data
  })
}

// 更新测点表单提交
export function updateDevicePoint(data) {
  return request({
    url: '/measurePoint/measurePointManagement',
    method: 'put',
    data: data
  })
}

// 获取快速傅里叶类型
export function getFastFourierType() {
  return request({
    url: '/measureInfo/fastFourierTransformType/list',
    method: 'get'
  })
}

// 获取频谱单位
export function getFrequencyUnit() {
  return request({
    url: '/measureInfo/spectrumUnnit/list',
    method: 'get'
  })
}

// 获取时间信号单位
export function getTimeSignalUnit() {
  return request({
    url: '/measureInfo/timeSignalUnit/list',
    method: 'get'
  })
}

// 获取窗函数
export function getWindowFunction() {
  return request({
    url: '/measureInfo/windowsFunction/list',
    method: 'get'
  })
}

// 获取所有传感器
export function getAllSensor() {
  return request({
    url: '/sensorType/sensorType/list',
    method: 'get'
  })
}

// 获取谱线数
export function getSpectralLineCount() {
  return request({
    url: '/linesNum/linesNum/list',
    method: 'get'
  })
}

// 更新树结构信息
export function updateTreeData(data) {
  return request({
    url: '/deviceManagement/factorymanagement',
    method: 'put',
    data: data
  })
}
// 获取设备模板数据
export function getDeviceTemplateData() {
  return request({
    url: '/dynamic-entity/device_model',
    method: 'get'
  })
}

// 获取指定模板数据
export function getTemplateData(id) {
  return request({
    url: '/dynamic-entity/device_value/' + id,
    method: 'get'
  })
}

// 更新模板数据
export function updateTemplateData(data) {
  return request({
    url: '/dynamic-entity/device_model/save',
    method: 'post',
    data: data
  })
}

// 批量更新设备测点（测量定义）
export function batchUpdateDevicePoint(data) {
  return request({
    url: '/deviceManagement/factorymanagement/update/lot',
    method: 'post',
    data: data
  })
}

// 根据指定id查询到测量定义
export function getMeasurementDefine(id) {
  return request({
    url: '/measureDefinition/measureDefinition/' + id,
    method: 'get'
  })
}

// 删除测量定义
export function delMeasurementDefine(ids) {
  return request({
    url: '/measureDefinition/measureDefinition/' + ids,
    method: 'delete'
  })
}

// 查看测点信息
export function getMeasurePointInfo(id) {
  return request({
    url: '/measurePoint/measurePointManagement/' + id,
    method: 'get'
  })
}

// 批量新建/更新测量定义
export function batchAddMeasurementDefine(data) {
  return request({
    url: '/deviceManagement/factorymanagement/insert/aLot/newDefine',
    method: 'post',
    data: data
  })
}

// 批量删除测量定义
export function batchDelMeasurementDefine(data) {
  return request({
    url: '/measureDefinition/measureDefinition/' + data,
    method: 'delete'
  })
}

// 获取包络频率(贷款)
export function getEnvelopeFrequency() {
  return request({
    url: '/measureInfo/envelopeFrequency/list',
    method: 'get'
  })
}

// 获取数据重叠率
export function getDataOverlapRate() {
  return request({
    url: '/measureInfo/dataOverlapRate/list',
    method: 'get'
  })
}
