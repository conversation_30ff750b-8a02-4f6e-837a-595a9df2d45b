export default class ChartZoom {
  constructor(chart) {
    this.chart = chart
    this.startPoint = null
    this.isRightMouseDown = false
    this.initEvents()
  }

  initEvents() {
    const zr = this.chart.getZr()

    // 阻止默认右键菜单
    zr.on('contextmenu', (e) => {
      e.event.preventDefault()
      e.event.stopPropagation()
    })
    // 修改鼠标事件处理，只处理右键相关的操作
    zr.on('mousedown', (e) => {
      if (e.event.button === 2) {
        this.handleMouseDown(e)
      }
    })

    zr.on('mousemove', (e) => {
      if (this.isRightMouseDown) {
        this.handleMouseMove(e)
      }
    })

    zr.on('mouseup', (e) => {
      if (e.event.button === 2) {
        this.handleMouseUp(e)
      }
    })
    /* zr.on('mousedown', this.handleMouseDown.bind(this))
    zr.on('mousemove', this.handleMouseMove.bind(this))
    zr.on('mouseup', this.handleMouseUp.bind(this)) */
    zr.on('globalout', this.handleMouseOut.bind(this))
    // 添加双击重置功能
    zr.on('dblclick', this.handleDoubleClick.bind(this))
  }

  dispose() {
    const zr = this.chart.getZr()
    zr.off('contextmenu')
    zr.off('mousedown')
    zr.off('mousemove')
    zr.off('mouseup')
    zr.off('globalout')
    zr.off('dblclick')
  }

  handleMouseDown(params) {
    if (params.event.button === 2) {
      this.isRightMouseDown = true
      this.startPoint = [params.offsetX, params.offsetY]
      // console.log("起点", this.startPoint)
      this.updateSelectRect(
        this.startPoint[0],
        this.startPoint[1],
        this.startPoint[0],
        this.startPoint[1]
      )
    }
  }

  handleMouseMove(params) {
    if (this.isRightMouseDown && this.startPoint) {
      this.updateSelectRect(
        this.startPoint[0],
        this.startPoint[1],
        params.offsetX,
        params.offsetY
      )
    }
  }

  handleMouseUp(params) {
    if (params.event.button === 2 && this.startPoint) {
      const endPos = [params.offsetX, params.offsetY]

      // 计算拖拽距离
      const distance = Math.sqrt(
        Math.pow(endPos[0] - this.startPoint[0], 2) +
        Math.pow(endPos[1] - this.startPoint[1], 2)
      )

      // 如果拖拽距离太小，重置缩放
      if (distance < 5) {
        this.clearSelectRect()
        this.resetZoom()
      } else {
        // 获取选区的数据范围
        const startValue = this.chart.convertFromPixel(
          { xAxisIndex: 0 },
          Math.min(this.startPoint[0], endPos[0])
        )
        const endValue = this.chart.convertFromPixel(
          { xAxisIndex: 0 },
          Math.max(this.startPoint[0], endPos[0])
        )

        // 更新缩放范围
        this.updateZoom(startValue, endValue)

        // 清除选区
        requestAnimationFrame(() => {
          this.clearSelectRect()
        })
      }

      this.isRightMouseDown = false
      this.startPoint = null
    }
  }

  handleMouseOut() {
    if (this.isRightMouseDown) {
      this.isRightMouseDown = false
      this.startPoint = null
      this.clearSelectRect()
    }
  }

  handleDoubleClick() {
    this.clearSelectRect()
    this.resetZoom()
  }

  updateSelectRect(startX, startY, endX, endY) {
    const x = Math.min(startX, endX)
    const y = Math.min(startY, endY)
    const width = Math.abs(endX - startX)
    const height = Math.abs(endY - startY)

    this.chart.setOption({
      graphic: [{
        type: 'rect',
        id: 'selectRect',
        shape: {
          x: x,
          y: y,
          width: width,
          height: height
        },
        style: {
          fill: 'rgba(0,0,0,0.2)'
        }
      }]
    })
  }

  clearSelectRect() {
    this.chart.setOption({
      graphic: []
    }, {
      replaceMerge: ['graphic']
    })
  }
  // 更新缩放
  updateZoom(startValue, endValue) {
    const option = this.chart.getOption();

    // 保存当前的 markLine 配置
    const markLines = option.series[0].markLine;

    this.chart.setOption({
      dataZoom: [{
        type: 'inside',
        startValue: startValue,
        endValue: endValue,
        moveOnMouseMove: false,
        zoomOnMouseWheel: true,
      }],
      series: [{
        markLine: markLines  // 保持原有的标记线配置
      }]
    });
  }
  // 重置缩放
  resetZoom() {
    const option = this.chart.getOption();

    // 保存当前的 markLine 配置
    const markLines = option.series[0].markLine;

    // 获取数据总长度
    const series = this.chart.getOption().series;
    const data = series[0]?.data || [];
    const startValue = data[0]?.[0] || 0;
    const endValue = data[data.length - 1]?.[0] || 0;

    this.chart.setOption({
      dataZoom: [{
        type: 'inside',
        startValue: startValue,
        endValue: endValue,
        moveOnMouseMove: false,
        zoomOnMouseWheel: true,
        preventDefaultMouseMove: false
      }],
      series: [{
        markLine: markLines  // 保持原有的标记线配置
      }]
    });
  }
}
