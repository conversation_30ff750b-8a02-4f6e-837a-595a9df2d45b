<template>
  <div class="trend-chart-group">
    <template v-if="paginatedData.length">
      <div v-for="(series, index) in paginatedData" :key="series.name + index" class="trend-chart">
        <div class="chart-header">
          <div class="title">{{ getSeriesTitle(series) }}</div>
          <div class="selected-point" v-if="selectedPoints[series.name]">
            <span class="point-info">
              X: {{ selectedPoints[series.name].x }} | Y: {{ selectedPoints[series.name].y }}
            </span>
          </div>
          <div class="actions">
            <i
              class="el-icon-arrow-left"
              :class="{ 'disabled': !canMovePrevious(series.name) }"
              @click="moveToPreviousPoint(series, index)"
              title="上一个数据点"
            ></i>
            <i
              class="el-icon-arrow-right"
              :class="{ 'disabled': !canMoveNext(series.name) }"
              @click="moveToNextPoint(series, index)"
              title="下一个数据点"
            ></i>
            <i class="el-icon-full-screen" @click="handleFullScreen" title="全屏"></i>
          </div>
        </div>
        <div :ref="'chart_' + index" class="chart-container"></div>
      </div>
      <!-- 分页器 -->
      <div class="pagination">
        <div class="pagination-left">
          <el-button
            type="primary"
            size="small"
            :icon="hideOtherCharts ? 'el-icon-view' : 'el-icon-s-fold'"
            @click="toggleOtherCharts"
          >
            {{ hideOtherCharts ? '显示波形频谱' : '隐藏波形频谱' }}
          </el-button>
        </div>
        <div class="pagination-center">
          <el-pagination
            :current-page.sync="currentPage"
            :page-size="pageSize"
            :page-sizes="pageSizes"
            :total="formattedData.length"
            layout="sizes, prev, pager, next"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </template>
    <div v-else class="no-data">
      <span>暂无趋势数据</span>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getParams } from '../../utils/getParams'

export default {
  name: 'TrendChartGroup',

  props: {
    chartsData: {
      type: Object,
      default: () => ({})
    },
    measureDefPointMap: {
      type: Object,
      default: () => ({})
    },
    pointsInfo: {
      type: Array,
      default: () => []
    },
    // 新增：接收指标选择器的顺序
    selectedIndicators: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chartInstances: [],
      formattedData: [],
      currentPage: 1,
      pageSize: 4,
      pageSizes: [1, 2, 3, 4, 5, 6, 7, 8],  // 添加页面大小选项
      hideOtherCharts: false,  // 控制是否隐藏其他图表
      selectedPoints: {}  // 存储每个图表选中的点的坐标
    }
  },
  computed: {
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.formattedData.slice(start, end);
    }
  },
  watch: {
    chartsData: {
      handler(newData) {
        this.formatChartData(newData);
        this.initCharts();
      },
      deep: true
    }
  },

  methods: {
    formatChartData(data) {
      if (!data) return;

      this.formattedData = [];

      // 遍历每个指标类型（accPeak, accPpeak等）
      for (const indicatorType in data) {
        const pointData = data[indicatorType];

        // 遍历每个测点
        for (const pointId in pointData) {
          const seriesData = pointData[pointId];

          if (seriesData && seriesData.x && seriesData.y) {
            // 打印原始数据
            console.log('原始数据:', {
              indicatorType,
              pointId,
              seriesData
            });

            // 确保 z 数组存在
            const zData = seriesData.z || [];
            console.log('Z数组数据:', zData);

            // 构建数据点数组，包含完整信息
            const dataPoints = seriesData.x.map((time, index) => {
              const point = {
                time,
                value: Number(seriesData.y[index]),
                recordId: zData[index] || null  // 直接使用 z 数组的值作为 recordId
              };
              console.log(`数据点 ${index}:`, point);
              return point;
            });

            this.formattedData.push({
              name: `${indicatorType}-${pointId}`,
              xData: seriesData.x,
              yData: seriesData.y.map(val => Number(val)),
              zData: zData,
              pointId: pointId,
              indicatorType: indicatorType,
              dataPoints
            });
          }
        }
      }
      // 新增：排序，保证顺序与 selectedIndicators 一致
      if (this.selectedIndicators && this.selectedIndicators.length > 0) {
        this.formattedData.sort((a, b) => {
          return this.selectedIndicators.indexOf(a.indicatorType) - this.selectedIndicators.indexOf(b.indicatorType);
        });
      }
    },

    initCharts() {
      // 清除旧的图表实例
      if (this.chartInstances.length) {
        this.chartInstances.forEach(chart => chart.dispose());
        this.chartInstances = [];
      }

      this.$nextTick(() => {
        // 遍历分页后的数据，使用对应的 ref 获取容器
        this.paginatedData.forEach((series, index) => {
          // 使用新的 ref 获取方式
          const container = this.$refs[`chart_${index}`];
          if (container && container[0]) {
            const chart = echarts.init(container[0]);

            // 计算数据的最大值
            const maxValue = Math.max(...series.yData.filter(val => !isNaN(val)));
            const yAxisMax = maxValue * 1.5;
            const indicatorLabel = this.getIndicatorLabel(series.indicatorType);

            const option = {
              animation: false,
              grid: {
                top: 18,
                right: 10,
                bottom: 18,
                left: 50
              },
              tooltip: {
                trigger: 'axis',
                formatter: (params) => {
                  const param = params[0];
                  const dataPoint = series.dataPoints[param.dataIndex];
                  // 获取指标的中文名称
                  let tooltipText = `${param.name}<br/>${indicatorLabel}: ${param.value}`;

                  // 只添加 recordId 信息
                  if (dataPoint.recordId) {
                    tooltipText += `<br/>记录ID: ${dataPoint.recordId}`;
                  }

                  return tooltipText;
                }
              },
              dataZoom: [
                {
                  type: 'inside',
                  xAxisIndex: 0,
                  filterMode: 'none'
                }
              ],
              xAxis: {
                type: 'category',
                data: series.xData || [],
                axisLabel: {
                  formatter: (value) => {
                    return value.split(' ')[1]; // 只显示时间部分
                  }
                }
              },
              yAxis: {
                type: 'value',
                name: indicatorLabel,
                nameGap: 35,
                max: yAxisMax,
                axisLabel: {
                  formatter: (value) => {
                    return value.toFixed(4);
                  }
                }
              },
              series: [{
                data: series.yData || [],
                type: 'line',
                name: series.name,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                  width: 2
                },
                markLine: {
                  silent: true,
                  data: []
                }
              }]
            };

            chart.setOption(option);
            this.chartInstances.push(chart);

            // 添加点击事件监听
            chart.on('click', (params) => {
              const dataPoint = series.dataPoints[params.dataIndex];

              // 保存选中点的坐标信息和索引
              this.$set(this.selectedPoints, series.name, {
                x: params.name,  // 时间
                y: params.value.toFixed(4),  // 数值，保留4位小数
                dataIndex: params.dataIndex,  // 数据索引
                chartIndex: index  // 图表索引
              });

              // 更新标记线
              chart.setOption({
                series: [{
                  markLine: {
                    silent: true,
                    data: [{
                      xAxis: params.dataIndex,
                      lineStyle: {
                        color: '#ff0000',
                        width: 2
                      }
                    }]
                  }
                }]
              });
              this.handleDataPointClick(params, series, dataPoint);
            });

            // 添加缩放事件监听
            chart.on('datazoom', this.handleZoom);
          }
        });
      });
    },

    // 处理数据点击事件
    handleDataPointClick(params, series, dataPoint) {
      const timePoint = params.name;
      const [indicatorType, measureDefId] = series.name.split('-');
      const pointId = this.measureDefPointMap[measureDefId];

      console.log('点击数据点信息：', {
        timePoint,        // 时间点
        measureDefId,     // 测量定义ID
        pointId,         // 测点ID（父节点）
        indicatorType,   // 指标类型
        value: params.value,  // 当前值
        recordId: dataPoint.recordId,  // 记录ID
        measureDefPointMap: this.measureDefPointMap
      });

      // 发出点击事件，让父组件处理测量定义的获取
      this.$emit('dataPointClick', {
        timePoint,
        measureDefId,
        pointId,
        indicatorType,
        value: params.value,
        recordId: dataPoint.recordId
      });
    },

    handleZoom(range) {
      this.$emit('zoom', range)
    },
    handleResize() {
      // 使用 requestAnimationFrame 来优化性能
      if (this.resizeTimer) {
        cancelAnimationFrame(this.resizeTimer);
      }
      this.resizeTimer = requestAnimationFrame(() => {
        this.chartInstances.forEach(chart => {
          chart && chart.resize();
        });
      });
    },
    handleFullScreen(event) {
      const chartContainer = event.target.closest('.trend-chart');
      if (!chartContainer) return;

      const isFullscreen = document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement;

      if (!isFullscreen) {
        if (chartContainer.requestFullscreen) {
          chartContainer.requestFullscreen();
        } else if (chartContainer.webkitRequestFullscreen) {
          chartContainer.webkitRequestFullscreen();
        } else if (chartContainer.mozRequestFullScreen) {
          chartContainer.mozRequestFullScreen();
        } else if (chartContainer.msRequestFullscreen) {
          chartContainer.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    },
    handlePageChange(page) {
      this.currentPage = page;
      // 清除选中点数据
      this.selectedPoints = {};
      this.$nextTick(() => {
        this.initCharts();
      });
    },
    handleSizeChange(size) {
      this.pageSize = size;
      // 重新计算当前页，确保数据正确显示
      const maxPage = Math.ceil(this.formattedData.length / size);
      if (this.currentPage > maxPage) {
        this.currentPage = maxPage;
      }
      // 清除选中点数据
      this.selectedPoints = {};
      this.$nextTick(() => {
        this.initCharts();
      });
    },
        // 切换其他图表的显示/隐藏
    toggleOtherCharts() {
      this.hideOtherCharts = !this.hideOtherCharts;

      // 发送事件给父组件，控制左边波形图和频谱图的显示/隐藏
      this.$emit('toggleOtherCharts', this.hideOtherCharts);
    },

    // 检查是否可以移动到上一个数据点
    canMovePrevious(seriesName) {
      const selectedPoint = this.selectedPoints[seriesName];
      return selectedPoint && selectedPoint.dataIndex > 0;
    },

    // 检查是否可以移动到下一个数据点
    canMoveNext(seriesName) {
      const selectedPoint = this.selectedPoints[seriesName];
      if (!selectedPoint) return false;

      const series = this.paginatedData[selectedPoint.chartIndex];
      return series && selectedPoint.dataIndex < series.yData.length - 1;
    },

    // 移动到上一个数据点
    moveToPreviousPoint(series, chartIndex) {
      const seriesName = series.name;
      const selectedPoint = this.selectedPoints[seriesName];

      if (!selectedPoint || selectedPoint.dataIndex <= 0) return;

      const newIndex = selectedPoint.dataIndex - 1;
      this.moveToDataPoint(series, chartIndex, newIndex);
    },

    // 移动到下一个数据点
    moveToNextPoint(series, chartIndex) {
      const seriesName = series.name;
      const selectedPoint = this.selectedPoints[seriesName];

      if (!selectedPoint || selectedPoint.dataIndex >= series.yData.length - 1) return;

      const newIndex = selectedPoint.dataIndex + 1;
      this.moveToDataPoint(series, chartIndex, newIndex);
    },

    // 移动到指定的数据点
    moveToDataPoint(series, chartIndex, dataIndex) {
      const seriesName = series.name;
      const xValue = series.xData[dataIndex];
      const yValue = series.yData[dataIndex];
      const dataPoint = series.dataPoints[dataIndex];

      // 更新选中点信息
      this.$set(this.selectedPoints, seriesName, {
        x: xValue,
        y: yValue.toFixed(4),
        dataIndex: dataIndex,
        chartIndex: chartIndex
      });

      // 更新图表标记线
      const chart = this.chartInstances[chartIndex];
      if (chart) {
        chart.setOption({
          series: [{
            markLine: {
              silent: true,
              data: [{
                xAxis: dataIndex,
                lineStyle: {
                  color: '#ff0000',
                  width: 2
                }
              }]
            }
          }]
        });
      }

      // 触发数据点点击事件
      const params = {
        name: xValue,
        value: yValue,
        dataIndex: dataIndex
      };
      this.handleDataPointClick(params, series, dataPoint);
    },
    // 添加获取指标中文标签的方法
    getIndicatorLabel(indicatorType) {
      const indicatorMap = {
        'accPeak': '加速度峰值',
        'accPpeak': '加速度峰峰值',
        'kurt': '峭度指标',
        'accrms': '加速度有效值',
        'enValue': '包络',
        'velrms': '速度有效值',
        'disrms': '位移有效值',
        'disppk': '位移峰峰值',
        'temperature': '温度'
      };
      return indicatorMap[indicatorType] || indicatorType;
    },
    getSeriesTitle(series) {
      // 获取测点信息
      const pointInfo = this.pointsInfo.find(p => {
        return p.definitionId === parseInt(series.pointId) ||
               p.definitionId === series.pointId ||
               p.definitionId === String(series.pointId);
      });
      let baseName = pointInfo && pointInfo.name ? pointInfo.name : '';
      // 所有可能的指标中文名
      const allLabels = [
        '加速度峰值', '加速度峰峰值', '峭度指标', '加速度有效值', '包络',
        '速度有效值', '位移有效值', '位移峰峰值', '温度'
      ];
      for (const label of allLabels) {
        if (baseName.endsWith(label)) {
          baseName = baseName.slice(0, -label.length);
          break;
        }
      }
      const indicatorLabel = this.getIndicatorLabel(series.indicatorType);
      return `${baseName}${indicatorLabel}趋势图`;
    },
    getPointName(pointId) {
      const pointInfo = this.pointsInfo.find(p => {
        return p.definitionId === parseInt(pointId) ||
              p.definitionId === pointId ||
              p.definitionId === String(pointId);
      });
      return pointInfo && pointInfo.name ? pointInfo.name : '';
    },
    getPointPath(pointId) {
      // 从 formattedData 中获取对应的系列数据
      const series = this.formattedData.find(s => s.pointId === pointId);
      if (!series) return '';

      const indicatorType = series.indicatorType;
      const indicatorLabel = this.getIndicatorLabel(indicatorType);
      const measureDefId = pointId;

      // 从 pointsInfo 中找到对应的测点信息
      const pointInfo = this.pointsInfo.find(p => {
        return p.definitionId === parseInt(measureDefId) ||
               p.definitionId === measureDefId ||
               p.definitionId === String(measureDefId);
      });

      // 如果找到了完整信息，返回完整路径和指标类型
      if (pointInfo && pointInfo.name) {
        return `${pointInfo.name} ${indicatorLabel}趋势图`;
      }

      // 如果没找到完整信息，返回简单格式
      return `${indicatorLabel}趋势图`;
    }
  },

  mounted() {
    this.formatChartData(this.chartsData);
    this.initCharts();
    window.addEventListener('resize', this.handleResize);

    // 监听父容器宽度变化
    this.resizeObserver = new ResizeObserver(() => {
      this.handleResize();
    });
    this.resizeObserver.observe(this.$el);
  },

  beforeDestroy() {
    // 确保清理所有图表实例
    if (this.chartInstances.length) {
      this.chartInstances.forEach(chart => {
        chart.dispose();
      });
      this.chartInstances = [];
    }

    window.removeEventListener('resize', this.handleResize);
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
}
</script>


<style lang="scss" scoped>
.trend-chart-group {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative; // 为分页器定位

  .chart-header {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;

    .title {
      font-size: 12px;
      font-weight: bolder;
      color: #303133;
      margin: 0;
      flex: 0 0 auto;
    }

    .selected-point {
      flex: 1;
      display: flex;
      justify-content: start;

      .point-info {
        font-size: 11px;
        color: #606266;
        background-color: #f0f2f5;
        padding: 2px 8px;
        border-radius: 3px;
        white-space: nowrap;
      }
    }

    .actions {
      display: flex;
      gap: 4px;

      i {
        font-size: 16px;
        color: #606266;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover:not(.disabled) {
          color: #409EFF;
          background-color: #ecf5ff;
        }

        &.disabled {
          color: #c0c4cc;
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }

  .chart-container {
    flex: 1;
    width: 100%;
  }

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #909399;
    font-size: 14px;
  }

  .trend-chart {
    display: flex;
    flex-direction: column;
    height: calc((100% - 48px) / 4); // 减去分页器高度，平均分配高度
    width: 100%;

    &:fullscreen {
      height: 100vh;
      margin: 0;
      padding: 16px;
      box-sizing: border-box;
      background: #fff;

      .chart-container {
        flex: 1;
      }
    }
  }

  .pagination {
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
    padding: 0 16px;

    .pagination-left {
      flex: 0 0 auto;
    }

    .pagination-center {
      flex: 1;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
