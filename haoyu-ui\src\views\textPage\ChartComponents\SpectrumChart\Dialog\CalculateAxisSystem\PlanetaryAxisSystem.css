.planetary-section {
    padding: 20px;
    .type-description {
        margin-bottom: 20px;
    }
    .gearbox-overview {
        display: flex;
        gap: 32px;
        margin-bottom: 20px;
        padding: 16px 24px;
        background: #f5f7fa;
        border-radius: 4px;
        flex-wrap: wrap;
        .overview-item {
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
            .label {
                color: #606266;
                font-size: 14px;
                white-space: nowrap;
            }
            .value {
                color: #303133;
                font-weight: 500;
                font-size: 15px;
            }
        }
    }
    .gear-detail-table {
        margin-bottom: 20px;
    }
    .selected-frequencies {
        padding: 16px 24px;
        background: #f5f7fa;
        border-radius: 4px;
        .frequency-title {
            font-size: 14px;
            margin-bottom: 12px;
        }
        .frequency-tag {
            margin-right: 12px;
            margin-bottom: 12px;
            font-size: 13px;
            padding: 6px 12px;
        }
    }
}

.gear-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    .gear-type {
        font-size: 13px;
        color: #606266;
    }
}

.frequency-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 12px;
}

.frequency-section {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.frequency-title {
    color: #606266;
    font-size: 13px;
    font-weight: 500;
}

.frequency-value {
    color: #409EFF;
    font-size: 15px;
    font-weight: 500;
    text-align: center;
}

.formula {
    color: #909399;
    font-size: 12px;
    font-style: italic;
    text-align: center;
    cursor: help;
    &:hover {
        color: #409EFF;
    }
}

.gear-detail-table {
    margin: 20px 0;
    .el-table__row {
        .frequency-info {
            max-width: 100%;
            overflow: hidden;
        }
    }
}

.multiple-selection {
    .mesh-description {
        margin-bottom: 8px;
        color: #606266;
    }
}

.selected-frequencies {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    .frequency-title {
        margin-bottom: 12px;
        color: #606266;
        font-weight: 500;
    }
    .frequency-tag {
        margin: 4px;
    }
}

.editable-cell {
    .cell-value {
        padding: 5px;
        border: 1px solid transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        &:hover {
            border-color: #dcdfe6;
            border-radius: 4px;
        }
    }
    .el-input.is-disabled .el-input__inner {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #909399;
        cursor: not-allowed;
    }
}

.frequency-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.frequency-detail {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.frequency-option {
    padding: 8px 0;
    .frequency-option-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 13px;
        color: #606266;
    }
    .frequency-option-formula {
        display: flex;
        align-items: center;
        gap: 4px;
        padding-top: 8px;
        border-top: 1px dashed #EBEEF5;
        .formula-label {
            color: #909399;
            font-size: 12px;
        }
        .formula-text {
            color: #606266;
            font-size: 12px;
            font-style: italic;
        }
    }
}

.formula-container {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    .formula-label {
        color: #909399;
        font-size: 12px;
    }
    .formula-text {
        color: #606266;
        font-size: 12px;
        font-style: italic;
    }
}

.frequency-select-dropdown {
    .el-select-dropdown__item {
        padding: 0 12px;
        height: auto;
        line-height: 1.4;
        &.selected {
            .frequency-option-formula {
                .formula-text {
                    color: #409EFF;
                }
            }
        }
    }
}