<!-- 平行轴系组件 -->
<template>
  <div class="parallel-section">
    <!-- 齿轮箱参数概览 -->
    <div class="gearbox-overview">
      <div class="overview-item">
        <span class="label">总传动比:</span>
        <span class="value">{{ calculateTotalRatio }}</span>
      </div>
      <div class="overview-item">
        <span class="label">输入转速:</span>
        <el-input-number
          v-model="gearbox.children[0].inputSpeed"
          :min="0"
          :max="100000"
          :step="1"
          size="mini"
          @change="handleInputSpeedChange"
          style="width: 120px"
        >
          <template slot="suffix">rpm</template>
        </el-input-number>
      </div>
      <div class="overview-item">
        <span class="label">输出转速:</span>
        <span class="value">{{ getOutputSpeed }} rpm</span>
      </div>
    </div>

    <!-- 齿轮详细信息表格 -->
    <el-table
      :data="gearbox.children"
      border
      class="gear-detail-table"
      size="mini"
    >
      <!-- 级数列 -->
      <el-table-column
        prop="level"
        label="级数"
        width="80"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag size="mini" type="info">{{ scope.row.axis || scope.row.mainAxis }}</el-tag>
        </template>
      </el-table-column>

      <!-- 从动轮列 -->
      <el-table-column
        label="从动轮"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.description">
            <div class="gear-info">
              <span class="gear-type">{{ scope.row.description }}</span>
              <div class="editable-cell">
                <template v-if="scope.row.editableTeethCount">
                  <el-input
                    v-model.number="scope.row.teethCount"
                    size="mini"
                    @blur="handleBlur(scope.row, 'teethCount')"
                    @input="handleInput(scope.row, 'teethCount', $event)"
                    @keyup.enter.native="handleBlur(scope.row, 'teethCount')"
                  >
                    <template slot="append">齿</template>
                  </el-input>
                </template>
                <div
                  v-else
                  class="cell-value"
                  @dblclick="handleEdit(scope.row, 'teethCount')"
                >
                  {{ scope.row.teethCount }} 齿
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <span class="no-gear">-</span>
          </template>
        </template>
      </el-table-column>

      <!-- 驱动轮列 -->
      <el-table-column
        label="驱动轮"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.mainDescription">
            <div class="gear-info">
              <span class="gear-type">{{ scope.row.mainDescription }}</span>
              <div class="editable-cell">
                <template v-if="scope.row.editableMainTeethCount">
                  <el-input
                    v-model.number="scope.row.mainTeethCount"
                    size="mini"
                    @blur="handleBlur(scope.row, 'mainTeethCount')"
                    @input="handleInput(scope.row, 'mainTeethCount', $event)"
                    @keyup.enter.native="handleBlur(scope.row, 'mainTeethCount')"
                  >
                    <template slot="append">齿</template>
                  </el-input>
                </template>
                <div
                  v-else
                  class="cell-value"
                  @dblclick="handleEdit(scope.row, 'mainTeethCount')"
                >
                  {{ scope.row.mainTeethCount }} 齿
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <span class="no-gear">-</span>
          </template>
        </template>
      </el-table-column>

      <!-- 转速列 -->
      <el-table-column
        prop="inputSpeed"
        label="转速"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <div class="editable-cell">
            <template v-if="scope.row.editableInputSpeed">
              <el-input
                v-model.number="scope.row.inputSpeed"
                size="mini"
                @blur="handleBlur(scope.row, 'inputSpeed')"
                @keyup.enter.native="handleBlur(scope.row, 'inputSpeed')"
              >
                <template slot="append">rpm</template>
              </el-input>
            </template>
            <div
              v-else
              class="cell-value"
              @dblclick="handleEdit(scope.row, 'inputSpeed')"
            >
              {{ scope.row.inputSpeed }} rpm
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 啮合频率列 -->
      <el-table-column
        label="啮合频率"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <div class="frequency-info">
            <template v-if="scope.row.description && scope.row.teethCount">
              <div class="frequency-row">
                <span class="frequency-label">从动轮:</span>
                <span class="frequency-value">{{ calculateMeshFrequency(scope.row, 'follower') }}</span>
                <span class="frequency-unit">Hz</span>
              </div>
            </template>
            <template v-if="scope.row.mainDescription && scope.row.mainTeethCount">
              <div class="frequency-row">
                <span class="frequency-label">驱动轮:</span>
                <span class="frequency-value">{{ calculateMeshFrequency(scope.row, 'driver') }}</span>
                <span class="frequency-unit">Hz</span>
              </div>
            </template>
            <template v-if="!scope.row.description && !scope.row.mainDescription">
              <span class="no-gear">-</span>
            </template>
          </div>
        </template>
      </el-table-column>

      <!-- 倍频选择列 -->
      <el-table-column
        label="倍频选择"
        width="250"
        align="center"
      >
        <template slot="header">
          <span>倍频选择</span>
          <el-tooltip
            content="输入数字N，将自动选择1~N倍频"
            placement="top"
            effect="light"
          >
            <i class="el-icon-question" style="color:#409EFF;cursor:pointer;margin-left:5px;"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div class="multiple-selection">
            <!-- <template v-if="scope.row.description && scope.row.teethCount">
              <div class="mesh-description">从动轮-驱动轮啮合</div>
              <div class="multiple-row">
                <el-input
                  v-model.number="scope.row.followerMultipleInput"
                  size="mini"
                  placeholder="输入倍频数"
                  @input="handleMultipleInput(scope.row, 'follower')"
                >
                  <template slot="append">倍</template>
                </el-input>
              </div>
            </template> -->
            <template v-if="scope.row.mainDescription && scope.row.mainTeethCount">
              <!-- <div class="mesh-description">驱动轮-从动轮啮合</div> -->
              <div class="multiple-row">
                <el-input
                  v-model.number="scope.row.driverMultipleInput"
                  size="mini"
                  placeholder="输入倍频数"
                  @input="handleMultipleInput(scope.row, 'driver')"
                >
                  <template slot="append">倍</template>
                </el-input>
              </div>
            </template>
            <template v-if="!scope.row.description && !scope.row.mainDescription">
              <div class="mesh-description">-</div>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 选中倍频的频率显示 -->
    <div v-if="hasSelectedMultiples" class="selected-frequencies">
      <div class="frequency-title">选中的频率点：</div>
      <el-tag
        v-for="freq in selectedFrequencies"
        :key="freq.id"
        size="mini"
        type="info"
        class="frequency-tag"
      >
        {{ freq.value.toFixed(2) }} Hz ({{ freq.multiple }}倍频)
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ParallelAxisSystem',
  props: {
    gearbox: {
      type: Object,
      required: true
    }
  },
  computed: {
    // 计算总传动比
    calculateTotalRatio() {
      if (!this.gearbox.children || this.gearbox.children.length === 0) {
        return '0.00';
      }

      let ratio = 1;
      let validRatioFound = false;

      // 遍历相邻的齿轮对计算传动比
      for (let i = 0; i < this.gearbox.children.length - 1; i++) {
        const currentGear = this.gearbox.children[i];
        const nextGear = this.gearbox.children[i + 1];
        
        // 检查当前齿轮的驱动轮齿数和下一个齿轮的从动轮齿数是否有效
        if (currentGear.mainTeethCount && nextGear.teethCount) {
          // 传动比 = 从动轮齿数 / 驱动轮齿数
          ratio *= Number(nextGear.teethCount) / Number(currentGear.mainTeethCount);
          validRatioFound = true;
        }
      }

      return validRatioFound ? ratio.toFixed(2) : '0.00';
    },

    // 获取输入转速
    getInputSpeed() {
      const firstGear = this.gearbox.children[0];
      return firstGear ? Number(firstGear.inputSpeed).toFixed(2) : '0.00';
    },

    // 获取输出转速
    getOutputSpeed() {
      if (!this.gearbox.children || this.gearbox.children.length === 0) {
        return '0.00';
      }

      const lastGear = this.gearbox.children[this.gearbox.children.length - 1];
      return lastGear ? Number(lastGear.inputSpeed).toFixed(2) : '0.00';
    },

    // 是否有选中的倍频
    hasSelectedMultiples() {
      return this.gearbox.children.some(gear => 
        (gear.selectedFollowerMultiples && gear.selectedFollowerMultiples.length > 0) ||
        (gear.selectedDriverMultiples && gear.selectedDriverMultiples.length > 0)
      );
    },

    // 获取选中的频率点
    selectedFrequencies() {
      const frequencies = [];
      this.gearbox.children.forEach(gear => {
        // 处理从动轮频率
        if (gear.selectedFollowerMultiples && gear.selectedFollowerMultiples.length > 0 && gear.description) {
          const baseFrequency = this.calculateMeshFrequency(gear, 'follower');
          gear.selectedFollowerMultiples.forEach(multiple => {
            frequencies.push({
              id: `${gear.id}_follower_${multiple}`,
              value: Number(baseFrequency) * multiple,
              multiple,
              type: '从动轮'
            });
          });
        }

        // 处理驱动轮频率
        if (gear.selectedDriverMultiples && gear.selectedDriverMultiples.length > 0 && gear.mainDescription) {
          const baseFrequency = this.calculateMeshFrequency(gear, 'driver');
          gear.selectedDriverMultiples.forEach(multiple => {
            frequencies.push({
              id: `${gear.id}_driver_${multiple}`,
              value: Number(baseFrequency) * multiple,
              multiple,
              type: '驱动轮'
            });
          });
        }
      });
      return frequencies;
    }
  },
  methods: {
    // 计算啮合频率
    calculateMeshFrequency(gear, type) {
      if (!gear || !gear.inputSpeed) return '0.00';

      if (type === 'follower' && gear.teethCount) {
        // 从动轮啮合频率 = 从动轮齿数 × (转速/60)
        return ((Number(gear.teethCount) * Number(gear.inputSpeed)) / 60).toFixed(2);
      } else if (type === 'driver' && gear.mainTeethCount) {
        // 驱动轮啮合频率 = 驱动轮齿数 × (转速/60)
        return ((Number(gear.mainTeethCount) * Number(gear.inputSpeed)) / 60).toFixed(2);
      }
      return '0.00';
    },

    // 处理编辑状态
    handleEdit(row, field) {
      this.$set(row, `editable${this.capitalizeFirstLetter(field)}`, true);
      this.$nextTick(() => {
        const input = this.$refs[`input${this.capitalizeFirstLetter(field)}_${row.index}`];
        if (input && input.focus) {
          input.focus();
        }
      });
    },

    // 处理失去焦点
    handleBlur(row, field) {
      this.$set(row, `editable${this.capitalizeFirstLetter(field)}`, false);
      
      if (field === 'teethCount' || field === 'mainTeethCount') {
        const value = Number(row[field]);
        if (isNaN(value) || value <= 0) {
          this.$message.warning('齿数必须为大于0的数字');
          row[field] = 0;
          return;
        }

        // 找到当前齿轮在数组中的索引
        const currentIndex = this.gearbox.children.indexOf(row);
        
        // 如果不是第一个齿轮，更新当前齿轮的转速
        if (currentIndex > 0) {
          const prevGear = this.gearbox.children[currentIndex - 1];
          if (prevGear.inputSpeed && row.teethCount && prevGear.mainTeethCount) {
            // 计算新的转速 = 前一级转速 * (前一级驱动轮齿数 / 当前从动轮齿数)
            const newSpeed = (Number(prevGear.inputSpeed) * Number(prevGear.mainTeethCount)) / Number(row.teethCount);
            this.$set(row, 'inputSpeed', Number(newSpeed.toFixed(2)));
          }
        }

        // 更新后续齿轮的转速
        this.calculateSpeedsFromIndex(this.gearbox.children, currentIndex);

        // 强制更新组件
        this.$forceUpdate();

        // 触发更新事件,通知父组件传动比已更新
        this.$emit('update', JSON.parse(JSON.stringify(this.gearbox)));
      }

      if (field === 'inputSpeed') {
        const value = Number(row.inputSpeed);
        if (isNaN(value) || value < 0) {
          this.$message.warning('转速必须为大于等于0的数字');
          row.inputSpeed = 0;
          return;
        }

        // 找到当前齿轮在数组中的索引
        const currentIndex = this.gearbox.children.indexOf(row);
        
        // 更新后续齿轮的转速
        this.calculateSpeedsFromIndex(this.gearbox.children, currentIndex);
      }

      // 触发更新事件
      this.$emit('update', this.gearbox);
    },

    // 处理倍频输入
    handleMultipleInput(row, type) {
      const input = type === 'follower' ? row.followerMultipleInput : row.driverMultipleInput;
      if (!input || isNaN(input) || input <= 0) {
        if (type === 'follower') {
          row.selectedFollowerMultiples = [];
        } else {
          row.selectedDriverMultiples = [];
        }
        return;
      }

      const maxMultiple = parseInt(input);
      const multiples = Array.from(
        { length: maxMultiple },
        (_, i) => i + 1
      );

      // 计算基频
      let baseFrequency = 0;
      if (type === 'follower' && row.teethCount && row.inputSpeed) {
        // 从动轮啮合频率 = 从动轮齿数 × (转速/60)
        baseFrequency = (Number(row.teethCount) * Number(row.inputSpeed)) / 60;
      } else if (type === 'driver' && row.mainTeethCount && row.inputSpeed) {
        // 驱动轮啮合频率 = 驱动轮齿数 × (转速/60)
        baseFrequency = (Number(row.mainTeethCount) * Number(row.inputSpeed)) / 60;
      }

      // 保存基频和倍频
      if (type === 'follower') {
        row.selectedFollowerMultiples = multiples;
        row.baseFrequency = baseFrequency;
      } else {
        row.selectedDriverMultiples = multiples;
        row.baseFrequency = baseFrequency;
      }

      // 构建更新数据
      const updatedGear = {
        ...row,
        baseFrequency: Number(baseFrequency.toFixed(2)),
        frequencyType: type,
        description: type === 'follower' ? 
          `${row.description}-从动轮啮合` : 
          `${row.mainDescription}-驱动轮啮合`,
        selectedMultiples: type === 'follower' ? row.selectedFollowerMultiples : row.selectedDriverMultiples
      };

      // 触发更新事件
      this.$emit('frequency-change', updatedGear);

      // 触发更新事件
      this.$emit('update', this.gearbox);
    },

    // 首字母大写
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    // 计算后续齿轮的转速
    calculateSpeedsFromIndex(gearArray, index) {
      for (let i = index + 1; i < gearArray.length; i++) {
        const currentGear = gearArray[i];
        const prevGear = gearArray[i - 1];

        if (prevGear.mainTeethCount && currentGear.teethCount) {
          // 计算当前齿轮的转速 = 前一级转速 × (前一级驱动轮齿数 / 当前从动轮齿数)
          const speed = (Number(prevGear.inputSpeed) * Number(prevGear.mainTeethCount)) / Number(currentGear.teethCount);
          this.$set(currentGear, 'inputSpeed', Number(speed.toFixed(2)));
        }
      }
    },

    // 处理输入转速变化
    handleInputSpeedChange(value) {
      // 更新第一个齿轮的转速
      this.$set(this.gearbox.children[0], 'inputSpeed', value);
      // 重新计算所有后续齿轮的转速
      this.calculateSpeedsFromIndex(this.gearbox.children, 0);
      
      // 触发更新事件
      this.$emit('update', this.gearbox);
    },

    // 处理输入事件
    handleInput(row, field, event) {
      const value = Number(event.target.value);
      if (isNaN(value) || value <= 0) {
        this.$message.warning(`${this.capitalizeFirstLetter(field)}必须为大于0的数字`);
        row[field] = 0;
        return;
      }

      // 找到当前齿轮在数组中的索引
      const currentIndex = this.gearbox.children.indexOf(row);
      
      // 如果不是第一个齿轮，更新当前齿轮的转速
      if (currentIndex > 0) {
        const prevGear = this.gearbox.children[currentIndex - 1];
        if (prevGear.inputSpeed && row.teethCount && prevGear.mainTeethCount) {
          // 计算新的转速 = 前一级转速 * (前一级驱动轮齿数 / 当前从动轮齿数)
          const newSpeed = (Number(prevGear.inputSpeed) * Number(prevGear.mainTeethCount)) / Number(row.teethCount);
          this.$set(row, 'inputSpeed', Number(newSpeed.toFixed(2)));
        }
      }

      // 更新后续齿轮的转速
      this.calculateSpeedsFromIndex(this.gearbox.children, currentIndex);

      // 强制更新组件
      this.$forceUpdate();

      // 触发更新事件,通知父组件传动比已更新
      this.$emit('update', JSON.parse(JSON.stringify(this.gearbox)));
    }
  }
}
</script>

<style scoped lang="scss">
@import './ParallelAxisSystem.css';
</style> 