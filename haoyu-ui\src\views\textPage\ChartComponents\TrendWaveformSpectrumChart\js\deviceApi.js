import request from '@/utils/request'

// 获取测量点信息
export function getMeasurePointManagement(id) {
    return request({
        url: `/measurePoint/measurePointManagement/${id}`,
        method: 'get'
    })
}

export function getData(id) {
    const url = `/waveForm/waveFormDataInfo/list/definePoint/${id}`
    return request({
        url,
        method: 'get',
        params: {
            pageNum: 1,
            pageSize: 1,
            waveState: 1
        }
    })
}

export function getMoreInfo(queryObj) {
    const url = `/data/eigenvalues/tender/list/more`
    return request({
        url,
        method: 'get',
        params: queryObj
    })
}