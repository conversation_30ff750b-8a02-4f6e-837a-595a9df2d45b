<template>
  <div class="main-container">
    <!-- 左卡片 -->
    <div class="left_card">
      <div class="Show_area">
        <div v-for="(chart, index) in chartComponents" :key="index" class="charts"
        :class="{ expanded: activeChart === index }"
        v-show="activeChart === null || activeChart === index"
        >
          <component
            :is="chart.component"
            :class="{ expanded: activeChart === index }"
            :isExpanded="activeChart === index"
            @toggleExpand="toggleExpand(index)"
            style="width: 100%; height: 100%;"
          />
        </div>
      </div>
    </div>

    <!-- 右侧卡片,在图表未放大时显示 -->
    <div class="right_card" v-show="activeChart === null">
      <div class="right_container">
        <!-- 设备铭片卡片 -->
        <div class="card_data" v-if="formItems.length > 0">
          <h4 class="card_title">设备铭片</h4>
          <div class="deviceINFO">
            <div class="labeldev" v-for="(item, index) in formItems" :key="index">
              <span>{{ item.label }}</span>
              <span>{{ form[item.prop] || '--' }}</span>
            </div>
          </div>
        </div>

        <div class="card_data">
          <h4 class="card_title">实况参数</h4>
          <div class="deviceINFO">

            <div class="labeldev">
              <span>加速度RMS</span>
              <span>{{ Moreinfo.accrms || '--' }}</span>
            </div>
            <div class="labeldev">
              <span>速度RMS</span>
              <span>{{ Moreinfo.velrms || '--' }}</span>
            </div>

            <div class="labeldev">
              <span>峭度</span>
              <span>{{ Moreinfo.kurt || '--' }}</span>
            </div>
            <div class="labeldev">
              <span>加速度峰值</span>
              <span>{{ Moreinfo.accPeak || '--' }}</span>
            </div>
            <div class="labeldev">
              <span>加速度峰峰值</span>
              <span>{{ Moreinfo.accPpeak || '--' }}</span>
            </div>
            <div class="labeldev">
              <span>能量值</span>
              <span>{{ Moreinfo.enValue || '--' }}</span>
            </div>
            <div class="labeldev">
              <span>位移RMS</span>
              <span>{{ Moreinfo.disrms || '--' }}</span>
            </div>
            <div class="labeldev">
              <span>位移峰峰值</span>
              <span>{{ Moreinfo.disppk || '--' }}</span>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import SpectrumChart from '../SpectrumChart/SpectrumChart.vue';
import TrendChart from '../TrendChart/TrendChart.vue';
import TimeDomainChart from '../TimeDomainChart/TimeDomainChart.vue';
import { chartDownloadMixin } from '../mixins/chartDownload'
import { getMeasurePointManagement,getData,getMoreInfo } from './js/deviceApi'
import {
  getDeviceTemplateData,
  getTemplateData,
} from '@/api/haoyu-system/SystemAdministration/SystemAdministration'

export default {
name: 'Card',
components: {
  SpectrumChart,
  TrendChart,
  TimeDomainChart
},
data(){
  return {
    formItems: [],
    activeChart: null,
    chartComponents: [
      { component: 'TrendChart' }, // 图表组件
      { component: 'TimeDomainChart' }, // 第二个图表组件
      { component: 'SpectrumChart' }  // 第三个图表组件
    ],
    /* cardData: ['设备卡片', '运行信息', '告警信息'], // 卡片数据，可以根据需要放置对象或其他内容 */
    deviceInfo: {
      number: 'DEV-001234',
      name: '高性能传感器',
      value: null,
      skew: null
    },
    charts: [], // 用于存储 echarts 实例
    drawerVisible: false, // 控制抽屉显示状态
    measurementConfig: {
      lines: '',
      speed: '',
      freqMin: '',
      freqMax: ''
    },
    Moreinfo:{
      accPeak: '',
      accPpeak: '',
      kurt: '',
      accrms: '',
      velrms: '',
      enValue: '',
      disrms: '',
      disppk: ''
    },
  }
},
mounted(){
  // 确保在 DOM 加载完成后初始化图表
  this.$nextTick(() => {
    // 监听运行信息容器
    const runtimeInfo = document.querySelector('.runtimeINFO');
    if (runtimeInfo) {
      observer.observe(runtimeInfo);
    }
  });
  // 监听窗口调整事件
  window.addEventListener('resize', () => {
    this.charts.forEach(chart => {
      chart.resize();
    });
  });
},

created(){
},

watch:{
  '$store.state.tree.selectedTreeNode':{
    async handler(newNodeInfo) {
      console.log('节点ID变化trend:', newNodeInfo);
      if (!newNodeInfo) return;

      // 清空旧数据
      this.formItems = [];
      this.form = {};
      this.Moreinfo = {
        accPeak: '',
        accPpeak: '',
        kurt: '',
        accrms: '',
        velrms: '',
        enValue: '',
        disrms: '',
        disppk: ''
      };

      if (newNodeInfo.treeicon === "waterPump") {
        await this.getDeviceData(newNodeInfo.id);
        await this.getMoreinfoForForm(newNodeInfo.id);
      } else if(newNodeInfo.treeicon === "bearing") {
        const res = await getMeasurePointManagement(newNodeInfo.id);
        await this.getDeviceData(res.data.deviceId);
        await this.getMoreinfoForForm(res.data.deviceId);
      } else if(newNodeInfo.treeicon === "measureDef") {
        const res = await getData(newNodeInfo.id);
        if(res.rows.length > 0) {
          await this.getDeviceData(res.rows[0].deviceId);
          await this.getMoreinfoForForm(newNodeInfo.id);
        } else {
          console.log('未找到波形数据')
        }
      }
    },
    immediate: true
  },

  '$store.state.dataStore.time_point': {
    async handler(newTime) {
      const selectedNode = this.$store.state.tree.selectedTreeNode;
      if (selectedNode && selectedNode.treeicon === "measureDef") {
        await this.getMoreinfoForForm(selectedNode.id);
      }
    },
    immediate: true
  }
},

beforeDestroy() {
  /* disposeCharts(this.charts);
  this.charts = []; // 清空数组，防止内存泄漏 */
},
methods:{
  toggleExpand(index) {
    this.activeChart = this.activeChart === index ? null : index; // 收起或展开
  },

  updateData(newData) {
    updateChartData(this.charts, newData);
  },

  handleMeasurementUpdate() {
    // 处理测量定义更新
    console.log('更新测量定义:', this.measurementConfig);
    this.$message.success('测量定义已更新');
  },

  // 获取指定设备数据
  getDeviceData(id) {
    getTemplateData(id).then(res => {
      this.formItemsStagingArea = res
      this.formItems = res.formItems
      this.form = res.form
    })
  },

  async getMoreinfoForForm(id){
    // 从store中获取时间范围
    const timeRange = this.$store.state.dataStore.time_point;

    // 格式化时间函数
    const formatTime = (date) => {
      const pad = num => num.toString().padStart(2, '0');
      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
    };

    const middleDate = timeRange ? new Date(timeRange) : new Date();
    const startDate = new Date(middleDate.getTime() - 1000);
    const endDate = new Date(middleDate.getTime() + 1000);

    const queryObj = {
      ids: id,
      time: `${formatTime(startDate)},${formatTime(endDate)}`,
      type: "accPeak,accPpeak,kurt,accrms,velrms,enValue,disrms,disppk",
      originType: "wave"
    }

    try {
      const res = await getMoreInfo(queryObj);
      for(let key in this.Moreinfo){
        this.Moreinfo[key] = res.trendData[key][id].y[0] || '--';
      }
    } catch (error) {
      console.error('获取更多信息失败:', error);
    }
  },

  // 获取设备模板数据
  getDeviceModel() {
    getDeviceTemplateData().then(res => {
      // 存储表单模板
      this.formItemsStagingArea = res
      this.formItems = res.formItems
      this.form = res.form
    })
  },


}
}
</script>

<style scoped lang="scss">
.main-container {
  width: 100%;
  height: 100%;
  margin: 0 !important;
  box-sizing: border-box;
  padding: 5px;
  border-radius: 8px;
  filter: drop-shadow(10px);
  backdrop-filter: blur(10px);
  position: relative;
  display: flex;

  // 左边卡片
  .left_card {
    flex: 1;
    height: 100%;
    border-radius: inherit;
    display: flex;
    flex-direction: column;
    gap: 5px;
    overflow: hidden;

    // 展示区域
    .Show_area {
      position: relative;
      flex: 1;
      border-radius: inherit;
      filter: drop-shadow(10px);
      display: flex;
      flex-direction: column;
      gap: 5px;
      min-height: 0;
      height: 100%;
      overflow: hidden;

      .charts {
        flex: 1;
        background-color: rgba(100, 100, 100, 0.3);
        border-radius: 5px;
        box-sizing: border-box;
        padding: 5px;
        min-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        height: calc(33.33% - 5px);
        position: relative;

        &.expanded {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100% !important;
          height: 100% !important;
          z-index: 10;
        }

        :deep(.sycontainer) {
          height: 100%;
          .spectrum-toolbar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1;
          }
          .chart {
            margin-top: 30px;
            height: calc(100% - 60px);
          }
        }
      }
    }
  }

  // 右边卡片
  .right_card {
    width: 300px;
    margin-left: 10px;
    height: 100%;

    .right_container {
      height: 100%;
      background: rgba(100, 100, 100, 0.3);
      border-radius: 8px;
      padding: 4px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 10px;
      overflow-y: auto;

      // 设置卡片占比
      .card_data:first-child {
        flex: 2;  // 设备铭片占比更大
      }

      .card_data:last-child {
        flex: 2;  // 测量定义占比更大
      }
    }
  }

  // 卡片样式
  .card_data {
    background: #f0f0f0;
    border-radius: 12px;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }

    .card_title {
      height: 48px;
      line-height: 48px;
      padding: 0 20px;
      font-size: 15px;
      font-weight: 600;
      color: #1f2937;
      background: #f0f0f0;
      border-bottom: 1px solid #edf2f7;
      border-radius: 12px 12px 0 0;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: #409EFF;
        border-radius: 0 2px 2px 0;
      }
    }

    .deviceINFO {
      padding: 10px;

      .labeldev {
        margin-bottom: 10px;
        padding: 8px 12px;
        font-size: 13px;
        display: flex;
        align-items: center;
        background: #f0f2f5;
        border-radius: 8px;
        transition: all 0.3s;

        &:hover {
          background: #e6e8eb;
        }

        &:last-child {
          margin-bottom: 0;
        }

        span:first-child {
          width: 100px;
          color: #6b7280;
          margin-right: 20px;
        }

        span:last-child {
          color: #111827;
          font-weight: 500;
          flex: 1;
        }
      }
    }

    .Warn {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 12px;

      label {
        display: flex;
        align-items: center;
        font-size: 14px;
        height: 36px;

        span:first-child {
          width: 80px;
          color: #6b7280;
        }

        .el-input {
          width: 100px;
          margin: 0 8px;

          :deep(.el-input__inner) {
            height: 36px;
            line-height: 36px;
            border-radius: 8px;
            border-color: #e5e7eb;
            transition: all 0.3s;
            background: #f0f2f5;
          }
        }

        // 频率范围的输入框样式
        &.freq-range {
          .el-input {
            width: 80px;  // 频率范围的输入框稍窄一些
          }
        }
      }

      .el-button {
        display: block;
        width: 100%;
        margin-top: 8px;
        height: 36px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
      }
    }

    .testAI {
      display: block;
      width: calc(100% - 40px);
      margin: 0 20px 20px;
      height: 40px;
      background: #409EFF;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: #66b1ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
        background: #3a8ee6;
      }
    }
  }
}
</style>
