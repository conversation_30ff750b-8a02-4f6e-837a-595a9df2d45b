.parallel-section {
    padding: 20px;
    .gearbox-overview {
        display: flex;
        gap: 32px;
        margin-bottom: 20px;
        padding: 16px 24px;
        background: #f5f7fa;
        border-radius: 4px;
        flex-wrap: wrap;
        .overview-item {
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
            .label {
                color: #606266;
                font-size: 14px;
                white-space: nowrap;
            }
            .value {
                color: #303133;
                font-weight: 500;
                font-size: 15px;
            }
        }
    }
    .gear-detail-table {
        margin-bottom: 20px;
    }
    .selected-frequencies {
        padding: 16px 24px;
        background: #f5f7fa;
        border-radius: 4px;
        .frequency-title {
            font-size: 14px;
            margin-bottom: 12px;
        }
        .frequency-tag {
            margin-right: 12px;
            margin-bottom: 12px;
            font-size: 13px;
            padding: 6px 12px;
        }
    }
}

.gear-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    .gear-type {
        font-size: 13px;
        color: #606266;
    }
}

.frequency-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px 8px;
    .frequency-row {
        display: flex;
        align-items: center;
        gap: 8px;
        .frequency-label {
            color: #606266;
            font-size: 13px;
            min-width: 60px;
        }
        .frequency-value {
            color: #409EFF;
            font-weight: 500;
        }
        .frequency-unit {
            color: #909399;
            font-size: 12px;
        }
    }
}

.multiple-selection {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 8px;
    .mesh-description {
        color: #606266;
        font-size: 13px;
        line-height: 1.4;
    }
    .multiple-row {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        .el-input {
            width: 120px;
        }
    }
}

.editable-cell {
    .cell-value {
        padding: 5px;
        border: 1px solid transparent;
        cursor: pointer;
        &:hover {
            border-color: #dcdfe6;
            border-radius: 4px;
        }
    }
}

.no-gear {
    color: #909399;
    font-size: 14px;
}