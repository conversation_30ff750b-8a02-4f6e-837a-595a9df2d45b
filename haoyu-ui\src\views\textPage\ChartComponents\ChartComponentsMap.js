const components = {
  TrendChart: () => import('./TrendChart/TrendChart.vue'),
  TimeDomainChart: () => import('./TimeDomainChart/TimeDomainChart.vue'),
  SpectrumChart: () => import('./SpectrumChart/SpectrumChart.vue'),
  MultiTimeDomainChart: () => import('./MultiTimeDomainChart/MultiTimeDomainChart.vue'),
  MultiSpectrumChart: () => import('./MultiSpectrumChart/MultiSpectrumChart.vue'),
  TrendWaveformSpectrumChart: () => import('./TrendWaveformSpectrumChart/TrendWaveformSpectrumChart.vue'),
  CepstrumChart: () => import('./CepstrumChart/CepstrumChart.vue'),
  ChannelTemperatureChart: () => import('./ChannelTemperatureChart/ChannelTemperatureChart.vue'),
  ChannelLogChart: () => import('./ChannelLogChart/ChannelLogChart.vue'),
  EnvelopeSpectrumChart: () => import('./EnvelopeSpectrumChart/EnvelopeSpectrumChart.vue'),
  LongWaveformChart: () => import('./LongWaveformChart/LongWaveformChart.vue'),
  CrossPhaseChart: () => import('./CrossPhaseChart/CrossPhaseChart.vue'),
  WaterfallChart: () => import('./WaterfallChart/WaterfallChart.vue'),
  MultiTrendTimeDomainChart: () => import('./MultiTrendTimeDomainChart/MultiTrendTimeDomainChart.vue'),
  MultiTrendDeviceTemperatureChart: () => import('./MultiTrendDeviceTemperatureChart/MultiTrendDeviceTemperatureChart.vue'),
  MultiParameterChart: () => import('./MultiParameterChart/MultiParameterChart.vue'),
  SpectrumEnvelopeTrendChart: () => import('./SpectrumEnvelopeTrendChart/SpectrumEnvelopeTrendChart.vue'),
  OrderAnalysisChart: () => import('./OrderAnalysisChart/OrderAnalysisChart.vue'),
  ProcessTrendChart: () => import('./ProcessTrendChart/ProcessTrendChart.vue'),
  GeneralPictureView: () => import('./GeneralPictureView/GeneralPictureView.vue'),
  AlarmView: () => import('./AlarmView/AlarmView.vue'),
  FaultCaseView: () => import('./FaultCaseView/FaultCaseView.vue'),
  DiagnosticReport: () => import('./DiagnosticReport/DiagnosticReport.vue'),
  HealthReport: () => import('./HealthReport/HealthReport.vue'),
  DetectionMode: () => import('./DetectionMode/DetectionMode.vue'),
  SettingsView: () => import('./SettingsView/SettingsView.vue'),
  HealthDegree: () => import('./HealthDegreeChart/HealthDegreeChart.vue'),
  DataExport: () => import('./DataExport/index.vue')
}

export default components
