<template>
  <div class="parent-container">
    <template v-if="!showWeb3D">
      <div class="top-section">
        <div class="image-section">
          <div class="toolbar">
            <div class="toolbar-left">
              <el-button
                class="toolbar-button"
                icon="el-icon-back"
                @click="Goback"
              >
                返回
              </el-button>
            </div>
            <div class="toolbar-center">
              <el-switch
                v-model="show3D"
                active-text="3D视图"
                inactive-text="2D视图"
                class="view-switch"
                @change="handleViewChange"
              />
            </div>
            <div class="toolbar-right">
              <el-button
                v-if="!show3D"
                class="toolbar-button"
                type="primary"
                icon="el-icon-picture-outline"
                @click="triggerFileUpload"
              >
                选择背景
              </el-button>
              <el-button
                v-if="show3D"
                class="toolbar-button"
                type="primary"
                icon="el-icon-view"
                @click="GoTo3D"
              >
                3D模型
              </el-button>
              <el-button
                v-if="show3D"
                class="toolbar-button"
                type="primary"
                icon="el-icon-connection"
                @click="handleMatchClick"
              >
                匹配模型与测点
              </el-button>
            </div>
          </div>
          <div v-show="!show3D" class="canvas-container" ref="canvasContainer" />
          <div v-show="show3D && !showWeb3D" class="viewer-container">
            <objView
              v-if="show3D && !showWeb3D"
              :data="data"
              :point-list="flattenedData"
              @Goback="handleGoback"
              @definition-click="handleDefinitionClick"
              @update-trend="handleUpdateTrend"
              ref="objView"
            />
          </div>
        </div>

        <device-status
          :device-name="deviceName"
          :device-id="deviceId"
          :current-measure-name="currentMeasureName"
          :alarm-data="alarmData"
          :health-value="healthValue"
          :training-status="trainingStatus"
          :data-time="healthDataTime"
          @display-info-updated="handleDisplayInfoUpdated"
        />
      </div>

      <div class="bottom-section">
        <div class="trend-header">
          <h3>趋势图</h3>
          <div class="trend-controls">
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              size="small"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :picker-options="pickerOptions"
              @change="handleTimeRangeChange"
            ></el-date-picker>
            <el-button
              size="small"
              icon="el-icon-refresh"
              class="reset-btn"
              @click="resetTimeRange"
            >
              重置
            </el-button>
          </div>
        </div>
        <div class="trend-chart-container"></div>
      </div>
    </template>

    <web3d v-else :data="selectedData" @Goback="handleWeb3DBack" class="web3d-container" />

    <!-- 图片选择器 -->
    <image-selector
      :visible.sync="showImageSelector"
      @select="handleImageSelect"
    />
  </div>
</template>

<script>
import Konva from 'konva'
import objView from './objview.vue'
import web3d from '@/views/textPage/web3d/index.vue'
import DeviceStatus from './components/DeviceStatus.vue'
import ImageSelector from './components/ImageSelector.vue'
import { getHealthValue,getAIisTrained,updateAIisTrained,getHaveTraining, getMeasurePointManagement as getMeasurePointFromHealth, UpdataMeasurePointManagement as updateMeasurePointFromHealth } from './api/getMachineHealth'
import { getHealthCheckUrl } from '@/config/api.config'
import { getChildImformation,getTrend,getMeasureDefinition,getMeasurePointManagement,UpdataMeasurePointManagement,getWarn,getLatestTime,getDeviceImageList,NewLatestTime,UpdateLatestTime,UpdateBackground} from './api/GeneralPictureApi'
import * as echarts from 'echarts'
import { get2DImageList,delete2DImage,update2DImage,add2DImage } from './api/GeneralPictureApi'

export default {
  name: 'WindTurbineTable',
  components: {
    objView,
    web3d,
    DeviceStatus,
    ImageSelector
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      flattenedData: [],
      connections: {},
      stage: null,
      layer: null,
      showWeb3D: false,
      deviceId: '',
      deviceName: '',
      selectedData: null,
      show3D: false,
      trendChart: null,
      gaugeChart: null,
      trendData: [],
      currentMeasureId: null,
      currentMeasureName: '',
      alarmData: [

      ],
      healthValue: 0,
      trainingStatus: 'no',
      healthDataTime: '--',
      // 添加时间选择相关的数据
      timeRange: null,
      originalTrendData: null, // 用于存储原始趋势数据
      pickerOptions: {
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      showImageSelector: false,
      // 添加分辨率和缩放相关数据
      displayInfo: {
        screenWidth: 1920,
        screenHeight: 1080,
        webWidth: 1920,
        webHeight: 1080,
        devicePixelRatio: 1,
        zoomPercentage: 100
      },
      cloudDisplayInfo: {
        screenWidth: 1920,
        screenHeight: 1080,
        webWidth: 1920,
        webHeight: 1080,
        devicePixelRatio: 1,
        zoomPercentage: 100
      },
      resizeObserver: null
    }
  },
  created() {
    getHealthCheckUrl().then(url => {
      console.log('健康检查URL:', url);
      this.healthCheckUrl = url; // 将URL保存到data中
    }).catch(error => {
      console.error('获取健康检查URL失败:', error);
    });
  },
  mounted() {
    console.log('接收到的数据:props ==> ' + JSON.stringify(this.data))
    // 初始化显示器信息检测
    this.initDisplayInfo()
    
    this.flattenData(this.data.children).then(() => {
      this.$nextTick(() => {
        this.initializeKonva()
        window.addEventListener('resize', this.handleResize)
        // 初始化趋势图

        if (this.flattenedData.length > 0) {
          this.currentMeasureId = this.flattenedData[0].measureDefinitions[0].id
          this.initTrendChart()
          console.log('当前测量点id',this.currentMeasureId)
          this.fetchTrendData(this.currentMeasureId)
        }
        // 初始化仪表盘
        this.initGaugeChart()
      })
    })
  },
  beforeDestroy() {
    if (this.updateRAF) {
      cancelAnimationFrame(this.updateRAF)
    }

    window.removeEventListener('resize', this.handleResize)
    window.removeEventListener('resize', this.handleChartResize)

    if (this.stage) {
      this.stage.destroy()
      this.stage = null
    }

    if (this.trendChart) {
      this.trendChart.dispose()
      this.trendChart = null
    }

    if (this.gaugeChart) {
      this.gaugeChart.dispose()
      this.gaugeChart = null
    }

    if (this.clickTimer) {
      clearTimeout(this.clickTimer)
    }

    if (this.defClickTimer) {
      clearTimeout(this.defClickTimer)
    }

    // 清理分辨率监控
    this.cleanupDisplayInfo()
  },
  methods: {
    handleViewChange(value) {
      console.log('View changed to:', value ? '3D' : '2D');
      if (value) {
        // 切换到3D视图
        this.show3D = true;

        // 等待DOM更新完成
        this.$nextTick(async () => {
          if (this.$refs.objView) {
            try {
              // 如果objView组件存在is2DView属性并且为true，先设置为false
              if (this.$refs.objView.is2DView === true) {
                this.$refs.objView.is2DView = false;
              }
              
              // 检查3D场景是否已初始化
              if (!this.$refs.objView.scene) {
                // 初始化3D视图
                await this.$refs.objView.initObjViewer();
              }
              
              // 检查模型是否已加载
              if (this.$refs.objView.models.length === 0) {
                // 加载模型
                await this.$refs.objView.loadDeviceModels();
              }
              
              // 加载浮框位置
              await this.$refs.objView.loadingModelPointMatches();
            } catch (error) {
              console.error('初始化3D视图失败:', error);
              // this.$message.error('加载3D模型失败');
              // 如果加载失败,切回2D视图
              this.show3D = false;
            }
          }
        });
      } else {
        // 切换回2D视图
        this.show3D = false;
        this.$nextTick(() => {
          // 清理旧的画布
          if (this.stage) {
            this.stage.destroy();
            this.stage = null;
          }
          // 重新初始化画布
          this.initializeKonva();
        });
      }
    },

    async flattenData(children) {
      if(this.deviceName === ''){
        this.deviceId = String(this.data.id)
        console.log('this.deviceId',this.deviceId)
        this.deviceName = this.data.deviceName
        console.log('this.deviceId',this.deviceName)
      }

      if (!children || !children.length) return

      const promises = []

      children.forEach(child => {
        if (child.treeIcon === 'bearing') {
          // 先建测量点基本信息
          const measurePoint = {
            id: child.id,
            title: child.title,
            status: child.status,
            measureDefinitions: [],  // 用于存储量定义列表
            pointLocation: null,      // 始化 pointLocation
            cloudDisplayInfo: null    // 添加云端分辨率信息
          }

          // 将 Promise 添加到数组中
          promises.push(
            Promise.all([
              // 获取量点位置信息
              getMeasurePointFromHealth(child.id).then(res => {
                if (res.data && res.data.pointLocation) {
                  measurePoint.pointLocation = res.data.pointLocation
                  
                  // 如果存在显示器信息，保存云端分辨率信息
                  if (res.data.displayInfo) {
                    measurePoint.cloudDisplayInfo = res.data.displayInfo
                    console.log(`测量点 ${child.id} 的云端分辨率信息:`, res.data.displayInfo)
                  }
                }
              }),

              // 获取测量定义信息
              getMeasureDefinition(child.id).then(res => {
                if(res.data && res.data.measurementDefinitionList) {
                  measurePoint.measureDefinitions = res.data.measurementDefinitionList.map(item => ({
                    id: item.id,
                    name: item.measureDefineName,
                    timeSignalUnitId: item.timeSignalUnitId,
                    spectrumUnitId: item.spectrumUnitId,
                    upperLimitFrequency: item.upperLimitFrequency,
                    lowerLimitFrequency: item.lowerLimitFrequency,
                    value: item.value
                  }))
                }
              })
            ])
          )

          this.flattenedData.push(measurePoint)
        }
      })

      // 等待所有请求完成
      await Promise.all(promises)
    },

    GoTo3D() {
      // 在切换前销毁objView组件的THREE.js资源
      if (this.$refs.objView) {
        // 如果组件有提供清理方法，调用它
        if (typeof this.$refs.objView.cleanupResources === 'function') {
          this.$refs.objView.cleanupResources();
        }
      }
      this.showWeb3D = true;
      this.selectedData = this.data;
    },
    handleWeb3DBack(data) {
      this.showWeb3D = false;
      // 更新数据
      this.selectedData = data;
      
      // 延迟一下再初始化objView和其他组件，确保web3d组件已完全销毁
      this.$nextTick(() => {
        // 如果处于3D视图模式且objView组件存在，则初始化它
        if (this.show3D && this.$refs.objView) {
          // 如果有初始化方法，调用它
          if (typeof this.$refs.objView.initScene === 'function') {
            this.$refs.objView.initScene();
          }
        }
        
        // 清理旧的画布
        if (this.stage) {
          this.stage.destroy();
          this.stage = null;
        }
        
        // 重新初始化画布
        this.initializeKonva();

        // 重新初始化趋势图和仪表盘
        this.initTrendChart();
        if (this.currentMeasureId) {
          this.fetchTrendData(this.currentMeasureId);
        } else if (this.flattenedData.length > 0 && this.flattenedData[0].measureDefinitions.length > 0) {
          this.currentMeasureId = this.flattenedData[0].measureDefinitions[0].id;
          this.fetchTrendData(this.currentMeasureId);
        }
        this.initGaugeChart();
      });
    },

    handleClick(row) {
      console.log('行点击:', row)
      this.$store.dispatch('selectedRowModule/setSelectedRow', row)
      // 更新趋势图
      this.$router.push({
        path: '/textPage',
        query: {
          fromGeneralPicture: true
        }
      })

      this.currentMeasureId = row.id
      this.fetchTrendData(row.id)
    },
    Goback() {
      this.$emit('Goback')
    },

    async initializeKonva() {
      const container = this.$refs.canvasContainer;
      if (!container) {
        console.warn('Canvas container not found');
        return;
      }

      // 确保容器有有效的尺寸
      if (container.offsetWidth <= 0 || container.offsetHeight <= 0) {
        console.warn('Container has invalid dimensions');
        return;
      }

      try {
      this.stage = new Konva.Stage({
        container: container,
        width: container.offsetWidth,
        height: container.offsetHeight
        });

        this.layer = new Konva.Layer();
        this.stage.add(this.layer);

      // 尝试加载设备图片
      try {
          const response = await getDeviceImageList(this.deviceId);
          const imageObj = new Image();

          console.log('response',response)

        if (response.data && response.data.deviceImageUrl) {
            imageObj.src = `${response.data.deviceImageUrl}`;
        } else {
            imageObj.src = require('@/assets/img/test.png');
        }

        imageObj.onload = () => {
            // 确保stage和layer仍然存在
            if (!this.stage || !this.layer) {
              console.warn('Stage or layer no longer exists');
              return;
            }

            // 再次检查容器尺寸
            if (container.offsetWidth <= 0 || container.offsetHeight <= 0) {
              console.warn('Container dimensions became invalid');
              return;
            }

          const konvaImage = new Konva.Image({
            x: 0,
            y: 0,
            image: imageObj,
            width: this.stage.width(),
            height: this.stage.height(),
            draggable: false
            });

            this.layer.add(konvaImage);

            // 继续添加其他元素...
            this.addMeasurementPoints();
          };
        } catch (error) {
          console.error('加载设备图片失败:', error);
          // 加载默认图片
          const imageObj = new Image();
          imageObj.src = require('@/assets/img/test.png');
          imageObj.onload = () => this.handleImageLoad(imageObj);
        }
      } catch (error) {
        console.error('初始化 Konva 失败:', error);
      }
    },

    // 添加一个新方法来处理图片加载
    handleImageLoad(imageObj) {
      if (!this.stage || !this.layer) {
        console.warn('Stage or layer no longer exists');
        return;
      }

      const container = this.$refs.canvasContainer;
      if (!container || container.offsetWidth <= 0 || container.offsetHeight <= 0) {
        console.warn('Container has invalid dimensions');
        return;
      }

          const konvaImage = new Konva.Image({
            x: 0,
            y: 0,
            image: imageObj,
            width: this.stage.width(),
            height: this.stage.height(),
            draggable: false
      });

      this.layer.add(konvaImage);
      this.addMeasurementPoints();
      
      // 在添加测量点后应用坐标映射
      this.$nextTick(() => {
        this.remapPointLocations();
      });
    },

    // 将测量点的添加逻辑分离到单独的方法
    addMeasurementPoints() {
      if (!this.stage || !this.layer) {
        console.warn('Stage or layer not initialized');
        return;
      }

      // 添加一个防抖变量
      let saveTimeout = null;
      let pendingUpdates = new Set();

      this.flattenedData.forEach((point, index) => {
        const baseHeight = 40;
        const childHeight = 30;
        const spacing = this.stage.width() / (this.flattenedData.length + 1);

        // 先创建临时文本对象来计算宽度
        const tempTitle = new Konva.Text({
          text: point.title,
          fontSize: 14,
          fontFamily: 'Microsoft YaHei',
          padding: 10
        });

        // 创建临时的长测量定义文本来计算宽度
        let maxDefWidth = 0;
        if (point.measureDefinitions && point.measureDefinitions.length > 0) {
          point.measureDefinitions.forEach(def => {

            const tempDefText = new Konva.Text({
              text: def.lowerLimitFrequency === '' || def.upperLimitFrequency === ''
                ? def.name + '       ' + def.value
                : def.name + '(' + def.lowerLimitFrequency + '-' + def.upperLimitFrequency + 'HZ' + ')' + '         ' + def.value + ' ' + this.getUnitText(def.timeSignalUnitId),
              fontSize: 12,
              padding: 8
            });
            maxDefWidth = Math.max(maxDefWidth, tempDefText.getTextWidth());
            tempDefText.destroy(); // 清理临时对象
          });
        }

        // 计算所需的框宽度
        const boxWidth = Math.max(
          maxDefWidth + 40, // 定义文本宽度 + padding
          tempTitle.getTextWidth() + 40, // 标题宽度 + padding
          200 // 最小宽度
        );

        tempTitle.destroy(); // 清理临时对象

        const x = spacing * (index + 1) - boxWidth/2;

        // 创建组
        const box = new Konva.Group({
          x: point.pointLocation ? point.pointLocation[0].locationInfo.x : x,
          y: point.pointLocation ? point.pointLocation[0].locationInfo.y : 50,
          draggable: true,
          id: point.id
        });

        // 创建外部容器
        const container = new Konva.Rect({
          width: boxWidth,
          height: baseHeight,
          fill: 'transparent',
          stroke: '#1890ff',
          strokeWidth: 1,
          cornerRadius: 4,
          shadowColor: 'rgba(0,0,0,0.1)',
          shadowBlur: 10,
          shadowOffset: { x: 0, y: 2 },
          shadowOpacity: 0.5
        });

        // 创建实际的标题
        const title = new Konva.Text({
          text: point.title,
          fontSize: 14,
          fontFamily: 'Microsoft YaHei',
          fill: '#1890ff',
          width: boxWidth,
          padding: 10,
          align: 'center'
        });

        // 创建分隔线
        const separator = new Konva.Line({
          points: [0, baseHeight, boxWidth, baseHeight],
          stroke: '#e8e8e8',
          strokeWidth: 1
        });

        // 添加到组
        box.add(container);
        box.add(title);
        box.add(separator);

        // 添加点击事件
        // 点击事件
        box.on('click', () => {
          this.handleBoxClick(point);
        });

        this.layer.add(box);

        // 如果已经有测量定义数据，直接更新显示
        if (point.measureDefinitions && point.measureDefinitions.length > 0) {
          const totalHeight = baseHeight + (point.measureDefinitions.length * childHeight);
          container.height(totalHeight);
          point.measureDefinitions.forEach((def, idx) => {
            const childY = baseHeight + (idx * childHeight);
            // 创建一个组来包含背景和文本
            const childGroup = new Konva.Group({
              x: 0,
              y: childY,
              width: boxWidth,
              height: childHeight
            });

            // 添加背景矩形
            const background = new Konva.Rect({
              width: boxWidth,
              height: childHeight,
              fill: 'transparent', // 默认透明
              cornerRadius: 2
            });

            const childText = new Konva.Text({
              text: def.lowerLimitFrequency === '' || def.upperLimitFrequency === ''
              ? def.name + '       ' + def.value
                : def.name + '(' + def.lowerLimitFrequency + '-' + def.upperLimitFrequency + 'HZ' + ')' + '         ' + def.value + ' ' + this.getUnitText(def.timeSignalUnitId),
              fontSize: 12,
              fill: '#666666',
              width: boxWidth - 20,
              padding: 8,
              x: 10,
              ellipsis: true // 添加省略号
            });

            // 为整个组添加鼠标事件
            childGroup.on('mouseover', () => {
              background.fill('#f5f5f5');
              box.getLayer().batchDraw();
            });

            childGroup.on('mouseout', () => {
              background.fill('transparent');
              box.getLayer().batchDraw();
            });

            childGroup.on('click', (e) => {
              // 阻止事件冒泡
              e.cancelBubble = true;

              // 更新趋势图和健康度
              this.currentMeasureId = def.id;
              this.currentMeasureName = point.title + '-' + def.name;
              this.fetchTrendData(def.id);
            });

            // 添加双击事件
            childGroup.on('dblclick', (e) => {
              // 阻止事件冒泡
              e.cancelBubble = true;

              this.handleDefinitionClick({
                ...def,
                pointId: point.id,
                pointTitle: point.title
              });
            });

            // 将背景和文本添加到组中
            childGroup.add(background);
            childGroup.add(childText);
            box.add(childGroup);
          });
        }


        // 计算终点位置
        const endPointY = point.pointLocation ? point.pointLocation[1].locationInfo.y : (this.stage.height() * 0.6);

        // 创建终点圆点
        const endPoint = new Konva.Circle({
          x: point.pointLocation ? point.pointLocation[1].locationInfo.x : (x + boxWidth/2),
          y: endPointY,
          radius: 5,
          fill: '#fff',
          stroke: '#1890ff',
          strokeWidth: 1.5,
          draggable: true
        });

        // 添加pointId属性用于映射时识别
        endPoint.setAttr('pointId', point.id);

        // 如果有云端位置数据，应用坐标映射
        if (point.pointLocation && point.pointLocation.length >= 2) {
          const mappedBox = this.mapCoordinatesFromCloud(
            point.pointLocation[0].locationInfo.x,
            point.pointLocation[0].locationInfo.y,
            point.cloudDisplayInfo
          );
          
          const mappedEndPoint = this.mapCoordinatesFromCloud(
            point.pointLocation[1].locationInfo.x,
            point.pointLocation[1].locationInfo.y,
            point.cloudDisplayInfo
          );

          box.position({
            x: mappedBox.x,
            y: mappedBox.y
          });

          endPoint.position({
            x: mappedEndPoint.x,
            y: mappedEndPoint.y
          });
        }

        // 创建三折线 - 使用映射后的实际位置
        const line = new Konva.Line({
          points: [
            box.x() + boxWidth/2, box.y() + container.height(),
            box.x() + boxWidth/2, (endPoint.y() + box.y() + container.height()) / 2,
            box.x() + boxWidth/2, (endPoint.y() + box.y() + container.height()) / 2,
            endPoint.x(), (endPoint.y() + box.y() + container.height()) / 2,
            endPoint.x(), endPoint.y()
          ],
          stroke: 'rgba(24, 144, 255, 0.5)',
          strokeWidth: 3,
          lineCap: 'round',
          lineJoin: 'round',
          dash: [4, 4]
        });

        // 添加pointId属性用于识别连接线
        line.setAttr('pointId', point.id);
        line.setAttr('lineType', 'connection');

        // 修改拖动事件处理
        box.on('dragmove', () => {
          // 使用新的更新连接线方法
          this.updateConnectionLine(point.id, box, endPoint);
          this.layer.batchDraw();

          // 将当前点添加到待更新集合
          pendingUpdates.add(point.id);

          // 清除之前的定时器
          if (saveTimeout) {
            clearTimeout(saveTimeout);
          }
        });

        box.on('dragend', () => {
          // 拖动结束时保存位置
          this.savePointLocations(point.id, box, endPoint);
        });

        endPoint.on('dragmove', () => {
          // 使用新的更新连接线方法
          this.updateConnectionLine(point.id, box, endPoint);
          this.layer.batchDraw();

          // 将当前点添加到待更新集合
          pendingUpdates.add(point.id);

          // 清除之前的定时器
          if (saveTimeout) {
            clearTimeout(saveTimeout);
          }
        });

        endPoint.on('dragend', () => {
          // 拖动结束时保存位置
          this.savePointLocations(point.id, box, endPoint);
        });

        this.layer.add(line);
        this.layer.add(endPoint);
      });

      this.layer.draw();

      // 添加画布失去焦点事件
      this.stage.on('mouseout', () => {
        if (pendingUpdates.size > 0) {
          // 保存所有待更新的点位置
          pendingUpdates.forEach(pointId => {
            const point = this.flattenedData.find(p => p.id === pointId);
            if (point) {
              const box = this.layer.findOne(`#${pointId}`);
              const endPoint = this.layer.findOne(`Circle`); // 需要根据实际情况调整选择器
              if (box && endPoint) {
                this.savePointLocations(pointId, box, endPoint);
              }
            }
          });
          pendingUpdates.clear();
        }
      });
    },

    // 添加新方法用于保存点位置
    savePointLocations(pointId, box, endPoint) {
      const boxPos = box.position();
      const endPointPos = endPoint.position();
      
      // 将本地坐标映射到云端标准坐标
      const mappedBox = this.mapCoordinatesToCloud(boxPos.x, boxPos.y);
      const mappedEndPoint = this.mapCoordinatesToCloud(endPointPos.x, endPointPos.y);
      
      const pointData = {
        id: pointId,
        pointLocation: [
          {
            type: "box",
            linesColor: "blue",
            locationInfo: {
              x: mappedBox.x,
              y: mappedBox.y
            }
          },
          {
            type: "point",
            linesColor: "blue",
            locationInfo: {
              x: mappedEndPoint.x,
              y: mappedEndPoint.y
            }
          }
        ],
        // 添加分辨率信息
        displayInfo: {
          localResolution: {
            containerWidth: this.$refs.canvasContainer ? this.$refs.canvasContainer.offsetWidth : 1920,
            containerHeight: this.$refs.canvasContainer ? this.$refs.canvasContainer.offsetHeight : 1080,
            screenWidth: this.displayInfo.screenWidth,      // 保留用于参考
            screenHeight: this.displayInfo.screenHeight,    // 保留用于参考
            webWidth: this.displayInfo.webWidth,            // 保留用于参考
            webHeight: this.displayInfo.webHeight,          // 保留用于参考
            zoomPercentage: this.displayInfo.zoomPercentage, // 保留用于参考
            devicePixelRatio: this.displayInfo.devicePixelRatio
          },
          cloudResolution: {
            containerWidth: 1920,     // 云端标准画布容器尺寸
            containerHeight: 1080,    // 云端标准画布容器尺寸
            screenWidth: 1920,        // 云端标准屏幕分辨率（参考）
            screenHeight: 1080,       // 云端标准屏幕分辨率（参考）
            webWidth: 1920,           // 云端标准网页分辨率（参考）
            webHeight: 1080,          // 云端标准网页分辨率（参考）
            zoomPercentage: 100,      // 云端标准缩放比例（参考）
            devicePixelRatio: 1
          },
          mappingInfo: {
            note: "坐标已按容器尺寸比例映射转换，确保不同设备显示一致",
            containerRatio: {
              width: this.$refs.canvasContainer ? (this.$refs.canvasContainer.offsetWidth / 1920) : 1,
              height: this.$refs.canvasContainer ? (this.$refs.canvasContainer.offsetHeight / 1080) : 1
            }
          },
          timestamp: new Date().toISOString()
        }
      };

      console.log('保存点位置信息:', {
        原始本地坐标: { box: boxPos, endPoint: endPointPos },
        映射云端坐标: { box: mappedBox, endPoint: mappedEndPoint },
        分辨率信息: pointData.displayInfo
      });

      updateMeasurePointFromHealth(pointData).catch(error => {
        console.error('更新测量点位置失败:', error);
      });
    },

    createConnection(id, endX, endY) {
      const tableContainer = this.$refs.tableContainer
      const tableRows = tableContainer.querySelectorAll('.el-table__row')
      let tableElement = null

      tableRows.forEach(row => {
        const idCell = row.querySelector('td:first-child')
        if (idCell && idCell.textContent.trim() === id.toString()) {
          tableElement = row
        }
      })

      if (!tableElement) {
        console.error(`无法找到 ID 为 ${id} 的表格行`)
        return
      }

      const rect = tableElement.getBoundingClientRect()
      const containerRect = this.$refs.canvasContainer.getBoundingClientRect()
      const startX = rect.left - containerRect.left + rect.width
      const startY = rect.top - containerRect.top + rect.height / 2

      const line = new Konva.Arrow({
        points: [startX, startY, endX, endY],
        stroke: '#0095ff',
        strokeWidth: 1,
        pointerLength: 0,
        pointerWidth: 0,
        lineCap: 'round',
        lineJoin: 'round'
      })
      this.layer.add(line)

      const point = new Konva.Circle({
        x: endX,
        y: endY,
        radius: 3,
        fill: '#0095ff',
        draggable: true
      })

      point.on('dragmove', () => {
        const rect = tableElement.getBoundingClientRect()
        const startX = rect.left - containerRect.left + rect.width
        const startY = rect.top - containerRect.top + rect.height / 2
        line.points([startX, startY, point.x(), point.y()])
        this.layer.batchDraw()
      })

      this.connections[id] = { line, point, tableElement }
      this.layer.add(point)
      this.layer.draw()
    },


    updateConnections() {
      const containerRect = this.$refs.canvasContainer.getBoundingClientRect()

      for (const id in this.connections) {
        const conn = this.connections[id]
        const tableElement = conn.tableElement

        if (tableElement) {
          const rect = tableElement.getBoundingClientRect()
          const startX = rect.left - containerRect.left + rect.width
          const startY = rect.top - containerRect.top + rect.height / 2
          conn.line.points([startX, startY, conn.point.x(), conn.point.y()])
        }
      }
      this.layer.batchDraw()
    },
    handleResize() {
      if (!this.stage || !this.$refs.canvasContainer) return;

      const container = this.$refs.canvasContainer;
      if (!container) return;

      const oldWidth = this.stage.width();
      const oldHeight = this.stage.height();
      const newWidth = container.offsetWidth;
      const newHeight = container.offsetHeight;

      // 只有当尺寸真正变化时才执行
      if (oldWidth !== newWidth || oldHeight !== newHeight) {
        console.log('容器尺寸变化:', {
          从: `${oldWidth}x${oldHeight}`,
          到: `${newWidth}x${newHeight}`
        });

        this.stage.width(newWidth);
        this.stage.height(newHeight);

        // 调整背景图尺寸
        const imageNode = this.layer?.findOne('Image');
        if (imageNode) {
          imageNode.width(newWidth);
          imageNode.height(newHeight);
        }

        // 更新显示器信息
        this.updateDisplayInfo();

        // 重新映射所有测量点位置
        this.remapPointLocations();

        if (this.layer) {
          this.layer.batchDraw();
        }
      }
    },
    handleGoback() {
      this.show3D = false;
      // 等待 DOM 更新完成
      this.$nextTick(() => {
        // 确保容器存在
        const container = this.$refs.canvasContainer;
        if (!container) {
          console.warn('Canvas container not found');
          return;
        }

        // 先销毁旧的实例
        if (this.stage) {
          this.stage.destroy();
          this.stage = null;
        }

        // 等待一个短暂的延迟，确保容器尺寸已经计算完成
        setTimeout(() => {
          if (container.offsetWidth > 0 && container.offsetHeight > 0) {
            this.initializeKonva();
          } else {
            console.warn('Container dimensions are zero');
          }
        }, 100);
      });
    },

    // 初始化趋势图
    initTrendChart() {
      const chartDom = this.$el.querySelector('.trend-chart-container')
      if (!chartDom) {
        console.warn('趋势图容器未找到')
        return
      }

      // 确保理旧的图表实例
      if (this.trendChart) {
        this.trendChart.dispose()
        this.trendChart = null
      }

      this.trendChart = echarts.init(chartDom)

      // 添加窗口大小改变的监听
      window.addEventListener('resize', this.handleChartResize)
    },

    // 处理图表大小变
    handleChartResize() {
      if (this.trendChart) {
        this.trendChart.resize()
      }
      if (this.gaugeChart) {
        this.gaugeChart.resize()
      }
    },

    // 获取趋势数据
    async fetchTrendData(id) {
      // console.log('趋势图id',id)
      getWarn({
        definitionId: id,
      }).then(res => {
        // console.log('报警记录:', res)
        if (res.rows && res.rows.length > 0) {
          // 更新测量点和定义名称
          this.currentMeasureName = res.rows[0].measurementName + '-' + res.rows[0].definitionName

          // 转换警记录数据格式
          this.alarmData = res.rows.map(item => ({
            warnTime: item.warnTime,
            type: this.getWarnLevelType(item.warnValue, item.measurementDefinition),
            warnMsg: `${item.warnMsg} (${item.warnValue})`
          }))
        } else {
          this.alarmData = [] // 如果没有数据则清空
        }
      })

      try {
        const response = await getTrend(id)
        if (response.status === 1 && response.data) {
          this.originalTrendData = JSON.parse(JSON.stringify(response.data)) // 保存原始数据的副本
          this.trendData = response.data
          this.timeRange = null // 重置时间范围
          this.updateTrendChart()
          this.fetchHealthValue(id)
        } else {
          console.error('获取趋势数据失败:', response.message)
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error)
      }
    },

    // 处理时间范围变更
    handleTimeRangeChange(timeRange) {
      if (!timeRange || !this.originalTrendData || !this.originalTrendData.x || this.originalTrendData.x.length === 0) {
        return
      }

      // 获取选择的开始和结束时间
      const startTime = timeRange[0].getTime()
      const endTime = timeRange[1].getTime()

      // 过滤数据以仅显示选定时间范围内的数据点
      const filteredData = {
        x: [],
        y: [],
        z: []
      }

      // 遍历原始数据并根据时间范围过滤
      this.originalTrendData.x.forEach((dateStr, index) => {
        const dateTime = new Date(dateStr).getTime()
        if (dateTime >= startTime && dateTime <= endTime) {
          filteredData.x.push(this.originalTrendData.x[index])
          if (this.originalTrendData.y && this.originalTrendData.y[index] !== undefined) {
            filteredData.y.push(this.originalTrendData.y[index])
          }
          if (this.originalTrendData.z && this.originalTrendData.z[index] !== undefined) {
            filteredData.z.push(this.originalTrendData.z[index])
          }
        }
      })

      // 更新趋势数据并重新绘制图表
      this.trendData = filteredData
      this.updateTrendChart()
    },

    // 重置时间范围
    resetTimeRange() {
      this.timeRange = null
      if (this.originalTrendData) {
        this.trendData = JSON.parse(JSON.stringify(this.originalTrendData))
        this.updateTrendChart()
      }
    },

    // 更新趋势图
    updateTrendChart() {
      if (!this.trendChart) return

      // 检查是否有数据
      const hasData = this.trendData.x && this.trendData.x.length > 0

      const option = {
        title: {
          show: !hasData,  // 当没有据时显示标题
          text: '暂无数',
          textStyle: {
            color: '#999',
            fontSize: 14,
            fontWeight: 'normal'
          },
          left: 'center',
          top: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        toolbox: {
          show: true,
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          },
          right: 20
        },
        legend: {
          data: ['测量值'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          }
        ],
        xAxis: {
          type: 'category',
          data: this.trendData.x || [],
          axisLabel: {
            formatter: (value) => {
              return value ? value.substring(11, 19) : '' // 只显示时间部分
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '测量值',
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '测量值',
            type: 'line',
            data: this.trendData.y || [],
            smooth: true,
            showSymbol: true,
            lineStyle: {
              width: 2
            }
          }
        ]
      }

      this.trendChart.setOption(option)
    },

    // 获健康值AIAIAIAIAIAI
    async fetchHealthValue(id) {
      try {
        const trainedResponse = await getAIisTrained(id)
        const measurePointResponse = await getMeasurePointFromHealth(trainedResponse.data.factoryManagementId)
        const haveTrainingResponse = await getHaveTraining()

        this.trainingStatus = measurePointResponse.data.trained
        // 判断已经存在正在训练的模型
        if(haveTrainingResponse.rows.length > 0){
          this.trainingStatus = "training"
        }

        // 检查 this.trendData 是否存在有数据
        if (!this.trendData || !this.trendData.z || !this.trendData.z.length || !this.trendData.x || !this.trendData.x.length) {
          this.trainingStatus = "当前节点无数据接入"
          this.healthValue = 100;
          this.healthDataTime = '--'
          return;
        }

        const timePoint = await getLatestTime(id,1)
        console.log('timePoint:', timePoint.rows[0].collectorTime)

        console.log('this.trendData.z[0]:', this.trendData.z[0])
        const baseParams = {
          "id": String(this.trendData.z[0]),
          "device_id": String(this.deviceId),
          "point_id": String(trainedResponse.data.factoryManagementId),
          "time_point": timePoint.rows[0].collectorTime,
          "fs_lines": [2000, 2000],
          "device_type": 2,
          "status": "train"
        }


        if (measurePointResponse.data.trained === "no" && this.trainingStatus != "training") {
          const trainParams = {
            ...baseParams,
            "status": "train"
          }
          console.log("1,train")
          // 更新状态为 training
          this.trainingStatus = "training"
          await updateMeasurePointFromHealth({
            id: trainedResponse.data.factoryManagementId,
            trained: "training"
          })

          console.log('准备开训练')
          fetch(this.healthCheckUrl + 'ai_health', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(trainParams)
          })
          .then(response => response.json())  // 解析响应JSON
          .then(data => {
            // 如果成功抓取到数据,就输出到控制台
            console.log('AI训练响应数据:', data)
            if(data.message == "没有找到训练数据"){
              updateMeasurePointFromHealth({
                id: trainedResponse.data.factoryManagementId,
                trained: "no"
              })
              this.trainingStatus = "no"
            }
            // console.error('没有找到训练数据')

          })
          .catch(() => {
            // 忽略所有错误
          })
          console.log('训练请求已发送')
          this.healthValue = 100
          this.healthDataTime = timePoint.rows[0].collectorTime

        } else if (measurePointResponse.data.trained === "yes") {
          this.trainingStatus = 'yes'
          // 如果已训练完成，发送试请求
          const testParams = {
            ...baseParams,
            "status": "test",
            prediction_days: 30 ,
          }
          console.log("训练完成，开始检测")

          const url = this.healthCheckUrl + 'ai_health'
          const response = await getHealthValue(url,testParams)
          if (response && response.data) {
            console.log('最大健康度:', response.data.health_value)
            this.healthValue = response.data.health_value || 100
            this.healthDataTime = timePoint.rows[0].collectorTime
          }
        }else{
          console.log('this.trainingStatus:', this.trainingStatus)
          console.log('measurePointResponse.data.trained:', measurePointResponse.data.trained)
          console.log('what?')
        }

      } catch (error) {
        console.error('获取健康值失败:', error)
        this.healthValue = 100
        this.healthDataTime = '--'
        this.trainingStatus = 'no'
      }
    },

    // 初始化仪表盘
    initGaugeChart() {
      const chartDom = this.$refs.gaugeContainer
      if (!chartDom) {
        console.warn('仪表盘容器未找到')
        return
      }

      // 确保清理的图表实例
      if (this.gaugeChart) {
        this.gaugeChart.dispose()
        this.gaugeChart = null
      }

      this.gaugeChart = echarts.init(chartDom)

      // 生成随机数(0-100)
      const randomValue = Math.floor(Math.random() * 100)

      const option = {
        backgroundColor: '#fff',
        series: [{
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,    // 改为半圆形
          min: 0,
          max: 100,
          radius: '100%', // 满容器
          progress: {
            show: true,
            roundCap: true,
            width: 8,     // 更细的进度条
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [{
                  offset: 0,
                  color: '#1890FF' // 使用单一主题色
                }, {
                  offset: 1,
                  color: '#1890FF'
                }]
              },
              shadowColor: 'rgba(24,144,255,0.2)',
              shadowBlur: 10,
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          },
          pointer: {
            show: false
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 8,    // 与进度条宽度一致
              color: [
                [1, '#F0F2F5'] // 更浅的背景色
              ]
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          title: {
            show: true,
            fontSize: 14,
            color: '#8C8C8C',
            fontWeight: 'normal',
            offsetCenter: [0, '60%']  // 将题移到更下方
          },
          detail: {
            show: true,
            width: 120,
            height: 40,
            fontSize: 56,    // 更大字号
            fontWeight: 'bold',
            color: '#1890FF', // 使用主色
            formatter: function(value) {
              return '{value|' + value.toFixed(0) + '}{unit|%}';
            },
            rich: {
              value: {
                fontSize: 56,
                fontWeight: 'bold',
                color: '#1890FF',
                padding: [0, 0, 0, 0],
                fontFamily: 'DIN'  // 使用字字体
              },
              unit: {
                fontSize: 28,      // 百分号字号减半
                fontWeight: 'normal',
                color: '#1890FF',
                padding: [0, 0, 20, 0]  // 向上偏移
              }
            },
            valueAnimation: true,
            offsetCenter: [0, '10%']
          },
          data: [{
            value: randomValue,
            name: '设备健康度'
          }]
        }]
      }

      this.gaugeChart.setOption(option)
    },

    handleBoxClick(row) {
      console.log('矩形框点击:', row);
      if (this.clickTimer) {
        clearTimeout(this.clickTimer);
        this.clickTimer = null;
        this.$store.dispatch('selectedRowModule/setSelectedRow', row);
        this.$router.push({
          path: '/textPage',
          query: {
            fromGeneralPicture: true
          }
        });
      } else {
        this.clickTimer = setTimeout(() => {
          console.log('矩形框点击:', row);
          this.clickTimer = null;
        }, 300);
      }
    },


    // 测量定义点击
    handleDefinitionClick(data) {
      console.log('handleDefinitionClick:测量定义点击', data)

      // 构建完整的数据对象
      const selectedRow = {
        id: data.id,
        pointId: data.pointId,
        title: data.title,
        pointTitle: data.pointTitle,
        deviceId: this.deviceId,
        deviceName: this.deviceName
      }

      // 存储选中的行数据到 Vuex
      this.$store.dispatch('selectedRowModule/setSelectedRow', selectedRow)

      // 设置要显示的图谱类型
      this.$store.dispatch('chartSwitcher/setActiveChart', 'TrendWaveformSpectrumChart')

      // 跳转到 textPage
      this.$router.push({
        path: '/textPage',
        query: {
          fromGeneralPicture: true,
          chartType: 'TrendWaveformSpectrumChart'  // 添加图表类型参数
        }
      })
    },

    // 添加一新方法用于确定报警等级
    getWarnLevelType(warnValue, measurementDefinition) {
      if (!measurementDefinition) return '警告'

      const value = parseFloat(warnValue)
      const forth = parseFloat(measurementDefinition.forthAlarmValue)
      const third = parseFloat(measurementDefinition.thirdAlarmValue)
      const second = parseFloat(measurementDefinition.secondAlarmValue)
      const first = parseFloat(measurementDefinition.firstAlarmValue)

      if (value >= forth) return '严重警告'
      if (value >= third) return '严重'
      if (value >= second) return '警告'
      if (value >= first) return '警告'
      return '警告'
    },

    triggerFileUpload() {
      this.showImageSelector = true
    },

    handleImageSelect(image) {
      // 处理图片选择后的逻辑
      console.log('Selected image:', image);
      this.setCanvasBackground(image)
    },

    setCanvasBackground(image) {
      if (!this.stage || !this.layer) {
        console.warn('Canvas not initialized')
        return
      }

      const imageObj = new Image()
      imageObj.crossOrigin = 'anonymous'
      
      imageObj.onload = () => {
        // 查找现有的背景图片并删除
        const existingImage = this.layer.findOne('Image')
        if (existingImage) {
          existingImage.destroy()
        }

        // 创建新的背景图片
        const konvaImage = new Konva.Image({
          x: 0,
          y: 0,
          image: imageObj,
          width: this.stage.width(),
          height: this.stage.height(),
          draggable: false
        })

        // 将背景图片放到最底层
        this.layer.add(konvaImage)
        konvaImage.moveToBottom()
        this.layer.batchDraw()

        // 保存背景图片信息到设备
        this.saveBackgroundImage(image)
      }

      imageObj.onerror = () => {
        console.error('Failed to load image:', image)
        this.$message.error('图片加载失败')
      }

      // 关键修复：添加图片源地址
      imageObj.src = image.imageUrl
    },

    async saveBackgroundImage(image) {
      let loading = null
      try {
        // 显示加载状态
        loading = this.$loading({
          lock: true,
          text: '正在设置背景图片...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 先检查设备是否已有图片
        const deviceImageResponse = await getDeviceImageList(this.deviceId)
        console.log('设备图片信息:', deviceImageResponse)

        // 根据是否有data字段来决定使用哪个API
        if (deviceImageResponse.data && deviceImageResponse.data.deviceImageUrl) {
          // 设备已有图片，使用UpdateLatestTime更新
          console.log('设备已有图片，使用UpdateLatestTime更新')
          await UpdateLatestTime(this.deviceId, image.imageUrl)
        } else {
          // 设备没有图片，使用NewLatestTime新增
          console.log('设备没有图片，使用NewLatestTime新增')
          await NewLatestTime(this.deviceId, image.imageUrl)
        }
        
        loading.close()
        
        console.log('设备背景图片设置成功:', {
          deviceId: this.deviceId,
          imageId: image.id,
          imageUrl: image.imageUrl
        })
        
        this.$message.success('背景图片设置成功')
      } catch (error) {
        if (loading) {
          loading.close()
        }
        console.error('保存背景图片失败:', error)
        this.$message.error('设置背景图片失败: ' + (error.message || '未知错误'))
      }
    },

    handleMatchClick() {
      if (this.$refs.objView) {
        this.$refs.objView.matchDialogVisible = true;
      }
    },

    handleUpdateTrend(data) {
      // 更新当前测量点ID和名称
      this.currentMeasureId = data.id;
      this.currentMeasureName = data.pointTitle + '-' + data.definitionName;
      // 获取趋势数据
      this.fetchTrendData(data.id);
    },

    // 添加一个获取单位的辅助函数
    getUnitText(unitName) {
      switch(unitName) {
        case "2":
          return 'm/s²';
        case "1":
          return 'mm/s';
        case "4":
          return 'm/s²';
        case "3":
          return 'μm';
        default:
          return '';
      }
    },

    // 初始化显示器信息检测
    initDisplayInfo() {
      // 获取初始显示器信息
      this.updateDisplayInfo()
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleDisplayInfoChange)
      
      // 监听屏幕方向变化
      if (window.screen.orientation) {
        window.screen.orientation.addEventListener('change', this.handleDisplayInfoChange)
      }
      
      // 使用ResizeObserver监听更精确的变化
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          this.updateDisplayInfo()
        })
        this.resizeObserver.observe(document.body)
      }
      
      // 定期检查缩放变化
      this.zoomCheckInterval = setInterval(() => {
        const currentZoom = Math.round(window.devicePixelRatio * 100)
        if (currentZoom !== this.displayInfo.zoomPercentage) {
          this.updateDisplayInfo()
        }
      }, 1000)
    },

    // 清理显示器信息监控
    cleanupDisplayInfo() {
      window.removeEventListener('resize', this.handleDisplayInfoChange)
      
      if (window.screen.orientation) {
        window.screen.orientation.removeEventListener('change', this.handleDisplayInfoChange)
      }
      
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }
      
      if (this.zoomCheckInterval) {
        clearInterval(this.zoomCheckInterval)
        this.zoomCheckInterval = null
      }
    },

    // 更新显示器信息
    updateDisplayInfo() {
      const webWidth = window.innerWidth
      const webHeight = window.innerHeight
      const screenWidth = window.screen.width
      const screenHeight = window.screen.height
      const devicePixelRatio = window.devicePixelRatio
      const zoomPercentage = Math.round(devicePixelRatio * 100)

      this.displayInfo = {
        screenWidth,
        screenHeight,
        webWidth,
        webHeight,
        devicePixelRatio,
        zoomPercentage
      }

      // 获取画布容器尺寸
      const container = this.$refs.canvasContainer;
      const containerWidth = container ? container.offsetWidth : 0;
      const containerHeight = container ? container.offsetHeight : 0;
      
      console.log('=== 显示器和容器信息 ===')
      console.log('屏幕分辨率:', `${screenWidth}x${screenHeight}`)
      console.log('网页视口:', `${webWidth}x${webHeight}`)
      console.log('画布容器:', `${containerWidth}x${containerHeight}`)
      console.log('缩放比例:', `${zoomPercentage}%`)
      
      if (containerWidth > 0 && containerHeight > 0) {
        const cloudStandard = { width: 1920, height: 1080 };
        const widthRatio = containerWidth / cloudStandard.width;
        const heightRatio = containerHeight / cloudStandard.height;
        
        console.log('容器映射分析:')
        console.log(`  云端标准容器: ${cloudStandard.width}x${cloudStandard.height}`)
        console.log(`  本地容器比例: 宽${widthRatio.toFixed(3)} 高${heightRatio.toFixed(3)}`)
        console.log(`  容器占网页比例: 宽${(containerWidth/webWidth).toFixed(3)} 高${(containerHeight/webHeight).toFixed(3)}`)
      }
      console.log('========================')
    },

    // 处理显示器信息变化
    handleDisplayInfoChange() {
      this.updateDisplayInfo()
      // 如果需要重新映射坐标，可以在这里调用
      this.remapPointLocations()
    },

    // 坐标映射函数 - 从云端坐标映射到本地坐标
    mapCoordinatesFromCloud(cloudX, cloudY, cloudDisplayInfo) {
      // 获取当前画布容器的实际尺寸
      const container = this.$refs.canvasContainer;
      if (!container) {
        console.warn('画布容器未找到，使用默认映射');
        return { x: cloudX, y: cloudY };
      }

      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      // 云端标准配置：画布容器1920x1080
      let cloudContainerWidth, cloudContainerHeight;
      
      if (cloudDisplayInfo && cloudDisplayInfo.cloudResolution) {
        cloudContainerWidth = cloudDisplayInfo.cloudResolution.containerWidth || 1920;
        cloudContainerHeight = cloudDisplayInfo.cloudResolution.containerHeight || 1080;
      } else {
        // 默认云端标准：画布容器1920x1080
        cloudContainerWidth = 1920;
        cloudContainerHeight = 1080;
      }

      // 简化映射：直接基于容器尺寸比例
      const widthRatio = containerWidth / cloudContainerWidth;
      const heightRatio = containerHeight / cloudContainerHeight;
      
      const mappedX = cloudX * widthRatio;
      const mappedY = cloudY * heightRatio;

      console.log('容器坐标映射 (云端->本地):', {
        原始云端坐标: { x: cloudX, y: cloudY },
        云端容器尺寸: `${cloudContainerWidth}x${cloudContainerHeight}`,
        本地容器尺寸: `${containerWidth}x${containerHeight}`,
        映射比例: { width: widthRatio.toFixed(3), height: heightRatio.toFixed(3) },
        映射后坐标: { x: Math.round(mappedX), y: Math.round(mappedY) }
      });

      return {
        x: Math.round(mappedX),
        y: Math.round(mappedY)
      };
    },

    // 坐标映射函数 - 从本地坐标映射到云端坐标
    mapCoordinatesToCloud(localX, localY) {
      // 获取当前画布容器的实际尺寸
      const container = this.$refs.canvasContainer;
      if (!container) {
        console.warn('画布容器未找到，使用默认映射');
        return { x: localX, y: localY };
      }

      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      // 云端标准配置：画布容器1920x1080
      const cloudContainerWidth = 1920;
      const cloudContainerHeight = 1080;

      // 简化映射：直接基于容器尺寸比例
      const widthRatio = cloudContainerWidth / containerWidth;
      const heightRatio = cloudContainerHeight / containerHeight;
      
      const mappedX = localX * widthRatio;
      const mappedY = localY * heightRatio;

      console.log('容器坐标映射 (本地->云端):', {
        原始本地坐标: { x: localX, y: localY },
        本地容器尺寸: `${containerWidth}x${containerHeight}`,
        云端容器尺寸: `${cloudContainerWidth}x${cloudContainerHeight}`,
        映射比例: { width: widthRatio.toFixed(3), height: heightRatio.toFixed(3) },
        映射后坐标: { x: Math.round(mappedX), y: Math.round(mappedY) }
      });

      return {
        x: Math.round(mappedX),
        y: Math.round(mappedY)
      };
    },

    // 重新映射所有点位置
    remapPointLocations() {
      if (!this.stage || !this.layer) return

      this.flattenedData.forEach(point => {
        if (point.pointLocation && point.pointLocation.length >= 2) {
          const box = this.layer.findOne(`#${point.id}`)
          if (box) {
            // 从云端坐标映射到当前本地坐标
            const mappedBox = this.mapCoordinatesFromCloud(
              point.pointLocation[0].locationInfo.x,
              point.pointLocation[0].locationInfo.y,
              point.cloudDisplayInfo
            )
            
            box.position({
              x: mappedBox.x,
              y: mappedBox.y
            })

            // 找到对应的终点圆点并更新位置
            const circles = this.layer.find('Circle')
            circles.forEach(circle => {
              if (circle.getAttr('pointId') === point.id) {
                const mappedPoint = this.mapCoordinatesFromCloud(
                  point.pointLocation[1].locationInfo.x,
                  point.pointLocation[1].locationInfo.y,
                  point.cloudDisplayInfo
                )
                
                circle.position({
                  x: mappedPoint.x,
                  y: mappedPoint.y
                })

                // 使用统一的更新连接线方法
                this.updateConnectionLine(point.id, box, circle)
              }
            })
          }
        }
      })

      this.layer.batchDraw()
    },

    handleDisplayInfoUpdated() {
      // 处理显示器信息更新后的逻辑
      console.log('显示器信息已更新');
      
      // 测试坐标映射的正确性
      this.testCoordinateMapping();
      
      this.remapPointLocations();
    },

    // 测试坐标映射功能
    testCoordinateMapping() {
      console.log('=== 容器坐标映射测试 ===');
      
      const container = this.$refs.canvasContainer;
      if (!container) {
        console.log('画布容器未找到，跳过测试');
        return;
      }
      
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;
      
      console.log(`当前容器尺寸: ${containerWidth}x${containerHeight}`);
      console.log(`云端标准容器: 1920x1080`);
      
      // 测试坐标点 (云端标准1920x1080容器下的坐标)
      const testCloudCoords = [
        { x: 100, y: 100, name: '左上角区域' },
        { x: 960, y: 540, name: '容器中心点' },
        { x: 1820, y: 980, name: '右下角区域' },
        { x: 480, y: 270, name: '左上四分之一' },
        { x: 1440, y: 810, name: '右下四分之三' }
      ];
      
      testCloudCoords.forEach(coord => {
        // 从云端映射到本地
        const localMapped = this.mapCoordinatesFromCloud(coord.x, coord.y);
        
        // 再从本地映射回云端
        const cloudMapped = this.mapCoordinatesToCloud(localMapped.x, localMapped.y);
        
        const errorX = Math.abs(cloudMapped.x - coord.x);
        const errorY = Math.abs(cloudMapped.y - coord.y);
        
        console.log(`${coord.name}:`, {
          原始云端: coord,
          映射到本地: localMapped,
          回映射到云端: cloudMapped,
          误差: { x: errorX, y: errorY },
          精度: errorX <= 1 && errorY <= 1 ? '✓ 精确' : '⚠ 有误差'
        });
      });
      
      console.log('==================');
    },

    // 添加新的更新连接线方法
    updateConnectionLine(pointId, box, endPoint) {
      const connectionLine = this.layer.findOne(node => 
        node.getAttr('pointId') === pointId && node.getAttr('lineType') === 'connection'
      )
      
      if (connectionLine && box && endPoint) {
        const container = box.findOne('Rect')
        const containerHeight = container ? container.height() : 40
        const boxWidth = container ? container.width() : 200
        const midPointY = (endPoint.y() + box.y() + containerHeight) / 2

        connectionLine.points([
          box.x() + boxWidth/2, box.y() + containerHeight,
          box.x() + boxWidth/2, midPointY,
          box.x() + boxWidth/2, midPointY,
          endPoint.x(), midPointY,
          endPoint.x(), endPoint.y()
        ])
      }
    }
  }
}
</script>

<style scoped>
@import "./css/GeneralPicture.css";
</style>
