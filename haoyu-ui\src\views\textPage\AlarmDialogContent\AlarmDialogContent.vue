<!-- DialogContent.vue -->
<template>
  <div class="dialog-content">
    <!-- 搜索框区域，使用 el-form 重构 -->
    <el-form :inline="true" label-position="right" label-width="80px" class="search-form">
      <!-- 搜索表单项：报警类型 -->
      <el-form-item label="报警类型">
        <el-input
          v-model="searchInput1"
          placeholder="请输入报警类型"
          class="search-input"
          clearable
        />
      </el-form-item>
      <!-- 搜索表单项：报警时间 -->
      <el-form-item label="报警时间">
        <el-input
          v-model="searchInput2"
          placeholder="请输入报警时间"
          class="search-input"
          clearable
        />
      </el-form-item>
      <!-- 搜索按钮 -->
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </el-form-item>
      <!-- 重置按钮 -->
      <el-form-item>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <el-table :data="filteredData" style="width: 100%" height="300">
      <el-table-column prop="alarmType" label="报警类型" width="180"></el-table-column>
      <el-table-column prop="alarmTime" label="报警时间" width="180"></el-table-column>
      <el-table-column prop="alarmDescription" label="描述"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchInput1: '', // 搜索框1的输入
      searchInput2: '', // 搜索框2的输入
      tableData: [
        // 示例表格数据
        { alarmType: '高温报警', alarmTime: '2023-09-01 10:00', alarmDescription: '温度超过阈值' },
        { alarmType: '震动报警', alarmTime: '2023-09-02 14:00', alarmDescription: '震动频率异常' },
        { alarmType: '电压报警', alarmTime: '2023-09-03 09:30', alarmDescription: '电压波动异常' }
      ],
      filteredData: [] // 保存经过搜索过滤的表格数据
    }
  },
  mounted() {
    // 初始化时显示全部数据
    this.filteredData = [...this.tableData]
  },
  methods: {
    // 搜索功能，点击按钮后过滤表格数据
    handleSearch() {
      this.filteredData = this.tableData.filter((item) => {
        const matchesType = item.alarmType.includes(this.searchInput1)
        const matchesTime = item.alarmTime.includes(this.searchInput2)
        return matchesType && matchesTime
      })
    },
    // 重置功能，清空搜索框并显示所有数据
    handleReset() {
      this.searchInput1 = ''
      this.searchInput2 = ''
      this.filteredData = [...this.tableData] // 恢复所有数据
    }
  }
}
</script>

<style scoped>
.dialog-content {
  padding: 20px;
}

.search-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-input {
  width: 180px;
}

.el-button {
  margin-left: 10px; /* 添加按钮间距 */
}
</style>
