<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ECharts 拖动 MarkLine 和 频率间隔调整</title>
  <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
  <style>
    #chart {
      width: 100%;
      height: 500px;
    }
  </style>
</head>
<body>
  <div id="chart"></div>

  <script>
    // 初始化图表
    const chart = echarts.init(document.getElementById('chart'));

    let initialFrequency = 5; // 初始频率间隔为5Hz
    let graphicXData = [initialFrequency]; // 第一条游标的初始位置为5Hz

    // 计算游标位置
    function calculateXData() {
      const newData = [];
      for (let i = 0; i < 10; i++) {
        newData.push(graphicXData[0] * (i + 1)); // 按倍数递增，后续游标的位置为倍数
      }
      return newData;
    }

    // 初始绘制图表
    function drawLine() {
      const option = {
        xAxis: {
          id: '1',
          type: 'value',
          min: 0,
          max: 100,
          interval: 5, // 每隔5Hz显示一个刻度
          axisLabel: {
            formatter: '{value} Hz',
          },
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            type: 'line',
            data: Array.from({ length: 100 }, () => Math.random() * 100), // 模拟Y轴数据
          },
        ],
        markLine: {
          symbol: 'none',
          lineStyle: {
            color: 'blue',
          },
          data: calculateXData().map((x) => ({ xAxis: x })), // 根据计算的数据绘制MarkLine
        },
      };

      chart.setOption(option);

      // 绘制可拖动的graphic
      refreshGraphic();
    }

    // 渲染可拖动的graphic
    function refreshGraphic() {
      const graphic = [];
      chart.setOption({ graphic: [] }); // 清除旧的graphic

      // 为每一个游标绘制可拖动的图形
      calculateXData().map((item, index) => {
        graphic.push({
          type: 'rect',
          z: 101,
          shape: {
            width: 5,  // 增大矩形的点击区域
            height: 180, // 设置矩形高度
          },
          position: [
            chart.convertToPixel({ xAxisId: '1' }, item), // 将X轴坐标转换为像素
            110,
          ],
          draggable: index === 0, // 仅第一条游标可以拖动
          style: {
            fill: 'red', // 设置填充颜色
            lineWidth: 1,
          },
          cursor: 'ew-resize', // 设置鼠标样式为左右拖动
          ondrag: (param) => {
            // 拖动时动态更新所有游标
            if (index === 0) {
              onDrag(param);
            }
          },
          ondragend: (param) => {
            // 拖动结束后更新位置
            if (index === 0) {
              onDragend(param);
            }
          },
        });
      });

      chart.setOption({ graphic }); // 更新graphic
    }

    // 拖动时更新位置
    function onDrag(param) {
      const position = param.target.x;
      const newX = chart.convertFromPixel({ xAxisId: '1' }, position);
      graphicXData[0] = newX; // 更新第一条游标的位置
      refreshGraphic(); // 动态更新位置
    }

    // 拖动结束时更新所有游标的位置，并重新绘制图表
    function onDragend(param) {
      const position = param.target.x;
      const newX = chart.convertFromPixel({ xAxisId: '1' }, position);
      graphicXData[0] = newX; // 更新第一条游标的位置
      drawLine(); // 重新绘制所有游标
    }

    // 初始绘制图表
    drawLine();

    // 监听dataZoom事件，缩放时更新graphic的位置
    chart.on('dataZoom', () => {
      refreshGraphic(); // 在缩放后刷新graphic
    });
  </script>
</body>
</html>
