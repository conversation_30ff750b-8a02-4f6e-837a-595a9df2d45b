<template>
  <div class="model-viewer-container">
    <!-- 全局加载遮罩 - 只在上传操作时显示 -->
    <div v-if="isLoading && showProgress" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
      <div v-if="showProgress" class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${uploadProgress}%` }"></div>
        </div>
        <div class="progress-text">{{ uploadProgress.toFixed(0) }}% ({{ successfulUploadsCount }}/{{ totalUploadsCount }})</div>
      </div>
    </div>

    <div class="left-panel">
      <!-- 模型列表面板 -->
      <ModelList
        :models="models"
        :selectedModelIndices="selectedModelIndices"
        :hasChanges="hasUnsavedChanges"
        :is-loading="isLoading"
        @update:selectedModelIndices="selectedModelIndices = $event"
        @name-updated="updateModelName"
        @copy-model="copyModel"
        @delete-model="deleteModel"
        @save-changes="mergeAndUploadModels_split"
      />

      <!-- 模型控制面板 -->
      <div class="control-panel">
        <ModelControls
          :color="color"
          :opacity="opacity"
          :is-loading="isLoading"
          @color-change="changeColor"
          @opacity-change="changeOpacity"
        />

        <div class="control-group">
          <div class="control-row">
            <span>模型名称:</span>
            <el-input 
              v-model="currentModelName" 
              size="mini" 
              placeholder="请输入模型名称"
              :disabled="selectedModelIndices.length === 0 || isLoading"
              @change="updateModelName"
              style="width: 150px;"
            ></el-input>
          </div>
          <div class="control-row">
            <span>缩放比例:</span>
            <el-input-number 
              v-model="scale" 
              @change="scaleModel" 
              size="mini"
              :disabled="isLoading"
            ></el-input-number>
          </div>
          <div v-if="isHaveModels" class="control-row">
            <span >背景颜色:</span>
            <el-color-picker 
              v-model="backgroundColor" 
              size="mini" 
              @active-change="changeBackgroundColor" 
              :show-alpha="true"
              :disabled="isLoading"
            ></el-color-picker>
          </div>
          <div v-if="isHaveModels" class="control-row">
            <span>背景图片:</span>
            <el-upload
              class="background-texture-upload"
              action="#"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="handleBackgroundTextureUpload"
              :disabled="isLoading"
            >
              <el-button size="mini" :disabled="isLoading">选择图片</el-button>
            </el-upload>
          </div>
          <div v-if="isHaveModels" class="control-row">
            <span>地面颜色:</span>
            <el-color-picker 
              v-model="groundColor" 
              size="mini" 
              @active-change="changeGroundColor" 
              :show-alpha="true"
              :disabled="isLoading"
            ></el-color-picker>
          </div>
          <div v-if="isHaveModels" class="control-row">
            <span>地面纹理:</span>
            <el-upload
              class="ground-texture-upload"
              action="#"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="handleGroundTextureUpload"
              :disabled="isLoading"
            >
              <el-button size="mini" :disabled="isLoading">选择图片</el-button>
            </el-upload>
          </div>
        </div>

        <div class="control-group">
          <div class="movement-controls">
            <el-button-group :disabled="isLoading">
              <el-button size="mini" @click="moveModel('left',null)" :disabled="isLoading">←</el-button>
              <el-button size="mini" @click="moveModel('right',null)" :disabled="isLoading">→</el-button>
              <el-button size="mini" @click="moveModel('up',null)" :disabled="isLoading">↑</el-button>
              <el-button size="mini" @click="moveModel('down',null)" :disabled="isLoading">↓</el-button>
            </el-button-group>
            <div class="step-control">
              <span>步长:</span>
              <el-input-number 
                v-model="inputStep" 
                :step="0.02" 
                :min="0.01" 
                :precision="2" 
                size="mini"
                :disabled="isLoading"
              ></el-input-number>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <!-- 工具栏 -->
      <Toolbar 
        :canCreate="canCreate"
        :is-loading="isLoading"
        @go-back="$emit('Goback')"
        @load-model="openDialog"
        @merge-model="mergeAndUploadModels"
        @upload-model="mergeAndUploadModels_split"
        @export-model="mergeAndUploadModels"
        @clear-model="clearModel"
        @save-as-example="saveModel"
      />

      <!-- 3D渲染器 -->
      <ModelRenderer
        ref="modelRenderer"
        :backgroundColor="backgroundColor"
        :groundColor="groundColor"
      />
    </div>

    <ModelLoaderDialog
      :canCreate.sync="canCreate"
      :dialogVisible.sync="dialogVisible"
      @model-loaded="handleModelLoaded"
    />

    <SaveModelDialog
      :dialogVisible.sync="saveModelDialogVisible"
      @confirm="handleSaveModelInfo"
    />
  </div>
</template>

<script>
import * as THREE from 'three';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { v4 as uuidv4 } from 'uuid';

// 导入组件
import ModelLoaderDialog from './Dialog/New3dModel.vue';
import ModelControls from './ModelControls.vue';
import ModelList from './components/ModelList.vue';
import ModelRenderer from './components/ModelRenderer.vue';
import Toolbar from './components/Toolbar.vue';
import SaveModelDialog from './Dialog/SaveModelDialog.vue';

// 导入服务和工具
import * as ApiService from './components/ApiService.js';
import * as ModelUtils from './components/ModelUtils.js';
import KeyboardControls from './components/KeyboardControls.js';

export default {
  name: 'ObjViewer',

  components: {
    ModelLoaderDialog,
    ModelControls,
    ModelList,
    ModelRenderer,
    Toolbar,
    SaveModelDialog
  },

  props: {
    msg: String,
    data: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      isLoading: false,
      isHaveModels: false,
      canCreate: true,
      color: '#ffffff',
      backgroundColor: '#ffffff',
      groundColor: '#ffffff',
      currentModelName: '',
      models: [],
      selectedModelIndices: [],
      ObjUrl: '',
      pageSize: 1,
      pageNum: 1,
      selectedFile: null,
      objFileUrl: null,
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      scale: 1,
      inputStep: 0.5,
      opacity: 1,
      dialogVisible: false,
      RelationId: 1,
      fileList: [],
      uploadProgress: 0,
      moveSpeed: 0.1,
      rotateSpeed: 0.5,
      keyboardController: null,
      
      // 添加变更跟踪
      hasUnsavedChanges: false,
      modelSnapshot: null,
      backgroundSnapshot: null,
      groundSnapshot: null,

      // 添加请求取消控制
      abortControllers: [],
      isDestroyed: false,
      loadingMessage: '初始化中...',
      isAllowSave: true,

      // 新增上传进度显示
      showProgress: false,
      totalUploadsCount: 0,
      successfulUploadsCount: 0,
      failedUploadsCount: 0,
      saveModelDialogVisible: false
    };
  },

  watch: {
    inputStep(newVal) {
      let parsedVal = parseFloat(newVal);
      if (!isNaN(parsedVal)) {
        this.step = parsedVal;
        if (this.keyboardController) {
          this.keyboardController.setInputStep(parsedVal);
        }
      } else {
        this.$message.error('请输入有效的数字');
      }
    },
    
    selectedModelIndices() {
      this.updateControlPanelValues();
      if (this.keyboardController) {
        this.keyboardController.setSelectedIndices(this.selectedModelIndices);
      }
    },
    
    models: {
      handler(newModels) {
        if (this.keyboardController) {
          this.keyboardController.setModels(newModels);
        }
      },
      deep: false
    },
    
    backgroundColor(newColor) {
      // 使用简单的标记变更而不是调用不存在的函数
      this.hasUnsavedChanges = this.backgroundSnapshot !== newColor;
    },
    
    groundColor(newColor) {
      // 使用简单的标记变更而不是调用不存在的函数
      this.hasUnsavedChanges = this.groundSnapshot !== newColor;
    }
  },

  mounted() {
    console.log('接收到的数据:props ==> ' + JSON.stringify(this.data));
    this.deviceId = this.data.id;
    
    // 确保初始化时不显示全屏加载遮罩
    this.isLoading = false;
    this.showProgress = false;
    
    // 延迟初始化，确保DOM已加载
    this.$nextTick(() => {
      this.loadBackgroundSettings();
      this.fetchObjList();
    });
  },

  created() {
    // 监听路由变化，取消未完成的下载
    this.$watch(
      () => this.$route.path,
      (newPath, oldPath) => {
        if (newPath !== oldPath) {
          console.log('路由变化，取消所有下载');
          this.cancelAllDownloads();
        }
      }
    );
  },

  beforeDestroy() {
    // 设置销毁标志
    this.isDestroyed = true;
    
    // 取消所有进行中的下载
    this.cancelAllDownloads();
    
    // 移除事件监听器
    if (this.$refs.modelRenderer) {
      this.$refs.modelRenderer.dispose();
    }
    
    // 确保清理所有资源
    this.cleanupResources();
  },

  methods: {
    // 取消所有下载
    cancelAllDownloads() {
      console.log(`取消 ${this.abortControllers.length} 个下载请求`);
      this.abortControllers.forEach(controller => {
        try {
          controller.abort();
        } catch (error) {
          console.error('取消下载请求失败', error);
        }
      });
      this.abortControllers = [];
    },

    // 辅助方法：检查场景变化
    checkSceneChanges() {
      if (!this.backgroundSnapshot || !this.groundSnapshot) return;
      
      const backgroundChanged = this.backgroundColor !== this.backgroundSnapshot;
      const groundChanged = this.groundColor !== this.groundSnapshot;
      
      this.hasUnsavedChanges = backgroundChanged || groundChanged;
    },

    // 辅助方法：安全关闭加载提示
    safeCloseLoading(loadingInstance) {
      if (loadingInstance && typeof loadingInstance.close === 'function') {
        try {
          loadingInstance.close();
        } catch (error) {
          console.error('关闭加载提示出错:', error);
        }
      }
    },

    // 检查初始化 - 只在需要时执行
    checkInitialize() {
      if (!this.keyboardController && this.$refs.modelRenderer) {
        this.initKeyboardController();
      }
    },

    // 简化版本的模型变更检测，只执行必要的检查
    saveModelSnapshot() {
      this.modelSnapshot = this.models.map(model => ({
        position: model.position.clone(),
        rotation: model.rotation.clone(),
        scale: model.scale.clone(),
        name: model.name
      }));
      
      this.backgroundSnapshot = this.backgroundColor;
      this.groundSnapshot = this.groundColor;
      this.hasUnsavedChanges = false;
    },

    openDialog() {
      this.dialogVisible = true;
    },

    async fetchObjList() {
      try {
        // 首先取消所有正在进行的下载
        this.cancelAllDownloads();
        
        // 只设置按钮和控件的禁用状态，不触发全屏遮罩
        this.isLoading = true;
        this.showProgress = false; // 确保不显示全屏遮罩
        let loadingInstance = null;
        
        try {
          // 使用Element UI的加载提示
          loadingInstance = this.$loading({
            lock: true,
            text: '正在加载模型...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          // 确保渲染器初始化完成
          if (!this.$refs.modelRenderer || !this.$refs.modelRenderer.scene) {
            console.log('等待渲染器初始化...');
            
            // 显式调用初始化并等待
            await new Promise((resolve) => {
              this.$nextTick(async () => {
                if (!this.$refs.modelRenderer) {
                  console.error('渲染器组件未找到');
                  resolve(false);
                  return;
                }
                
                const initialized = this.$refs.modelRenderer.initScene();
                
                if (!initialized) {
                  console.error('场景初始化失败');
                  resolve(false);
                  return;
                }
                
                // 给渲染器一些时间完全初始化
                setTimeout(() => resolve(true), 500);
              });
            });
            
            // 再次检查初始化结果
            if (!this.$refs.modelRenderer || !this.$refs.modelRenderer.scene) {
              throw new Error('渲染器初始化失败');
            }
            
            console.log('渲染器初始化完成');
          }
          
          // 如果组件已销毁，不继续加载
          if (this.isDestroyed) {
            console.log('组件已销毁，停止加载模型列表');
            this.safeCloseLoading(loadingInstance);
            return;
          }
          
          const deviceModelData = await ApiService.getDeviceModels(this.deviceId);
          
          // 检查组件是否销毁
          if (this.isDestroyed) {
            console.log('组件已销毁，停止处理模型数据');
            this.safeCloseLoading(loadingInstance);
            return;
          }
          
          if (!deviceModelData || deviceModelData.error) {
            console.log("没有关联模型，可以新建");
            this.safeCloseLoading(loadingInstance);
            this.isLoading = false;
            return;
          }
          
          this.canCreate = false;
          this.isHaveModels = true;
          
          // 获取模型ID列表，过滤掉无效值
          const modelIds = Object.keys(deviceModelData).filter(id => id !== 'error');
          
          if (modelIds.length === 0) {
            this.safeCloseLoading(loadingInstance);
            this.$message.info("没有找到关联模型");
            this.isLoading = false;
            return;
          }
          
          // 更新加载文本
          const totalModels = modelIds.length;
          loadingInstance.text = '加载模型 (0/' + totalModels + ')';
          
          try {
            // 依次加载模型而不是并行加载，避免竞态条件
            let successCount = 0;
            
            for (let i = 0; i < modelIds.length; i++) {
              // 检查组件是否已销毁
              if (this.isDestroyed) {
                console.log('组件已销毁，停止加载模型');
                break;
              }
              
              const id = modelIds[i];
              try {
                loadingInstance.text = `加载模型 (${i + 1}/${totalModels})`;
                await this.loadModelById(id);
                successCount++;
              } catch (modelError) {
                // 如果是取消错误，不显示错误信息
                if (modelError.message === '下载已取消') {
                  console.log(`模型 ${id} 下载已取消`);
                  // 如果是因为组件销毁导致的取消，中断整个循环
                  if (this.isDestroyed) {
                    break;
                  }
                } else {
                  console.error(`加载模型 ${id} 失败:`, modelError);
                }
              }
            }
            
            // 如果组件已销毁，不显示消息
            if (!this.isDestroyed) {
              if (successCount === 0) {
                this.$message.warning('未能成功加载任何模型');
              } else if (successCount === modelIds.length) {
                this.$message.success(`成功加载了所有 ${successCount} 个模型`);
              } else {
                this.$message.success(`成功加载了 ${successCount} 个模型`);
              }
            }
          } catch (innerError) {
            if (!this.isDestroyed) {
              console.error('加载模型过程中出错:', innerError);
              this.$message.error('加载模型过程中出错');
            }
          } finally {
            // 关闭加载提示
            this.safeCloseLoading(loadingInstance);
          }
          
          if (this.models.length > 0 && !this.isDestroyed) {
            // 保存初始快照
            this.saveModelSnapshot();
            
            // 确保控制器已初始化
            this.checkInitialize();
          } else if (!this.isDestroyed) {
            this.$message.info("未成功加载任何模型");
          }
        } catch (processingError) {
          if (!this.isDestroyed) {
            console.error('处理模型过程中出错:', processingError);
            this.$message.error(`处理模型失败: ${processingError.message || '未知错误'}`);
          }
          this.safeCloseLoading(loadingInstance);
        }
        
        this.isLoading = false;
      } catch(error) {
        if (!this.isDestroyed) {
          console.error('获取模型关联信息出错:', error);
          this.$message.error('获取模型信息失败');
        }
        this.isLoading = false;
      }
    },

    async handleModelLoaded(modelData) {
      try {
        if (!modelData || !modelData.models || modelData.models.length === 0) {
          this.$message.warning('没有可加载的模型数据');
          return;
        }
        
        const loadingMessage = this.$message({
          message: `正在处理 ${modelData.models.length} 个模型...`,
          duration: 0,
          type: 'info'
        });
        
        if (modelData.directTransfer) {
          // 处理直接传递的模型数据
          for (const modelInfo of modelData.models) {
            const modelGroup = new THREE.Group();
            
            // 设置基本变换
            modelGroup.name = modelInfo.name || `模型${this.models.length}`;
            if (modelInfo.position) modelGroup.position.copy(modelInfo.position);
            if (modelInfo.rotation) modelGroup.rotation.copy(modelInfo.rotation);
            if (modelInfo.scale) modelGroup.scale.copy(modelInfo.scale);
            
            // 添加所有网格
            if (modelInfo.meshes && modelInfo.meshes.length > 0) {
              for (const meshData of modelInfo.meshes) {
                if (!meshData.geometry) continue;
                
                const material = new THREE.MeshStandardMaterial({
                  color: new THREE.Color(`#${meshData.material.color}`),
                  roughness: meshData.material.roughness || 0.3,
                  metalness: meshData.material.metalness || 0.8,
                  transparent: meshData.material.opacity < 1,
                  opacity: meshData.material.opacity || 1
                });
                
                const mesh = new THREE.Mesh(meshData.geometry, material);
                mesh.name = meshData.name || `部件${modelGroup.children.length + 1}`;
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                
                modelGroup.add(mesh);
              }
            }
            
            this.$refs.modelRenderer.addModelToScene(modelGroup);
            this.models.push(modelGroup);
          }
        } else {
          // 处理普通模型数据
          for (const modelInfo of modelData.models) {
            const object = await this.$refs.modelRenderer.loadObjModel(modelInfo.url);
            const modelGroup = new THREE.Group();
            
            // 设置基本属性
            modelGroup.name = modelInfo.name || `模型${this.models.length}`;
            
            // 处理材质和几何体
            object.traverse(child => {
              if (child.isMesh) {
                const material = new THREE.MeshStandardMaterial({
                  color: new THREE.Color(modelInfo.color || 0xffffff),
                  roughness: 0.3,
                  metalness: 0.8,
                  transparent: modelInfo.opacity < 1,
                  opacity: modelInfo.opacity || 1
                });
                
                const clonedMesh = new THREE.Mesh(child.geometry, material);
                clonedMesh.castShadow = true;
                clonedMesh.receiveShadow = true;
                modelGroup.add(clonedMesh);
              }
            });
            
            // 设置变换 - 位置和旋转
            if (modelInfo.position) modelGroup.position.copy(modelInfo.position);
            if (modelInfo.rotation) modelGroup.rotation.copy(modelInfo.rotation);
            
            // 设置缩放 - 对于新加载的模型，我们仍然应用缩放值
            // 因为这些模型的几何体还没有包含缩放信息
            if (modelInfo.scale) modelGroup.scale.copy(modelInfo.scale);
            
            this.$refs.modelRenderer.addModelToScene(modelGroup);
            this.models.push(modelGroup);
          }
        }
        
        // 更新UI
        if (this.models.length > 0 && this.selectedModelIndices.length === 0) {
          this.selectedModelIndices = [this.models.length - 1];
          this.updateControlPanelValues();
        }
        
        loadingMessage.close();
        this.$message.success(`已加载 ${modelData.models.length} 个模型`);
      } catch (error) {
        console.error('处理模型数据失败:', error);
        this.$message.error('处理模型数据失败');
      }
    },

    async mergeAndUploadModels_split(event) {
      if (this.models.length === 0) {
        this.$message.error('没有可上传的模型');
        return;
      }

      this.isLoading = true;
      this.showProgress = true;
      this.uploadProgress = 0;
      this.successfulUploadsCount = 0;
      this.failedUploadsCount = 0;
      this.loadingMessage = '正在处理模型数据，准备上传...';

      try {
        const meshes = [];
        const modelInfos = [];
        
        this.models.forEach((model, index) => {
          // 确保矩阵更新是最新的
          model.updateMatrixWorld(true);
          model.traverse((child) => {
            if (child.isMesh) {
              meshes.push(child);
              
              const color = child.material.color;
              const opacity = child.material.opacity || 1;
              const rgba = `rgba(${Math.round(color.r * 255)},${Math.round(color.g * 255)},${Math.round(color.b * 255)},${opacity})`;
              
              // 保存模型当前的缩放值
              const currentScale = model.scale.x;
              
              modelInfos.push({
                name: child.name || `未命名模型${index + 1}`,
                color: rgba,
                scale: currentScale.toString(), // 保存当前缩放值，加载时将被忽略，因为几何体已包含缩放
                position: `(${model.position.x},${model.position.y},${model.position.z})`
              });
            }
          });
        });

        this.totalUploadsCount = meshes.length;
        this.loadingMessage = `正在上传模型 (0/${this.totalUploadsCount})...`;
        this.successfulUploadsCount = 0;
        this.failedUploadsCount = 0;

        // 改为顺序上传以减少并发请求数，并更新进度
        const successfulUploadIds = [];

        for (let i = 0; i < meshes.length; i++) {
          const child = meshes[i];
          const index = i;
          
          try {
            this.loadingMessage = `正在上传模型 (${i+1}/${this.totalUploadsCount})...`;
            
            const geometry = child.geometry.clone();
            if (geometry.attributes.uv) delete geometry.attributes.uv;
            if (geometry.attributes.normal) delete geometry.attributes.normal;
            
            // 应用完整的世界矩阵（包含位置、旋转和缩放）到几何体
            geometry.applyMatrix4(child.matrixWorld);
            
            const mergedMesh = new THREE.Mesh(
              geometry,
              new THREE.MeshStandardMaterial({ color: this.color })
            );
            
            // 使用原始模型的名称
            const objFileName = child.name || `模型部件_${index + 1}`;
            const objData = ModelUtils.exportToOBJ(mergedMesh);
            const fileId = await ApiService.uploadModelData(objData, objFileName);
            
            await ApiService.updateModelPositionInfo(fileId, modelInfos[index]);
            this.successfulUploadsCount++;
            successfulUploadIds.push(fileId);
            
            // 更新进度
            this.uploadProgress = (this.successfulUploadsCount / this.totalUploadsCount) * 100;
          } catch (error) {
            console.error(`上传第 ${index + 1} 个网格失败:`, error);
            this.failedUploadsCount++;
            // 更新进度，即使失败也计入总进度
            this.uploadProgress = ((this.successfulUploadsCount + this.failedUploadsCount) / this.totalUploadsCount) * 100;
          }
        }

        if (successfulUploadIds.length === 0 && meshes.length > 0) {
          this.$message.error(`所有模型 (${meshes.length}个) 上传失败。`);
          return;
        }
        
        if (successfulUploadIds.length > 0) {
          this.loadingMessage = `正在更新设备关联信息...`;
          const response = await ApiService.createOrUpdateDeviceModelRelation(
            this.data.id, 
            successfulUploadIds, 
            this.isHaveModels,
            false  // 明确标记不是示例模型
          );
          
          if (response.code === 200) {
            let successMsg = `模型上传操作完成：${this.successfulUploadsCount} 个成功`;
            if (this.failedUploadsCount > 0) {
              successMsg += `，${this.failedUploadsCount} 个失败。`;
            } else {
              successMsg += `。`;
            }
            this.$message.success(successMsg);
            this.canCreate = false;
            this.isHaveModels = true;
            
            this.saveModelSnapshot();
            this.backgroundSnapshot = this.backgroundColor;
            this.groundSnapshot = this.groundColor;
            this.hasUnsavedChanges = false;
          } else {
            throw new Error(response.msg || `更新关联失败 (成功 ${this.successfulUploadsCount}, 失败 ${this.failedUploadsCount})`);
          }
        } else if (meshes.length === 0) {
          this.$message.info('没有需要上传的网格数据。');
        }

      } catch (error) {
        console.error('上传过程错误:', error);
        let errorMsg = `上传失败: ${error.message || '未知错误'}`;
        this.$message.error(errorMsg);
      } finally {
        this.isLoading = false;
        this.showProgress = false;
      }
    },

    async mergeAndUploadModels(event) {
      if (this.models.length === 0) {
        this.$message.error('没有可合并的模型');
        return;
      }
      
      // 合并模型
      const mergedMesh = ModelUtils.mergeModels(this.models, {
        removeUV: true,
        removeNormal: true,
        color: this.color,
        opacity: this.opacity
      });
      
      if (!mergedMesh) {
        this.$message.error('合并模型失败');
        return;
      }
      
      // 导出合并后的模型
      const objData = ModelUtils.exportToOBJ(mergedMesh);
      
      if (event === 'out') {
        // 下载模型
        ModelUtils.downloadOBJ(mergedMesh, 'mergedModel.obj');
      } else {
        // 清除现有模型并加载合并后的模型
        this.clearModels();
        
        const blob = new Blob([objData], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        // 使用渲染器加载模型
        const object = await this.$refs.modelRenderer.loadObjModel(url);
        const modelGroup = new THREE.Group();
        
        object.traverse(child => {
          if (child.isMesh) {
            const material = new THREE.MeshStandardMaterial({
              color: this.color,
              roughness: 0.3,
              metalness: 0.8,
              opacity: this.opacity,
              transparent: this.opacity < 1
            });
            
            const clonedMesh = new THREE.Mesh(child.geometry, material);
            clonedMesh.castShadow = true;
            clonedMesh.receiveShadow = true;
            modelGroup.add(clonedMesh);
          }
        });
        
        this.$refs.modelRenderer.addModelToScene(modelGroup);
        this.models.push(modelGroup);
        this.selectedModelIndices = [0];
      }
    },

    async saveModel(event) {
      if (this.models.length === 0) {
        this.$message.error('没有可上传的模型');
        return;
      }
      
      // 打开对话框收集信息
      this.saveModelDialogVisible = true;
    },
    
    // 处理模型信息确认后的保存操作
    async handleSaveModelInfo(modelInfo) {
      if (this.models.length === 0) {
        this.$message.error('没有可上传的模型');
        return;
      }

      this.isLoading = true;
      this.showProgress = true;
      this.uploadProgress = 0;
      this.successfulUploadsCount = 0;
      this.failedUploadsCount = 0;
      this.loadingMessage = '正在处理模型数据，准备保存为示例...';

      try {
        const meshes = [];
        const modelInfos = [];
        
        this.models.forEach((model, index) => {
          // 确保矩阵更新是最新的
          model.updateMatrixWorld(true);
          model.traverse((child) => {
            if (child.isMesh) {
              meshes.push(child);
              
              const color = child.material.color;
              const opacity = child.material.opacity || 1;
              const rgba = `rgba(${Math.round(color.r * 255)},${Math.round(color.g * 255)},${Math.round(color.b * 255)},${opacity})`;
              
              // 保存模型当前缩放值
              const currentScale = model.scale.x;
              
              // 使用传入的模型名称或使用默认名称
              const meshName = modelInfo.name || `未命名模型${index + 1}`;
              
              const info = {
                name: child.name || meshName, // 优先使用网格自身的名称，只有当网格没有名称时才使用对话框中的名称
                color: rgba,
                scale: currentScale.toString(), // 保存的缩放值将在加载时被忽略，因为几何体已包含缩放
                position: `(${model.position.x},${model.position.y},${model.position.z})`
              };
              
              // 如果有描述信息，添加到模型信息中
              if (modelInfo.note) {
                info.description = modelInfo.note;
              }
              
              modelInfos.push(info);
            }
          });
        });

        this.totalUploadsCount = meshes.length;
        this.loadingMessage = `正在上传模型 (0/${this.totalUploadsCount})...`;
        this.successfulUploadsCount = 0;
        this.failedUploadsCount = 0;

        // 改为顺序上传以减少并发请求数，并更新进度
        const successfulUploadIds = [];

        for (let i = 0; i < meshes.length; i++) {
          const child = meshes[i];
          const index = i;
          
          try {
            this.loadingMessage = `正在上传模型 (${i+1}/${this.totalUploadsCount})...`;
            
            const geometry = child.geometry.clone();
            if (geometry.attributes.uv) delete geometry.attributes.uv;
            if (geometry.attributes.normal) delete geometry.attributes.normal;
            
            // 应用完整的世界矩阵（包含位置、旋转和缩放）到几何体
            geometry.applyMatrix4(child.matrixWorld);
            
            const mergedMesh = new THREE.Mesh(
              geometry,
              new THREE.MeshStandardMaterial({ color: this.color })
            );
            
            const objData = ModelUtils.exportToOBJ(mergedMesh);
            
            // 使用原始模型的名称
            const objFileName = child.name || `模型部件_${index + 1}`;
            const fileId = await ApiService.uploadModelData(objData, objFileName);
            
            await ApiService.updateModelPositionInfo(fileId, modelInfos[index]);
            this.successfulUploadsCount++;
            successfulUploadIds.push(fileId);
            
            // 更新进度
            this.uploadProgress = (this.successfulUploadsCount / this.totalUploadsCount) * 100;
          } catch (error) {
            console.error(`保存为示例时，第 ${index + 1} 个模型部分失败:`, error);
            this.failedUploadsCount++;
            // 更新进度，即使失败也计入总进度
            this.uploadProgress = ((this.successfulUploadsCount + this.failedUploadsCount) / this.totalUploadsCount) * 100;
          }
        }

        if (successfulUploadIds.length === 0 && meshes.length > 0) {
          this.$message.error(`所有模型 (${meshes.length}个) 保存为示例操作失败。`);
          return;
        }

        if (successfulUploadIds.length > 0) {
          this.loadingMessage = `正在创建示例模型关联...`;
          const deviceId = uuidv4(); 
          const response = await ApiService.createOrUpdateDeviceModelRelation(
            deviceId, 
            successfulUploadIds,
            false,
            true,  // 标记为示例模型
            {
              fileNote: modelInfo.name || "",
              backgroundImageUrl: modelInfo.backgroundImageUrl || ""
            }
          );
          
          if (response.code === 200) {
            // 关闭保存对话框
            this.saveModelDialogVisible = false;
            
            let successMsg = `示例模型"${modelInfo.name}"保存完成：${this.successfulUploadsCount} 个部件成功`;
            if (this.failedUploadsCount > 0) {
              successMsg += `，${this.failedUploadsCount} 个部件失败。`;
            } else {
              successMsg += `。`;
            }
            this.$message.success(successMsg);
          } else {
            throw new Error(response.msg || `创建示例关联失败 (成功 ${this.successfulUploadsCount}, 失败 ${this.failedUploadsCount})`);
          }
        } else if (meshes.length === 0) {
          this.$message.info('没有需要保存为示例的网格数据。');
        }

      } catch (error) {
        console.error('保存为示例错误:', error);
        this.$message.error(`保存为示例失败: ${error.message || '未知错误'}`);
      } finally {
        this.isLoading = false;
        this.showProgress = false;
      }
    },

    async clearModel() {
      try {
        await ApiService.deleteDeviceModelRelation(this.data.id);
        this.canCreate = true;
        
        // 清空场景中的所有模型
        this.models.forEach((model) => {
          this.$refs.modelRenderer.scene.remove(model);
          model.traverse((child) => {
            if (child.isMesh) {
              child.geometry.dispose();
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach((material) => material.dispose());
                } else {
                  child.material.dispose();
                }
              }
            }
          });
        });
        
        // 清空模型列表
        this.models = [];
        this.selectedModelIndices = [];
        this.isHaveModels = false;
        
        this.$message.success('模型已清空');
      } catch (error) {
        console.error('清空模型失败:', error);
        this.$message.error('清空模型失败');
      }
    },

    copyModel(index) {
      const originalModel = this.models[index];
      if (originalModel) {
        const clonedModel = ModelUtils.cloneModel(originalModel, {
          offset: { x: 1, y: 0, z: 0 },
          nameSuffix: ' - 副本'
        });
        
        this.$refs.modelRenderer.addModelToScene(clonedModel);
        this.models.push(clonedModel);
      }
    },

    deleteModel(index) {
      const model = this.models[index];
      if (model) {
        this.$refs.modelRenderer.scene.remove(model);
        model.traverse((child) => {
          if (child.isMesh) {
            child.geometry.dispose();
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material) => material.dispose());
              } else {
                child.material.dispose();
              }
            }
          }
        });
        
        this.models.splice(index, 1);
        
        // 更新选中索引
        this.selectedModelIndices = this.selectedModelIndices
          .filter(i => i !== index)
          .map(i => i > index ? i - 1 : i);
        
        this.updateControlPanelValues();
      }
    },

    scaleModel() {
      if (this.selectedModelIndices.length > 0) {
        this.selectedModelIndices.forEach(index => {
          const model = this.models[index];
          if (model) {
            model.scale.set(this.scale, this.scale, this.scale);
          }
        });
      }
    },

    updateModelName() {
      if (this.selectedModelIndices.length === 1) {
        const model = this.models[this.selectedModelIndices[0]];
        if (model) {
          if (!this.currentModelName || this.currentModelName.trim() === '') {
            this.currentModelName = `未命名模型${this.selectedModelIndices[0] + 1}`;
          }
          
          const newName = this.currentModelName.trim();
          model.name = newName;
          
          // 更新所有子对象的名称
          model.traverse((child) => {
            child.name = newName;
          });
        }
      }
    },

    async loadBackgroundSettings() {
      try {
        const data = await ApiService.loadBackgroundSettings(this.deviceId);
        
        // 设置背景颜色
        if (data['color']) {
          const colorArray = JSON.parse(data.color);
          const color = `rgb(${colorArray[0]},${colorArray[1]},${colorArray[2]})`;
          this.backgroundColor = color;
          this.$refs.modelRenderer.changeBackgroundColor(color);
        }
        
        // 设置地面颜色
        if (data['ground-color']) {
          const groundColorArray = JSON.parse(data['ground-color']);
          const groundColor = groundColorArray.length === 4 ? 
            `rgba(${groundColorArray[0]},${groundColorArray[1]},${groundColorArray[2]},${groundColorArray[3]})` :
            `rgb(${groundColorArray[0]},${groundColorArray[1]},${groundColorArray[2]})`;
          this.groundColor = groundColor;
          this.$refs.modelRenderer.changeGroundColor(groundColor);
        }

        // 设置背景和地面纹理
        if (data['ground-image']) {
          this.loadGroundTexture(data['ground-image']);
        }

        if (data['background-image']) {
          this.loadBackgroundTexture(data['background-image']);
        }
      } catch (error) {
        console.error('加载背景设置失败:', error);
      }
    },

    loadGroundTexture(url) {
      const loader = new THREE.TextureLoader();
      loader.setCrossOrigin('Anonymous');
      loader.load(
        url,
        (texture) => {
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.repeat.set(10, 10);
          this.$refs.modelRenderer.applyGroundTexture(texture);
        },
        undefined,
        (error) => {
          console.error('加载地面纹理失败:', error);
        }
      );
    },

    loadBackgroundTexture(url) {
      const loader = new THREE.TextureLoader();
      loader.setCrossOrigin('Anonymous');
      loader.load(
        url,
        (texture) => {
          this.$refs.modelRenderer.applyBackgroundTexture(texture);
        },
        undefined,
        (error) => {
          console.error('加载背景纹理失败:', error);
        }
      );
    },

    async loadModelById(id) {
      try {
        // 如果组件已销毁，不继续加载
        if (this.isDestroyed) {
          // console.log('组件已销毁，停止加载模型');
          throw new Error('组件已销毁');
        }
        
        // console.log(`开始加载模型 ID: ${id}`);
        
        // 检查：确保渲染器和场景已初始化
        if (!this.$refs.modelRenderer) {
          throw new Error('渲染器组件未找到');
        }
        
        if (!this.$refs.modelRenderer.scene) {
          console.log('场景未初始化，尝试初始化...');
          const initialized = this.$refs.modelRenderer.initScene();
          
          if (!initialized) {
            throw new Error('场景初始化失败');
          }
          
          // 给渲染器一些时间完全初始化
          await new Promise(resolve => setTimeout(resolve, 300));
          
          // 再次检查
          if (!this.$refs.modelRenderer.scene) {
            throw new Error('场景初始化后仍不可用');
          }
        }
        
        // 创建取消控制器
        const controller = new AbortController();
        this.abortControllers.push(controller);
        
        try {
          // 下载模型，传递信号
          // console.log(`下载模型 ID: ${id}`);
          const url = await ApiService.downloadModel(id, controller.signal);
          
          // 从控制器数组中移除
          const index = this.abortControllers.indexOf(controller);
          if (index !== -1) {
            this.abortControllers.splice(index, 1);
          }
          
          // 如果组件已销毁，不继续处理
          if (this.isDestroyed) {
            URL.revokeObjectURL(url);
            throw new Error('组件已销毁');
          }
          
          // 加载模型
          // console.log(`解析模型 ID: ${id}`);
          const object = await this.$refs.modelRenderer.loadObjModel(url);
          
          // 创建模型组
          const modelGroup = new THREE.Group();
          modelGroup.name = `模型${id}`;
          
          // 应用默认材质
          const material = new THREE.MeshStandardMaterial({
            color: 0xcccccc,
            roughness: 0.3,
            metalness: 0.8
          });
          
          let hasMeshes = false;
          
          // 处理所有网格
          object.traverse(child => {
            if (child.isMesh) {
              hasMeshes = true;
              const clonedMesh = new THREE.Mesh(child.geometry, material.clone());
              clonedMesh.castShadow = true;
              clonedMesh.receiveShadow = true;
              modelGroup.add(clonedMesh);
            }
          });
          
          // 确保有网格被添加
          if (!hasMeshes) {
            throw new Error(`模型 ${id} 不包含网格`);
          }
          
          // 添加到场景
          // console.log(`添加模型到场景 ID: ${id}`);
          this.$refs.modelRenderer.addModelToScene(modelGroup);
          this.models.push(modelGroup);
          
          // 获取模型信息
          try {
            const modelInfo = await ApiService.getModelPositionInfo(id);
            
            // 更新名称
            if (modelInfo && modelInfo.name) {
              const updatedName = modelInfo.name;
              modelGroup.name = updatedName;
              modelGroup.traverse(child => {
                child.name = updatedName;
              });
            }
            
            // 更新颜色和透明度
            if (modelInfo && modelInfo.color) {
              ModelUtils.changeModelColor(modelGroup, modelInfo.color);
            }
            
            // 更新位置
            if (modelInfo && modelInfo.position) {
              try {
                const posMatch = modelInfo.position.match(/\((-?\d+\.?\d*),(-?\d+\.?\d*),(-?\d+\.?\d*)\)/);
                if (posMatch) {
                  modelGroup.position.set(
                    parseFloat(posMatch[1]),
                    parseFloat(posMatch[2]),
                    parseFloat(posMatch[3])
                  );
                }
              } catch (e) {
                console.error('解析位置信息失败:', e);
              }
            }
            
            // 更新缩放 - 由于在保存时几何体已经应用了世界矩阵，这里我们不再应用缩放
            // 或者保持缩放为1，从而避免缩放应用两次
            if (modelInfo && modelInfo.scale) {
              // 不再设置缩放，因为几何体已经被缩放了
              console.log(`模型 ${id} 的缩放值: ${modelInfo.scale} (几何体已包含缩放)`);
            }
          } catch (error) {
            console.error('获取模型信息失败:', error);
            // 继续加载模型，只是没有额外信息
          }
          
          // 释放URL
          URL.revokeObjectURL(url);
          
          console.log(`模型 ID: ${id} 加载完成`);
          return modelGroup;
        } catch (innerError) {
          // 从控制器数组中移除
          const index = this.abortControllers.indexOf(controller);
          if (index !== -1) {
            this.abortControllers.splice(index, 1);
          }
          
          // 如果是取消错误，特殊处理
          if (innerError.name === 'AbortError') {
            console.log(`模型 ${id} 下载已取消`);
            throw new Error('下载已取消');
          }
          
          // 重新抛出其他错误
          throw innerError;
        }
      } catch (error) {
        console.error(`加载模型 ID: ${id} 失败:`, error);
        throw error;
      }
    },

    changeColor(newColor) {
      if (this.selectedModelIndices.length > 0) {
        this.color = newColor;
        
        this.selectedModelIndices.forEach(index => {
          const model = this.models[index];
          if (model) {
            ModelUtils.changeModelColor(model, newColor);
          }
        });
      }
    },

    changeOpacity(newOpacity) {
      this.opacity = Number(newOpacity);
      if (this.selectedModelIndices.length > 0) {
        this.selectedModelIndices.forEach(index => {
          const model = this.models[index];
          if (model) {
            ModelUtils.changeModelColor(model, this.color, newOpacity);
          }
        });
      }
    },

    changeBackgroundColor(color) {
      this.backgroundColor = color;
      this.$refs.modelRenderer.changeBackgroundColor(color);
      
      // 获取颜色RGB数组
      const threeColor = new THREE.Color(color);
      const colorArray = [
        Math.round(threeColor.r * 255),
        Math.round(threeColor.g * 255),
        Math.round(threeColor.b * 255)
      ];
      
      // 获取地面颜色数组
      let groundColorArray = [255, 255, 255, 1];
      if (this.groundColor) {
        if (this.groundColor.startsWith('rgba')) {
          const matches = this.groundColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
          if (matches) {
            groundColorArray = [
              parseInt(matches[1]),
              parseInt(matches[2]),
              parseInt(matches[3]),
              parseFloat(matches[4])
            ];
          }
        }
      }
      
      // 更新颜色到后端
      ApiService.updateColors(this.deviceId, colorArray, groundColorArray)
        .then(() => {
          // 更新背景快照
          this.backgroundSnapshot = color;
          this.hasUnsavedChanges = false;
        })
        .catch(error => {
          console.error('更新背景颜色失败:', error);
          this.$message.error('更新背景颜色失败');
        });
    },

    changeGroundColor(color) {
      this.groundColor = color;
      this.$refs.modelRenderer.changeGroundColor(color);
      
      // 解析颜色数组
      const colorArray = [255, 255, 255];
      if (this.backgroundColor) {
        const threeColor = new THREE.Color(this.backgroundColor);
        colorArray[0] = Math.round(threeColor.r * 255);
        colorArray[1] = Math.round(threeColor.g * 255);
        colorArray[2] = Math.round(threeColor.b * 255);
      }
      
      // 解析地面颜色
      const groundColorArray = [255, 255, 255, 1];
      if (color.startsWith('rgba')) {
        const matches = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        if (matches) {
          groundColorArray[0] = parseInt(matches[1]);
          groundColorArray[1] = parseInt(matches[2]);
          groundColorArray[2] = parseInt(matches[3]);
          groundColorArray[3] = parseFloat(matches[4]);
        }
      }
      
      // 更新颜色到后端
      ApiService.updateColors(this.deviceId, colorArray, groundColorArray)
        .then(() => {
          // 更新地面颜色快照
          this.groundSnapshot = color;
          this.hasUnsavedChanges = false;
        })
        .catch(error => {
          console.error('更新地面颜色失败:', error);
          this.$message.error('更新地面颜色失败');
        });
    },

    handleGroundTextureUpload(file) {
      ApiService.updateTexture('ground', file.raw, this.deviceId)
        .then(response => {
          if (response.code === 200) {
            this.$message.success('地面纹理上传成功');
            
            // 加载纹理
            const reader = new FileReader();
            reader.onload = (e) => {
              const textureLoader = new THREE.TextureLoader();
              textureLoader.load(e.target.result, (texture) => {
                texture.wrapS = THREE.RepeatWrapping;
                texture.wrapT = THREE.RepeatWrapping;
                texture.repeat.set(10, 10);
                this.$refs.modelRenderer.applyGroundTexture(texture);
              });
            };
            reader.readAsDataURL(file.raw);
          } else {
            this.$message.error('地面纹理上传失败');
          }
        })
        .catch(error => {
          console.error('上传失败:', error);
          this.$message.error('地面纹理上传失败');
        });

      return false;
    },

    handleBackgroundTextureUpload(file) {
      ApiService.updateTexture('background', file.raw, this.deviceId)
        .then(response => {
          if (response.code === 200) {
            this.$message.success('背景纹理上传成功');
            
            // 加载纹理
            const reader = new FileReader();
            reader.onload = (e) => {
              const textureLoader = new THREE.TextureLoader();
              textureLoader.load(e.target.result, (texture) => {
                this.$refs.modelRenderer.applyBackgroundTexture(texture);
              });
            };
            reader.readAsDataURL(file.raw);
          } else {
            this.$message.error('背景纹理上传失败');
          }
        })
        .catch(error => {
          console.error('上传失败:', error);
          this.$message.error('背景纹理上传失败');
        });

      return false;
    },

    clearModels() {
      // 从场景中移除所有模型
      this.models.forEach(model => {
        this.$refs.modelRenderer.scene.remove(model);
      });
      
      // 清空模型数组和选中索引
      this.models = [];
      this.selectedModelIndices = [];
    },

    updateControlPanelValues() {
      if (this.selectedModelIndices.length === 0) return;
      
      // 获取第一个选中模型的值作为初始值
      const firstModel = this.models[this.selectedModelIndices[0]];
      if (!firstModel) return;

      this.currentModelName = this.selectedModelIndices.length === 1 ? 
        (firstModel.name || `未命名模型${this.selectedModelIndices[0] + 1}`) : 
        `已选择 ${this.selectedModelIndices.length} 个模型`;

      // 更新旋转值
      this.rotationX = THREE.MathUtils.radToDeg(firstModel.rotation.x);
      this.rotationY = THREE.MathUtils.radToDeg(firstModel.rotation.y);
      this.rotationZ = THREE.MathUtils.radToDeg(firstModel.rotation.z);

      // 更新缩放值
      this.scale = firstModel.scale.x;

      // 更新颜色和透明度
      firstModel.traverse((child) => {
        if (child.isMesh && child.material) {
          const material = child.material;
          const color = material.color;
          this.color = `rgba(${Math.round(color.r * 255)},${Math.round(color.g * 255)},${Math.round(color.b * 255)},${material.opacity})`;
          this.opacity = material.opacity;
        }
      });
    },

    moveModel(direction, step) {
      if (this.keyboardController) {
        this.keyboardController.moveModel(direction, step);
      }
    },

    // 优化键盘控制器初始化，避免事件冲突
    initKeyboardController() {
      // 如果已经初始化过，先清理旧的控制器
      if (this.keyboardController) {
        this.keyboardController.cleanup();
      }
      
      this.keyboardController = new KeyboardControls({
        moveSpeed: this.moveSpeed,
        rotateSpeed: this.rotateSpeed,
        inputStep: this.inputStep
      });
      this.keyboardController.setModels(this.models);
      this.keyboardController.setSelectedIndices(this.selectedModelIndices);
      
      // 注册一个简单的事件处理程序，使用函数引用而非内联函数
      this.$refs.modelRenderer.$off('before-render', this.handleBeforeRender);
      this.$refs.modelRenderer.$on('before-render', this.handleBeforeRender);
    },
    
    // 将处理程序提取为单独的方法
    handleBeforeRender() {
      // 检查是否有键盘控制器
      if (!this.keyboardController) return;
      
      // 检查模型移动
      const hasMovement = this.keyboardController.updateModelMovement();
      if (hasMovement) {
        // 使用防抖处理UI更新，只在最后一次移动后更新
        if (this._uiUpdateTimer) clearTimeout(this._uiUpdateTimer);
        this._uiUpdateTimer = setTimeout(() => {
          this.updateControlPanelValues();
          this.hasUnsavedChanges = true;
        }, 300);
      }
    },

    cleanupResources() {
      // 清理资源和事件监听
      if (this.keyboardController) {
        this.keyboardController.cleanup();
        this.keyboardController = null;
      }
      
      // 清理模型资源
      this.models.forEach(model => {
        if (model) {
          model.traverse(child => {
            if (child.isMesh) {
              if (child.geometry) child.geometry.dispose();
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach(material => material.dispose());
                } else {
                  child.material.dispose();
                }
              }
            }
          });
        }
      });
      
      this.models = [];
    }
  }
};
</script>

<style scoped>
.model-viewer-container {
  display: flex;
  height: 100vh;
  padding: 20px;
  gap: 20px;
  background: #f5f7fa;
}

.left-panel {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-panel {
  background: white;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.control-group {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.control-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.movement-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.step-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 全屏加载遮罩层样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: #fff;
  font-size: 16px;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #1989fa;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.progress-container {
  width: 80%;
  max-width: 400px;
  margin-top: 20px;
}

.progress-bar {
  height: 20px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #1989fa;
  transition: width 0.3s;
}

.progress-text {
  text-align: center;
  font-size: 14px;
  color: #fff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
