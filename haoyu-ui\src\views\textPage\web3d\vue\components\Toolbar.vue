<template>
  <div class="toolbar">
    <el-button-group>
      <el-button size="small" @click="$emit('go-back')" :disabled="isLoading">返回</el-button>
      <el-button size="small" @click="$emit('load-model')" :disabled="isLoading">载入模型</el-button>
      <!-- <el-button size="small" @click="$emit('merge-model', 'no')" v-show="!canCreate" :disabled="isLoading">拼合模型</el-button> -->
      <el-button size="small" @click="$emit('upload-model', 'split')" v-show="!canCreate" :disabled="isLoading">上传模型</el-button>
      <!-- <el-button size="small" @click="$emit('export-model', 'out')" v-show="!canCreate" :disabled="isLoading">导出模型</el-button> -->
      <!-- <el-button size="small" @click="$emit('clear-model')" v-show="!canCreate" :disabled="isLoading">清空模型</el-button> -->
      <el-button size="small" @click="$emit('save-as-example', 'example')" v-show="!canCreate" :disabled="isLoading">上传为示例</el-button>
    </el-button-group>

    <el-popover
      placement="bottom"
      width="300"
      trigger="hover">
      <div class="keyboard-tips">
        <h4>快捷键说明：</h4>
        <p>移动控制：</p>
        <ul>
          <li>W/↑: 向上移动</li>
          <li>S/↓: 向下移动</li>
          <li>A/←: 向左移动</li>
          <li>D/→: 向右移动</li>
          <li>Q: 向前移动</li>
          <li>E: 向后移动</li>
        </ul>
        <p>旋转控制：</p>
        <ul>
          <li>R/F: X轴旋转</li>
          <li>T/G: Y轴旋转</li>
          <li>Y/H: Z轴旋转</li>
        </ul>
      </div>
      <el-button slot="reference" size="small" type="text" :disabled="isLoading">
        查看快捷键说明
      </el-button>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'Toolbar',
  
  props: {
    canCreate: {
      type: Boolean,
      default: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped>
.toolbar {
  background: white;
  padding: 12px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  position: relative;
  top: -10px; /* 向上移动10像素 */
  margin-bottom: 0; /* 避免压缩3D空间 */
}

.keyboard-tips {
  font-size: 14px;
}

.keyboard-tips h4 {
  margin-top: 0;
  margin-bottom: 12px;
}

.keyboard-tips ul {
  padding-left: 20px;
  margin: 8px 0;
}

.keyboard-tips li {
  margin: 4px 0;
}
</style> 