import {
    UploadFile,
    GetImgObjList,
    UpdateBackgroundColor,
    GetBackgroundPhoto,
    DownloadImgObjList,
    DeleteItem,
    ChunkFile,
    MergeFile,
    UpLoadRelationImformation,
    DeleteDeviceRelation,
    CreateRelationImformation,
    GetRelationList,
    GetModelRelationListByDeviceId,
    UpdateDeviceRelation,
    GetOrNewDateDeviceRelation,
    getModelPosition,
    UpdateModelPosition,
    UpdateBackground,

} from '@/api/haoyu-system/web3d_api/web_3dapi.js';

import { chunkAndMergeFile } from '../../js/ChunkAndMergeFile.js';

/**
 * 获取设备关联的3D模型
 * @param {String} deviceId 设备ID
 * @returns {Promise} 模型关联信息
 */
export async function getDeviceModels(deviceId) {
    try {
        const response = await GetOrNewDateDeviceRelation(deviceId);
        return response.data;
    } catch (error) {
        console.error('获取设备关联模型失败:', error);
        throw error;
    }
}

/**
 * 下载并加载模型
 * @param {String} modelId 模型ID
 * @param {AbortSignal} signal 取消信号，用于中断下载
 * @returns {Promise<String>} 返回模型的ObjectURL
 */
export async function downloadModel(modelId, signal) {
    try {
        const response = await DownloadImgObjList(modelId, signal);
        const blob = new Blob([response], { type: 'model/obj' });
        const url = URL.createObjectURL(blob);
        return url;
    } catch (error) {
        console.error(`下载模型 ID: ${modelId} 失败:`, error);
        throw error;
    }
}

/**
 * 获取模型位置信息
 * @param {String} modelId 模型ID
 * @returns {Promise} 模型位置信息
 */
export async function getModelPositionInfo(modelId) {
    try {
        const response = await getModelPosition(modelId);
        return response.rows[0].fileModelInfo[0];
    } catch (error) {
        console.error('获取模型位置失败:', error);
        throw error;
    }
}

/**
 * 更新模型位置信息
 * @param {String} modelId 模型ID
 * @param {Object} modelInfo 模型信息
 * @returns {Promise} 更新结果
 */
export async function updateModelPositionInfo(modelId, modelInfo) {
    try {
        return await UpdateModelPosition({
            id: modelId,
            fileModelInfo: [modelInfo]
        });
    } catch (error) {
        console.error('更新模型位置失败:', error);
        throw error;
    }
}

/**
 * 上传模型数据
 * @param {String} objData 模型OBJ格式数据
 * @param {String} modelName 模型名称
 * @returns {Promise<String>} 返回上传后的模型ID
 */
export async function uploadModelData(objData, modelName) {
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
        try {
            return await chunkAndMergeFile(objData, modelName);
        } catch (error) {
            attempt++;
            console.error(`上传模型数据失败 (尝试 ${attempt}/${maxRetries}):`, error);

            if (attempt >= maxRetries) {
                throw error;
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
}

/**
 * 上传模型数据（包含附加信息）
 * @param {String} objData 模型OBJ格式数据
 * @param {String} modelName 模型名称
 * @returns {Promise<String>} 返回上传后的模型ID
 */
export async function uploadModelDataWithInfo(objData, modelName) {
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
        try {
            // 确保使用正确的文件名
            const fileName = modelName || 'model.obj';

            return await chunkAndMergeFile(objData, fileName);
        } catch (error) {
            attempt++;
            console.error(`上传模型数据失败 (尝试 ${attempt}/${maxRetries}):`, error);

            if (attempt >= maxRetries) {
                throw error;
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
}

/**
 * 创建或更新设备与模型的关联
 * @param {String} deviceId 设备ID
 * @param {Array<String>} modelIds 模型ID数组
 * @param {Boolean} isUpdate 是否是更新操作
 * @param {Boolean} isExample 是否是保存为示例模型
 * @param {Object} extraInfo 额外信息，用于示例模型
 * @returns {Promise} 关联结果
 */
export async function createOrUpdateDeviceModelRelation(deviceId, modelIds, isUpdate = false, isExample = false, extraInfo = {}) {
    try {
        const formattedInput = modelIds.join(",");
        const params = {
            deviceId: deviceId,
            fileRelation: formattedInput
        };

        // 如果是示例模型，添加额外参数
        if (isExample) {
            params.fileNote = extraInfo.fileNote || "";
            params.backgroundImageUrl = extraInfo.backgroundImageUrl || "";
        }

        if (isUpdate) {
            return await UpLoadRelationImformation(params);
        } else {
            return await CreateRelationImformation(params);
        }
    } catch (error) {
        console.error('创建/更新设备模型关联失败:', error);
        throw error;
    }
}

/**
 * 删除设备模型关联
 * @param {String} deviceId 设备ID
 * @returns {Promise} 删除结果
 */
export async function deleteDeviceModelRelation(deviceId) {
    try {
        return await DeleteDeviceRelation(deviceId);
    } catch (error) {
        console.error('删除设备模型关联失败:', error);
        throw error;
    }
}

/**
 * 更新背景颜色和地面颜色
 * @param {String} deviceId 设备ID
 * @param {Array} backgroundColor 背景颜色RGB数组
 * @param {Array} groundColor 地面颜色RGBA数组
 * @returns {Promise} 更新结果
 */
export async function updateColors(deviceId, backgroundColor, groundColor) {
    try {
        const params = {
            deviceId: deviceId,
            colorNum: JSON.stringify(backgroundColor),
            groundColor: JSON.stringify(groundColor)
        };
        return await UpdateBackgroundColor(params);
    } catch (error) {
        console.error('更新颜色失败:', error);
        throw error;
    }
}

/**
 * 更新背景或地面纹理
 * @param {String} type 类型，'background'或'ground'
 * @param {File} file 图片文件
 * @param {String} deviceId 设备ID
 * @returns {Promise} 更新结果
 */
export async function updateTexture(type, file, deviceId) {
    try {
        return await UpdateBackground(type, file, deviceId);
    } catch (error) {
        console.error(`更新${type}纹理失败:`, error);
        throw error;
    }
}

/**
 * 加载背景设置
 * @param {String} deviceId 设备ID
 * @returns {Promise} 背景设置
 */
export async function loadBackgroundSettings(deviceId) {
    try {
        const response = await GetBackgroundPhoto(deviceId);
        return response.data;
    } catch (error) {
        console.error('加载背景设置失败:', error);
        throw error;
    }
}