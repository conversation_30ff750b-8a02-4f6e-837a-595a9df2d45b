<template>
  <div ref="loadingContainer" class="loading-container">
    <h3 class="Point-title">测量定义</h3>

    <el-row :gutter="20" class="main-row">
      <!-- 左边盒子 -->
      <el-col :span="12" class="full-height">
        <el-row :gutter="10" class="left-col full-height">
          <!-- 上容器 -->
          <el-col :span="24" class="half-height">
            <div class="form-box box-shadow">
              <h4 class="form-box-title">基本配置</h4>
              <el-form ref="form" :model="formData" class="small-form">
                <el-row :gutter="10" class="form-row">
                  <!-- 名称 -->
                  <el-col :span="8" class="form-label">
                    <label>名称：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.measureDefineName" placeholder="请输入名称" class="mini-input" size="mini" @input="handleInput" />
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 时间信号单位 -->
                  <el-col :span="8" class="form-label">
                    <label>时间信号单位：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select
                      v-model.number="formData.timeSignalUnitId"
                      value-key="value"
                      placeholder="请选择单位"
                      class="mini-input"
                      size="mini"
                      :disabled="formData.measureDefineName === '包络'"
                    >
                      <el-option
                        v-for="option in timeSignalUnitOptions"
                        :key="option.value"
                        :label="formData.measureDefineName === '包络' ? formData.timeSignalUnitName : option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 频谱单位 -->
                  <el-col :span="8" class="form-label">
                    <label>频谱单位：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select
                      v-model.number="formData.spectrumUnitId"
                      value-key="value"
                      placeholder="请选择单位"
                      class="mini-input"
                      size="mini"
                      :disabled="formData.measureDefineName === '包络'"
                    >
                      <el-option
                        v-for="option in spectrumUnitOptions"
                        :key="option.value"
                        :label="formData.measureDefineName === '包络' ? formData.spectrumUnitName : option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>

          <!-- 下容器 -->
          <el-col :span="24" class="half-height">
            <div class="form-box box-shadow">
              <h4 class="form-box-title">设置</h4>
              <el-form ref="form" :model="formData" class="small-form">
                <el-row :gutter="10" class="form-row">
                  <!-- 下限频率HZ -->
                  <el-col :span="8" class="form-label">
                    <label>下限频率HZ：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input
                    v-model="formData.lowerLimitFrequency"
                    placeholder="请输入下限频率"
                    class="mini-input"
                    size="mini"
                    @input="handleInput"
                    :min="0"
                    />
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <el-col :span="8" class="form-label">
                    <label>上限频率HZ：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select
                      v-model="formData.upperLimitFrequency"
                      placeholder="请选择上限频率"
                      class="mini-input"
                      size="mini"
                      @change="handleUpperFrequencyChange"
                    >
                      <el-option
                        v-for="option in upperLimitFrequencyOptions"
                        :key="option"
                        :label="option"
                        :value="option"
                      />
                    </el-select>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 窗函数 -->
                  <el-col :span="8" class="form-label">
                    <label>窗函数：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select v-model="formData.windowsFunctionId" placeholder="请选择窗函数" class="mini-input" size="mini">
                      <el-option v-for="option in windowFunctionOptions" :key="option.value" :label="option.label" :value="option.value" />
                    </el-select>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 谱线数 -->
                  <el-col :span="8" class="form-label">
                    <label>谱线数：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select v-model="formData.numberOfSpectralLines" placeholder="请选择谱线数" class="mini-input" size="mini" @input="handleInput">
                      <el-option
                        v-for="option in SpectralLineCountOptions"
                        :key="option.id"
                        :label="option.linesNum"
                        :value="option.linesNum"
                      />
                    </el-select>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 包络频率 -->
                  <el-col :span="8" class="form-label">
                    <label>带宽：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select
                      v-model="formData.envelopeFrequency"
                      placeholder="请选择包络频率"
                      class="mini-input"
                      size="mini"
                      :disabled="formData.spectrumUnitId !== 4"
                    >
                      <el-option
                        v-for="option in envelopeFrequencyOptions"
                        :key="option.id"
                        :label="option.envelopeFrequency"
                        :value="option.id"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>
        </el-row>
      </el-col>

      <!-- 右边盒子 -->
      <el-col :span="12" class="full-height">
        <el-row :gutter="10" class="right-col full-height">
          <!-- 上容器 -->
          <el-col :span="24" class="half-height">
            <div class="form-box box-shadow">
              <h4 class="form-box-title">高级配置</h4>
              <el-form ref="form" :model="formData" class="small-form">
                <el-row :gutter="10" class="form-row">
                  <!-- 快速傅里叶变化类型 -->
                  <el-col :span="8" class="form-label">
                    <label>快速傅里叶变化类型：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select
                      v-model="formData.fastFourierTransformTypeId"
                      placeholder="请选择FFT类型"
                      class="mini-input"
                      size="mini"
                      @change="handleFFTTypeChange"
                    >
                      <el-option
                        v-for="option in fftTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 平均类型 -->
                  <el-col :span="8" class="form-label">
                    <label>平均类型：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.averageType" placeholder="请输入平均类型" class="mini-input" size="mini" :disabled="isDisabled" />
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 平均次数 -->
                  <el-col :span="8" class="form-label">
                    <label>平均次数：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.averageValue" placeholder="请输入平均次数" class="mini-input" size="mini" :disabled="isDisabled" />
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 数据重叠率 -->
                  <el-col :span="8" class="form-label">
                    <label>数据重叠率：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-select
                      v-model="formData.dataOverlapRate"
                      placeholder="请选择数据重叠率"
                      class="mini-input"
                      size="mini"
                      :disabled="isDisabled"
                    >
                      <el-option
                        v-for="option in dataOverlapRateOptions"
                        :key="option.id"
                        :label="option.dataOverlapRate"
                        :value="option.id"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>

          <!-- 下容器 -->
          <el-col :span="24" class="half-height">
            <div class="form-box box-shadow">
              <h4 class="form-box-title">报警警戒值</h4>
              <el-form ref="form" :model="formData" class="small-form">
                <el-row :gutter="10" class="form-row">
                  <!-- 一级报警值 -->
                  <el-col :span="8" class="form-label">
                    <label>一级报警值：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.firstAlarmValue" placeholder="请输入一级报警值" class="mini-input" size="mini" :disabled="true"/>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 二级报警值 -->
                  <el-col :span="8" class="form-label">
                    <label>二级报警值：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.secondAlarmValue" placeholder="请输入二级报警值" class="mini-input" size="mini" :disabled="true" />
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 三级报警值 -->
                  <el-col :span="8" class="form-label">
                    <label>三级报警值：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.thirdAlarmValue" placeholder="请输入三级报警值" class="mini-input" size="mini" :disabled="true"/>
                  </el-col>
                </el-row>

                <el-row :gutter="10" class="form-row">
                  <!-- 四级报警值 -->
                  <el-col :span="8" class="form-label">
                    <label>四级报警值：</label>
                  </el-col>
                  <el-col :span="16">
                    <el-input v-model="formData.forthAlarmValue" placeholder="请输入四级报警值" class="mini-input" size="mini" :disabled="true"/>
                  </el-col>
                </el-row>

              </el-form>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <!-- 添加自定义遮罩层模板 -->
    <div v-if="isLoading" class="custom-loading-mask">
      <div class="loading-content">
        <i class="el-icon-loading"></i>
        <p class="loading-text">请选择测量定义</p>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getFastFourierType,
  getFrequencyUnit,
  getTimeSignalUnit,
  getWindowFunction,
  getSpectralLineCount,
  getEnvelopeFrequency,
  getDataOverlapRate
} from '@/api/haoyu-system/SystemAdministration/SystemAdministration.js'

export default {
  name: 'VibrationForm',
  props: {
    formData: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    return {
      isDisabled: false,
      upperLimitFrequencyOptions: [800, 1000, 2000, 5000, 10000], // 添加上限频率选项
      dataOverlapRateOptions: [],
      envelopeFrequencyOptions: [], // 带宽的数组
      fftTypeOptions: [], // FFT类型选项
      timeSignalUnitOptions: [], // 时间信号单位选项
      spectrumUnitOptions: [], // 频谱单位选项
      windowFunctionOptions: [], // 窗函数选项
      SpectralLineCountOptions: [], // 谱线数选项
      loadingInstance: null, // 用于保存加载实例
      loadingTimeout: null, // 用于存储关闭时的 timeout
      isLoading: false // 标识是否正在加载
    }
  },
  watch: {
    // 监听 formData 的变化
    formData: {
      handler(newVal, oldVal) {
        this.handleFFTTypeChange(newVal.fastFourierTransformTypeId)
        this.upperchange()
        // 记录表单数据变化
        this.logFormData()
      },
      deep: true
    },
    'formData.measureDefineName': {
      handler(newVal) {
        if (newVal === '包络') {
          // 设置实际值为4
          this.formData.timeSignalUnitId = 4
          this.formData.spectrumUnitId = 4
          this.formData.timeSignalUnitName = '包络'
          this.formData.spectrumUnitName = '包络'
          // 强制更新选项的显示
          this.$forceUpdate()
        }
      },
      immediate: true
    }
  },
  created() {
    // 获取所有下拉框数据
    this.getAllOptions()
    this.handleFFTTypeChange(this.formData.fastFourierTransformTypeId)
    setTimeout(() => {
      this.upperchange(this.formData.upperLimitFrequency)
    }, 1000)
  },
  mounted() {
    // 启动遮罩层
    this.startLoading()
    this.$on('trigger-start-loading', this.startLoading)
    this.$on('trigger-stop-loading', this.stopLoading)
  },
  methods: {
    upperchange() {
      this.SpectralLineCountOptions = []
      for(let i = 0; (400 * (2 ** i)) <= (1.6 * (this.formData.upperLimitFrequency)); i++) {
        this.SpectralLineCountOptions.push({
          id: i,
          linesNum: 400 * (2 ** i)
        })
      }

      // 检查并确保当前选中的谱线数在选项范围内
      if (this.formData.numberOfSpectralLines) {
        // 确保是数字类型
        this.formData.numberOfSpectralLines = Number(this.formData.numberOfSpectralLines)

        // 检查是否在新的选项列表中
        const exists = this.SpectralLineCountOptions.some(option =>
          option.linesNum === this.formData.numberOfSpectralLines
        )

        // 如果不存在，则设置为最接近的值
        if (!exists && this.SpectralLineCountOptions.length > 0) {
          const closest = this.SpectralLineCountOptions.reduce((prev, curr) => {
            return (Math.abs(curr.linesNum - this.formData.numberOfSpectralLines) <
                    Math.abs(prev.linesNum - this.formData.numberOfSpectralLines))
                  ? curr : prev
          })

          this.formData.numberOfSpectralLines = closest.linesNum

          // 通知父组件
          this.$emit('update', { ...this.formData })

          console.log('已更新谱线数为:', this.formData.numberOfSpectralLines)
        }
      }
    },

    // 打印表单数据
    logFormData() {
      console.log('当前测量定义表单数据:', JSON.stringify({
        measureDefineName: this.formData.measureDefineName,
        timeSignalUnitId: this.formData.timeSignalUnitId,
        spectrumUnitId: this.formData.spectrumUnitId,
        upperLimitFrequency: this.formData.upperLimitFrequency,
        lowerLimitFrequency: this.formData.lowerLimitFrequency,
        windowsFunctionId: this.formData.windowsFunctionId,
        numberOfSpectralLines: this.formData.numberOfSpectralLines,
        envelopeFrequency: this.formData.envelopeFrequency,
        fastFourierTransformTypeId: this.formData.fastFourierTransformTypeId,
        averageType: this.formData.averageType || '',
        averageValue: this.formData.averageValue || '',
        dataOverlapRate: this.formData.dataOverlapRate || '',
        firstAlarmValue: this.formData.firstAlarmValue || '',
        secondAlarmValue: this.formData.secondAlarmValue || '',
        thirdAlarmValue: this.formData.thirdAlarmValue || '',
        forthAlarmValue: this.formData.forthAlarmValue || ''
      }, null, 2))
    },

    handleInput() {
      this.upperchange()
      this.logFormData() // 添加打印表单数据

      // 发送更新事件通知父组件表单数据已更改
      this.$emit('update', { ...this.formData })
    },
    // 编写当选择变化时需要触发的逻辑
    handleFFTTypeChange(value) {
      // console.log('选中的 FFT 类型 ID:', value)
      if (value === 0 || value === 1) {
        this.isDisabled = true
        this.formData.averageType = ''
        this.formData.averageValue = ''
        this.formData.dataOverlapRate = ''
        // console.log(this.formData.dataOverlapRate)
      } else {
        this.isDisabled = false
      }
    },
    // 启动加载遮罩层
    startLoading() {
      if (this.isLoading) return

      if (this.loadingTimeout) {
        clearTimeout(this.loadingTimeout)
        this.loadingTimeout = null
      }

      // 直接设置状态,不再使用 element-ui 的 loading
      this.isLoading = true
    },

    // 关闭加载遮罩层
    stopLoading() {
      this.isLoading = false

      this.loadingTimeout = setTimeout(() => {
        this.isLoading = false
      }, 300)
    },

    // 获取所有下拉框数据
    getAllOptions() {
      // console.log(this.formData)
      getFastFourierType().then(res => {
        this.fftTypeOptions = res.rows.map(item => ({
          label: item.fastFourierTransformTypeName,
          value: item.id
        }))
      })
      getFrequencyUnit().then(res => {
        this.spectrumUnitOptions = res.rows.map(item => ({
          label: item.spectrumUnitName,
          value: item.id,
          displayLabel: item.id === 4 && this.formData.measureDefineName === '包络' ? '加速度' : item.spectrumUnitName
        }))
      })
      getTimeSignalUnit().then(res => {
        this.timeSignalUnitOptions = res.rows.map(item => ({
          label: item.timeSignalUnitName,
          value: item.id,
          displayLabel: item.id === 4 && this.formData.measureDefineName === '包络' ? '加速度' : item.timeSignalUnitName
        }))
      })
      getWindowFunction().then(res => {
        this.windowFunctionOptions = res.rows.map(item => ({
          label: item.windowFunctionName,
          value: item.id
        }))
      })
      getEnvelopeFrequency().then(res => {
        this.envelopeFrequencyOptions = res.rows
      })
      getDataOverlapRate().then(res => {
        this.dataOverlapRateOptions = res.rows
      })
    },
    handleUpperFrequencyChange(value) {
      this.formData.upperLimitFrequency = value;
      this.upperchange();

      // 如果谱线数改变，确保转为数字类型
      if (this.formData.numberOfSpectralLines) {
        this.formData.numberOfSpectralLines = Number(this.formData.numberOfSpectralLines)
      }

      this.logFormData(); // 记录表单数据
      // 发送更新事件通知父组件表单数据已更改
      this.$emit('update', { ...this.formData });
    }
  },
  computed: {
    displayTimeSignalUnit() {
      if (this.formData.measureDefineName === '包络') {
        return this.timeSignalUnitOptions.find(option => option.value === 2)?.label || ''
      }
      return this.timeSignalUnitOptions.find(option => option.value === this.formData.timeSignalUnitId)?.label || ''
    },
    displaySpectrumUnit() {
      if (this.formData.measureDefineName === '包络') {
        return this.spectrumUnitOptions.find(option => option.value === 2)?.label || ''
      }
      return this.spectrumUnitOptions.find(option => option.value === this.formData.spectrumUnitId)?.label || ''
    },
    // 获取显示的标签
    getDisplayLabel() {
      if (this.formData.measureDefineName === '包络') {
        // 找到加速度对应的选项
        const accelerationOption = this.timeSignalUnitOptions.find(option => option.value === 2)
        return accelerationOption ? accelerationOption.label : ''
      }
      return ''
    }
  }
}
</script>

<style scoped>
/* 样式代码保持不变 */
.Point-title {
  margin-bottom: 20px;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.main-row {
  height: 94%;  /* 总容器高度 */
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.full-height {
  height: 100%;
}

.left-col, .right-col {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 上下容器之间的间距 */
}

.half-height {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 确保内容从顶部开始 */
}

.form-box {
  background-color: #ffffff; /* 使用白色背景 */
  padding: 20px;
  border-radius: 8px; /* 圆角边框 */
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.box-shadow {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增加更明显的阴影效果 */
  border: 1px solid #e0e0e0; /* 增加浅色边框 */
}

.form-box-title {
  font-size: 14px; /* 稍微增大字体 */
  font-weight: bold;
  color: #666; /* 使用浅灰色字体 */
  margin-bottom: 15px; /* 增加与表单内容之间的间距 */
  text-align: left;
  border-left: 4px solid #007bff; /* 在标题左侧添加颜色条 */
  padding-left: 10px; /* 标题内间距 */
}

.el-col {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  font-weight: bold;
  color: #333;
  font-size: 12px; /* 调整标签字体稍大一些 */
}

.mini-input .el-input__inner,
.mini-input .el-select .el-input__inner {
  background-color: #f5f5f5; /* 更柔和的背景色 */
  border-radius: 4px;
  border: 1px solid #ccc;
  font-size: 10px; /* 非常小的字体 */
  height: 22px; /* 调整输入框高度 */
  padding: 0 4px; /* 调整内间距 */
}

.el-input__inner:focus, .el-select .el-input__inner:focus {
  border-color: #007bff; /* 焦点状态下的边框颜色 */
}

.form-row {
  margin-bottom: 8px; /* 为每个输入添加下间距 */
}

/* 添加新的样式 */
.loading-container {
  position: relative;
  min-height: 200px;
}

.custom-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: all 0.3s ease-in-out;
}

.loading-content {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loading-content i {
  font-size: 30px;
  color: #fff;
  animation: rotate 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  color: #fff;
  font-size: 14px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
