<!-- 趋势波形频谱 -->
<template>
  <div class="wrapper">
    <svg-icon icon-class="value"></svg-icon>
    <!-- 绑定 ref 用于图表初始化 -->
    <div ref="trendChartContainer" class="chart-item"></div>
    <div ref="spectrumChartContainer" class="chart-item"></div>
    <div ref="waveformChartContainer" class="chart-item"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      trendChart: null, // 趋势图实例
      spectrumChart: null, // 频谱图实例
      waveformChart: null, // 波形图实例
      showSymbols: [false, false, false], // 控制每个图表的 showSymbol 状态
      lineStyles: ['solid', 'dashed', 'dotted'], // 定义折线样式
      currentLineStyle: [0, 0, 0], // 保存当前每个图表的折线样式索引
      showGrids: [true, true, true], // 控制每个图表是否显示网格
      isFullscreen: false, // 是否为全屏模式
      fullscreenIndex: null, // 当前放大的图表索引
      alertValues: { // 警戒值
        safe: 600,
        blueAlert: 800,
        yellowAlert: 1000,
        redAlert: 1200
      },
      showDialog: false, // 控制弹窗的显示
      tempAlertValues: { // 用于临时存储用户输入的警戒值
        safe: 600,
        blueAlert: 800,
        yellowAlert: 1000,
        redAlert: 1200
      }
    }
  },
  mounted() {
    this.initCharts() // 初始化图表
    window.addEventListener('resize', this.resizeCharts) // 监听窗口大小变化，调整图表大小
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts) // 组件销毁时移除监听器
    this.disposeCharts() // 销毁图表实例
  },
  methods: {
    // 更新警戒值并关闭弹窗
    submitAlertValues() {
      this.alertValues = { ...this.tempAlertValues } // 将用户输入的值应用到警戒值
      this.updateChart(0) // 更新趋势图
      this.showDialog = false // 关闭弹窗
    },
    // 更新警戒值
    updateAlertValues() {
      this.alertValues.safe = prompt('请输入新的安全警戒值', this.alertValues.safe)
      this.alertValues.blueAlert = prompt('请输入新的蓝色警戒值', this.alertValues.blueAlert)
      this.alertValues.yellowAlert = prompt('请输入新的黄色警戒值', this.alertValues.yellowAlert)
      this.alertValues.redAlert = prompt('请输入新的红色警戒值', this.alertValues.redAlert)
      this.updateChart(0) // 更新趋势图
    },

    submitSpeed() {
      // 处理提交转速的逻辑，例如保存到数据库或进行验证
      alert(`提交的转速值是: ${this.speed}`)
      // 清空输入框
      this.speed = ''
    },
    initCharts() {
      // 初始化 ECharts 实例
      this.trendChart = echarts.init(this.$refs.trendChartContainer)
      this.spectrumChart = echarts.init(this.$refs.spectrumChartContainer)
      this.waveformChart = echarts.init(this.$refs.waveformChartContainer)

      // 设置 ECharts 配置项
      this.setOption()
    },
    // 切换图表符号显示状态
    toggleShowSymbol(index) {
      this.showSymbols[index] = !this.showSymbols[index] // 切换状态
      this.updateChart(index)
    },

    // 切换折线样式
    toggleLineStyle(index) {
      this.currentLineStyle[index] = (this.currentLineStyle[index] + 1) % this.lineStyles.length
      this.updateChart(index)
    },

    // 切换网格线显示状态
    toggleGrid(index) {
      this.showGrids[index] = !this.showGrids[index]
      this.updateChart(index)
    },
    // 更新图表配置
    updateChart(index) {
      if (index === 0) {
        this.trendChart.setOption(this.getOptionForChart(0, this.generateTrendData()))
      } else if (index === 1) {
        this.spectrumChart.setOption(this.getOptionForChart(1, this.generateSpectrumData()))
      } else if (index === 2) {
        this.waveformChart.setOption(this.getOptionForChart(2, this.generateWaveformData()))
      }
    },
    disposeCharts() {
      if (this.trendChart) {
        this.trendChart.dispose()
        this.trendChart = null
      }
      if (this.spectrumChart) {
        this.spectrumChart.dispose()
        this.spectrumChart = null
      }
      if (this.waveformChart) {
        this.waveformChart.dispose()
        this.waveformChart = null
      }
    },

    setOption() {
      // 设置三个图表的配置
      this.trendChart.setOption(this.getOptionForChart(0, this.generateTrendData()))
      this.spectrumChart.setOption(this.getOptionForChart(1, this.generateSpectrumData()))
      this.waveformChart.setOption(this.getOptionForChart(2, this.generateWaveformData()))
    },

    // 获取图表的配置项，包含警戒线
    getOptionForChart(index, data) {
      return {
        tooltip: { trigger: 'axis', axisPointer: { animation: false }},
        grid: { top: '10%', left: '1%', right: '1%', bottom: '5%', containLabel: true, show: this.showGrids[index] },
        xAxis: { type: 'category', data: Array.from({ length: 1000 }, (_, i) => `t${i}`), boundaryGap: false },
        yAxis: { type: 'value' },
        series: [
          {
            name: `图表 ${index + 1}`,
            type: 'line',
            showSymbol: this.showSymbols[index],
            lineStyle: { type: this.lineStyles[this.currentLineStyle[index]] },
            data,
            markLine: {
              data: [
                {
                  yAxis: this.alertValues.safe,
                  lineStyle: { color: 'green', width: 2 },
                  label: { formatter: '安全', position: 'start', fontSize: 10, color: '#808080', offset: [0, 10] }
                },
                {
                  yAxis: this.alertValues.blueAlert,
                  lineStyle: { color: 'blue', width: 2 },
                  label: { formatter: '正常', position: 'start', fontSize: 10, color: '#808080', offset: [0, 10] }
                },
                {
                  yAxis: this.alertValues.yellowAlert,
                  lineStyle: { color: 'yellow', width: 2 },
                  label: { formatter: '警告', position: 'start', fontSize: 10, color: '#808080', offset: [0, 10] }
                },
                {
                  yAxis: this.alertValues.redAlert,
                  lineStyle: { color: 'red', width: 2 },
                  label: { formatter: '报警', position: 'start', fontSize: 10, color: '#808080', offset: [0, 10] }
                }
              ]
            }
          }
        ],
        dataZoom: [{ type: 'inside', xAxisIndex: [0], filterMode: 'filter', start: 0, end: 100 }]
      }
    },

    // 切换全屏状态
    toggleFullscreen(index) {
      this.isFullscreen = !this.isFullscreen
      this.fullscreenIndex = this.isFullscreen ? index : null

      // 使用 $nextTick 强制更新布局
      this.$nextTick(() => {
        this.resizeCharts() // 重新调整所有图表大小

        if (!this.isFullscreen) {
          // 退出全屏时，销毁并重新初始化所有图表
          this.disposeCharts()
          this.initCharts()
        }
      })
    },

    // 生成趋势图数据（1000 条）
    generateTrendData() {
      const data = []
      let value = 1000
      for (let i = 0; i < 1000; i++) {
        value += Math.random() * 20 - 10 // 模拟波动
        data.push([i, value])
      }
      return data
    },

    // 生成频谱图数据（1000 条）
    generateSpectrumData() {
      const data = []
      for (let i = 0; i < 1000; i++) {
        const value = Math.sin(i / 100) * 100 + Math.random() * 20 // 模拟频率数据
        data.push([i, value])
      }
      return data
    },

    // 生成波形图数据（1000 条）
    generateWaveformData() {
      const data = []
      for (let i = 0; i < 1000; i++) {
        const value = Math.sin(i / 50) * 100 + Math.random() * 10 // 模拟波形数据
        data.push([i, value])
      }
      return data
    },

    // 监听窗口变化，动态调整图表大小
    resizeCharts() {
      if (this.trendChart) {
        this.trendChart.resize()
      }
      if (this.spectrumChart) {
        this.spectrumChart.resize()
      }
      if (this.waveformChart) {
        this.waveformChart.resize()
      }
    }
  }
}
</script>

<style scoped>
/* 父容器设置为 flex 布局 */
.wrapper {
  display: flex;
  height: 80vh; /* 设置高度等于视口高度 */
}

.container {
  flex: 1; /* 左边容器占满剩余空间 */
  display: flex;
  flex-direction: column;
}

.side-element {
  width: 100%; /* 右边元素宽度占满 */
  background-color: #f0f0f0;
  display: flex;
  flex-direction: column; /* 垂直排列上下部分 */
  height: 100%; /* 占满容器的高度 */
}

/* 上部内容样式，占据 40% 高度 */
.top-part {
  flex: 0 0 40%; /* 高度占比 40% */
  background-color: #ebf0eba1;
  padding: 10px;
  border-bottom: 1px solid #ccc; /* 添加分割线 */
  width: 100%; /* 宽度为 100% */
}

h2 {
  margin-bottom: 10px;
  text-align: center; /* 标题居中 */
}

/* 测量定义容器样式 */
.definition-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.definition-container input {
  padding: 5px;
  margin-top: 5px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.definition-container button {
  padding: 5px 10px;
  background-color: #adafb1;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.definition-container button:hover {
  background-color: #7b7e81;
}

/* 测量定义信息容器样式 */
.measurement-info {
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
}

.value {
  color: #333;
}

/* 下部内容样式，占据 60% 高度 */
.bottom-part {
  flex: 1; /* 剩余高度占比 60% */
  background-color: #ebf0eb;
  padding: 10px;
  width: 100%; /* 宽度为 100% */
}

.feature-list {
  margin-top: 10px;
  display: flex;
  flex-direction: column; /* 垂直排列特征 */
  gap: 10px; /* 每个特征间距 */
}

.feature {
  background-color: #fff;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  text-align: center;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.controls {
  display: flex;
  flex-direction: column;
}

.right-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-item {
  flex: 1;
}
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 80vh; /* 设置整个容器的高度为视口高度 */
}

.side-element {
  width: 300px; /* 右边元素固定宽度 */
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: flex-start; /* 控制按钮从顶部开始对齐 */
  flex: 1; /* 让每个图表容器占满可用空间 */
}

.controls {
  display: flex;
  flex-direction: column;
  height: 100%; /* 控制高度与图表部分相同 */
}

.bordered {
  border: 2px solid #ccc; /* 给控制区域和图表区域添加边框 */
  padding: 10px;
  background-color: #f9f9f9;
}

.right-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%; /* 保持与容器的高度一致 */
}

.title {
  display: flex;
  justify-content: space-between; /* 左右空间均分，保证文本靠左，按钮靠右 */
  align-items: center;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  font-size: 16px;
  padding: 0 10px; /* 给左右内容一点间距 */
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
}

.chart-item {
  flex: 1;
  width: 100%; /* 宽度设置为100% */
  height: 100%; /* 高度设置为100% */
}

.fullscreen {
  height: 80vh; /* 当全屏时，将高度设置为视口高度 */
}
</style>
