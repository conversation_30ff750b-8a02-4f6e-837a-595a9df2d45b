<template>
  <div class="SystemAdministration-layout">
    <div class="SystemAdministration-body">
      <!-- 左边导航 -->
      <div style="height: 100%">
        <LeftSidebar @company-added="handleCompanyAdded" />
      </div>
      <div ref="container" class="container_c">
        <div class="split-container">
          <div ref="leftContainer" class="left-container_l">
            <!-- 数据采集表格 -->
            <DataConfiguration ref="dataConfig" @switch-component="switchComponent" />
          </div>
          <!-- 分割线 -->
          <div class="resizer" @mousedown="initResize" />
          <div ref="rightContainer" class="right-container_r">
            <!-- 动态组件 -->
            <component :is="currentComponent" :nodeData="nodeData" />
          </div>
        </div>
      </div>
    </div>
    <div v-if="tooltip.show" :style="tooltipStyle" class="tooltip">{{ tooltip.content }}</div>
  </div>
</template>

<script>
import LeftSidebar from '../../LeftSidebar/LeftSidebar.vue'
import DataConfiguration from './DeviceManagementTree/DeviceManagementTree.vue'
// 导入可能的组件
import FactoryComponent from './DeviceManagementTree/selectTreePage/factoryPage/index.vue'
import GradingComponent from './DeviceManagementTree/selectTreePage/gradingPage/index.vue'
import WaterPumpComponent from './DeviceManagementTree/selectTreePage/waterPumpPage/index.vue'
import BearingComponent from './DeviceManagementTree/selectTreePage/bearingPage/index.vue'
export default {
  components: {
    DataConfiguration,
    LeftSidebar,
    FactoryComponent,
    GradingComponent,
    WaterPumpComponent,
    BearingComponent
  },
  data() {
    return {
      currentComponent: null, // 定义 currentComponent，初始值为 null
      nodeData: null, // 定义 nodeData，初始值为 null
      startX: 0, // 初始X位置
      startWidth: 0, // 初始宽度
      isResizing: false, // 是否正在调整大小
      tooltip: { // tooltip 数据
        show: false,
        content: '',
        left: 0,
        top: 0
      }
    }
  },
  computed: {
    tooltipStyle() {
      return {
        position: 'absolute',
        backgroundColor: 'white',
        border: '1px solid black',
        padding: '5px',
        left: this.tooltip.left + 'px',
        top: this.tooltip.top + 'px',
        display: this.tooltip.show ? 'block' : 'none'
      }
    }
  },
  // 当页面切换回来时，重新绑定事件或重置状态
  activated() {
    this.isResizing = false
    if (this.$refs.leftContainer) {
      this.startWidth = this.$refs.leftContainer.offsetWidth
    }
  },
  // 当页面切换走时，移除事件监听器
  deactivated() {
    document.removeEventListener('mousemove', this.resize)
    document.removeEventListener('mouseup', this.stopResize)
  },

  methods: {
    // 切换右边显示的组件
    switchComponent(componentName, node) {
      // console.log('切换组件:', componentName)
      // console.log('节点数据:', node)
      this.currentComponent = componentName
      this.nodeData = node
      console.log('node->',node);
      
      // console.log('当前组件:', this.currentComponent)
    },
    // 初始化调整大小
    initResize(e) {
      e.preventDefault()
      this.startX = e.clientX
      this.startWidth = this.$refs.leftContainer.offsetWidth
      this.isResizing = true
      // console.log('开始调整大小')
      document.addEventListener('mousemove', this.resize)
      document.addEventListener('mouseup', this.stopResize)
    },
    // 调整大小
    resize(e) {
      if (this.isResizing) {
        const newWidth = this.startWidth + (e.clientX - this.startX)
        const maxWidth = this.$refs.container.offsetWidth * 0.5 // 父级宽度的50%
        const minWidth = Math.max(this.startWidth * 0.5, 100) // 当前宽度的50%或最小100
        if (newWidth >= minWidth && newWidth <= maxWidth) {
          this.$refs.leftContainer.style.width = `${newWidth}px`
          this.$refs.rightContainer.style.width = `calc(100% - ${newWidth}px)`
          // this.$refs.chartComponent.resizeChart()
        }
      }
    },
    // 停止调整大小
    stopResize() {
      // console.log('停止调整大小')
      this.isResizing = false
      document.removeEventListener('mousemove', this.resize)
      document.removeEventListener('mouseup', this.stopResize)
    },
    // 调用 DataConfiguration 组件中的 WorkTree 子组件的方法
    handleCompanyAdded() {
      this.$refs.dataConfig.$refs.workTree.getTreelist()
    }
  }
}
</script>

<style scoped>
@import './SystemAdministration.css';
</style>
