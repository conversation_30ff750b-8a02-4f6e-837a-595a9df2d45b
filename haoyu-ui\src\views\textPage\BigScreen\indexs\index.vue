<template>
  <div class="screen-container">
    <div class="content-wrapper">
      <!-- 顶部工具栏 -->
      <div class="header-toolbar">
        <div class="toolbar-left">
          <div class="brand">
            <i class="el-icon-monitor"></i>
            <span>设备监控中心</span>
          </div>
        </div>
        <div class="toolbar-center">
          <div class="data-overview">
            <div class="overview-item">
              <span class="label">在线设备</span>
              <span class="value">{{ onlineDevices }}</span>
            </div>
            <div class="overview-item">
              <span class="label">报警设备</span>
              <span class="value warning">{{ alarmDevices }}</span>
            </div>
            <div class="overview-item">
              <span class="label">健康指数</span>
              <span class="value success">{{ healthIndex }}%</span>
            </div>
          </div>
        </div>
        <div class="toolbar-right">
          <el-button
            type="text"
            icon="el-icon-refresh"
            @click="refreshData"
          >
            刷新
          </el-button>
          <el-button
            type="text"
            icon="el-icon-full-screen"
            @click="toggleFullScreen"
          >
            全屏
          </el-button>
          <el-button
            type="text"
            :icon="viewMode === 'grid' ? 'el-icon-menu' : 'el-icon-s-grid'"
            @click="toggleViewMode"
          >
            {{ viewMode === 'grid' ? '列表视图' : '网格视图' }}
          </el-button>
        </div>
      </div>

      <!-- 主要内容区 -->
      <div class="main-content">
        <div class="content-layout">
          <!-- 左侧地图区域 -->
          <div class="map-wrapper">
            <div class="map-toolbar">
              <div class="section-title">
                <i class="el-icon-map-location"></i>
                <span>设备分布</span>
              </div>
            </div>

            <div class="map-container" :class="{ 'blur-bg': loading }">
              <div v-if="loading" class="loading-overlay">
                <el-spinner type="spinner" size="32"></el-spinner>
                <span>加载中...</span>
              </div>
              <CenterMap
                class="center-map"
                @switchToGeneralPicture="handleSwitchToGeneralPicture"
                @statistics-update="handleStatisticsUpdate"
                :viewMode="viewMode"
                ref="centerMap"
              />
            </div>
          </div>

          <!-- 右侧报警信息 -->
          <div class="alarm-wrapper">
            <div class="alarm-header">
              <div class="section-title">
                <i class="el-icon-warning"></i>
                <span>实时报警</span>
              </div>
              <span class="update-time">更新时间: {{ updateTime }}</span>
            </div>

            <div class="alarm-list">
              <div class="table-container" style="overflow-x: auto;">
                <el-table
                  :data="alarmList"
                  style="width: 100%; min-width: 800px;"
                  height="100%"
                  :header-cell-style="{
                    backgroundColor: '#f5f7fa',
                    color: '#2c3e50',
                    fontWeight: '500'
                  }"
                  :cell-style="{
                    backgroundColor: '#ffffff',
                    color: '#2c3e50'
                  }"
                  @scroll="handleTableScroll"
                  v-loading="loading"
                  @row-dblclick="handleDeviceDoubleClick"
                  :scrollbar-always-on="true"
                >
                  <el-table-column
                    prop="warnTime"
                    label="时间"
                    width="160"
                  >
                    <template slot-scope="scope">
                      <span class="clickable-text">{{ scope.row.warnTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="warnLevel"
                    label="等级"
                    width="100"
                  >
                    <template slot-scope="scope">
                      <el-tag
                        :type="getAlarmLevelTag(scope.row.warnLevel)"
                        size="mini"
                      >
                        {{ getAlarmLevelText(scope.row.warnLevel) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="deviceName"
                    label="设备名称"
                    width="150"
                  >
                    <template slot-scope="scope">
                      <span class="clickable-text">{{ scope.row.deviceName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="warnMsg"
                    label="报警信息"
                    min-width="220"
                  >
                    <template slot-scope="scope">
                      <span class="clickable-text">{{ scope.row.warnMsg }}</span>
                    </template>
                  </el-table-column>
                  <template slot="append">
                    <div v-if="loading" class="loading-more">
                      <span>加载中...</span>
                    </div>
                    <div v-else-if="noMore" class="no-more">
                      <span>没有更多数据了</span>
                    </div>
                  </template>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CenterMap from '@/views/textPage/BigScreen/indexs/center-table.vue'
import { debounce } from 'lodash'
import dayjs from 'dayjs'
import { getWarnRecord, geDeviceMeasureDefine, getNextLevelImformation } from '@/views/textPage/BigScreen/api/apiBigscreen'
import WebSocketService from '@/utils/alarmSocket/websocket'

export default {
  name: 'BigScreen',
  components: {
    CenterMap
  },
  data() {
    return {
      loading: false,
      onlineDevices: 0,
      alarmDevices: 0,
      healthIndex: 0,
      isFullScreen: false,
      alarmList: [],
      updateTime: '--',
      updateTimer: null,
      pageNum: 1,  // 当前页码
      pageSize: 10, // 每页数量
      total: 0,    // 总数据量
      noMore: false, // 是否还有更多数据
      viewMode: 'grid' // 新增：视图模式，'grid' 或 'list'
    }
  },
  created() {
    this.initData()
    this.startAutoUpdate()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
  },
  mounted() {
    // 注册WebSocket消息回调
    if (WebSocketService) {
      WebSocketService.onMessage((data) => {
        console.log("data",data)
        this.updateAlarmTime();
        // 这里可以添加其他处理报警信息的逻辑
      });
    } else {
      console.error('WebSocket服务未初始化');
    }
  },
  methods: {
    updateAlarmTime() {
      console.log("更新报警时间")
      this.fetchAlarmData().then(data => {
        this.alarmList = data
        this.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      })
    },

    async initData() {
      this.loading = true
      try {
        // 重置页码
        this.pageNum = 1
        this.alarmList = []

        const alarmData = await this.fetchAlarmData()
        this.alarmList = alarmData
        this.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

        // 更新统计数据
        this.onlineDevices = 125  // 这里应该从API获取实际数据
        this.alarmDevices = this.alarmList.length
        this.healthIndex = 95     // 这里应该从API获取实际数据

      } catch (error) {
        console.error('数据加载失败:', error)
        this.$message.error('数据加载失败，请检查网络连接')
      } finally {
        this.loading = false
      }
    },

    startAutoUpdate() {

    },

    async fetchAlarmData() {
      try {
        const response = await getWarnRecord(this.pageNum)
        // console.log("报警数据response",response)
        if (!response || !response.rows) {
          throw new Error('API返回数据格式错误')
        }

        // 更新总数据量
        this.total = response.total

        // 判断是否还有更多数据
        this.noMore = this.alarmList.length >= response.data

        return response.rows || []
      } catch (error) {
        console.error('获取报警数据失败:', error.message)
        this.$message.error(`获取报警数据失败: ${error.message}`)
        return []
      }
    },

    getAlarmLevelTag(level) {
      const levelMap = {
        '1': 'warning',
        '2': 'info',
        '3': 'danger',
        '4': 'danger'
      }
      return levelMap[level] || 'info'
    },

    getAlarmLevelText(level) {
      const levelMap = {
        '1': '一级报警',
        '2': '二级报警',
        '3': '三级报警',
        '4': '四级报警'
      }
      return levelMap[level] || '未知等级'
    },

    handleSwitchToGeneralPicture(selectedRowData) {
      this.$emit('switchToGeneralPicture', selectedRowData)
    },
    refreshData: debounce(function() {
      this.initData()
    }, 300),
    toggleFullScreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
        this.isFullScreen = true
      } else {
        document.exitFullscreen()
        this.isFullScreen = false
      }
    },
    handleResize() {
      if (this.$refs.centerMap) {
        this.$refs.centerMap.handleResize?.()
      }
    },
    // 加载更多数据
    async loadMore() {
      if (this.loading || this.noMore) return

      this.loading = true
      try {
        this.pageNum++
        const moreData = await this.fetchAlarmData()
        this.alarmList = [...this.alarmList, ...moreData]
      } catch (error) {
        console.error('加载更多数据失败:', error)
        this.$message.error('加载更多数据失败')
        this.pageNum-- // 恢复页码
      } finally {
        this.loading = false
      }
    },
    // 监听表格滚动
    handleTableScroll({ scrollTop, scrollHeight, clientHeight }) {
      // 当距离底部小于50px时加载更多
      if (scrollHeight - scrollTop - clientHeight < 50 && !this.loading && !this.noMore) {
        this.loadMore()
      }
    },

    // 处理设备名称双击事件
    async handleDeviceDoubleClick(row) {
      const response = await getNextLevelImformation(row.deviceId)
      console.log('设备测量定义信息:', response)
      const rowsent = {
        id: response.data.id,
        deviceName: response.data.title,
        status: response.data.status,
        mongodbId: response.data.mongodbId,
        children: response.data.children
      }
      this.$emit('switchToGeneralPicture', rowsent)
    },

    // 处理统计数据更新
    handleStatisticsUpdate(stats) {
      this.onlineDevices = stats.onlineDevices
      this.alarmDevices = stats.alarmDevices
      this.healthIndex = stats.healthIndex
    },

    // 切换视图模式
    toggleViewMode() {
      this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid'
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #e8edf3 0%, #f7f9fc 100%);
  color: #2c3e50;
  display: flex;
  padding: 16px;
  box-sizing: border-box;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #dcdfe6;
}

.header-toolbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;

  .brand {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 500;

    i {
      font-size: 24px;
      color: #409EFF;
    }
  }
}

.data-overview {
  display: flex;
  gap: 32px;

  .overview-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      font-size: 12px;
      color: #606266;
      margin-bottom: 4px;
    }

    .value {
      font-size: 24px;
      font-weight: 600;
      font-family: 'DIN';

      &.warning {
        color: #E6A23C;
      }

      &.success {
        color: #67C23A;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 0 16px 16px;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.content-layout {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
  min-height: 0;
}

.map-wrapper {
  flex: 2;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.alarm-wrapper {
  flex: 1;
  min-width: 400px;
  max-width: 600px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;

  i {
    font-size: 18px;
    color: #409EFF;
  }
}

.alarm-header {
  height: 48px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;

  .update-time {
    font-size: 12px;
    color: #909399;
  }
}

.alarm-list {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.table-container::-webkit-scrollbar {
  width: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.map-toolbar {
  height: 48px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f5f7fa;

  .search-box {
    width: 240px;
  }
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 0;

  &.blur-bg {
    filter: blur(2px);
  }

  .center-map {
    width: 100%;
    height: 100%;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  z-index: 100;

  span {
    color: #2c3e50;
    font-size: 14px;
  }
}

// 自定义Element UI组件样式
:deep(.el-button--text) {
  color: #409EFF;

  &:hover {
    color: #66b1ff;
  }
}

:deep(.el-radio-button__inner) {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
  background-color: #409EFF;
  border-color: #409EFF;
  box-shadow: -1px 0 0 0 #409EFF;
}

:deep(.el-input__inner) {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
  color: #fff;

  &::placeholder {
    color: rgba(255, 255, 255, 0.3);
  }
}

:deep(.el-input__prefix) {
  color: rgba(255, 255, 255, 0.3);
}

// Element UI 表格样式覆盖
:deep(.el-table) {
  background-color: #ffffff !important;

  &::before {
    display: none;
  }

  th.el-table__cell {
    background-color: #f5f7fa !important;
    border-bottom: 1px solid #e4e7ed;
    color: #2c3e50;
    font-weight: 500;
  }

  td.el-table__cell {
    background-color: #ffffff !important;
    border-bottom: 1px solid #e4e7ed;
    color: #2c3e50;
  }

  tr {
    background-color: #ffffff !important;

    &:hover > td {
      background-color: #f5f7fa !important;
    }
  }
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

.loading-more, .no-more {
  text-align: center;
  padding: 10px 0;
  color: #606266;
  font-size: 12px;
}

.clickable-text {
  cursor: pointer;

  &:hover {
    color: #409EFF;
  }
}

:deep(.el-table__row) {
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }
}
</style>


