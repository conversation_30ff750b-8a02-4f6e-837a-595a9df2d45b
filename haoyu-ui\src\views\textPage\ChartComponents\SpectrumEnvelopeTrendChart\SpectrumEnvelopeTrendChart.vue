<!-- 频谱-包络趋势 -->
<template>
  <div class="chart-container">
    <div ref="chart" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SpectrumEnvelopeChart',
  data() {
    return {
      chartInstance: null,
      frequencyData: [10, 20, 30, 50, 80, 60, 50, 40, 30, 20, 10], // 频谱数据
      envelopeData: [15, 25, 35, 55, 85, 65, 55, 45, 35, 25, 15], // 包络数据
      xData: ['1Hz', '2Hz', '3Hz', '4Hz', '5Hz', '6Hz', '7Hz', '8Hz', '9Hz', '10Hz', '11Hz'] // X 轴数据
    }
  },
  beforeDestroy() {
  // 销毁实例，避免内存泄漏
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      // 初始化 ECharts 实例
      this.chartInstance = echarts.init(this.$refs.chart)

      // 配置项
      const option = {
        title: {
          text: '频谱-包络趋势图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['频谱', '包络']
        },
        xAxis: {
          type: 'category',
          data: this.xData
        },
        yAxis: {
          type: 'value',
          name: '幅值'
        },
        series: [
          {
            name: '频谱',
            type: 'line',
            smooth: true,
            data: this.frequencyData,
            lineStyle: {
              width: 2,
              color: 'blue'
            }
          },
          {
            name: '包络',
            type: 'line',
            smooth: true,
            data: this.envelopeData,
            lineStyle: {
              width: 2,
              color: 'red'
            }
          }
        ]
      }

      // 使用配置项和数据显示图表
      this.chartInstance.setOption(option)
    },
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
