<template>
  <div class="device-status">
    <div class="status-box">
      <h3>设备状态</h3>
      <div class="status-header">
        <div class="device-name-button" @click="handleDeviceNameClick">
          {{ deviceName }}
        </div>
        <el-tag
          :type="getTrainingStatusType"
          size="mini"
          class="training-status"
        >
          {{ getTrainingStatusText }}
        </el-tag>
      </div>

      <div class="data-time">
        数据时间：{{ dataTime }}
      </div>

      <div class="status-content">
        <div class="gauge-container" ref="gaugeContainer"></div>

        <div class="health-info">
          <div class="precise-health">
            健康度：{{ healthValue.toFixed(4) }}%
          </div>

          <!-- <div class="remaining-days">
            预计剩余可用天数：{{ getRemainingDays }}天
          </div> -->
        </div>
      </div>
    </div>

    <div class="alarm-box">
      <h3>
        <div class="alarm-title">
          <span class="main-title">报警记录</span>
          <span v-if="currentMeasureName" class="sub-title">{{ currentMeasureName }}</span>
        </div>
      </h3>
      <div class="alarm-list">
        <template v-if="alarmData && alarmData.length > 0">
          <el-table
            :data="alarmData"
            style="width: 100%"
            height="420"
            :header-cell-style="{
              background:'#f5f7fa',
              padding: '4px 0',
              fontSize: '12px',
              height: '32px'
            }"
            :cell-style="getCellStyle"
            size="mini"
          >
            <el-table-column
              prop="warnTime"
              label="时间"
              width="90">
              <template slot-scope="scope">
                <div class="time-column">
                  <div class="date">{{ formatDate(scope.row.warnTime) }}</div>
                  <div class="time">{{ formatTime(scope.row.warnTime) }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="warnMsg"
              label="报警原因"
              min-width="150">
              <template slot-scope="scope">
                <div style="white-space: pre-line; line-height: 1.5;">{{ scope.row.warnMsg }}</div>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template v-else>
          <div class="no-data-tip">
            设备很健康，无报警记录
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DeviceStatus',
  props: {
    deviceName: {
      type: String,
      default: ''
    },
    deviceId: {
      type: [String, Number],
      default: ''
    },
    currentMeasureName: {
      type: String,
      default: ''
    },
    alarmData: {
      type: Array,
      default: () => []
    },
    healthValue: {
      type: Number,
      default: 100
    },
    trainingStatus: {
      type: String,
      default: 'no' // 可能的值: 'no'(未训练), 'training'(训练中), 'yes'(正在监测)
    },
    dataTime: { // 新增 prop 接收数据时间
      type: String,
      default: '--'
    }
  },
  data() {
    return {
      gaugeChart: null,
      currentTime: '',
      timer: null,
      resizeHandler: null,
      orientationHandler: null
    }
  },
  mounted() {
    this.updateTime()
    this.timer = setInterval(this.updateTime, 1000)
    this.initGaugeChart()

    // 获取网页分辨率和缩放信息
    this.getDisplayInfo()
  },
  
  beforeDestroy() {
    if (this.gaugeChart) {
      this.gaugeChart.dispose()
      this.gaugeChart = null
    }
    window.removeEventListener('resize', this.handleChartResize)
    if (this.timer) {
      clearInterval(this.timer)
    }
    
    // 清理显示器信息相关的事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }
    if (this.orientationHandler && window.screen.orientation) {
      window.screen.orientation.removeEventListener('change', this.orientationHandler)
    }
  },
  computed: {
    getTrainingStatusText() {
      const statusMap = {
        'no': '未训练',
        'training': '训练中',
        'yes': '正在监测',
        '当前节点无数据接入': '当前节点无数据接入'
      }
      return statusMap[this.trainingStatus] || '未训练'
    },

    getTrainingStatusType() {
      const typeMap = {
        'no': 'warning',
        'training': 'info',
        'yes': 'success',
        '当前节点无数据接入': 'warning'
      }
      return typeMap[this.trainingStatus] || 'warning'
    },
    getRemainingDays() {
      if (this.healthValue === 0 || !this.healthValue) {
        return '9999+'
      }
      if (this.healthValue === 100) {
        return '9999+'
      }
      return Math.round(365 * (this.healthValue / 100))
    }
  },
  methods: {
    // 获取显示器和浏览器信息
    getDisplayInfo() {
      // 获取基本信息
      const webWidth = window.innerWidth
      const webHeight = window.innerHeight
      const screenWidth = window.screen.width
      const screenHeight = window.screen.height
      const devicePixelRatio = window.devicePixelRatio
      const webZoom = Math.round(devicePixelRatio * 100)

      console.log('=== 设备显示信息 ===')
      console.log('屏幕分辨率:', `${screenWidth}x${screenHeight}`)
      console.log('网页视口:', `${webWidth}x${webHeight}`)
      console.log('设备像素比:', devicePixelRatio)
      console.log('缩放比例:', `${webZoom}%`)
      console.log('颜色深度:', window.screen.colorDepth + '位')

      // 屏幕方向信息
      if (window.screen.orientation) {
        console.log('屏幕方向:', window.screen.orientation.type)
        console.log('屏幕角度:', window.screen.orientation.angle + '度')
      }

      // 构建简化的显示信息
      const displayInfo = {
        local: {
          screenWidth,
          screenHeight,
          webWidth,
          webHeight,
          zoomPercentage: webZoom,
          devicePixelRatio,
          colorDepth: window.screen.colorDepth
        },
        cloud: {
          screenWidth: 1920,
          screenHeight: 1080,
          webWidth: 1920,
          webHeight: 1080,
          zoomPercentage: 100,
          devicePixelRatio: 1
        },
        timestamp: new Date().toISOString()
      }

      console.log('==================')

      // 发送信息到父组件
      this.$emit('display-info-updated', displayInfo)

      // 监听变化
      this.setupDisplayChangeListeners()
    },

    // 设置显示变化监听器
    setupDisplayChangeListeners() {
      // 避免重复设置监听器
      if (this.resizeHandler) return

      // 窗口大小变化监听
      this.resizeHandler = () => {
        console.log('检测到窗口大小或缩放变化，重新获取显示信息')
        setTimeout(() => this.getDisplayInfo(), 100) // 短延迟确保数值稳定
      }
      window.addEventListener('resize', this.resizeHandler)

      // 屏幕方向变化监听
      if (window.screen.orientation) {
        this.orientationHandler = () => {
          console.log('检测到屏幕方向变化')
          setTimeout(() => this.getDisplayInfo(), 100)
        }
        window.screen.orientation.addEventListener('change', this.orientationHandler)
      }
    },
    handleDeviceNameClick() {
      // 存储完整的设备信息
      this.$store.dispatch('selectedRowModule/setSelectedRow', {
        id: this.deviceId,
        deviceName: this.deviceName
      })

      // 使用带参数的路由跳转
      this.$router.push({
        path: '/textPage',
        query: {
          deviceId: this.deviceId,
          fromGeneralPicture: true
        }
      })
    },

    handleChartResize() {
      if (this.gaugeChart) {
        this.gaugeChart.resize()
      }
    },

    initGaugeChart() {
      const chartDom = this.$refs.gaugeContainer
      if (!chartDom) {
        console.warn('表盘容器未找到')
        return
      }

      if (this.gaugeChart) {
        this.gaugeChart.dispose()
        this.gaugeChart = null
      }

      this.gaugeChart = echarts.init(chartDom)

      const option = {
        backgroundColor: '#fff',
        series: [{
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          min: 0,
          max: 100,
          radius: '95%',
          center: ['50%', '50%'],
          progress: {
            show: true,
            roundCap: true,
            width: 12,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [{
                  offset: 0,
                  color: '#1890FF'
                }, {
                  offset: 1,
                  color: '#36CBCB'
                }]
              },
              shadowColor: 'rgba(24,144,255,0.35)',
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowOffsetY: 5
            }
          },
          pointer: {
            show: false
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 12,
              color: [
                [1, 'rgba(240,242,245,0.3)']
              ]
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          title: {
            show: true,
            fontSize: 13,
            color: '#8C8C8C',
            fontWeight: 'normal',
            offsetCenter: [0, '65%']
          },
          detail: {
            show: true,
            width: 120,
            height: 40,
            fontSize: 52,
            fontWeight: 'bold',
            color: '#1890FF',
            formatter: function(value) {
              return '{value|' + Math.round(value) + '}{unit|%}'
            },
            rich: {
              value: {
                fontSize: 52,
                fontWeight: 'bold',
                color: '#1890FF',
                padding: [0, 0, 0, 0],
                fontFamily: 'DIN',
                textShadow: '0 0 10px rgba(24,144,255,0.2)'
              },
              unit: {
                fontSize: 26,
                fontWeight: 'normal',
                color: '#1890FF',
                padding: [0, 0, 20, 0],
                textShadow: '0 0 10px rgba(24,144,255,0.2)'
              }
            },
            valueAnimation: true,
            offsetCenter: [0, '15%']
          },
          data: [{
            value: this.healthValue,
            name: '设备健康度'
          }]
        }]
      }

      this.gaugeChart.setOption(option)
      window.addEventListener('resize', this.handleChartResize)
    },
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    },
    getCellStyle({ row, column }) {
      // 根据报警类型设置不同的颜色
      const color = row.type === '严重' ? '#F56C6C' : '#E6A23C';
      return {
        padding: '2px 0',
        fontSize: '12px',
        color: color
      };
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    formatTime(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    }
  },
  watch: {
    healthValue: {
      handler(newVal) {
        if (this.gaugeChart) {
          this.gaugeChart.setOption({
            series: [{
              data: [{
                value: newVal
              }]
            }]
          })
        }
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
.device-status {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.status-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 0 0 auto;
  max-height: min(360px, 40vh);
  overflow: hidden;
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  position: relative;
  padding: 10px 0;
}

.gauge-container {
  height: 180px;
  min-height: min(160px, 18vh);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
  position: relative;
  max-height: min(200px, 20vh);
}

.health-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: -10px;
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  padding: 6px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(4px);
}

.precise-health {
  text-align: center;
  color: #1890FF;
  font-size: 16px;
  font-weight: 500;
  font-family: 'DIN';
  margin: 0;
  letter-spacing: 0.5px;
  background: linear-gradient(45deg, #1890FF, #36CBCB);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.remaining-days {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding: 6px 16px;
  background: rgba(245, 247, 250, 0.8);
  border-radius: 6px;
  margin: 0;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.alarm-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  max-height: calc(85vh);
}

h3 {
  color: #333;
  font-size: 16px;
  margin: 0 0 6px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #1890ff;
  flex-shrink: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 12px;
}

h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #1890ff;
  border-radius: 2px;
}

.device-name-button {
  padding: 6px 16px;
  background: transparent;
  border: none;
  color: #1890ff;
  font-size: 14px;
  cursor: pointer;
  text-align: center;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.device-name-button:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #40a9ff;
}

.alarm-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.alarm-list >>> .el-table {
  font-size: 12px;
}

.alarm-list >>> .el-table th {
  padding: 4px 0;
  font-weight: 500;
  height: 32px;
  line-height: 24px;
}

.alarm-list >>> .el-table td {
  padding: 6px 0;
  height: auto;
  line-height: 1.5;
}

.alarm-list >>> .el-table .cell {
  line-height: 1.5;
  padding: 0 6px;
  white-space: normal;
}

.alarm-list >>> .el-tag {
  margin: 0;
  height: 20px;
  line-height: 18px;
  padding: 0 4px;
}

.alarm-list >>> .el-table__header-wrapper th {
  background: #f5f7fa;
  color: #606266;
  font-weight: 500;
  font-size: 12px;
  height: 32px;
  padding: 4px 0;
}

.alarm-list >>> .el-table__row {
  height: auto;
}

.no-data-tip {
  text-align: center;
  color: #67C23A;
  padding: 20px 0;
  font-size: 13px;
  background: #f8f8f8;
  border-radius: 4px;
  margin-top: 10px;
}

.status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.device-name-button {
  margin-bottom: 0; /* 覆盖原来的 margin-bottom */
}

.training-status {
  margin-left: 8px;
}

.device-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.device-time {
  color: #666;
  font-size: 14px;
  margin-top: 8px;
}

.data-time {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin: 8px 0;
}

.alarm-box h3 {
  font-weight: normal;
  padding-bottom: 12px;
}

.alarm-box h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #1890ff;
  border-radius: 2px;
}

.alarm-box h3::after {
  content: none;
}

.alarm-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.main-title {
  font-weight: 600;
  font-size: 16px;
}

.sub-title {
  font-size: 14px;
  color: #666;
}

.el-table {
  height: 420px;
}

.time-column {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.time-column .date {
  color: #666;
  margin-bottom: 2px;
}

.time-column .time {
  color: inherit;
}

.alarm-list >>> .el-table td {
  padding: 6px 0;
}

.alarm-list >>> .el-table .cell {
  line-height: 1.5;
  padding: 0 6px;
}
</style>
