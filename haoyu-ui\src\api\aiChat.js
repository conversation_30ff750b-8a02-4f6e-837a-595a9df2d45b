import axios from 'axios'

// 直接硬编码API配置
const apiClient = axios.create({
  baseURL: 'https://api.dify.ai',  // 移除 /v1，因为会在具体请求中添加
  headers: {
    'Authorization': 'Bearer app-rUX2Q7b865AUBwTLkJODEWDe',
    'Content-Type': 'application/json'
  }
})

// 添加请求拦截器，用于调试
apiClient.interceptors.request.use(config => {
  console.log('发送请求:', config.method.toUpperCase(), config.url, config.data)
  return config
})

// 添加响应拦截器，用于调试
apiClient.interceptors.response.use(
  response => {
    console.log('收到响应:', response.data)
    return response
  },
  error => {
    console.error('请求错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export const chatService = {
  // 发送消息并获取回复
  async sendMessage(message, conversationId = null) {
    try {
      const response = await apiClient.post('/v1/chat-messages', {
        inputs: {},  // 如果有额外输入参数可以在这里添加
        query: message,
        response_mode: "blocking",  // 使用阻塞模式，等待完整回复
        conversation_id: conversationId,
        user: 'web-user'
      })
      return response.data
    } catch (error) {
      console.error('AI回复出错:', error)
      throw error
    }
  },

  // 创建新的对话
  async createConversation() {
    try {
      const response = await apiClient.post('/v1/conversations', {
        user: 'web-user'
      })
      return response.data
    } catch (error) {
      console.error('创建对话失败:', error)
      throw error
    }
  }
}

