import request from '@/utils/request'

// 获取轴承库
export function getBearingLibrary(query) {
  return request({
    url: '/measureInfo/warehouse/list',
    method: 'get',
    params: query
  })
}

// 获取组态部件
export function getGroupComponent(id) {
  return request({
    url: '/deviceConfiguration/configuration/list/' + id,
    method: 'get'
  })
}

// 获取轴承厂商
export function getBearingManufacturer() {
  return request({
    url: '/measureInfo/warehouse/manufactruer/list',
    method: 'get'
  })
}

// 新建组态组件
export function addGroupComponent(data) {
  return request({
    url: '/deviceConfiguration/configuration',
    method: 'post',
    data
  })
}

// 删除组态及其下属节点
export function deleteGroupComponent(parentId, id) {
  return request({
    url: '/deviceConfiguration/configuration/del/' + parentId + '/' + id,
    method: 'delete'
  })
}

// 新建本地轴承
export function addBearing(data) {
  return request({
    url: '/measureInfo/warehouse',
    method: 'post',
    data
  })
}

// 更新本地轴承
export function updateBearing(data) {
  return request({
    url: '/measureInfo/warehouse',
    method: 'put',
    data
  })
}

// 删除本地轴承
export function deleteBearing(id) {
  return request({
    url: '/measureInfo/warehouse/' + id,
    method: 'delete'
  })
}

// 查询组态对象信息
export function getGroupComponentInfo(id) {
  return request({
    url: '/deviceConfiguration/configuration/' + id,
    method: 'get'
  })
}

// 更新组态信息
export function updateGroupComponent(data) {
  return request({
    url: '/deviceConfiguration/configuration',
    method: 'put',
    data: data
  })
}

// 批量新建组态
export function batchAddGroupComponent(data) {
  return request({
    url: '/deviceConfiguration/configuration/addALot',
    method: 'post',
    data: data
  })
}

// 查询组态对象信息
export function getGroupComponentInfoByCode(id) {
  return request({
    url: '/deviceConfiguration/configuration/child/' + id,
    method: 'get'
  })
}

// 组态复制
export function copyGroupComponent(deviceId, targetId) {
  return request({
    url: '/deviceConfiguration/configuration/' + deviceId + '/' + targetId,
    method: 'post'
  })
}

// 批量更新组态及其下属节点
export function batchUpdateGroupComponent(data) {
  return request({
    url: '/deviceConfiguration/configuration/update/alot',
    method: 'put',
    data: data
  })
}

// 批量更新组态列表
export function batchUpdateGroupComponentList(data) {
  return request({
    url: '/deviceConfiguration/configuration/update/RuoyiDeviceConfiguration/alot',
    method: 'put',
    data: data
  })
}
