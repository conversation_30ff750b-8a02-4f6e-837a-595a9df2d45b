<!-- 频谱图组件 -->
<template>
  <div class="spectrum-chart">
    <div class="chart-header">
      <div class="title">{{ title || '频谱图' }}频谱图</div>
      <div class="actions">
        <i class="el-icon-full-screen" @click="handleFullScreen"></i>
      </div>
    </div>
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SpectrumChart',

  props: {
    spectrumData: {
      type: Array,
      default: () => []
    },
    freqRange: {
      type: Object,
      default: () => ({
        start: null,
        end: null
      })
    },
    title: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      chart: null,
      options: {
        animation: false,
        grid: {
          top: 40,
          right: 20,
          bottom: 40,
          left: 50,
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          },
          formatter: function(params) {
            return `${params[0].value[0].toFixed(4)}, ${params[0].value[1].toFixed(4)}`;
          }
        },
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0,
            filterMode: 'none'
          },
          {
            type: 'inside',
            yAxisIndex: 0,
            filterMode: 'none'
          }
        ],
        xAxis: {
          type: 'value',
          axisLabel: {
            formatter: value => value.toFixed(4)
          },
          name: '频率(Hz)',
          show: false,
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '幅值',
          show: false,
          splitLine: {
            show: false
          }
        },
        series: [{
          type: 'line',
          showSymbol: false,
          data: []
        }]
      }
    }
  },

  watch: {
    spectrumData: {
      handler(newData) {
        this.updateChart(newData)
      },
      deep: true
    },
    freqRange: {
      handler(newRange) {
        this.updateFreqRange(newRange)
      },
      deep: true
    }
  },

  methods: {
    initChart() {
      if (this.chart) {
        this.chart.dispose()
      }
      this.chart = echarts.init(this.$refs.chartContainer)
      this.chart.setOption(this.options)

      this.chart.on('datazoom', this.handleDataZoom)
      window.addEventListener('resize', this.handleResize)
    },

    updateChart(data) {
      if (!this.chart) return;

      const hasData = data && data.length > 0;
      const series = [{
        ...this.options.series[0],
        data: data
      }];

      // 计算 X 轴范围
      let xMin = 0, xMax = 0;
      if (hasData) {
        xMax = data[data.length - 1][0];
      }

      this.chart.setOption({
        grid: {
          ...this.options.grid,
          show: hasData
        },
        xAxis: {
          ...this.options.xAxis,
          show: hasData,
          min: xMin,
          max: xMax
        },
        yAxis: {
          ...this.options.yAxis,
          show: hasData
        },
        series
      });
    },

    updateFreqRange(range) {
      if (!this.chart || !range.start || !range.end) return

      this.chart.setOption({
        xAxis: {
          min: range.start,
          max: range.end,
          show: this.spectrumData && this.spectrumData.length > 0
        }
      })
    },

    handleDataZoom(params) {
      this.$emit('zoom', {
        start: params.start,
        end: params.end
      })
    },

    handleResize() {
      this.chart && this.chart.resize()
    },

    handleFullScreen() {
      this.$emit('fullscreen', 'spectrum')
    }
  },

  mounted() {
    this.initChart()
  },

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="scss" scoped>
.spectrum-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .chart-header {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;

    .title {
      font-size: 12px;
      margin: 0;
      font-weight: bolder;
      color: #303133;
    }

    .actions {
      i {
        font-size: 16px;
        color: #606266;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          color: #409EFF;
          background-color: #ecf5ff;
        }
      }
    }
  }

  .chart-container {
    flex: 1;
    width: 100%;
  }
}
</style>
