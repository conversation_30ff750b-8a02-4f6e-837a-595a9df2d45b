<!-- 图表切换 -->
<template>
  <div class="chart-switcher">
    <!-- 图表切换器标签 -->
    <a-tooltip
      v-for="(button, index) in buttons"
      :key="index"
      :placement="button.placement"
      :title="button.title"
      overlay-class-name="bgc_tooltip"
    >
      <div class="icon-container">
        <a-button class="header-button" @click="button.onClick">
          <svg-icon
            :icon-class="button.iconClass"
            :class="{ 'active-icon': activeChart === button.chartType }"
          />
        </a-button>
      </div>
    </a-tooltip>
  </div>
</template>

<script>
// 引入svg按钮标签
import { getChartButtons } from './ChartButtons'
import AlarmDialogContent from '../AlarmDialogContent/AlarmDialogContent.vue'
export default {
  components:{
    AlarmDialogContent
  },
  props: {
    // 接收父组件传递的当前图表类型
    initialChartType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 分别是工具提示框文本，svg标签，工具提示框出现的位置
      buttons: [],
      showDialog: false,
      activeChart: this.initialChartType // 初始化为父组件传递的图表类型
    }
  },
  created() {
    this.buttons = getChartButtons(this.switchChart)
  },
  methods: {
    switchChart(chartType) {
      this.activeChart = chartType // 更新当前激活的图表类型
      this.$emit('chart-change', chartType) // 触发自定义事件，将图表类型传递给父组件
    }
  },
  // 监听props变化
  watch: {
    initialChartType(newVal) {
      this.activeChart = newVal
    }
  }
}
</script>

<style scoped>
@import "./ChartSwitcher.css";

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  width: 50px;
  margin: 0 2px;
}

.header-button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  width: 40px;
}

.svg-icon {
  width: 2em;
  height: 2em;
  transition: all 0.3s ease;
  position: relative;
}

.active-icon {
  transform: scale(1.5);
  color: #1890ff;
  filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.5));
  animation: iconPulse 1.5s infinite alternate;
}

@keyframes iconPulse {
  0% {
    filter: drop-shadow(0 0 2px rgba(24, 144, 255, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(24, 144, 255, 0.8));
  }
}
</style>
