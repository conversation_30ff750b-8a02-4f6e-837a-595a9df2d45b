<template>
  <div class="grading-component">
    <h2>厂区详情</h2>
    <p>这里展示与厂区相关的信息。</p>
    <p>厂区等级: {{ nodeData.level }}</p>
    <p>厂区面积: {{ nodeData.area }} 平方米</p>
  </div>
</template>

<script>
export default {
  props: {
    nodeData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.grading-component {
  padding: 20px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
}
</style>
