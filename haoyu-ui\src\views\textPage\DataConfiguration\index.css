.container1 {
  display: flex;
  flex-direction: column;
  height: 100%; /* 设置整个容器的高度为视口高度 */
  border: 2px solid #dcdcdc; /* 边框颜色 */
  border-radius: 8px 0 0 8px; /* 左边圆角，右边无圆角 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  padding: 1px; /* 内部填充 */
  background-color: #fff; /* 背景颜色 */
}
/* .left-sidebar {
  width: 200px;
  border-right: 2px solid #dcdcdc;
  border-radius: 0;
} */
.half-height {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* .other-area {
  border-top: 2px solid #dcdcdc;
  background-color: #f0f0f0;
  border-radius: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
 */
/* 自定义滚动条样式 */
.half-height::-webkit-scrollbar {
  width: 8px; /* 设置滚动条宽度 */
  height: 8px; /* 设置滚动条高度 */
}

.half-height::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 设置滚动条颜色 */
  border-radius: 4px; /* 设置滚动条圆角 */
}

.half-height::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1); /* 设置滚动条轨道颜色 */
}
/* .resizer {
  width: 8px;
  background: #ccc;
  cursor: ew-resize;
  height: 100%;
} */

