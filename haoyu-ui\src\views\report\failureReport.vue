<template>
  <div class="failure-report">
    <el-card class="report-card">
      <div class="card-header" slot="header">
        <div class="header-content">
          <i class="el-icon-warning"></i>
          <span>故障报告</span>
        </div>
      </div>
      
      <el-form :model="reportForm" :rules="rules" ref="reportForm" label-width="100px">
        <!-- 报告依据 -->
        <el-form-item label="报告依据" prop="basisFiles">
          <el-upload
            class="upload-area"
            action="#"
            :show-file-list="true"
            :before-upload="handleBasisFileUpload"
            :file-list="reportForm.basisFiles"
            :on-remove="handleBasisFileRemove"
            multiple>
            <el-button type="primary">上传文件</el-button>
            <div slot="tip" class="el-upload__tip">可以上传多个依据文件</div>
          </el-upload>
        </el-form-item>

        <!-- 报告类型 -->
        <el-form-item label="报告类型" prop="type">
          <el-select
            v-model="reportForm.type"
            placeholder="请选择或添加报告类型"
            class="form-select"
            allow-create
            filterable
            default-first-option>
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 评论 -->
        <el-form-item label="评论" prop="comment">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入报告评论"
            v-model="reportForm.comment"
            class="form-textarea">
          </el-input>
        </el-form-item>

        <!-- 上传报告 -->
        <el-form-item label="上传报告" prop="files" class="upload-report-section">
          <el-upload
            class="upload-area"
            action="#"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :on-change="handleChange"
            :file-list="reportForm.files"
            :auto-upload="false"
            multiple>
            <el-button type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持上传pdf、word、excel等文件</div>
          </el-upload>
        </el-form-item>

        <el-form-item class="form-buttons">
          <el-button type="primary" @click="submitForm('reportForm')">提交报告</el-button>
          <el-button @click="resetForm('reportForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
// import { Client } from "@gradio/client";
// import { Client } from "https://cdn.jsdelivr.net/npm/@gradio/client/dist/index.min.js";

export default {
  name: "failureReport",
  data() {
    return {
      reportForm: {
        basisFiles: [],
        type: '',
        comment: '',
        files: []
      },
      typeOptions: [
        { value: 'urgent', label: '紧急报告' },
        { value: 'regular', label: '常规报告' },
        { value: 'followup', label: '跟踪报告' }
      ],
      rules: {
        basisFiles: [
          { type: 'array', required: true, message: '请上传报告依据文件', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择或输入报告类型', trigger: 'change' }
        ],
        comment: [
          { required: true, message: '请输入评论', trigger: 'blur' },
          { min: 10, message: '评论长度不能少于10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleBasisFileUpload(file) {
      this.reportForm.basisFiles.push(file);
      return false; // 阻止自动上传
    },
    handleBasisFileRemove(file, fileList) {
      this.reportForm.basisFiles = fileList;
    },

    async submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {


        try {
          // 连接Gradio服务
          const client = await Client.connect("http://10.147.20.131:42345/", {
            timeout: 9999
          });

          // 处理文件上传（假设第一个上传组件是要上传的图片）
          if (this.reportForm.basisFiles.length === 0) {
            throw new Error("请先上传依据文件");
          }

          // 转换文件为Gradio需要的格式
          const basisFile = this.reportForm.basisFiles[0].raw;
          
          // 第一步：上传图片文件
          await client.predict("/update", [
            { name: basisFile.name, data: basisFile, is_file: true }
          ]);

          // 第二步：上传视频路径（根据实际情况调整）
          // 注意：浏览器环境下不能直接访问本地路径，需要实际文件上传
          // 这里假设第二个上传组件是视频文件
          if (this.reportForm.files.length === 0) {
            throw new Error("请先上传报告文件");
          }

          const videoFile = this.reportForm.files[0].raw;
          await client.predict("/update_1", [
            { name: videoFile.name, data: videoFile, is_file: true }
          ]);

          // 第三步：执行并获取结果
          const [_, video, qrcode] = await client.predict("/run");

          // 处理返回结果
          console.log("处理结果:", {
            video: video.value.video,
            qrcode: qrcode.value
          });

          this.$message.success('报告提交成功');
          this.$emit('submit-success', {
            video: video.value.video,
            qrcode: qrcode.value
          });

        } catch (error) {
          console.error('提交失败:', error);
          this.$message.error(`提交失败: ${error.message}`);
        }
      });
      
      if (!valid) {
          this.$message.error('请完善表单信息');
          return false;
        }
    },
  
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.reportForm.basisFiles = [];
    },
    handleRemove(file, fileList) {
      this.reportForm.files = fileList;
    },
    handlePreview(file) {
      // TODO: 实现文件预览逻辑
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleChange(file, fileList) {
      this.reportForm.files = fileList;
    }
  }
}
</script>

<style scoped>
.failure-report {
  padding: 20px;
}

.report-card {
  background-color: #fff;
}

.card-header {
  border-bottom: 2px solid #dcdfe6;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.header-content i {
  font-size: 20px;
  color: #409EFF;
  margin: 0 10px;
}

.header-content span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-select,
.form-textarea {
  width: 100%;
  max-width: 400px;
}

.upload-area {
  width: 100%;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.form-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: flex-start;
}

.form-buttons .el-button {
  margin-right: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-textarea__inner) {
  min-height: 120px;
}

:deep(.el-upload-list) {
  margin-top: 10px;
}
</style>
