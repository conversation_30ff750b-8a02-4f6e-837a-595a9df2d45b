{"name": "<PERSON><PERSON><PERSON>", "version": "3.8.2", "description": "昊宇检测", "author": "昊宇", "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/Ruoyi-Vue.git"}, "dependencies": {"@gradio/client": "^1.10.0", "@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "0.4.0", "ant-design-vue": "^1.7.2", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.19.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "element-ui": "2.15.8", "file-saver": "^2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "hotkeys-js": "^3.13.7", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "konva": "^9.3.14", "nprogress": "0.2.0", "pinyin-pro": "^3.24.2", "quill": "1.3.7", "Ruoyi": "file:", "screenfull": "5.0.2", "sortablejs": "1.10.2", "systeminformation": "^5.27.6", "three": "^0.168.0", "vue": "2.6.12", "vue-class-component": "^7.2.6", "vue-contextmenujs": "^1.4.11", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-draggable-resizable": "^2.3.0", "vue-easytable": "^2.27.1", "vue-meta": "2.4.0", "vue-property-decorator": "^9.1.2", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "^1.0.6", "vue2-editor": "^2.10.3", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.26.7", "@types/vue": "^1.0.31", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-loader": "^8.2.5", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}