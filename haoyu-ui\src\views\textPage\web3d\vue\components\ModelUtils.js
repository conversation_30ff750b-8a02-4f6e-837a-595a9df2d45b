import * as THREE from 'three';
import { OBJExporter } from 'three/examples/jsm/exporters/OBJExporter.js';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';

/**
 * 合并模型
 * @param {Array} models 要合并的模型数组
 * @param {Object} options 配置选项
 * @returns {THREE.Mesh} 合并后的网格
 */
export function mergeModels(models, options = {}) {
    const geometries = [];
    models.forEach((model) => {
        model.updateMatrixWorld(true);
        model.traverse((child) => {
            if (child.isMesh) {
                const geometry = child.geometry.clone();
                // 可选地删除UV和法线属性
                if (options.removeUV && geometry.attributes.uv) {
                    delete geometry.attributes.uv;
                }
                if (options.removeNormal && geometry.attributes.normal) {
                    delete geometry.attributes.normal;
                }
                geometry.applyMatrix4(child.matrixWorld);
                geometries.push(geometry);
            }
        });
    });

    if (geometries.length === 0) {
        console.error('没有找到可合并的几何体');
        return null;
    }

    // 合并几何体
    const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
    const material = new THREE.MeshStandardMaterial({
        color: options.color || 0xffffff,
        roughness: options.roughness || 0.3,
        metalness: options.metalness || 0.8,
        transparent: options.opacity < 1,
        opacity: options.opacity || 1
    });

    return new THREE.Mesh(mergedGeometry, material);
}

/**
 * 导出模型为OBJ格式
 * @param {THREE.Object3D} model 要导出的模型
 * @returns {String} OBJ格式的字符串
 */
export function exportToOBJ(model) {
    const exporter = new OBJExporter();
    return exporter.parse(model);
}

/**
 * 下载模型为OBJ文件
 * @param {THREE.Object3D} model 要下载的模型
 * @param {String} filename 文件名
 */
export function downloadOBJ(model, filename = 'model.obj') {
    const objData = exportToOBJ(model);
    const blob = new Blob([objData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(url), 100);
}

/**
 * 复制模型
 * @param {THREE.Object3D} model 要复制的模型
 * @param {Object} options 配置选项
 * @returns {THREE.Object3D} 复制后的模型
 */
export function cloneModel(model, options = {}) {
    const clone = model.clone();

    // 复制材质
    clone.traverse((child) => {
        if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
                child.material = child.material.map(m => m.clone());
            } else {
                child.material = child.material.clone();
            }
        }
    });

    // 应用位置偏移
    if (options.offset) {
        clone.position.x += options.offset.x || 0;
        clone.position.y += options.offset.y || 0;
        clone.position.z += options.offset.z || 0;
    }

    // 更新名称
    if (options.nameSuffix) {
        clone.name = `${model.name || '未命名模型'}${options.nameSuffix}`;
        clone.traverse((child) => {
            child.name = clone.name;
        });
    }

    return clone;
}

/**
 * 更改模型颜色
 * @param {THREE.Object3D} model 要更改颜色的模型
 * @param {String} color 颜色字符串，可以是hex或rgba格式
 * @param {Number} opacity 透明度，0-1之间的值
 */
export function changeModelColor(model, color, opacity = 1) {
    let threeColor;

    // 处理rgba格式
    if (color.startsWith('rgba')) {
        const matches = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        if (matches) {
            const [_, r, g, b, a] = matches;
            threeColor = new THREE.Color(
                parseInt(r) / 255,
                parseInt(g) / 255,
                parseInt(b) / 255
            );
            opacity = parseFloat(a);
        } else {
            threeColor = new THREE.Color(color);
        }
    } else {
        // 处理hex格式或其他颜色格式
        threeColor = new THREE.Color(color);
    }

    model.traverse((child) => {
        if (child.isMesh) {
            if (Array.isArray(child.material)) {
                child.material.forEach(material => {
                    material.color = threeColor;
                    material.opacity = opacity;
                    material.transparent = opacity < 1;
                    material.needsUpdate = true;
                });
            } else if (child.material) {
                child.material.color = threeColor;
                child.material.opacity = opacity;
                child.material.transparent = opacity < 1;
                child.material.needsUpdate = true;
            }
        }
    });
}