import { getTrend } from '@/views/textPage/ChartComponents/TrendChart/getTrendData'

class DataManager {
  constructor() {
    this.cache = new Map() // 数据缓存
    this.pendingRequests = new Map() // 正在进行的请求
    this.requestQueue = new Map() // 请求队列，防止重复请求
  }

  /**
   * 获取趋势数据（带缓存和去重）
   * @param {string} nodeId - 节点ID
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Object>} 趋势数据
   */
  async getTrendData(nodeId, forceRefresh = false) {
    if (!nodeId) {
      return { hasData: false, data: null }
    }

    const cacheKey = `trend_${nodeId}`

    // 如果不强制刷新且缓存中有数据，直接返回
    if (!forceRefresh && this.cache.has(cacheKey)) {
      const cachedData = this.cache.get(cacheKey)
      console.log(`📦 使用缓存的趋势数据: ${nodeId}`)
      return cachedData
    }

    // 如果已有相同的请求在进行中，等待该请求完成
    if (this.pendingRequests.has(cacheKey)) {
      console.log(`⏳ 等待进行中的趋势数据请求: ${nodeId}`)
      return await this.pendingRequests.get(cacheKey)
    }

    // 创建新的请求
    const requestPromise = this.fetchTrendData(nodeId, cacheKey)
    this.pendingRequests.set(cacheKey, requestPromise)

    try {
      const result = await requestPromise
      return result
    } finally {
      // 请求完成后从待处理列表中移除
      this.pendingRequests.delete(cacheKey)
    }
  }

  /**
   * 实际获取趋势数据的方法
   * @private
   */
  async fetchTrendData(nodeId, cacheKey) {
    console.log(`🌐 发起趋势数据请求: ${nodeId}`)

    try {
      const trendResponse = await getTrend(nodeId)

      const hasData = trendResponse.data &&
                     trendResponse.data.x &&
                     trendResponse.data.y &&
                     trendResponse.data.x.length > 0 &&
                     trendResponse.data.y.length > 0

      const result = {
        hasData,
        data: trendResponse.data,
        timestamp: Date.now()
      }

      // 缓存结果（设置5分钟过期时间）
      this.cache.set(cacheKey, result)

      // 设置缓存过期时间
      setTimeout(() => {
        this.cache.delete(cacheKey)
        console.log(`🗑️ 清理过期缓存: ${nodeId}`)
      }, 5 * 60 * 1000) // 5分钟

      console.log(`✅ 趋势数据请求完成: ${nodeId}, 有数据: ${hasData}`)
      return result

    } catch (error) {
      console.error(`❌ 趋势数据请求失败: ${nodeId}`, error)
      return { hasData: false, data: null, error }
    }
  }

  /**
   * 检查节点是否有趋势数据（轻量级检查）
   * @param {string} nodeId - 节点ID
   * @returns {Promise<boolean>} 是否有数据
   */
  async hasTrendData(nodeId) {
    const result = await this.getTrendData(nodeId)
    return result.hasData
  }

  /**
   * 预加载多个节点的趋势数据
   * @param {Array<string>} nodeIds - 节点ID数组
   */
  async preloadTrendData(nodeIds) {
    console.log(`🚀 预加载趋势数据:`, nodeIds)

    const promises = nodeIds.map(nodeId =>
      this.getTrendData(nodeId).catch(error => {
        console.warn(`预加载失败: ${nodeId}`, error)
        return null
      })
    )

    try {
      await Promise.all(promises)
      console.log(`✅ 预加载完成`)
    } catch (error) {
      console.error(`❌ 预加载部分失败:`, error)
    }
  }

  /**
   * 清理指定节点的缓存
   * @param {string} nodeId - 节点ID
   */
  clearCache(nodeId) {
    const cacheKey = `trend_${nodeId}`
    this.cache.delete(cacheKey)
    console.log(`🧹 清理缓存: ${nodeId}`)
  }

  /**
   * 清理所有缓存
   */
  clearAllCache() {
    this.cache.clear()
    console.log(`🧹 清理所有缓存`)
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      cachedKeys: Array.from(this.cache.keys())
    }
  }

  /**
   * 批量获取多个节点的趋势数据状态
   * @param {Array<string>} nodeIds - 节点ID数组
   * @returns {Promise<Object>} 节点数据状态映射
   */
  async getBatchTrendDataStatus(nodeIds) {
    const results = {}

    // 并发请求所有节点数据
    const promises = nodeIds.map(async (nodeId) => {
      try {
        const result = await this.getTrendData(nodeId)
        results[nodeId] = result.hasData
      } catch (error) {
        results[nodeId] = false
      }
    })

    await Promise.all(promises)
    return results
  }
}

// 创建单例实例
const dataManager = new DataManager()

export default dataManager
