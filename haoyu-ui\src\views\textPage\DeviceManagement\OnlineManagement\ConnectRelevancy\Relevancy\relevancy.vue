<template>
  <div class="table-relevancy-container">
    <h3>采集站 - 设备关联</h3>
    <el-dropdown size="mini" @command="handleCommand">
      <el-button type="primary" size="mini">
        添加传感器<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="importExcel">导入 Excel</el-dropdown-item>
        <el-dropdown-item command="importWord">导入 Word</el-dropdown-item>
        <el-dropdown-item command="batchAdd">批量添加传感器</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-table :data="tableData" border stripe>
      <el-table-column prop="deviceCoding" label="采集站名称" />
      <el-table-column prop="deviceTypeName" label="采集站类型" />
      <!-- <el-table-column prop="serverName" label="服务器名称" /> -->
      <el-table-column prop="deviceMoitor" label="检测设备" />
    </el-table>
    <!-- 用于上传的组件 -->
    <el-upload
      ref="uploadExcel"
      action="/api/upload/excel"
      :show-file-list="false"
      accept=".xlsx, .xls"
      :auto-upload="true"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
    >
      <!-- <input ref="inputExcel" type="file" style="display: none"> -->
    </el-upload>
    <el-upload
      ref="uploadWord"
      action="/api/upload/word"
      :show-file-list="false"
      accept=".doc, .docx"
      :auto-upload="true"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
    />
  </div>
</template>

<script>
import { getBaseStationList } from '@/api/haoyu-system/OnlineManagement/BaseStationTree'
import { mapGetters } from 'vuex'

export default {
  name: 'DefaultTable',
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    ...mapGetters('SettingConnect', ['companyId']) // 获取公司选中的id
  },
  watch: {
    companyId(newVal, oldVal) {
      // 重新获取更换的公司表格信息
      this.getTableList()
    }
  },
  created() {
    setTimeout(() => { this.getTableList }, 10)
    // this.getTableList()
  },
  methods: {
    handleCommand(command) {
      if (command === 'importExcel') {
        this.$refs.uploadExcel.$el.querySelector('input[type="file"]').click()
        /* this.$refs.inputExcel.click() // 触发 Excel 文件上传 */
      } else if (command === 'importWord') {
        this.$refs.uploadWord.$el.querySelector('input[type="file"]').click()
        /* this.$refs.inputWord.click() // 触发 Word 文件上传 */
      } else if (command === 'batchAdd') {
        this.batchAddSensors()
      }
    },
    importExcel() {
      // 实现导入 Excel 的逻辑
      console.log('导入 Excel')
    },
    importWord() {
      // 实现导入 Word 的逻辑
      console.log('导入 Word')
    },
    batchAddSensors() {
      // 实现批量添加传感器的逻辑
      console.log('批量添加传感器')
    },
    handleUploadSuccess(response, file) {
    // 处理上传成功的逻辑
      console.log('文件上传成功', response)
      // 这里可以处理返回的数据，更新表格或给用户提示
    },
    handleUploadError(err, file) {
      // 处理上传失败的逻辑
      console.error('文件上传失败', err)
    },
    // 获取表格数据
    getTableList() {
      console.log('companyId:', this.companyId) // 输出当前的公司ID
      getBaseStationList(this.companyId).then(res => {
        this.tableData = res.data
        console.log('响应数据：()=>',res.data)

      })
    }
  }
}
</script>

<style scoped>
.table-relevancy-container {
  height: 400px;
  width: 100%;
  overflow-y: auto; /* 当内容超出时，显示滚动条 */
  background-color: #f8f9fa; /* 更明亮的背景色 */
  padding: 20px; /* 增加内边距 */
  border-radius: 8px; /* 圆角边框 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 适度阴影效果 */
  box-sizing: border-box;
}

h3 {
  color: #007bff; /* 标题颜色，选择亮蓝色 */
  font-size: 18px; /* 标题字体大小 */
  font-weight: bold; /* 标题加粗 */
  margin-bottom: 15px; /* 标题和表格之间的距离 */
  border-bottom: 2px solid #007bff; /* 标题下方的分隔线 */
  padding-bottom: 8px;
}

.el-table {
  width: 100%;
  background-color: #ffffff; /* 表格背景色使用白色 */
  color: #495057; /* 表格文本颜色 */
  border-radius: 6px; /* 表格边框圆角 */
}

.el-table .el-table__header-wrapper {
  background-color: #e9ecef; /* 表头背景色 */
  color: #495057; /* 表头文字颜色 */
  font-weight: bold; /* 表头文字加粗 */
}

.el-table-column {
  text-align: center; /* 内容居中 */
}

.el-table-column .cell {
  padding: 10px; /* 增加单元格内边距 */
  border-bottom: 1px solid #dee2e6; /* 表格行之间的分隔线颜色 */
}

.el-table .el-table__body-wrapper {
  border-radius: 0 0 6px 6px; /* 表格底部圆角 */
}

.el-table .el-table__body {
  background-color: #ffffff; /* 表格内容背景色 */
}

.el-table .el-table__row:hover {
  background-color: #e9ecef !important; /* 行悬停颜色 */
  color: #495057;
}
</style>
