// src/store/modules/selectedRowModule.js
const state = {
  selectedRow: null // 存储选中的表格行数据
}

const mutations = {
  // 设置选中的表格行数据
  SET_SELECTED_ROW(state, row) {
    state.selectedRow = row
  },
  // 清除选中的表格行数据
  CLEAR_SELECTED_ROW(state) {
    state.selectedRow = null
  }
}

const actions = {
  // Action: 设置选中的表格行数据
  setSelectedRow({ commit }, row) {
    commit('SET_SELECTED_ROW', row)
  },
  // Action: 清除选中的表格行数据
  clearSelectedRow({ commit }) {
    commit('CLEAR_SELECTED_ROW')
  }
}

const getters = {
  // Getter: 获取选中的表格行数据
  selectedRow: state => state.selectedRow
}

export default {
  namespaced: true, // 启用命名空间
  state,
  mutations,
  actions,
  getters
}
