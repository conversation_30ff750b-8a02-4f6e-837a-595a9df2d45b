export const getMenuItems = (handleMenuSelect) => [{
        index: '1',
        label: '轴',
        icon: 'axis', // 对应的图标
        method: () => handleMenuSelect('轴', 'axis'),
        disabled: false
    },
    {
        index: '2',
        label: '齿轮箱',
        icon: 'gearbox', // 对应的图标
        disabled: false,
        children: [{
                index: '2-1',
                label: '行星轴系',
                disabled: false,
                icon: 'planetaryShaftSys', // 对应的图标
                children: [{
                        index: '2-1-1',
                        label: '行星架输入',
                        icon: 'planetaryShaftSys', // 对应的图标
                        method: () => handleMenuSelect('行星架输入', 'planetaryShaftSys', '1-1')
                    },
                    {
                        index: '2-1-2',
                        label: '太阳轮输入',
                        icon: 'planetaryShaftSys', // 对应的图标
                        method: () => handleMenuSelect('太阳轮输入', 'planetaryShaftSys', '1-2')
                    },
                ]
            },
            {
                index: '2-2',
                label: '平行轴系',
                icon: 'parallelShafting', // 对应的图标
                children: [{
                        index: '2-2-1',
                        label: '一级平行',
                        icon: 'parallelShafting', // 对应的图标
                        method: () => handleMenuSelect('NGW型(行星架输入, 太阳轮输出)', 'parallelShafting', '2-1')
                    },
                    {
                        index: '2-2-2',
                        label: '二级平行',
                        icon: 'parallelShafting', // 对应的图标
                        method: () => handleMenuSelect('NW型(内齿圈输入, 太阳轮输出)', 'parallelShafting', '2-2')
                    },
                    {
                        index: '2-2-3',
                        label: '三级平行',
                        icon: 'parallelShafting', // 对应的图标
                        method: () => handleMenuSelect('M2W1型(内齿圈输入, 太阳轮输出)', 'parallelShafting', '2-3')
                    },
                    {
                        index: '2-2-4',
                        label: '四级平行',
                        icon: 'parallelShafting', // 对应的图标
                        method: () => handleMenuSelect('M2W2型(行星架和内齿圈输入, 太阳轮输出)', 'parallelShafting', '2-4')
                    }
                ]
            }
        ]
    },
    {
        index: '3',
        label: '轴承',
        icon: 'bearing', // 对应的图标
        method: () => handleMenuSelect('轴承', 'bearing'),
        disabled: true
    },
    {
        index: '4',
        label: '叶片',
        icon: 'blade', // 对应的图标
        method: () => handleMenuSelect('叶片', 'blade'),
        disabled: true
    },
    {
        index: '5',
        label: '塔筒',
        icon: 'towers', // 对应的图标
        method: () => handleMenuSelect('塔筒', 'towers'),
        disabled: false
    },
    {
        index: '6',
        label: '发电机',
        icon: 'dynamo', // 对应的图标
        method: () => handleMenuSelect('发电机', 'dynamo'),
        disabled: false
    },
    {
        index: '7',
        label: '电机',
        icon: 'motor', // 对应的图标
        method: () => handleMenuSelect('电机', 'motor'),
        disabled: false
    },
    {
        index: '8',
        label: '泵',
        icon: 'Pump', // 对应的图标
        method: () => handleMenuSelect('泵', 'Pump'),
        disabled: false
    },
    {
        index: '9',
        label: '风机',
        icon: 'fans', // 对应的图标
        method: () => handleMenuSelect('风机', 'fans'),
        disabled: false
    },
    {
        index: '10',
        label: '皮带和皮带轮',
        icon: 'pulleys', // 对应的图标
        disabled: false,
        method: () => handleMenuSelect('皮带和皮带轮', 'pulleys')
    },
    {
        index: '11',
        label: '联轴器',
        icon: 'coupling', // 对应的图标
        disabled: false,
        method: () => handleMenuSelect('联轴器', 'coupling')
    },
    {
        index: '12',
        label: '辊子',
        icon: 'roller', // 对应的图标
        disabled: false,
        method: () => handleMenuSelect('辊子', 'roller')
    }
]