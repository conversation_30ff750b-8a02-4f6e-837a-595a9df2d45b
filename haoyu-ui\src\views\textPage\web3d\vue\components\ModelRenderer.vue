<template>
  <div ref="objViewer" class="obj-viewer"></div>
</template>

<script>
import * as THREE from 'three';
import { ConvexGeometry } from 'three/examples/jsm/geometries/ConvexGeometry.js';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { OBJExporter } from 'three/examples/jsm/exporters/OBJExporter.js';

export default {
  name: 'ModelRenderer',
  
  props: {
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    groundColor: {
      type: String,
      default: '#ffffff'
    }
  },
  
  data() {
    return {
      scene: null,
      renderer: null,
      camera: null,
      controls: null,
      animationFrameId: null,
      groundTexture: null,
      backgroundTexture: null,
      backgroundMesh: null,
    };
  },

  mounted() {
    this.initScene();
    window.addEventListener('resize', this.onWindowResize);
  },

  beforeDestroy() {
    this.cleanupResources();
  },

  methods: {
    initScene() {
      try {
        const container = this.$refs.objViewer;
        if (!container) {
            console.error('容器元素不存在');
            return false;
        }
        
        // 创建场景 - 极简配置
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(this.backgroundColor);
        
        // 设置相机 - 保持简单
        const aspectRatio = container.offsetWidth / container.offsetHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspectRatio, 0.1, 1000);
        this.camera.position.set(0, 2, 5);
        
        // 简化渲染器配置
        try {
          this.renderer = new THREE.WebGLRenderer({ 
              antialias: true,
              powerPreference: 'high-performance' // 提示浏览器使用高性能GPU
          });
          
          // 更简洁的渲染器设置
          this.renderer.setPixelRatio(1); // 固定像素比为1，提高性能
          this.renderer.setSize(container.offsetWidth, container.offsetHeight);
          this.renderer.shadowMap.enabled = false; // 关闭阴影以提高性能
          
          container.appendChild(this.renderer.domElement);
        } catch (renderError) {
          console.error('初始化渲染器失败:', renderError);
          this.scene = null;
          return false;
        }
        
        // 极简光照
        this.addBasicLighting();
        
        // 简化的地面
        this.addSimpleGround();
        
        // 控制器 - 仅保留必要参数
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.update();
        
        // 初始渲染
        this.renderer.render(this.scene, this.camera);
        
        // 初始化完成后启动动画循环
        this.startAnimationLoop();
        
        return true;
      } catch (error) {
        console.error('初始化场景失败:', error);
        // 重置所有状态
        this.scene = null;
        this.camera = null;
        this.controls = null;
        if (this.renderer) {
          this.renderer.dispose();
          this.renderer = null;
        }
        return false;
      }
    },
    
    // 替换复杂光照系统为最简单的光照
    addBasicLighting() {
      // 仅添加环境光和一个方向光，不产生阴影
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
      this.scene.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(5, 10, 7.5);
      this.scene.add(directionalLight);
    },
    
    // 使用简化的地面实现
    addSimpleGround() {
      const planeGeometry = new THREE.PlaneGeometry(100, 100);
      const planeMaterial = new THREE.MeshBasicMaterial({ 
        color: new THREE.Color(this.groundColor),
        side: THREE.DoubleSide
      });
      
      const plane = new THREE.Mesh(planeGeometry, planeMaterial);
      plane.rotation.x = -Math.PI / 2;
      plane.position.y = -1;
      plane.isGround = true;
      this.scene.add(plane);
    },
    
    startAnimationLoop() {
      // 简化动画循环，去除帧率控制
      const animate = () => {
        // 仅在需要时更新场景
        if (this.controls && this.scene && this.camera) {
          // 告知父组件即将渲染
          this.$emit('before-render');
          
          // 更新控制器
          this.controls.update();
          
          // 渲染场景
          this.renderer.render(this.scene, this.camera);
        }
        
        // 请求下一帧
        this.animationFrameId = requestAnimationFrame(animate);
      };
      
      // 启动动画循环
      this.animationFrameId = requestAnimationFrame(animate);
    },
    
    onWindowResize() {
      const container = this.$refs.objViewer;
      if (!container) return;
      
      this.camera.aspect = container.offsetWidth / container.offsetHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(container.offsetWidth, container.offsetHeight);
    },
    
    addModelToScene(model) {
      if (!this.scene) {
        console.error('场景未初始化，无法添加模型');
        throw new Error('场景未初始化，请稍后再试');
        return;
      }
      this.scene.add(model);
      this.$emit('model-added', model);
    },
    
    loadObjModel(url) {
      return new Promise((resolve, reject) => {
        if (!this.scene) {
          console.error('场景未初始化，无法加载模型');
          reject(new Error('场景未初始化，请稍后再试'));
          return;
        }
        
        const loader = new OBJLoader();
        loader.load(
          url,
          (object) => {
            resolve(object);
          },
          (xhr) => {
            // 进度回调
          },
          (error) => {
            // console.error('模型加载失败:', error);
            reject(error);
          }
        );
      });
    },
    
    changeBackgroundColor(color) {
      if (this.scene) {
        this.scene.background = new THREE.Color(color);
      }
    },
    
    changeGroundColor(color) {
      if (!this.scene) return;
      
      // 解析颜色中的透明度
      let opacity = 1;
      if (color.startsWith('rgba')) {
        const matches = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
        if (matches) {
          opacity = parseFloat(matches[4]);
        }
      }

      // 查找现有地面或创建新地面
      let groundExists = false;
      this.scene.traverse((object) => {
        if (object.isGround) {
          groundExists = true;
          object.material.color = new THREE.Color(color);
          object.material.transparent = opacity < 1;
          object.material.opacity = opacity;
          // 保持纹理不变
          if (this.groundTexture) {
            object.material.map = this.groundTexture;
          }
          object.material.needsUpdate = true;
        }
      });

      // 如果不存在地面且透明度不为0，创建新地面
      if (!groundExists && opacity > 0) {
        const planeGeometry = new THREE.PlaneGeometry(100, 100);
        const planeMaterial = new THREE.MeshBasicMaterial({ 
          color: new THREE.Color(color),
          side: THREE.DoubleSide,
          transparent: opacity < 1,
          opacity: opacity
        });

        if (this.groundTexture) {
          planeMaterial.map = this.groundTexture;
        }

        const plane = new THREE.Mesh(planeGeometry, planeMaterial);
        plane.rotation.x = -Math.PI / 2;
        plane.position.y = -1;
        plane.receiveShadow = true;
        plane.isGround = true;
        this.scene.add(plane);
      }
    },
    
    applyGroundTexture(texture) {
      this.groundTexture = texture;
      
      // 应用纹理到地面
      this.scene.traverse((object) => {
        if (object.isGround) {
          object.material.map = texture;
          object.material.needsUpdate = true;
        }
      });
    },
    
    applyBackgroundTexture(texture) {
      this.backgroundTexture = texture;
      
      // 如果已有背景网格，更新它
      if (this.backgroundMesh) {
        this.backgroundMesh.material.map = texture;
        this.backgroundMesh.material.needsUpdate = true;
      } else {
        // 创建新的背景球体
        const geometry = new THREE.SphereGeometry(500, 60, 40);
        geometry.scale(-1, 1, 1);
        const material = new THREE.MeshBasicMaterial({ map: texture });
        this.backgroundMesh = new THREE.Mesh(geometry, material);
        this.scene.add(this.backgroundMesh);
      }
      
      // 使用纹理时，需要清除纯色背景
      this.scene.background = null;
    },
    
    exportObjData(mesh) {
      const exporter = new OBJExporter();
      return exporter.parse(mesh);
    },
    
    cleanupResources() {
      window.removeEventListener('resize', this.onWindowResize);
      
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
      
      if (this.scene) {
        this.scene.traverse((obj) => {
          if (obj.isMesh) {
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) {
              if (Array.isArray(obj.material)) {
                obj.material.forEach(m => m.dispose());
              } else {
                obj.material.dispose();
              }
            }
          }
        });
        this.scene = null;
      }
      
      if (this.renderer) {
        this.renderer.dispose();
        this.renderer.forceContextLoss();
        this.renderer.domElement = null;
        this.renderer = null;
      }
      
      this.camera = null;
      this.controls = null;
      
      if (this.groundTexture) {
        this.groundTexture.dispose();
        this.groundTexture = null;
      }
      
      if (this.backgroundTexture) {
        this.backgroundTexture.dispose();
        this.backgroundTexture = null;
      }
    }
  }
};
</script>

<style scoped>
.obj-viewer {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}
</style> 