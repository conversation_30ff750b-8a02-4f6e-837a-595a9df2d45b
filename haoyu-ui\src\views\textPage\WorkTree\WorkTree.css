.a-input {
  width: auto
}
.tree-container {
  border-radius: 4px;
  padding: 10px;
}

.custom-tree .ant-tree {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
}

.custom-tree .ant-tree-treenode {
  padding-left: 16px; /* 一级和二级节点之间的间隙 */
}

.custom-tree {
  max-width: 100%; /* 设置最大宽度 */
  max-height: 100%; /* 设置最大高度 */
}


.custom-tree .ant-tree-node-content-wrapper {
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.custom-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f5f5f5;
}
.form-container {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 设置每个表单项之间的间距 */
}

.form-item {
  display: flex;
  align-items: center;
}

.form-item label {
  width: 150px; /* 设置标签的宽度 */
  margin-right: 10px; /* 设置标签和输入框之间的间距 */
  text-align: right; /* 使标签右对齐 */
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px; /* 设置底部按钮和表单之间的间距 */
}
/* 自定义滚动条样式 */
.ant-tree::-webkit-scrollbar {
  width: 8px; /* 设置滚动条宽度 */
  height: 8px; /* 设置滚动条高度 */
}

.ant-treet::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 设置滚动条颜色 */
  border-radius: 4px; /* 设置滚动条圆角 */
}

.ant-tree::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1); /* 设置滚动条轨道颜色 */
}

.tree-container .ant-tree{
  height: 100%;
}

.tree-container {
  display: flex;
  align-items: flex-start;
  height: 100%;
  max-height: 410px;
  width: 100%;
}

.tree-icon {
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 300px;

}

.search-container .ant-input {
  margin-right: 10px; /* 搜索框与按钮之间的间距 */
}

.search-container .ant-btn {
  margin-left: 10px; /* 按钮与输入框之间的间距 */
}


.ant-tree-show-line .ant-tree-switcher.ant-tree-switcher-noop:before {
  content: " ";
  width: 16px;
  border-bottom: 1px solid #d9d9d9;
  height: 50%;
  position: absolute;
  left: 12px;
}

.ant-tree-show-line .ant-tree-child-tree>li:last-child>.ant-tree-switcher.ant-tree-switcher-noop:before, .ant-tree-show-line>li:last-child>.ant-tree-switcher.ant-tree-switcher-noop:before {
  display: block;
  content: " " !important;
  width: 16px !important;
  border-left: 1px solid #d9d9d9 !important;
  border-bottom: 1px solid #d9d9d9 !important;
  height: 50% !important;
  position: absolute !important;
  left: 12px !important;
}

.ant-tree-show-line .ant-tree-switcher.ant-tree-switcher-noop:after {
  content: " " !important;
  width: 1px !important;
  height: 100% !important;
  position: absolute !important;
  left: 12px !important;
  background: linear-gradient(to bottom, #d9d9d9 50%, transparent 50%) !important;
}

.ant-tree-show-line .anticon-file {
  display: none !important;
}

