<template>
  <div class="sidebar">
    <a-tooltip
      v-for="(button, index) in buttons"
      :key="index"
      :placement="button.placement"
      :title="button.title"
      overlay-class-name="bgc_tooltip"
    >
      <a-button class="sidebar-button" @click="button.onClick">
        <svg-icon :icon-class="button.iconClass" />
      </a-button>
    </a-tooltip>

    <!-- 弹窗 -->
    <el-dialog
      title="添加公司"
      :visible.sync="isModalVisible"
      width="500px"
      :before-close="handleClose"
    >
      <el-form :model="form">
        <el-form-item label="公司名称">
          <el-input v-model="form.title" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="密钥">
          <el-input v-model="form.companyKeys" placeholder="请输入密钥" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleOk">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCompaniesList } from '@/api/haoyu-system/OnlineManagement/BaseStationTree.js'
import { addCompaniesList } from '@/api/haoyu-system/SystemAdministration/SystemAdministration'
import { mapActions, mapState } from 'vuex'

export default {
  data() {
    return {
      // 分别是工具提示框文本，svg标签，工具提示框出现的位置
      buttons: [
        { title: '添加', iconClass: 'add', placement: 'right', onClick: this.showAddModal },
        { title: '删除', iconClass: 'delete', placement: 'right', onClick: this.handleDelete },
        { title: '复制', iconClass: 'copy', placement: 'right', onClick: this.handleCopy },
        { title: '粘贴', iconClass: 'stickup', placement: 'right', onClick: this.handlePaste },
        { title: '剪切', iconClass: 'shear', placement: 'right', onClick: this.handleShear },
        { title: '向上一级', iconClass: 'move-up', placement: 'right', onClick: this.handleMoveUp },
        { title: '向下一级', iconClass: 'move-down', placement: 'right', onClick: this.handleMoveDown },
        { title: '全部展开', iconClass: 'shrink', placement: 'right', onClick: this.expandAll },
        { title: '全部收缩', iconClass: 'unfold', placement: 'right', onClick: this.collapseAll },
        { title: '导出', iconClass: 'export', placement: 'right', onClick: this.handleExport },
      ],
      companyList: [],
      isModalVisible: false,
      form: {
      // 公司名称
        title: '',
        key: '',
        status: '',
        treeIcon: 'company',
        // 密钥输入框
        companyKeys: ''
      }
    }
  },
  computed: {
    ...mapState('tree', ['treeDataSend'])
  },
  created() {
    this.getCompanyList()
  },
  methods: {
    ...mapActions('tree', ['setTreeData', 'setExpandedKeys', 'setSelectedKey', 'moveNode']),
    showAddModal() {
      this.isModalVisible = true
    },
    handleClose() {
      this.isModalVisible = false
    },
    // 实现新建公司确认逻辑
    handleOk() {
      if (this.form.title.trim() === '') {
        this.$message.error('公司名称不能为空')
        return
      }

      // 获取最后一个公司的 ID
      // let lastCompanyId = 0
      // if (this.companyList.length > 0) {
      //   const lastCompany = this.companyList[this.companyList.length - 1]
      //   console.log('最后一个公司id', lastCompany)
      //   lastCompanyId = lastCompany.id // 提取最后的数字部分
      //   console.log('最后一个公司id', lastCompanyId)
      // }

      // 生成新的公司 ID 和 key
      // const newCompanyId = lastCompanyId + 1
      // const newCompanyKey = `0-${newCompanyId}`

      // 创建新的公司对象
      const newCompany = {
        title: this.form.title,
        status: 'disconnected',
        treeIcon: 'company',
        companyKeys: this.form.companyKeys
      }

      this.addNewCompaniesList(newCompany)
      // // 添加到公司列表
      // this.companyList.push(newCompany)

      // // 更新树结构数据
      // this.setTreeData(this.companyList)
      // 重置表单
    },
    addNewCompaniesList(newCompany) {
      addCompaniesList(newCompany).then(res => {
        this.$message.success('公司创建成功')
        // 给父组件传递信息并且刷新树状列表
        this.$emit('company-added')
        this.form.title = ''
        this.form.companyKeys = ''
        this.isModalVisible = false
      })
      console.log('新建公司数据', newCompany)
    },
    handleDelete() {
      // 实现删除逻辑
    },
    handleCopy() {
      // 实现复制逻辑
    },
    handlePaste() {
      // 实现粘贴逻辑
    },
    handleShear() {
      // 实现剪切逻辑
    },
    handleMoveUp() {
      this.moveNode(-1)
    },
    // 实现向下移动逻辑
    handleMoveDown() {
      this.moveNode(1)
    },
    // 全部展开，发送expand-all方法
    expandAll() {
      this.$emit('expand-all')
      // 同时触发全局事件
      this.$root.$emit('expand-all')
    },
    // 全部收缩，发送collapse-all方法
    collapseAll() {
      this.$emit('collapse-all')
      // 同时触发全局事件
      this.$root.$emit('collapse-all')
    },
    handleExport() {

    },
    // 获取到所有公司列表
    getCompanyList() {
      getCompaniesList().then(res => {
        this.companyList = res.data
        // console.log('获取到的列表', this.companyList)
      })
    }
  }
}
</script>

<style scoped>
@import './LeftSidebar.css';
</style>
