<!-- 瀑布图 -->
<template>
  <div>
    <div ref="chart" class="chart" :class="{ loading: loading }" />
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import { getParams } from '@/utils/graph_Interface'
import { getWaterfallData, req } from './data'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      chartInstance: null,
      chartData: {
        x: [],
        y: [],
        z: []
      },
      loading: false,
      error: null,
      resizeObserver: null
    }
  },
  computed:{
    ...mapState('dataStore',['selectedRowsData'])
  },
  watch: {
    selectedRowsData: {
      immediate: true,
      handler: 'handleSelectedRowsDataChange'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.resizeObserver = new ResizeObserver(() => {
        this.resizeChart()
      })
      if (this.$refs.chart) {
        this.resizeObserver.observe(this.$refs.chart)
      }
    })
  },
  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
  },
  methods: {
    async fetchData() {
      try {
        const response = await getWaterfallData(req)
        this.chartData = response.data
        console.log('瀑布图数据->',this.chartData);

        this.initChart()
      } catch (error) {
        console.error('获取数据失败:', error)
      }
    },
    initChart() {
      if (!this.$refs.chart) return

      if (this.chartInstance) {
        this.chartInstance.dispose()
      }

      this.chartInstance = echarts.init(this.$refs.chart)
      this.chartInstance.showLoading({
        text: '加载中...',
        maskColor: 'rgba(255, 255, 255, 0.8)'
      })
    },
    generateSeries() {
      return Array.from({ length: 8 }, (_, i) => `Series${i + 1}`)
    },
    generateSeriesData() {
      const seriesData = []
      const seriesCount = 8
      const pointCount = 1000
      for (let seriesIdx = 0; seriesIdx < seriesCount; seriesIdx++) {
        const data = []
        for (let pointIdx = 0; pointIdx < pointCount; pointIdx++) {
          data.push([pointIdx, seriesIdx, Math.round(Math.random() * 100)])
        }
        seriesData.push({
          type: 'line3D',
          data: data,
          lineStyle: {
            width: 2 // 调整线条宽度，使图表更清晰
          }
        })
      }
      return seriesData
    },
    processData() {
      const data = []
      const xLen = this.chartData.x.length
      const rowCount = this.chartData.rowCount

      // 为每个选中的行生成数据点
      for (let row = 0; row < rowCount; row++) {
        for (let x = 0; x < xLen; x++) {
          data.push([
            this.chartData.x[x],    // x轴值（时间）
            row + 1,               // y轴值（行号，从1开始）
            this.chartData.y[row][x] // z轴值（幅值）
          ])
        }
      }

      return data
    },
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    },
    async handleSelectedRowsDataChange(newData) {
      if (!this.chartInstance) {
        this.initChart()
      }

      if (!newData || newData.length === 0) {
        this.chartData = { x: [], y: [], z: [] }
        if (this.chartInstance) {
          this.chartInstance.clear()
          this.chartInstance.hideLoading()
        }
        return
      }

      try {
        this.loading = true
        this.error = null
        this.chartInstance?.showLoading()

        const dataPromises = newData.map(async (row, index) => {
          const params = await getParams(8)
          params.device_id = String(row.device_id)
          params.point_id = String(row.point_id)
          params.time_point = row.time_point

          return getWaterfallData(params)
        })

        const results = await Promise.all(dataPromises)
        console.log(results[0].data);

        // 数据验证
        if (!results[0]?.data?.x || !results[0]?.data?.y) {
          throw new Error('数据格式错误')
        }

        // 整合数据
        this.chartData = {
          x: results[0].data.y,        // x轴数据
          y: results.map(result => result.data.z),  // 每行的y值数组
          rowCount: results.length,    // 选中的行数
          // 为每个采样时间添加索引，确保即使重复的时间点也能区分
          sampleTimes: newData.map((row, index) => ({
            time: row.time_point,
            index,
            device_id: row.device_id,
            point_id: row.point_id
          }))
        }

        this.updateChart()
      } catch (error) {
        console.error('获取瀑布图数据失败:', error)
        this.error = '数据加载失败'
        this.chartInstance?.clear()
      } finally {
        this.loading = false
        this.chartInstance?.hideLoading()
      }
    },
    getArrayMaxMin(arr) {
      if (!Array.isArray(arr)) return { max: 0, min: 0 };

      let max = -Infinity;
      let min = Infinity;

      // 处理二维数组
      if (Array.isArray(arr[0])) {
        for (let i = 0; i < arr.length; i++) {
          if (!Array.isArray(arr[i])) continue;
          for (let j = 0; j < arr[i].length; j++) {
            const value = arr[i][j];
            if (typeof value === 'number' && !isNaN(value)) {
              max = Math.max(max, value);
              min = Math.min(min, value);
            }
          }
        }
      }
      // 处理一维数组
      else {
        for (let i = 0; i < arr.length; i++) {
          const value = arr[i];
          if (typeof value === 'number' && !isNaN(value)) {
            max = Math.max(max, value);
            min = Math.min(min, value);
          }
        }
      }

      return {
        max: max === -Infinity ? 0 : max,
        min: min === Infinity ? 0 : min
      };
    },
    updateChart() {
      if (!this.chartInstance || !this.chartData.x.length) return
      // 计算 y 值的范围
      const { max: maxValue, min: minValue } = this.getArrayMaxMin(this.chartData.y);

      // 计算 x 值的范围
      const { max: xMax, min: xMin } = this.getArrayMaxMin(this.chartData.x);
      const option = {
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: function (params) {
            const rowIndex = params.data[1]  // 索引对应采样时间数组的索引
            const sampleInfo = this.chartData.sampleTimes[rowIndex]
            return `
              <div style="padding: 3px;">
                <div>设备ID: ${sampleInfo.device_id}</div>
                <div>测点ID: ${sampleInfo.point_id}</div>
                <div>频率: ${Number(params.data[0]).toFixed(2)}</div>
                <div>采样时间: ${sampleInfo.time}</div>
                <div>幅值: ${Number(params.data[2]).toFixed(4)}</div>
              </div>
            `
          }.bind(this)  // 绑定this上下文
        },
        visualMap: {
          max:  maxValue,
          min: minValue,
          dimension: 2,
          calculable: true,
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf',
              '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          },
          formatter: (value) => {
            return value.toFixed(4); // 与Z轴保持一致，显示4位小数
          }
        },

        grid3D: {
          viewControl: {
            projection: 'perspective',
            alpha: 0,
            beta: 0,
            distance: 270,
            autoRotate: false,
            rotateSensitivity: [1,0],
            zoomSensitivity: 1,
            panSensitivity: 0.5,
            alphaMin: -45,
            alphaMax: 45,
            betaMin: -45,
            betaMax: 45
          },
          boxWidth: 200,
          boxHeight: 80,
          boxDepth: 200,
          left: '0%',
          right: '0%',
          top: '0%',
          bottom: '0%'
        },
        xAxis3D: {
          type: 'value',
          name: '频率',
          min: xMin,
          max: xMax,
          axisPointer: {
            show: false // 禁用 x 轴的框
          },
          axisLabel: {
            formatter: (value) => {
              // 限制频率小数位数
              return value.toFixed(2); // 显示2位小数
            }
          }
        },
        yAxis3D: {
          type: 'value',
          name: '采样时间',
          min: 0,
          max: this.chartData.rowCount - 1,
          interval: 1,
          nameGap: 60,
          axisPointer: {
            show: false // 禁用 y 轴的框
          },
          axisLabel: {
            formatter: (value) => {
              const timeInfo = this.chartData.sampleTimes[value];
              // 如果是数值，则限制小数位数
              if (typeof value === 'number' && !timeInfo) {
                return value.toFixed(0); // 不显示小数位
              }
              return timeInfo ? timeInfo.time : '';
            }
          }
        },
        zAxis3D: {
          type: 'value',
          name: '幅值',
          min:  minValue,
          max: maxValue,
          axisPointer: {
            show: false // 禁用 z 轴的框
          },
          axisLabel: {
            formatter: (value) => {
              // 限制幅值小数位数
              return value.toFixed(4); // 显示4位小数
            }
          }
        },
        series: this.chartData.y.map((yValues, rowIndex) => ({
          type: 'line3D',
          data: yValues.map((zValue, xIndex) => [
            this.chartData.x[xIndex],  // x轴值
            rowIndex,                  // y轴值，使用索引对应采样时间
            zValue                     // z轴值
          ]),
          lineStyle: {
            width: 2,
            opacity: 1,
          },
          itemStyle: {
            opacity: 1,
          },
        })),
        dataZoom: [{
          type: 'inside',
          xAxisIndex: 0,
          filterMode: 'none'
        }, {
          type: 'slider',
          xAxisIndex: 0,
          filterMode: 'none'
        }]
      }

      this.chartInstance.setOption(option, true)
    }
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart.loading {
  opacity: 0.7;
  pointer-events: none;
}

.error-message {
  color: #f56c6c;
  text-align: center;
  padding: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
