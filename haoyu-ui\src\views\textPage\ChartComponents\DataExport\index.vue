<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>modbus数据导出选项</span>
      </div>
      <el-form :inline="true" @submit.native.prevent>
        <el-form-item label="可选参数:">
          <el-checkbox-group v-model="selectedOptions">
            <el-checkbox label="temperature">温度</el-checkbox>
            <el-checkbox label="overall">总值</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleGenerate">生成</el-button>
          <el-button type="success" icon="el-icon-download" @click="handleExport" :disabled="!tableData.length">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="box-card table-card">
      <div slot="header" class="clearfix">
        <span>数据列表</span>
      </div>
      <el-table v-loading="loading" :data="tableData" style="width: 100%;box-sizing: border-box;
      " border height="100% ">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="definedPath" label="定义路径" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="definedCode" label="定义代码" width="120"></el-table-column>
        <el-table-column prop="definedNote" label="定义说明" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="definedId" label="定义ID" width="100"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getExportData } from '@/api/export';
import ExcelExporter from '@/utils/excelExport.js';

export default {
  name: 'DataExport',
  data() {
    return {
      selectedOptions: ['temperature','overall'],
      tableData: [],
      loading: false
    };
  },
  methods: {
    handleGenerate() {
      this.loading = true;
      const param = 'process';
      getExportData(param).then(response => {
        if (response.code === 200) {
          this.tableData = response.data;
          this.$message.success('数据生成成功！');
        } else {
          this.$message.error(response.msg || '数据生成失败！');
          this.tableData = [];
        }
      }).catch(error => {
        this.$message.error('请求失败：' + error);
        this.tableData = [];
      }).finally(() => {
        this.loading = false;
      });
    },
    handleExport() {
      if (this.tableData.length === 0) {
        this.$message.warning('没有可导出的数据！');
        return;
      }
      try {
        const dataToExport = {
          'Sheet1': this.tableData.map(item => ({
            'ID': item.id,
            '定义路径': item.definedPath,
            '定义代码': item.definedCode,
            '定义说明': item.definedNote,
            '定义ID': item.definedId
          }))
        };
        ExcelExporter.exportToExcel(dataToExport, 'exported_data.xlsx');
        this.$message.success('导出成功！');
      } catch (error) {
        this.$message.error('导出失败：' + error);
      }
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.box-card {
  margin-bottom: 20px;
}
.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.table-card >>> .el-card__body {
  flex: 1;
  padding: 0;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
