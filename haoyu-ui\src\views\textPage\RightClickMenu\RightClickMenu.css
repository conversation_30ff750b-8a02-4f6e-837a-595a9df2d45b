.right-click-menu {
  border: 1px solid #ccc;
  background: #fff;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  border-radius: 4px;
  padding: 5px 0;
}
.right-click-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.right-click-menu li {
  font-size: 14px;
  padding: 8px 12px;
  cursor: pointer;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.right-click-menu li:hover {
  background: #f5f5f5;
}
.right-click-menu li.disabled {
  color: #aaa;
  cursor: not-allowed;
}
.right-click-menu li.disabled:hover {
  background: #fff;
}
.sub-menu {
  position: absolute;
  top: 0;
  left: 100%;
  margin-left: 1px;
  border: 1px solid #ccc;
  background: white;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
  max-width: 300px;
  max-height: 400px;
  overflow-y: auto;
  border-radius: 4px;
  padding: 5px 0;
}
.shortcut {
  color: #888;
  margin-left: 10px;
}
.menu-icon {
  margin-left: 10px;
}
