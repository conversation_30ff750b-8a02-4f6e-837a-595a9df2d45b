<template>
  <div class="container">
    <!-- Button bar -->
    <div class="button-bar">
      <el-button type="primary" plain @click="exportData">导出xlsx</el-button>
      <el-button type="primary" plain @click="triggerImport">导入xlsx</el-button>
      <input
        type="file"
        ref="fileInput"
        style="display: none"
        @change="importData"
        accept=".xlsx, .xls"
      >
      <el-button type="primary" plain @click="saveData">保存</el-button>
      <el-button type="primary" plain @click="showAlarmDialog">报警记录</el-button>
    </div>

    <!-- Main layout for Alarm Levels and Historical Data -->
    <div class="layout">
      <!-- Alarm levels section with Table -->
      <div class="alarm-levels-table">
        <h3>设备报警值</h3>
        <div class="device-list">
          <!-- 无设备数据时显示提示 -->
          <div v-if="deviceData.length === 0" class="no-data-tip">
            暂无设备数据，请选择公司并刷新
          </div>
          <!-- 设备行和对应的监测项目 -->
          <div v-for="device in deviceData" :key="device.id" class="device-item">
            <!-- 设备行 -->
            <div class="device-row" @click="toggleExpand(device)">
              <div class="device-name">{{ device.title }}</div>
              <div class="device-action">
                <el-button
                  type="text"
                  @click.stop="toggleExpand(device)"
                >
                  {{ expandedRows.includes(device.id) ? '收起' : '展开' }}
                </el-button>
              </div>
            </div>

            <!-- 展开的监测项目表格 -->
            <div v-if="expandedRows.includes(device.id)" class="monitoring-items">
              <el-table
                :data="device.children"
                border
                stripe
                style="width: 100%;"
                row-key="id"
                class="monitoring-table"
                empty-text="暂无报警值数据"
              >
                <el-table-column
                  prop="title"
                  label="监测项目"

                  width="300"
                />
                <!-- 温度报警值 -->
                <el-table-column label="温度报警值" align="center">
                  <el-table-column
                    prop="tp_first_value"
                    label="一级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.tp_first_value" size="small" :disabled="scope.$index !== 0" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="tp_second_value"
                    label="二级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.tp_second_value" size="small" :disabled="scope.$index !== 0" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="tp_third_value"
                    label="三级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.tp_third_value" size="small" :disabled="scope.$index !== 0" />
                    </template>
                  </el-table-column>
                </el-table-column>

                <!-- 峭度报警值 -->
                <el-table-column label="峭度报警值" align="center">
                  <el-table-column
                    prop="kurtFirstAlarmValue"
                    label="一级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.kurtFirstAlarmValue" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="kurtSecondAlarmValue"
                    label="二级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.kurtSecondAlarmValue" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="kurtThirdAlarmValue"
                    label="三级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.kurtThirdAlarmValue" size="small" />
                    </template>
                  </el-table-column>
                </el-table-column>

                <!-- 有效值报警值 -->
                <el-table-column label="有效值报警值" align="center">
                  <el-table-column
                    prop="firstAlarmValue"
                    label="一级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.firstAlarmValue" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="secondAlarmValue"
                    label="二级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.secondAlarmValue" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="thirdAlarmValue"
                    label="三级警报"
                    width="100"
                  >
                    <template #default="scope">
                      <el-input v-model="scope.row.thirdAlarmValue" size="small" />
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 报警记录弹窗 -->
    <el-dialog
      title="历史报警记录"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="true"
      :modal="showMask"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <div class="alarm-dialog-content">
        <el-table
          ref="alarmTableRef"
          :data="historicalData"
          style="width: 100%"
          v-loading="loading"
          element-loading-text="加载中..."
          @row-click="handleAlarmRowClick"
          highlight-current-row
          empty-text="暂无报警记录"
        >
          <el-table-column prop="date" label="时间" width="160" show-overflow-tooltip>
            <template slot-scope="scope">
              <div>{{ formatDate(scope.row.date) }}</div>
              <div>{{ formatTime(scope.row.date) }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="deviceName" label="设备" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="measurementName" label="测点" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="报警详情" min-width="220" show-overflow-tooltip>
            <template slot-scope="scope">
              <div>{{ scope.row.description }}</div>
              <div v-if="scope.row.warnType" class="alarm-detail">
                <span class="alarm-type">{{ scope.row.warnType }}</span>
                <span class="alarm-values">标准值: {{ scope.row.normalValue || '-' }} / 当前值: {{ scope.row.warnValue || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="级别" width="90" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="getAlarmLevelTag(scope.row.level)"
                effect="dark"
                size="mini"
              >
                {{ getAlarmLevelText(scope.row.level) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelExporter from '@/utils/excelExport'
import request from '@/utils/request'
import { getWarnRecord } from '@/views/textPage/BigScreen/api/apiBigscreen'

export default {
  data() {
    return {
      // 控制展开行的数组
      expandedRows: [],
      // 控制弹窗显示
      dialogVisible: false,
      // 控制是否显示遮罩层
      showMask: true,
      // 控制加载状态
      loading: false,
      // 设备报警值数据，包含设备与其子项
      defaultDeviceData: [],
      deviceData: [],
      // 历史报警数据
      historicalData: []
    }
  },
  mounted() {
    // 调试语句，记录选中的节点
    console.log(this.$store.state.tree.selectedTreeNode.id)
    // 空值检查，提示用户在未选择公司的情况下需要做什么
    const selectedNode = this.$store.state.tree?.selectedTreeNode
    if (selectedNode && selectedNode.id !== 0) {
      this.GetAlarm()
    } else {
      this.$message.error('请先选中公司再打开此界面！')
    }
  },
  watch: {
    // 监听选中的节点的变化，方便切换公司的报警数据
    '$store.state.tree.selectedTreeNode': {
      handler(newNode, oldNode) {
        if (newNode && newNode.id !== oldNode?.id) {
          this.GetAlarm()
        }
      },
      deep: true
    }
  },
  methods: {
    // 显示报警记录弹窗
    showAlarmDialog() {
      this.dialogVisible = true;
      this.getWarnRecords();
    },
    // 获取报警记录
    async getWarnRecords() {
      this.loading = true;
      try {
        // 调用API获取报警记录
        const response = await getWarnRecord(1); // 使用导入的getWarnRecord方法，传入页码参数

        if (response && response.rows) {
          // 根据API返回的实际结构映射数据
          this.historicalData = response.rows.map(item => ({
            date: item.warnTime || '',
            description: item.warnMsg || '',
            deviceName: item.deviceName || '',
            measurementName: item.measurementName || '',
            level: item.warnLevel || '',
            warnType: item.warnType || '',
            normalValue: item.normalValue || '',
            warnValue: item.warnValue || ''
          }));
        } else {
          // 如果接口失败，提示错误
          console.error('获取报警记录失败');
          this.$message.error('获取报警记录失败，请稍后重试');
          this.historicalData = [];
        }
      } catch (error) {
        console.error('获取报警记录失败:', error);
        this.$message.error('获取报警记录失败，请稍后重试');
        this.historicalData = [];
      } finally {
        this.loading = false;
      }
    },
    // 处理报警行点击事件
    handleAlarmRowClick(row) {
      console.log('点击报警记录:', row);
      // 这里可以添加查看详情逻辑，本需求只展示不处理
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },
    // 格式化时间
    formatTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },
    // 获取报警级别标签类型
    getAlarmLevelTag(level) {
      const levelMap = {
        '1': 'warning',  // 一级警报
        '2': 'warning',  // 二级警报
        '3': 'danger'    // 三级警报
      };
      return levelMap[level] || 'info';
    },
    // 获取报警级别文本
    getAlarmLevelText(level) {
      const levelMap = {
        '1': '一级警报',
        '2': '二级警报',
        '3': '三级警报'
      };
      return levelMap[level] || '未知';
    },
    // 处理行点击事件
    handleRowClick(row) {
      this.toggleExpand(row);
    },
    // 切换展开/收起状态
    toggleExpand(row) {
      const index = this.expandedRows.indexOf(row.id);
      if (index === -1) {
        this.expandedRows.push(row.id);
      } else {
        this.expandedRows.splice(index, 1);
      }
    },
    // 导出excel文件
    exportData() {
      const flatDeviceData = this.flattenDeviceData()
      const data = {
        '报警级别': flatDeviceData,
        '历史报警': this.historicalData
      }
      ExcelExporter.exportToExcel(data, '报警数据.xlsx')
    },
    flattenDeviceData() {
      // 将设备及其子项的数据扁平化。
      const flatData = []
      this.deviceData.forEach(device => {
        if (device.children && device.children.length > 0) {
          device.children.forEach(child => {
            flatData.push({
              设备: device.title,
              监测项目: child.title,
              id: child.id,
              // 温度报警值
              温度一级报警: child.tp_first_value,
              温度二级报警: child.tp_second_value,
              温度三级报警: child.tp_third_value,
              // 峭度报警值
              峭度一级报警: child.kurtFirstAlarmValue,
              峭度二级报警: child.kurtSecondAlarmValue,
              峭度三级报警: child.kurtThirdAlarmValue,
              // 有效值报警值
              一级报警: child.firstAlarmValue,
              二级报警: child.secondAlarmValue,
              三级报警: child.thirdAlarmValue
            })
          })
        }
      })
      return flatData
    },
    //
    triggerImport() {
      this.$refs.fileInput.click()
    },
    // 导入excel数据
    async importData(event) {
      const file = event.target.files[0]
      if (!file) return

      try {
        const importedData = await ExcelExporter.importFromExcel(file)

        console.log('Imported Data:', importedData) // 检查导入的数据

        // 处理 '报警级别' 工作表的数据
        if (importedData['报警级别']) {
          this.deviceData = this.transformImportedDeviceData(importedData['报警级别'])
          console.log('Updated deviceData:', this.deviceData) // 检查更新后的 deviceData
        } else {
          console.error('No "报警级别" sheet found in imported data')
        }

        // 处理 '历史报警' 工作表的数据
        if (importedData['历史报警']) {
          this.historicalData = importedData['历史报警'].map(item => ({
            date: item['日期'] || item['date'],
            description: item['描述'] || item['description']
          }))
          console.log('Updated historicalData:', this.historicalData) // 检查更新后的 historicalData
        } else {
          console.error('No "历史报警" sheet found in imported data')
        }

        // 清除文件输入，以便可以重复导入相同的文件
        event.target.value = ''

        // 显示成功消息
        this.$message.success('数据导入成功')
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败，请检查文件格式')
      }
    },
    // 获取报警的数据
    GetAlarm() {
      const selectedNodeFromStore = this.$store.state.tree.selectedTreeNode; // This is {id, treeicon, status, title, key, children (if last change was kept)}
      console.log('[AlarmView Debug] selectedNode object from store in GetAlarm:', JSON.parse(JSON.stringify(selectedNodeFromStore)));

      if (!selectedNodeFromStore || !selectedNodeFromStore.id || selectedNodeFromStore.id === 0) {
        this.deviceData = [];
        if (!selectedNodeFromStore || selectedNodeFromStore.id === 0) {
         // console.log('[AlarmView Debug] No valid company/node selected.'); // Avoid message if already handled by mount/watch
        }
        return;
      }
      const selectedNodeId = selectedNodeFromStore.id;

      // Helper function to find a node by ID in a tree structure
      function findNodeByIdInTree(nodes, id) {
        if (!nodes || !Array.isArray(nodes)) {
          return null;
        }
        for (const node of nodes) {
          if (node.id === id) {
            return node;
          }
          if (node.children) {
            const found = findNodeByIdInTree(node.children, id);
            if (found) return found;
          }
        }
        return null;
      }

      const mainTreeData = this.$store.state.tree.treeDataSend;
      // console.log('[AlarmView Debug] Main tree data (treeDataSend) from store:', JSON.parse(JSON.stringify(mainTreeData))); // Can be very large

      const fullSelectedNode = findNodeByIdInTree(mainTreeData, selectedNodeId);
      console.log('[AlarmView Debug] Full selected node found in main tree data (treeDataSend):', JSON.parse(JSON.stringify(fullSelectedNode)));

      this.AlarmInfo(selectedNodeId).then(res => {
        if (res.code === 200 && res.data) {
          const apiDeviceData = res.data;
          console.log('[AlarmView Debug] API device data keys:', Object.keys(apiDeviceData));

          let orderedDeviceTitles = [];

          if (fullSelectedNode && fullSelectedNode.children && Array.isArray(fullSelectedNode.children)) {
            orderedDeviceTitles = fullSelectedNode.children
              .map(child => child && child.title ? String(child.title).trim() : null)
              .filter(title => title && title.length > 0);
            console.log('[AlarmView Debug] Titles from fullSelectedNode.children (orderedDeviceTitles):', JSON.parse(JSON.stringify(orderedDeviceTitles)));
          } else {
            console.warn('[AlarmView Debug] fullSelectedNode.children is not a valid array or is missing. Checking selectedNodeFromStore.children as a fallback (if available from previous attempts).');
            // Fallback for safety, though the primary logic is now fullSelectedNode.children
            if (selectedNodeFromStore && selectedNodeFromStore.children && Array.isArray(selectedNodeFromStore.children)) {
                console.log('[AlarmView Debug] Fallback: Using children from selectedNodeFromStore (this might be from a previous modification attempt on WorkTree)');
                orderedDeviceTitles = selectedNodeFromStore.children
                    .map(child => child && child.title ? String(child.title).trim() : null)
                    .filter(title => title && title.length > 0);
                console.log('[AlarmView Debug] Fallback Titles from selectedNodeFromStore.children:', JSON.parse(JSON.stringify(orderedDeviceTitles)));
            } else {
                 console.warn('[AlarmView Debug] Fallback also failed: selectedNodeFromStore.children is also not valid.');
            }
          }

          const newDeviceData = [];
          const processedApiTitles = new Set();

          if (orderedDeviceTitles.length > 0) {
            orderedDeviceTitles.forEach(title => {
              if (apiDeviceData.hasOwnProperty(title)) {
                newDeviceData.push({
                  title: title,
                  id: title,
                  children: apiDeviceData[title]
                });
                processedApiTitles.add(title);
              } else {
                console.warn(`[AlarmView Debug] Device title "${title}" from tree order not found in API data keys. API keys are:`, Object.keys(apiDeviceData));
              }
            });
          } else {
             console.warn('[AlarmView Debug] orderedDeviceTitles is empty. Displaying data in API order.');
          }

          Object.keys(apiDeviceData).forEach(title => {
            if (!processedApiTitles.has(title)) {
              newDeviceData.push({
                title: title,
                id: title,
                children: apiDeviceData[title]
              });
            }
          });

          this.deviceData = newDeviceData;
          console.log('[AlarmView Debug] Final deviceData for table (titles):', JSON.parse(JSON.stringify(this.deviceData.map(d => d.title))));
        } else {
          console.error('获取报警数据失败，响应码:', res.code);
          this.$message.error(`获取报警数据失败，请稍后重试`);
          this.deviceData = [];
        }
      }).catch(err => {
        console.error('获取报警数据时发生网络错误:', err);
        this.$message.error('网络请求错误，请稍后重试');
        this.deviceData = [];
      });
    },
    // 网络请求
    AlarmInfo(id) {
      return request({
        url: `/deviceManagement/warn/list/${id}`,
        method: 'get'
      })
    },
    transformDeviceData(data) {
      // 将 JSON 转换为适合 el-table 的数据结构
      return Object.keys(data).map(deviceName => ({
        title: deviceName,
        id: deviceName, // 设备名称作为 id，如果需要唯一标识可以根据实际情况修改
        children: data[deviceName] // 直接使用设备下的报警数据
      }))
    },
    // 扁平化数据为符合API需求的格式
    prepareSaveData() {
      const saveData = []
      this.deviceData.forEach(device => {
        if (device.children && device.children.length > 0) {
          device.children.forEach(child => {
            const dataItem = {
              // 温度报警值
              tp_first_value: child.tp_first_value,
              tp_second_value: child.tp_second_value,
              tp_third_value: child.tp_third_value,
              // 峭度报警值
              kurtFirstAlarmValue: child.kurtFirstAlarmValue,
              kurtSecondAlarmValue: child.kurtSecondAlarmValue,
              kurtThirdAlarmValue: child.kurtThirdAlarmValue,
              // 有效值报警值
              firstAlarmValue: child.firstAlarmValue,
              secondAlarmValue: child.secondAlarmValue,
              thirdAlarmValue: child.thirdAlarmValue
            }

            if (child.id !== null && child.id !== undefined) {
              dataItem.id = child.id
            }

            saveData.push(dataItem)
          })
        }
      })
      return saveData
    },
    // 保存数据
    saveData() {
      const dataToSave = this.prepareSaveData()
      console.log('发送到后端的数据 ===>', dataToSave)

      this.$loading({ lock: true, text: '正在保存数据...' })

      request({
        url: '/deviceManagement/warn',
        method: 'put',
        data: dataToSave // 将数据发送到API
      })
        .then(response => {
          this.$loading().close()
          console.log('Save response:', response) // 查看服务器的响应
          if (response.code === 200) {
            this.$message.success('保存成功')
          } else {
            this.$message.error('保存失败')
          }
        })
        .catch(error => {
          this.$loading().close()
          console.error('保存失败:', error)
          this.$message.error('保存失败，请稍后重试')
        })
    },
    transformImportedDeviceData(flatData) {
      // console.log('Flat Data:', flatData); // 检查导入的平面数据

      // 遍历导入的数据，并找到对应的设备和监测项目
      flatData.forEach(item => {
        const deviceTitle = item['设备']
        const childTitle = item['监测项目']
        const importedId = item['id'] // 从导入的数据中获取 id

        // 查找现有的数据中是否有该设备
        const existingDevice = this.deviceData.find(device => device.title === deviceTitle)

        if (existingDevice) {
          // 查找该设备下的监测项目
          const existingChild = existingDevice.children.find(child => child.title === childTitle)

          if (existingChild) {
            // 更新现有项目的报警值，保留原始 id
            // 温度报警值
            existingChild.tp_first_value = item['温度一级报警']
            existingChild.tp_second_value = item['温度二级报警']
            existingChild.tp_third_value = item['温度三级报警']
            // 峭度报警值
            existingChild.kurtFirstAlarmValue = item['峭度一级报警']
            existingChild.kurtSecondAlarmValue = item['峭度二级报警']
            existingChild.kurtThirdAlarmValue = item['峭度三级报警']
            // 有效值报警值
            existingChild.firstAlarmValue = item['一级报警']
            existingChild.secondAlarmValue = item['二级报警']
            existingChild.thirdAlarmValue = item['三级报警']

            // debug调试语句
            /* if (importedId && importedId === existingChild.id) {
              console.log(`项目 ${childTitle} 的 ID 已匹配`);
            } else {
              console.warn(`项目 ${childTitle} 的 ID 不匹配，跳过 ID 更新`);
            } */
          } else {
            // console.warn(`未找到设备 ${deviceTitle} 下的监测项目 ${childTitle}`);
          }
        } else {
          // console.warn(`未找到设备 ${deviceTitle}`);
        }
      })

      // console.log('Updated deviceData:', this.deviceData); // 检查更新后的数据
      return this.deviceData
    }

  }
}
</script>

<style scoped>
/* Container and font settings */
.container {
  padding: 20px;
  font-family: 'Roboto', sans-serif;
  background-color: #fafafa;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.button-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

/* Layout for Alarm Levels and Historical Data */
.layout {
  display: flex; /* 让设备报警表格和历史数据水平排列 */
  gap: 20px;
  width: 100%;
}

/* Alarm levels table styling */
.alarm-levels-table {
  width: 100%; /* 占据100%的宽度 */
  background-color: white;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: auto;
}

.device-list {
  width: 100%;
}

.device-item {
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.device-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: background-color 0.3s;
}

.device-row:hover {
  background-color: #eef1f6;
}

.device-name {
  font-weight: bold;
  color: #303133;
}

.device-action {
  display: flex;
  align-items: center;
}

.monitoring-items {
  background-color: #ffffff;
  padding: 15px;
  border-top: 1px solid #ebeef5;
  width: 100%;
}

.el-table {
  border-radius: 4px;
  margin-bottom: 0;
  width: 100% !important;
}

/* 强制表格填充可用宽度 */
::v-deep .el-table__header-wrapper,
::v-deep .el-table__body-wrapper {
  width: 100% !important;
}

::v-deep .el-table__inner-wrapper {
  width: 100% !important;
}

::v-deep .el-table__header,
::v-deep .el-table__body {
  width: 100% !important;
}

.el-table .el-input {
  width: 100%;
}

.monitoring-table {
  margin-top: 0;
}

.el-table__header th {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #333;
}

/* 分组列头样式 */
.el-table__header .el-table__cell.is-center {
  background-color: #eef1f6;
}

/* 确保嵌套表格头部样式一致 */
.el-table__header .el-table-column--selection .cell {
  padding-left: 14px;
  padding-right: 14px;
}

/* 报警弹窗样式 */
::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
}

::v-deep .el-dialog__header {
  flex-shrink: 0;
}

::v-deep .el-dialog__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px 20px;
  overflow: hidden;
  min-height: 0;
}

::v-deep .el-dialog__footer {
  flex-shrink: 0;
}

.alarm-dialog-content {
  /* max-height: 400px; */ /* Removed this line */
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

/* 确保 el-table 填满 .alarm-dialog-content 并能内部滚动 */
::v-deep .alarm-dialog-content .el-table {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 针对 el-table 内部的 body wrapper，确保它能滚动 */
::v-deep .alarm-dialog-content .el-table .el-table__body-wrapper {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}


.alarm-detail {
  margin-top: 5px;
  font-size: 12px;
  color: #606266;
}

.alarm-type {
  margin-right: 10px;
  background-color: #f0f9eb;
  color: #67c23a;
  padding: 2px 5px;
  border-radius: 2px;
  display: inline-block;
}

.alarm-values {
  color: #e6a23c;
  display: inline-block;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* 移除右侧历史数据样式 */
/* Responsive design */
@media (max-width: 768px) {
  .layout {
    flex-direction: column; /* Stack the sections vertically on smaller screens */
  }

  .alarm-levels-table {
    width: 100%;
  }
}

.no-data-tip {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
  background: #f8f8f9;
  border-radius: 4px;
  margin: 20px 0;
}

/* 修复表格布局问题 */
::v-deep .el-table {
  width: 100% !important;
  table-layout: fixed;
}

::v-deep .el-table__row {
  width: 100% !important;
}
</style>
