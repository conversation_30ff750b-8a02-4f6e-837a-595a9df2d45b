<template>
  <div class="basic-config">
    <div class="BaseConfig-left-container">
      <h3>采集站信息</h3>
      <el-form :model="form" label-width="80px" label-position="top" class="compact-form">
        <el-form-item label="采集站类型" class="compact-form-item">
          <el-select v-model="form.device_type" size="small" class="small-input" style="width: 80%;" @change="ChangeCollectorType">
            <el-option
              v-for="item in stationTypes"
              :key="item.id"
              :label="item.collectorTypeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="采集站编码" class="compact-form-item">
          <el-input v-model="form.device_coding" size="small" class="small-input" style="width: 80%;" />
        </el-form-item>

        <el-form-item label="采集站IP" class="compact-form-item">
          <div style="display: flex; align-items: center;">
            <el-input
              v-model="form.ip"
              size="small"
              class="small-input"
              style="width: 80%;"
            />
            <!-- <el-button
              type="primary"
              size="mini"
              style="margin-left: 10px;"
              @click="testConnection"
            >
              测试
            </el-button> -->
          </div>
        </el-form-item>

        <el-form-item label="服务器ip" class="compact-form-item">
          <el-input v-model="form.ServeIp" size="small" disabled class="small-input" style="width: 80%;" />
        </el-form-item>

        <el-form-item label="采集方式" class="compact-form-item">
          <el-select v-model="form.device_method" size="small" class="small-input" style="width: 80%;" placeholder="选择采集方式">
            <el-option
              v-for="item in collectionMethods"
              :key="item.id"
              :label="item.collectorMethod"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

      </el-form>

      <div class="additional-info">
        <h4>监测设备</h4>
        <el-form :model="form" label-width="0px" style="height: 100%;">
          <div class="BaseConfig-tree-container">
            <a-tree
              :tree-data="treeData"
              :default-expanded-keys="['0-2']"
              @select="handleNodeClick"
            />
          </div>
        </el-form>
      </div>
    </div>
    <!-- 通道配置选项 -->
    <div class="BaseConfig-right-container">
      <h3>通道配置</h3>
      <div class="node-list-container">
        <!-- 加入关联设备 -->
        <p class="section-title" />
        <ul class="node-list">
          <li v-for="(node, index) in baseStationNode" :key="index" class="node-item">
            <span>{{ node.title }}</span>
            <el-button
              type="danger"
              icon="el-icon-minus"
              size="mini"
              class="node-buttom"
              @click="removeNode(index, node)"
            />
          </li>
        </ul>
      </div>
      <el-tabs type="border-card">
        <el-tab-pane label="振动通道">
          <el-table :data="vibrationChannels" style="width: 100%" border>
            <el-table-column
              v-for="(column, index) in tableTitle"
              :key="index"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.type === 'checkbox' ? 210 : (column.type === 'dropdown' ? 180 : 130)"
            >
              <!-- 只在复选框列显示全选框 -->
              <template #header="{ column }">
                <div style="display: flex; align-items: center;">
                  <!-- 如果是复选框列，显示全选框 -->
                  <div v-if="column.type === 'checkbox'">
                    <el-checkbox
                      v-model="isAllSelected"
                      @change="toggleAllSelection"
                    />
                  </div>
                  <!-- 表头文本 -->
                  <span>{{ column.label }}</span>
                </div>
              </template>
              <!-- 通用 slot-scope 用于处理所有类型 -->
              <template slot-scope="scope">
                <!-- 输入框类型 -->
                <el-input
                  v-if="column.type === 'input'"
                  v-model="scope.row[column.prop]"
                  size="small"
                />

                <!-- 复选框类型 -->
                <el-checkbox
                  v-else-if="column.type === 'checkbox'"
                  v-model="scope.row[column.prop]"
                  :true-label="1"
                  :false-label="0"
                  @change="handleCheckboxChange(scope.row[column.prop])"
                />

                <!-- 下拉框类型 -->
                <el-select
                  v-else-if="column.type === 'dropdown'"
                  v-model="scope.row[column.prop]"
                  clearable
                  size="small"
                  placeholder="请选择"
                  @change="handleSelectChange(scope.row[column.prop], scope.row)"
                >
                  <!-- 动态过滤后的选项 -->
                  <el-option
                    v-for="item in filteredPointsOptions"
                    :key="item.id"
                    :label="item.sensorName"
                    :value="item.id"
                  />
                </el-select>

                <!-- 默认显示类型 -->
                <span v-else>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>
</template>

<script>
import {
  getCollectorTypeList,
  getcCollectorMethodList,
  getChannelModelList,
  getBaseStationDeviceList,
  addBaseStation,
  ConnectionTest,
  getBaseStationParamList,
  getBaseStationTableList,
  updateBaseStation,
  updateBaseStationTable,
  getDeviceMeasurePointList
} from '@/api/haoyu-system/OnlineManagement/BaseStationTree'
import { mapGetters, mapActions } from 'vuex'

export default {
  props: {
    mode: {
      type: String,
      default: 'add'
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedIds: [], // 用于存储已经选中的下拉框的 id
      // 关联测点下拉框数据
      PointsOptions: [],
      // 获取表格元素
      tableTitle: [],
      // 表格数据暂存区
      TableCachetable: [],
      selectAllTemperature: false, // 勾选框
      // 采集站信息的表单
      form: {
        deviceChannelValueId: '', // 采集站通道值ID
        worked: '1',
        lines: '6400',
        frequency: '16384',
        gap: '5',
        wave_gap: '30',
        id: null,
        device_moitor: '',
        ServeIp: '127.0.0.1',
        device_company: '',
        device_type: '', // 采集类型
        device_coding: '昊宇test', // 采集站编码
        ip: '*************', // 基站ip
        device_method: '', // 采集站方法
        company: '', // 初始值
        mysql_id: null, // 初始值
        collectTime: '2' // 添加采样时间字段，默认值为2
      },
      collectionMethods: [], // 采集方式下拉框初始化
      stationTypes: {}, // 采集站类型下拉框初始化
      vibrationChannels: [], // 表格数据
      treeData: [],
      // 工艺通道(暂时不用)
      processChannels: [
        {
          channelNumber: '1',
          associatedSensor: '测点1',
          inputUpperLimit: '100',
          inputLowerLimit: '0',
          correctionFactor: '1.0',
          compensationValue: '0'
        }
      ]
    }
  },
  computed: {
    ...mapGetters('SettingConnect', ['baseStationNode', 'selectedNodeID']),
    filteredPointsOptions() {
      console.log('filteredPointsOptions 计算属性被触发')
      console.log('当前 PointsOptions:', this.PointsOptions)
      console.log('当前 selectedIds:', this.selectedIds)
      if (this.PointsOptions) {
        const filtered = this.PointsOptions.filter(option => !this.selectedIds.includes(option.id))
        console.log('过滤后的选项:', filtered)
        return filtered
      } else {
        console.log('PointsOptions 为空，返回空数组')
        return []
      }
    },
    // 计算属性：判断是否所有复选框都被选中
    isAllSelected() {
      return this.vibrationChannels.length > 0 && this.vibrationChannels.every(item => item.selected === 1)
    }
  },
  watch: {
    visible(newVal) {
      // console.log('对话框状态:', newVal)
      if (newVal) {
        this.form.device_company = this.$store.state.SettingConnect.companyId
        // 对话框打开时，同步最新的 companyId
        this.getConpanyTreeData(this.$store.state.SettingConnect.companyId)
        if (newVal === 'false') {
          // 弹窗打开的话重置信息
          this.Reset()
        } else {
          this.initializeData()
        }
      }
    },
/*     'form.ip'() {
      // 当 IP 地址改变时，通知父组件将保存按钮设置为不可点击状态
      this.$emit('enable-save', false)
    }, */
    // 监听选择的树的id变化
    selectedNodeID(newVal) {
      if (newVal) {
        console.log('编辑传递的id', newVal)
        this.initializeData()
      }
    }
  },
  created() {
    this.initializeData()
  },
  mounted() {
    this.form.device_company = this.$store.state.SettingConnect.companyId
    this.getConpanyTreeData(this.$store.state.SettingConnect.companyId)
  },
  methods: {
    ...mapActions('SettingConnect', {
      removeNodeFromStore: 'removeBaseStationNode', // 将 Vuex 中的 removeNode action 映射为 removeNodeFromStore 方法
      clearBaseStationNode: 'clearBaseStationNode'
    }),
    // 全选复选框切换时触发
    toggleAllSelection(val) {
      this.vibrationChannels.forEach(row => {
        row.selected = val ? 1 : 0 // 全选或取消全选
      })
    },
    // 勾选框触发方法打印绑定值
    handleCheckboxChange(isChecked) {
      console.log('复选框状态:', isChecked)
    },
    // 获取关联测点下拉框属性
    getPointsOptions(node) {
      const ids = node.map(n => n.id)
      console.log('获取测点的设备IDs:', ids)
      if (ids.length !== 0) {
        getDeviceMeasurePointList(ids).then(res => {
          console.log('获取到的测点数据:', res)
          if (res.data && Array.isArray(res.data)) {
            this.PointsOptions = res.data
            console.log('更新后的PointsOptions:', this.PointsOptions)
          } else {
            console.error('测点数据格式不正确:', res)
            this.PointsOptions = []
          }
        }).catch(error => {
          console.error('获取设备测点列表失败:', error)
          this.PointsOptions = []
        })
      } else {
        console.log('没有可用的关联测点')
        this.PointsOptions = []
      }
    },
    // 处理下拉框变化
    handleSelectChange(selectedId, row) {
      console.log('选择测点:', selectedId, row)
      // 更新当前行的选中值
      row.selectedSensorId = selectedId
      row.associatedsensor = this.PointsOptions.find(option => option.id === selectedId)?.sensorName || ''

      // 更新选中的 ID 列表
      this.selectedIds = this.vibrationChannels
        .map(channel => channel.selectedSensorId)
        .filter(id => id)
      console.log('更新后的selectedIds:', this.selectedIds)
    },
    // 删除检测设备
    removeNode(index, node) {
      this.$confirm('您确定要删除这个节点吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // console.log(node)
        // 清除已选择的测点，如果选中的测点属于被删除的设备
        this.vibrationChannels.forEach(channel => {
          // console.log(channel)
          if (this.isPointFromDeletedDevice(channel.selectedSensorId, node.id)) {
            channel.selectedSensorId = null // 重置选中的测点
            channel.associatedsensor = null // 重置选中的测点
          }
        })
        this.selectedIds = []
        this.$message.success('删除成功')
        this.removeNodeFromStore(index)
          .then(() => {
            // removeNodeFromStore 完成后，检查 baseStationNode 是否存在
            console.log(this.baseStationNode)
            if (this.baseStationNode.length > 0) {
              this.getPointsOptions(this.baseStationNode) // 获取新的测点选项
            } else {
              console.log('进入')
              this.PointsOptions = [] // 如果 baseStationNode 不存在，清空测点选项
            }
          })
          .catch((error) => {
            // 处理错误
            console.error('删除节点时发生错误:', error)
          })
      })
    },

    // 检查测点是否属于已删除的设备
    isPointFromDeletedDevice(sensorId, deviceId) {
    // 在 PointsOptions 中查找这个测点是否属于该设备
      const point = this.PointsOptions.find(option => option.id === sensorId)
      return point && Number(point.deviceId) === deviceId
    },

    // 更新已选中的 ID 列表
    updateSelectedIds() {
      this.selectedIds = this.vibrationChannels.map(channel => channel.selectedSensorId).filter(id => id)
    },

    // 编辑的时候初始化节点
    initializeData() {
      if (this.mode === 'edit') {
        console.log('选中的ID:=>>>>>>',this.selectedNodeID);

        getBaseStationParamList(this.selectedNodeID).then(res => {
          console.log('后端发送的数据=>',res.data)
          this.form.deviceChannelValueId = res.data.deviceChannelValueId
          this.form.worked = res.data.worked
          this.form.lines = res.data.lines
          this.form.frequency = res.data.frequency
          this.form.gap = res.data.gap
          this.form.wave_Gap = res.data.waveGap
          this.form.device_moitor = res.data.deviceMoitor
          this.form.device_company = res.data.deviceCompany
          this.form.device_type = Number(res.data.deviceType)
          this.form.device_coding = res.data.deviceCoding
          this.form.ip = res.data.ip
          this.form.device_method = Number(res.data.deviceMethod)
          this.form.mysql_id = res.data.id
          this.form.collectTime = res.data.collectTime || '2'
          // 初始化监测设备节点
          // 重新清空vuex里面的数据防止上次取消弹窗的时候未清空
          this.clearBaseStationNode()
          const deviceIds = res.data.deviceMoitor ? res.data.deviceMoitor.split(',').filter(id => id.trim() !== '') : []
          const deviceNames = res.data.deviceMoitorName ? res.data.deviceMoitorName.split(',').filter(name => name.trim() !== '') : []

          // console.log(this.$store.state.SettingConnect.BaseStationNode)
          // 构建节点对象
          const storeNodes = deviceIds.map((id, index) => ({
            title: deviceNames[index] || '', // 使用设备名称，如果没有名称则为空
            key: '', // 设为默认值为空
            id: Number(id)
          }))
          // 初始化关联测点下拉框
          this.getPointsOptions(storeNodes)
          // 将节点存储到 Vuex
          storeNodes.forEach(node => {
            this.$store.dispatch('SettingConnect/addBaseStationNode', node)
          })
        })
        getBaseStationTableList(this.selectedNodeID).then(res => {
          console.log('获取到的表格数据:', res);

          this.tableTitle = res.table.table_title
          this.vibrationChannels = res.table.table_value
          console.log('编辑模式打印表格数据调试', this.vibrationChannels)
        })
      }
      // 初始化默认下拉选项
      getCollectorTypeList().then(res => {
        // console.log(res)
        this.stationTypes = res.rows
        if (this.stationTypes.length > 0 && this.mode === 'add') {
          this.form.device_type = this.stationTypes[0].id
          this.ChangeCollectorType(this.form.device_type)
        } else if (this.mode === 'edit') {
          // console.log('编辑模式', this.stationTypes[0].id)
          getChannelModelList(this.stationTypes[0].id).then(res => {
            this.tableTitle = res.table.table_title
            this.TableCachetable = res
            delete this.TableCachetable.id
          })
          // console.log('编辑模式下获取采集类型初始数据')
        } else {
          console.log('没有采集站类型信息')
        }
      })

      // 初始化collectionMethod
      getcCollectorMethodList().then(res => {
        this.collectionMethods = res.rows
        if (this.collectionMethods.length > 0 && this.mode === 'add') {
          this.form.device_method = this.collectionMethods[0].id
        } else if (this.mode === 'edit') {
          console.log('编辑模式下获取采集方式初始数据')
        } else {
          console.log('没有采集站方法信息')
        }
      })
      // console.log('初始化数据，当前节点 ID:', this.selectedNodeID)
    },
    // 测试基站ip的连接，如果不成功则无法保存
/*     testConnection() {
      const ipData = {
        ip: this.form.ip,
        lines: this.form.lines,
        frequency: this.form.frequency,
        gap: this.form.gap
      }
      ConnectionTest(ipData).then(res => {
        if (res.code === 200) {
          this.$emit('enable-save', true)
          this.$message.success('连接测试成功')
        } else {
          this.$emit('enable-save', false)
          this.$message.error('连接测试失败，请检查配置')
        }
      }).catch(error => {
        this.$emit('enable-save', false)
        this.$message.error('网络错误，无法测试连接')
        console.error('测试连接时发生错误:', error)
      })
    }, */
    // 表单提交（基站信息）
    submitForm() {
      const formData = {
        ip: this.form.ip,
        worked: this.form.worked,
        lines: this.form.lines,
        frequency: this.form.frequency,
        gap: this.form.gap,
        waveGap: this.form.wave_gap,
        collectTime: this.form.collectTime || '2',
        deviceCoding: this.form.device_coding,
        deviceMethod: this.form.device_method,
        deviceCompany: this.form.device_company,
        deviceMoitor: this.baseStationNode.map(node => node.id).join(',') + ',',
        deviceType: this.form.device_type,
        collectorTypeId: this.form.device_type, // 添加采集站类型ID
        ...this.TableCachetable,
        table: {
          table_title: this.tableTitle,
          table_value: this.vibrationChannels.map(channel => ({
            ...channel,
            selected: channel.selected || 0,
            selectedSensorId: channel.selectedSensorId || null,
            channelNumber: channel.channelNumber,
          }))
        }
      };
      if (this.mode === 'add') {
        // 新增模式：先添加基站，然后提交配置表格，并返回 Promise
        return addBaseStation(formData).then(res => {
          if (res.code === 200) {
            const tableData = {
              ...this.TableCachetable,
              mysql_id: res.data,
              ip: this.form.ip,
              collectorTypeId: this.form.device_type, // 添加采集站类型ID
              table: {
                table_title: this.tableTitle,
                table_value: this.vibrationChannels.map(channel => ({
                  ...channel,
                  selected: channel.selected || 0,
                  selectedSensorId: channel.selectedSensorId || null,
                  channelNumber: channel.channelNumber,
                }))
              }
            };
            // 提交配置并在成功后显示提示
            return updateBaseStationTable(tableData).then(resp => {
              this.$message.success('保存成功', resp.code);
            });
          } else {
            return Promise.reject(new Error(`服务器响应失败，状态码: ${res.code}`));
          }
        });
      } else if (this.mode === 'edit') {
        // 构建编辑模式表单数据
        const editFormData = {
          id: this.selectedNodeID,
          deviceChannelValueId: this.form.deviceChannelValueId,
          deviceCoding: this.form.device_coding,
          deviceCompany: Number(this.form.device_company),
          deviceMethod: Number(this.form.device_method),
          deviceMoitor: this.baseStationNode.map(node => node.id).join(','),
          deviceType: Number(this.form.device_type),
          frequency: this.form.frequency,
          gap: this.form.gap,
          ip: this.form.ip,
          lines: this.form.lines,
          waveGap: this.form.wave_gap,
          collectTime: this.form.collectTime || '2',
          worked: this.form.worked,
          mysql_id: this.form.mysql_id,
          ...this.TableCachetable,
          table: {
            table_title: this.tableTitle,
            table_value: this.vibrationChannels.map(channel => ({
              ...channel,
              selected: channel.selected || 0,
              selectedSensorId: channel.selectedSensorId || null,
              channelNumber: channel.channelNumber,
            }))
          }
        };
        // 编辑模式：先更新基站，然后提交配置表格，并返回 Promise
        return updateBaseStation(editFormData).then(res => {
          const tableData = {
            ...this.TableCachetable,
            mysql_id: editFormData.mysql_id,
            ip: this.form.ip,
            collectorTypeId: this.form.device_type, // 添加采集站类型ID
            collectorTypeId: this.form.device_type, // 添加采集站类型ID
            table: {
              table_title: this.tableTitle,
              table_value: this.vibrationChannels.map(channel => ({
                ...channel,
                selected: channel.selected || 0,
                selectedSensorId: channel.selectedSensorId || null,
                channelNumber: channel.channelNumber,
              }))
            }
          };
          return updateBaseStationTable(tableData).then(resp => {
            this.$message.success('保存成功', resp.code);
          });
        });
      }
    },
    // 存储节点
    handleNodeClick(selectedKeys, info) {
      if (info.node && info.node.title) {
        const node = info.node.dataRef
        console.log('选中的节点信息:', node)

        if (node && node.treeIcon === 'waterPump') {
          if (!this.$store.state.SettingConnect.BaseStationNode.some(n => n.id === node.id)) {
            this.$store.dispatch('SettingConnect/addBaseStationNode', { title: node.title, key: node.key, id: node.id })
              .then(() => {
                console.log('节点存储成功，当前BaseStationNode:', this.baseStationNode)
                this.getPointsOptions(this.baseStationNode)
                this.$message.success(`${node.title} 成功存储！`)
              })
              .catch(error => {
                console.error('存储节点信息失败:', error)
              })
          } else {
            this.$message.error(`${node.title} 已经存在！`, '警告')
          }
        }
      }
    },
    getConpanyTreeData(id) {
      // 初始化一个空数组来存放树形数据
      const newTreeData = []
      if(!id){
        return
      }
      // 调用接口获取数据
      getBaseStationDeviceList(id).then(res => {
        // 假设 res.data 是你需要的设备信息数组或对象，你可以将它直接 push 进 newTreeData
        newTreeData.push(res.data)

        // 最后将新的数组赋值给 this.treeData
        this.treeData = newTreeData
      }).catch(error => {
        console.error('获取公司下设备信息失败', error)
      })
    },
    // 更改采集站类型触发更换振动通道表格
    ChangeCollectorType(id) {
      getChannelModelList(id).then(res => {
        this.tableTitle = res.table.table_title
        this.vibrationChannels = res.table.table_value
        console.log('打印表格数据调试', this.vibrationChannels)
        // 暂存数据以用提交
        this.TableCachetable = res
        // 防止提交id到后端将后端模板覆盖
        delete this.TableCachetable.id
      })
      // console.log('ChangeCollectorType', id)
    },

    handleSelectAllTemperatureChange(value) {
      this.vibrationChannels.forEach(channel => {
        channel.temperatureMeasurement = value
      })
    },
    // 重置信息
    Reset() {
      this.form = {
        worked: '1',
        lines: '6400',
        frequency: '16384',
        gap: '5',
        wave_gap: '30',
        // 以上是暂时用不到的字段
        device_moitor: '',
        ServeIp: '127.0.0.1',
        device_company: '',
        device_type: '', // 采集类型
        device_coding: '', // 采集站编码
        ip: '', // 基站ip
        device_method: '', // 采集站方法
        company: '', // 初始值
        collectTime: '2' // 添加采样时间字段，默认值为2
      }
      this.tableTitle = []
      this.vibrationChannels = []
    }
  }
}
</script>

<style scoped>
@import './BaseConfig.css';

::v-deep .el-table td, .el-table th {
        padding: 0;
}
</style>
