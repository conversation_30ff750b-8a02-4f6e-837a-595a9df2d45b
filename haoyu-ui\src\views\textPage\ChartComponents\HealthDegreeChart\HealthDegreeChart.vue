<template>
  <div>
    <div class="date-picker-container">
      <div class="date-picker-wrapper">
        <div class="date-picker-label">
          <i class="el-icon-date"></i>
          <span>选择日期时间范围</span>
        </div>
        <el-date-picker
          v-model="dateTimeRange"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:00"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptions"
          @change="handleDateTimeChange"
          range-separator="至"
          class="date-picker-input"
        ></el-date-picker>
      </div>
    </div>
    <div class="chart-container" ref="chartContainer" style="width: 100%; height: 900px;">
      <!-- 高度和宽度对于 ECharts 渲染很重要 -->
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core';
import { HeatmapChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  VisualMapComponent,
  TitleComponent,
  DataZoomComponent // 引入 DataZoom 组件用于X轴缩放
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { mapState } from 'vuex';
import { getHealthDegreeChartData } from './api'; // 你的 API 文件

echarts.use([
  HeatmapChart,
  GridComponent,
  TooltipComponent,
  VisualMapComponent,
  TitleComponent,
  DataZoomComponent, // 注册 DataZoom
  CanvasRenderer
]);

export default {
  name: 'HealthDegreeChart',
  data() {
    return {
      chartInstance: null,
      xCategories: [], // X 轴：现在是 YYYY-MM-DD 格式的日期字符串
      yCategories: [], // Y 轴：测量点ID
      heatmapData: [], // 热力图数据: [[xIndex, yIndex, healthValue], ...]
      dateTimeRange: null, // 选择的日期时间范围 [开始时间戳, 结束时间戳]
      pickerOptions: {
        shortcuts: [{
          text: '最近一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 1);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      rawData: [] // 存储从API获取的原始数据
    };
  },
  computed: {
    ...mapState('tree', ['treePathText']), // 通过 Vuex 获取路径信息
    selectedNode() {
      return this.$store.state.tree.selectedTreeNode; // 返回整个对象，而不是只返回id
    }
  },
  watch: {
/*     selectedNode: {
      handler() {
        this.fetchAndSetChartData();
      },
      deep: true
    }, */
    // 当数据变化时，准备更新图表
    // 注意：如果数据量很大，频繁的完整重绘可能影响性能，但对于一般情况是可行的
    xCategories: 'updateChartIfReady',
    yCategories: 'updateChartIfReady',
    heatmapData: 'updateChartIfReady',
  },
  mounted() {
    this.fetchAndSetChartData();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleDateTimeChange() {
      // 不需要重新请求数据，只需重新处理已有数据
      this.processChartData();
    },

    async fetchAndSetChartData() {

      try {
        const response = await getHealthDegreeChartData(); // API应返回包含 collectorTimestamp 的数据

        // 存储原始数据，以便根据日期筛选时使用
        if (response && response.code === 200 && Array.isArray(response.rows)) {
          this.rawData = response.rows;
          this.processChartData();
        } else {
          console.error("API响应格式不正确或无数据:", response);
          this.xCategories = [];
          this.yCategories = [];
          this.heatmapData = [];
          this.updateChartIfReady();
        }
      } catch (error) {
        console.error("获取健康度图表数据时出错:", error);
        this.xCategories = [];
        this.yCategories = [];
        this.heatmapData = [];
        this.updateChartIfReady();
      }
    },

    processChartData() {
      try {
        if (!Array.isArray(this.rawData) || this.rawData.length === 0) {
          console.warn("没有可用的原始数据进行处理");
          this.xCategories = [];
          this.yCategories = [];
          this.heatmapData = [];
          this.updateChartIfReady();
          return;
        }

        let dataToProcess = this.rawData;

        // 如果选择了日期时间范围，则过滤数据
        if (this.dateTimeRange && this.dateTimeRange.length === 2) {
          const startTime = this.dateTimeRange[0];
          const endTime = this.dateTimeRange[1];

          dataToProcess = this.rawData.filter(item => {
            if (!item.collectorTimestamp) return false;

            // 将时间戳转换为毫秒进行比较
            const timestampInSeconds = parseInt(item.collectorTimestamp);
            if (isNaN(timestampInSeconds)) return false;

            const itemTime = timestampInSeconds * 1000; // 转为毫秒
            return itemTime >= startTime && itemTime <= endTime;
          });

          if (dataToProcess.length === 0) {
            const startDate = new Date(startTime);
            const endDate = new Date(endTime);
            console.warn(`所选日期时间范围 ${startDate.toLocaleString()} 至 ${endDate.toLocaleString()} 没有数据`);
          }
        }

        // --- 数据预处理开始: 按日期聚合 ---
        const dailyAggregatedData = {};
        const uniqueDates = new Set();
        const uniqueMeasurementIds = new Set();

        dataToProcess.forEach(item => {
          // 确保数据中有 collectorTimestamp 字段
          if (item.collectorTimestamp && item.measurementId !== undefined && item.healthValue !== undefined) {
            // 假设 collectorTimestamp 是以秒为单位的 Unix 时间戳字符串或数字
            const timestampInSeconds = parseInt(item.collectorTimestamp);
            if (isNaN(timestampInSeconds)) {
              console.warn("无效的时间戳:", item.collectorTimestamp, "跳过此条目。");
              return;
            }

            // 将 Unix 时间戳 (秒) 转换为 Date 对象
            // *** 如果你的时间戳是毫秒，则不需要 * 1000 ***
            const dateObj = new Date(timestampInSeconds * 1000);

            const year = dateObj.getFullYear();
            const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
            const hour = dateObj.getHours().toString().padStart(2, '0');
            const day = dateObj.getDate().toString().padStart(2, '0');
            const dateString = `${year}-${month}-${day}-${hour}`; // X轴类目：YYYY-MM-DD-HH

            uniqueDates.add(dateString);
            const measurementIdStr = item.measurementId.toString();
            uniqueMeasurementIds.add(measurementIdStr);

            const healthVal = parseFloat(item.healthValue);
            if (isNaN(healthVal)) {
              console.warn("无效的健康值:", item.healthValue, "对于时间戳:", item.collectorTimestamp, "ID:", measurementIdStr, "跳过此健康值。");
              return;
            }

            if (!dailyAggregatedData[dateString]) {
              dailyAggregatedData[dateString] = {};
            }
            if (!dailyAggregatedData[dateString][measurementIdStr]) {
              dailyAggregatedData[dateString][measurementIdStr] = { sum: 0, count: 0 };
            }
            dailyAggregatedData[dateString][measurementIdStr].sum += healthVal;
            dailyAggregatedData[dateString][measurementIdStr].count++;
          } else {
            // console.warn("原始数据条目缺少必要字段 (collectorTimestamp, measurementId, 或 healthValue):", item);
          }
        });

        this.xCategories = Array.from(uniqueDates).sort();
        this.yCategories = Array.from(uniqueMeasurementIds).sort((a, b) => parseInt(a) - parseInt(b)); // 按数字ID排序

        const finalHeatmapData = [];
        this.xCategories.forEach((dateStr, xIndex) => {
          if (dailyAggregatedData[dateStr]) {
            this.yCategories.forEach((measurementIdStr, yIndex) => {
              if (dailyAggregatedData[dateStr][measurementIdStr]) {
                const aggregate = dailyAggregatedData[dateStr][measurementIdStr];
                const avgHealthValue = aggregate.count > 0 ? aggregate.sum / aggregate.count : 0;
                finalHeatmapData.push([xIndex, yIndex, parseFloat(avgHealthValue.toFixed(2))]);
              } else {
                // 可选: 如果某个日期-ID组合没有数据，可以插入一个特殊值，例如 -1 (无数据)
                // finalHeatmapData.push([xIndex, yIndex, -1]);
              }
            });
          }
          // else {
          //   // 如果某一天没有任何数据，可以为所有 measurementId 填充默认值
          //   this.yCategories.forEach((_, yIndex) => {
          //     finalHeatmapData.push([xIndex, yIndex, -1]); // 假设 -1 代表无数据
          //   });
          // }
        });
        this.heatmapData = finalHeatmapData;
        // --- 数据预处理结束 ---

        if (this.heatmapData.length === 0 && dataToProcess.length > 0) {
          console.warn("过滤后的原始数据存在，但转换后的热力图数据为空。请检查数据和转换逻辑，特别是时间戳字段 'collectorTimestamp' 是否存在且正确。");
        }

        // 确保图表在数据处理完成后初始化或更新
        if (this.chartInstance) {
          this.updateChart();
        } else {
          this.initChart();
        }
      } catch (error) {
        console.error("处理健康度图表数据时出错:", error);
        this.xCategories = [];
        this.yCategories = [];
        this.heatmapData = [];
        this.updateChartIfReady();
      }
    },

    initChart() {
      if (this.$refs.chartContainer) {
        if (this.chartInstance) {
          this.chartInstance.dispose();
        }
        this.chartInstance = echarts.init(this.$refs.chartContainer);
        this.chartInstance.setOption(this.getChartOptions());
      } else {
        console.error("图表容器的 ref 未找到。");
      }
    },

    getChartOptions() {
      // minValue 和 maxValue 在 piecewise visualMap 中主要用于 calculable 滑块的范围
      // 你也可以不定义它们，ECharts 会尝试从 pieces 推断
      // const overallMin = -1; // 假设 -1 是可能的最小值
      // const overallMax = 100;

      return {
        title: {
          text: '设备健康度图',
          left: 'center'
        },
        tooltip: {
          position: 'top',
          formatter: (params) => {
            if (params.value && params.value.length === 3) {
              const xCat = this.xCategories[params.value[0]] || `X索引: ${params.value[0]}`; // 日期
              const yCat = this.yCategories[params.value[1]] || `Y索引: ${params.value[1]}`; // 测量点ID
              return `日期: ${xCat}<br/>测量点ID: ${yCat}<br/>平均健康值: ${params.value[2]}`;
            }
            return '';
          }
        },
        grid: {
          left: '3%',
          right: '10%', // 为 visualMap 图例留空间
          bottom: '25%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.xCategories, // 现在是 YYYY-MM-DD-HH 格式
          splitArea: { show: false }, // 通常热力图不需要 splitArea
          axisLabel: {
            rotate: 45,
            interval: 'auto', // 如果日期过多，ECharts 会自动跳过一些标签
            fontSize: 9,
            // formatter: function (value) { return value; } // 默认显示完整日期
          },
          axisTick: {
            show: true,
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'category',
          data: this.yCategories,
          splitArea: { show: false },
          axisLabel: {
            interval: 0 // 显示所有 Y 轴标签
          }
        },
        visualMap: {
          type: 'piecewise',
          calculable: true,
          orient: 'vertical',
          left: 'right',
          top: 'center',
          min: -1, // 确保滑块范围包含-1
          max: 100, // 确保滑块范围包含100
          pieces: [
            {
              value: -1,
              label: '状态: 异常/无效 (-1)',
              color: '#B0B0B0'
            },
            {
              value: 0,
              label: '状态: 零值/待机 (0)',
              color: '#FFFFFF',
              itemStyle: {
                borderColor: '#E0E0E0',
                borderWidth: 0.5
              }
            },
            // 你更新后的4段正健康值
            { gt: 0, lte: 25, label: '健康度: 1-25', color: '#FF0000' },
            { gt: 25, lte: 50, label: '健康度: 26-50', color: '#FF8C00' }, // DarkOrange
            { gt: 50, lte: 75, label: '健康度: 51-75', color: '#FFFF00' },
            { gt: 75, lte: 100, label: '健康度: 76-100', color: '#008000' }
          ],
          // 移除了 text 属性，因为它不适用于 piecewise 类型，或者说 piece 的 label 更合适
          // text: [`健康值: ${maxValue}`, `健康值: ${minValue}`],
          padding: 5,
        },
        dataZoom: [
            {
                type: 'inside',
                xAxisIndex: [0],
                start: 0,
                end: 100,
                filterMode: 'empty'
            }
        ],
        series: [{
          name: '健康度',
          type: 'heatmap',
          data: this.heatmapData,
          label: {
            show: false,
          },
          itemStyle: {
            borderColor: 'gray',
            borderWidth: 0.5
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
    },
    updateChartIfReady() {
      // 简单的直接更新，如果 chartInstance 存在
      if (this.chartInstance) {
        this.updateChart();
      }
    },
    updateChart() {
      if (this.chartInstance) {
        // 使用 notMerge: true 确保旧选项被完全替换，这在数据结构变化时更安全
        this.chartInstance.setOption(this.getChartOptions(), true);
      }
    },
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    }
    // setData 方法现在可能不再需要外部直接调用，
    // 因为数据获取和处理都在 fetchAndSetChartData 内部完成。
    // 如果你仍有场景需要外部设置数据，可以保留它，
    // 但要确保外部传入的数据格式与组件内部处理后的格式一致。
    // setData({ xCategories, yCategories, heatmapData }) {
    //   this.xCategories = xCategories || [];
    //   this.yCategories = yCategories || [];
    //   this.heatmapData = heatmapData || [];
    //   if (!this.chartInstance && this.$refs.chartContainer) {
    //     this.initChart();
    //   } else {
    //     this.updateChart();
    //   }
    // }
  }
}
</script>

<style scoped>
.chart-container {
  border: 1px solid #eee;
}

.date-picker-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8ecf0;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
}

.date-picker-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  gap: 8px;
}

.date-picker-label i {
  font-size: 16px;
  color: #409eff;
}

.date-picker-input {
  width: 400px !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  border-radius: 8px;
}

/* 自定义日期选择器内部样式 */
.date-picker-input /deep/ .el-input__inner {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
  font-size: 13px;
  padding: 0 12px;
  height: 36px;
  line-height: 36px;
}

.date-picker-input /deep/ .el-input__inner:hover {
  border-color: #409eff;
}

.date-picker-input /deep/ .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.date-picker-input /deep/ .el-range-separator {
  color: #606266;
  font-weight: 600;
}
</style>
