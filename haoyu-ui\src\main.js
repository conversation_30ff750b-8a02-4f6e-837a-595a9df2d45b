import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // Ruoyi css
// 大屏的样式
// import { loading, borderBox13, digitalFlop, capsuleChart, borderBox8 } from '@jiaminghi/data-view'
import dataV from "@jiaminghi/data-view"
import 'vue-easytable/libs/theme-default/index.css'
import '@/assets/css/public.scss'
import '@/assets/css/index.scss'
import Echart from './components/echart/index.vue'
import ItemWrap from './components/item-wrap/item-wrap.vue'
import Message from './components/message/message.vue'
import Reacquire from './components/reacquire/reacquire.vue'
import Messages from './components/message/message'

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from '@/api/system/dict/data'
import { getConfigKey } from '@/api/system/config'
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from '@/utils/ruoyi'
// 引入分割页面组件
import splitPane from 'vue-splitpane'
import WebSocketService from '@/utils/alarmSocket/websocket'
// 引入Ant design组件
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 富文本组件
import Editor from '@/components/Editor'
// 文件上传组件
import FileUpload from '@/components/FileUpload'
// 图片上传组件
import ImageUpload from '@/components/ImageUpload'
// 图片预览组件
import ImagePreview from '@/components/ImagePreview'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// 右键菜单组件
import Contextmenu from 'vue-contextmenujs'
// 引入AI客服组件
import AiChatPlugin from '@/components/AiChat/global'
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.$Message = Messages

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('SplitPane', splitPane)
Vue.component('Echart', Echart)
Vue.component('ItemWrap', ItemWrap)
Vue.component('Message', Message)
Vue.component('Reacquire', Reacquire)

// datav组件
// Vue.use(loading)
// Vue.use(borderBox13)
// Vue.use(borderBox8)
// Vue.use(digitalFlop)
// Vue.use(capsuleChart)
Vue.use(dataV)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
Vue.use(Antd)
Vue.use(Contextmenu)

// 注册AI客服组件
Vue.use(AiChatPlugin)

DictData.install()
// 判断是否属于内网环境，否则不连接
let wsUrl = null;
if (window.location.hostname.startsWith('192.168.')) {
  wsUrl = 'ws://*************:8089/ws/notifications';
}
if (wsUrl) {
  WebSocketService.connect(wsUrl);
}

// 将WebSocket服务挂载到Vue原型上
Vue.prototype.$ws = WebSocketService
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

import {
  Table,
  TableColumn,
  Checkbox,
  Button,
  Input
} from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Checkbox)
Vue.use(Button)
Vue.use(Input)
