import request from '@/utils/request'

// 获取所有公司
export function getCompaniesList() {
  return request({
    url: '/deviceManagement/factorymanagement/company/list',
    method: 'get'
  })
}

// 获取公司下的基站
export function getBaseStationList(id) {
  return request({
    url: '/deviceManagement/factorymanagement/collectorByCompany/list/' + id,
    method: 'get'
  })
}

// 获取公司下的每个设备
export function getBaseStationDeviceList(id) {
  return request({
    url: '/deviceManagement/factorymanagement/list/' + id,
    method: 'get'
  })
}

// 获取采集站类型
export function getCollectorTypeList() {
  return request({
    url: '/collectorType/collector_type/list',
    method: 'get'
  })
}

// 获取测点表格数据
export function getDeviceMeasurePointList(ids) {
  return request({
    url: '/measurePoint/measurePointManagement/unbound/list/' + ids,
    method: 'get'
  })
}

// 获取采集方法列表
export function getcCollectorMethodList() {
  return request({
    url: '/collectorMethod/collector_method/list',
    method: 'get'
  })
}

// 根据选择采集类型更改新建中的振动通道的表格
export function getChannelModelList(id) {
  return request({
    url: '/dynamic-entity/channel_model/' + id,
    method: 'get'
  })
}

// 新增指定基站数据
export function addBaseStation(data) {
  return request({
    url: '/deviceManagements/deviceManagements',
    method: 'post',
    data: data
  })
}

// 更新指定采集站数据
export function updateBaseStation(data) {
  return request({
    url: '/deviceManagements/deviceManagements',
    method: 'put',
    data: data
  })
}

// 获取指定基站类型参数列表
export function getBaseStationParamList(id) {
  return request({
    url: '/deviceManagements/deviceManagements/' + id,
    method: 'get'
  })
}

// 获取指定基站表格参数列表
export function getBaseStationTableList(id) {
  return request({
    url: '/dynamic-entity/channel_value/' + id,
    method: 'get'
  })
}

// 测试基站连接按钮
export function ConnectionTest(data) {
  return request({
    url: '/tcpModbus/render/post/createNewConnection',
    method: 'post',
    data: data
  })
}

// 新建/更新指定基站配置数据
export function updateBaseStationTable(data) {
  return request({
    url: '/dynamic-entity/channel_value/save',
    method: 'post',
    data: data
  })
}
