<template>
  <div class="device-relation">
    <div class="content-header">
      <h2>测点-组态关系绑定</h2>
    </div>
    
    <!-- 搜索框 -->
    <div class="operation-container">
      <el-input
        v-model="searchQuery"
        placeholder="请输入设备名称搜索"
        style="width: 200px;"
        @input="handleSearch"
      />
    </div>

    <!-- 设备关系表格 -->
    <el-table
      :data="deviceRelationList"
      border
      style="width: 100%"
      v-loading="loading"
    >
      <!-- 测点名称列 - 只读显示 -->
      <el-table-column 
        prop="pointName" 
        label="测点名称"
      />

      <!-- 一级组态名称列 -->
      <el-table-column label="一级组态名称">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.deviceName" 
            placeholder="请选择一级组态" 
            filterable
            @change="(value) => handleDeviceChange(value, scope.row)"
          >
            <el-option
              v-for="item in deviceConfigurationOptions"
              :key="item.id"
              :label="item.deviceName"
              :value="item.deviceName"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>

<script>
import {
  getDeviceConfiguration,
  getDeviceRelation,
  updateDeviceRelation,
} from '@/api/haoyu-system/deviceManagement/deviceManagement.js'

export default {
  props: {
    currentDevice: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      deviceRelationList: [],
      loading: false,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      searchQuery: '',
      deviceConfigurationOptions: {},
    }
  },
  created() {
    if (this.currentDevice) {
      this.searchQuery = this.currentDevice.title || this.currentDevice.deviceName
    }
    this.getList()
    this.getDeviceOptions()
  },
  methods: {
    // 获取列表数据
    async getList() {
      try {
        this.loading = true
        const res = await getDeviceRelation(this.currentDevice.id)
        if (res.rows) {
          this.deviceRelationList = res.rows.map(item => ({
            ...item,
            deviceName: item.deviceName || ''
          }))
          this.total = res.total
        }
      } catch (error) {
        console.error('获取设备关系列表失败:', error)
        this.$message.error('获取设备关系列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取一级组态选项
    async getDeviceOptions() {
      try {
        const res = await getDeviceConfiguration(this.currentDevice.id)
        if (res.code === 200 && res.rows) {
          this.deviceConfigurationOptions = res.rows
        }
      } catch (error) {
        console.error('获取一级组态列表失败:', error)
        this.$message.error('获取一级组态列表失败')
      }
    },

    // 搜索
    handleSearch() {
      this.pageNum = 1
      this.getList()
    },

    // 一级组态选择变化时
    async handleDeviceChange(value, row) {
      try {
        // 根据选择的deviceName找到对应的id
        const selectedDevice = this.deviceConfigurationOptions.find(item => item.deviceName === value)
        if (!selectedDevice) {
          this.$message.error('未找到对应的设备配置')
          return
        }

        const params = {
          id: row.id,
          devicePartId: String(selectedDevice.id),
          measurePointId: row.measurePointId
        }

        await updateDeviceRelation(params)
        this.$message.success('更新成功')
        this.getList()
      } catch (error) {
        console.error('更新失败:', error)
        this.$message.error('更新失败')
      }
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
  },
}
</script>

<style scoped>
.device-relation {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.content-header {
  margin-bottom: 20px;
}

.content-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.operation-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-pagination .btn-prev,
.el-pagination .btn-next {
  min-width: 30px !important;
}

.el-select {
  width: 100%;
}
</style>