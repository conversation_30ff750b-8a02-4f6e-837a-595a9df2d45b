<template>
  <div>
    <el-link href="https://element.eleme.io" target="_blank">默认链接</el-link>
    <el-link type="primary">主要链接</el-link>
    <el-link type="success">成功链接</el-link>
    <el-link type="warning">警告链接</el-link>
    <el-link type="danger">危险链接</el-link>
    <el-link type="info">信息链接</el-link>
  </div>
</template>

<script>
// import { UploadFile, GetImgObjList, DownloadImgObjList ,DeleteItem } from '@/api/haoyu-system/web3d_api/web_3dapi.js';
// import { chunkAndMergeFile } from '../js/ChunkAndMergeFile.js';
// import "../index.js"

export default {
data() {
  return {
  dialogImageUrl: '',

  };
},

created() {
  // 在组件创建时获取图片列表
  // this.fetchImageList();
},
watch(){

},
mounted() {

},

methods: {

}
};
</script>

<style>
/* @import '../css/FontStyle.css' */
</style>
