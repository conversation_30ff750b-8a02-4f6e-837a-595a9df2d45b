.settings-page {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 视口高度 */
  width: 100%; /* 宽度100% */
  box-sizing: border-box;
}

.header_text {
  font-size: 25px;
  text-align: left; /* 确保文本靠左对齐 */
}

.settings-content {
  flex: 1; /* 自动填充剩余空间 */
  overflow-y: auto; /* 超出时滚动 */
  padding: 10px 0; /* 上下内边距 */
}

.settings-operate {
  display: flex;
  justify-content: flex-start; /* 按钮之间均匀分布 */
  padding-top: 10px;
}

.settings-operate .el-button {
  margin-right: 10px;
}

.el-popper[x-placement^=top] {
  padding: 4px;
}
