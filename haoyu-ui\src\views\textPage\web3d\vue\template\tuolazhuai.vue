<template>
  <div id="tuolazhuai">
    <!-- Widget List -->
    <div class="widget-list">
      <div
        v-for="widget in widgetList"
        :key="widget.type"
        class="widget"
        draggable="true"
        @dragstart="onDragStart($event, widget)"
        @mousedown="onWidgetMouseDown($event)"
      >
        {{ widget.label }}
      </div>
    </div>

    <!-- Drop Panel -->
    <div
      class="panel"
      @dragover.prevent
      @drop="onDrop"
    >
      <div
        v-for="item in list"
        :key="item.id"
        class="box"
        :style="getBoxStyle(item)"
        @mousedown="onBoxMouseDown($event, item)"
      >
        我是{{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'tuolazhuai',
  data() {
    return {
      list: [], // 存储放置的组件
      widgetList: [
        { type: 'pie', label: '饼图' },
        { type: 'line', label: '折线图' },
        { type: 'bar', label: '柱状图' },
      ],
      currentId: 0,    // 用于生成唯一 ID
      dragData: {
        type: null,     // 'new' 或 'move'
        widget: null,   // 对于 'new'，是 widget 对象；对于 'move'，是 list 中的 item
        offsetX: 0,     // 拖拽时鼠标相对于组件的 X 偏移
        offsetY: 0,     // 拖拽时鼠标相对于组件的 Y 偏移
      },
      isDragging: false,  // 是否正在拖拽
      dragStartPos: { x: 0, y: 0 }, // 拖拽开始时鼠标位置
      dragMoveHandler: null, // 动态添加的 mousemove 事件处理器
      dragEndHandler: null,  // 动态添加的 mouseup 事件处理器
    };
  },
  methods: {
    /**
     * 处理拖拽开始事件（从 widget 列表拖拽到面板）
     * @param {DragEvent} event - 拖拽事件
     * @param {Object} widget - 当前拖拽的组件
     */
    onDragStart(event, widget) {
      if (event.dataTransfer) {
        // 设置拖拽的数据
        event.dataTransfer.setData('widgetType', widget.type);
        event.dataTransfer.setData('widgetLabel', widget.label);

        // 可选：设置自定义拖拽图标以提升用户体验
        const dragIcon = document.createElement('div');
        dragIcon.style.width = '100px';
        dragIcon.style.height = '100px';
        dragIcon.style.background = 'rgba(0,0,0,0.5)';
        dragIcon.style.color = '#fff';
        dragIcon.style.display = 'flex';
        dragIcon.style.alignItems = 'center';
        dragIcon.style.justifyContent = 'center';
        dragIcon.style.borderRadius = '8px';
        dragIcon.style.fontSize = '16px';
        dragIcon.innerText = widget.label;
        document.body.appendChild(dragIcon);
        event.dataTransfer.setDragImage(dragIcon, 50, 50);
        setTimeout(() => {
          document.body.removeChild(dragIcon);
        }, 0);
      }
      // 更新拖拽数据
      this.dragData.type = 'new';
      this.dragData.widget = widget;
    },

    /**
     * 处理放置事件（将组件放置到面板上）
     * @param {DragEvent} event - 放置事件
     */
    onDrop(event) {
      event.preventDefault();
      if (this.dragData.type !== 'new') return;

      const panel = event.currentTarget;
      const rect = panel.getBoundingClientRect();

      // 计算放置位置相对于面板的坐标
      const x = event.clientX - rect.left - this.dragData.offsetX;
      const y = event.clientY - rect.top - this.dragData.offsetY;

      // 确保组件放置在面板的边界内
      const boundedX = Math.max(0, Math.min(x, rect.width - 150)); // 150 是盒子的宽度
      const boundedY = Math.max(0, Math.min(y, rect.height - 100)); // 100 是盒子的高度

      // 将新的组件添加到列表中
      this.list.push({
        id: this.currentId++,
        x: boundedX,
        y: boundedY,
        label: this.dragData.widget.label,
      });

      // 重置拖拽数据
      this.dragData.type = null;
      this.dragData.widget = null;
    },

    /**
     * 处理鼠标按下事件（从 widget 列表拖拽的偏移量）
     * @param {MouseEvent} event - 鼠标事件
     */
    onWidgetMouseDown(event) {
      this.dragData.offsetX = event.offsetX;
      this.dragData.offsetY = event.offsetY;
    },

    /**
     * 处理鼠标按下事件（拖拽面板上的已放置组件）
     * @param {MouseEvent} event - 鼠标事件
     * @param {Object} item - 被拖拽的组件
     */
    onBoxMouseDown(event, item) {
      event.stopPropagation(); // 防止触发面板的拖拽事件

      this.isDragging = true;
      this.dragData.type = 'move';
      this.dragData.widget = item;
      this.dragData.offsetX = event.clientX - item.x;
      this.dragData.offsetY = event.clientY - item.y;

      // 记录拖拽开始时鼠标的位置
      this.dragStartPos = {
        x: event.clientX,
        y: event.clientY,
      };

      // 动态添加全局的 mousemove 和 mouseup 事件监听器
      this.dragMoveHandler = this.onGlobalMouseMove;
      this.dragEndHandler = this.onGlobalMouseUp;
      document.addEventListener('mousemove', this.dragMoveHandler);
      document.addEventListener('mouseup', this.dragEndHandler);
    },

    /**
     * 全局 mousemove 事件处理器
     * @param {MouseEvent} event - 鼠标移动事件
     */
    onGlobalMouseMove(event) {
      if (!this.isDragging || this.dragData.type !== 'move') return;

      const panel = this.$el.querySelector('.panel');
      const rect = panel.getBoundingClientRect();

      // 计算新的位置
      let newX = event.clientX - rect.left - this.dragData.offsetX;
      let newY = event.clientY - rect.top - this.dragData.offsetY;

      // 确保组件放置在面板的边界内
      newX = Math.max(0, Math.min(newX, rect.width - 150)); // 150 是盒子的宽度
      newY = Math.max(0, Math.min(newY, rect.height - 100)); // 100 是盒子的高度

      // 更新组件的位置
      this.dragData.widget.x = newX;
      this.dragData.widget.y = newY;
    },

    /**
     * 全局 mouseup 事件处理器
     * @param {MouseEvent} event - 鼠标释放事件
     */
    onGlobalMouseUp(event) {
      if (!this.isDragging) return;

      this.isDragging = false;
      this.dragData.type = null;
      this.dragData.widget = null;

      // 移除全局的 mousemove 和 mouseup 事件监听器
      document.removeEventListener('mousemove', this.dragMoveHandler);
      document.removeEventListener('mouseup', this.dragEndHandler);
      this.dragMoveHandler = null;
      this.dragEndHandler = null;
    },

    /**
     * 根据组件的位置生成样式
     * @param {Object} item - 列表中的组件项
     * @returns {Object} - 样式对象
     */
    getBoxStyle(item) {
      return {
        transform: `translate(${item.x}px, ${item.y}px)`,
        zIndex: this.isDragging && this.dragData.widget === item ? 1000 : 1, // 提升拖拽中的组件层级
      };
    },
  },
};
</script>

<style scoped>
#tuolazhuai {
  display: flex;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Widget List Styling */
.widget-list {
  width: 300px;
  background-color: #dfdfdf;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
}

.widget {
  width: 100px;
  height: 100px;
  border: 1px solid red;
  font-size: 20px;
  text-align: center;
  line-height: 100px;
  margin-bottom: 20px;
  cursor: grab;
  user-select: none;
  background-color: #fff;
  border-radius: 8px;
  transition: transform 0.2s;
}

.widget:hover {
  transform: scale(1.05);
}

.widget:active {
  cursor: grabbing;
}

/* Panel Styling */
.panel {
  flex: 1;
  background-color: #efefef;
  position: relative;
  overflow: hidden;
}

/* Dropped Box Styling */
.box {
  width: 150px;
  height: 100px;
  border: 1px solid blue;
  position: absolute;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  border-radius: 4px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s;
}

.box:hover {
  box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);
}
</style>
