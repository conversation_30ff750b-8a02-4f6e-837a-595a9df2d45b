<template>
  <el-dialog
    :visible.sync="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="30%"
    @close="handleCancel"
  >
    <el-form label-width="110px">
      <el-form-item label="描述">
        <el-input
          v-model="form.inputValue"
          size="mini"
          :placeholder="placeholder"
        />
      </el-form-item>

      <div v-if="treeIcon === 'axis'">
        <el-form-item label="转速">
          <el-input
            v-model="form.inputSpeed"
            size="mini"
            placeholder="请输入转速"
          />
        </el-form-item>
      </div>

      <!-- 动态显示额外的输入框 -->
      <div v-if="treeIcon === 'pulleys'">
        <el-form-item label="输入转速">
          <el-input
            v-model="form.inputSpeed"
            size="mini"
            placeholder="请输入输入转速"
          />
        </el-form-item>
        <el-form-item label="输出转速">
          <el-input
            v-model="form.outputSpeed"
            size="mini"
            placeholder="请输入输出转速"
          />
        </el-form-item>
        <el-form-item label="输入皮带轮周长">
          <el-input
            v-model="form.smallPulleyCircumference"
            size="mini"
            placeholder="请输入输入皮带轮周长"
          />
        </el-form-item>

        <el-form-item label="输出皮带轮周长">
          <el-input
            v-model="form.largePulleyCircumference"
            size="mini"
            placeholder="请输入输出皮带轮周长"
          />
        </el-form-item>

        <el-form-item label="皮带长度">
          <el-input
            v-model="form.beltLength"
            size="mini"
            placeholder="请输入皮带长度"
          />
        </el-form-item>
        <el-form-item label="传动比">
          <el-input
            v-model="form.ratio"
            size="mini"
            placeholder="请输入传动比"
          />
        </el-form-item>
      </div>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    editModel: {
      type: Boolean,
      default: false
    },
    tableDataTree: {
      type: [Object, Array], // 接受 Object 或 Array 类型
      required: true
    },
    selectedNode: {
      type: Object,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '项目'
    },
    defaultValue: {
      type: String,
      default: ''
    },
    treeIcon: {
      type: String,
      default: ''
    },
    dialogDataList: {
      typeof: Object,
      default: ''
    }
  },
  data() {
    return {
      form: {
        treeIcon: '',
        smallPulleyCircumference: '', // 小皮带轮周长
        largePulleyCircumference: '', // 大皮带轮周长
        beltLength: '', // 皮带长度
        ratio: '', // 传动比
        inSpeed: '', // 输入转速
        inputSpeed: '', // 输入转速
        outputSpeed: '', // 输出转速
        inputValue: this.defaultValue
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.editModel ? `编辑 ${this.title}` : `新建 ${this.title}`
    },
    placeholder() {
      return `请输入${this.title}`
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        if (this.treeIcon === 'pulleys') {
          this.form = this.selectedNode.table
        }
        // 如果 title 中有括号并带有数字，则使用正则表达式去除括号及其后内容
        if (this.form && this.form.inputValue !== null && this.form.inputValue !== undefined) {
          this.form.inputValue = this.title.replace(/\(.*\)$/, '')
        } else {
          console.error('form 或 inputValue 为 null 或未定义')
        }

        // 设置转速值
        if (this.dialogDataList && this.dialogDataList.inputSpeed) {
          this.form.inputSpeed = this.dialogDataList.inputSpeed
        } else if (this.selectedNode && this.selectedNode.inputSpeed) {
          this.form.inputSpeed = this.selectedNode.inputSpeed
        }
      }
    }
    // editModel(newVal, oldVal) {
    //   if (newVal !== oldVal) {
    //     if (newVal) {
    //       this.form = this.selectedNode.table
    //       console.log('进入编辑模式')
    //     } else {
    //       console.log('退出编辑模式')
    //     }
    //   }
    // }
  },
  methods: {
    // 重置方法
    Reset() {
      this.form = {
        smallPulleyCircumference: '', // 小皮带轮周长
        largePulleyCircumference: '', // 大皮带轮周长
        beltLength: '', // 皮带长度
        ratio: '', // 传动比
        inSpeed: '', // 输入转速
        inputSpeed: '', // 输入转速
        outputSpeed: '', // 输出转速
        inputValue: this.defaultValue
      }
    },
    handleConfirm() {
      const tableData = this.treeIcon === 'pulleys' ? {
        smallPulleyCircumference: this.form.smallPulleyCircumference,
        largePulleyCircumference: this.form.largePulleyCircumference,
        beltLength: this.form.beltLength,
        treeRank: treeRank, // 位置
        inputSpeed: this.form.inputSpeed, // 输入转速或转速
        outputSpeed: this.form.outputSpeed, // 输出转速
        inputValue: this.form.inputValue,
        ratio: this.form.ratio
      } : {
        inputValue: this.form.inputValue,
        inputSpeed: this.form.inputSpeed // 输入转速或转速
      }

      // 如果 treeIcon 是 axis 的情况下，加入 inputSpeed
      if (['axis'].includes(this.treeIcon)) {
        tableData.inputSpeed = this.form.inputSpeed
      }
      let treeRank = ''
      if (this.selectedNode) {
        if (this.selectedNode.children) {
          treeRank = this.selectedNode.children.length
        } else {
          treeRank = 0
        }
      } else {
        treeRank = this.tableDataTree.length
        // console.log(treeRank)
      }
      const data = {
        title: this.form.inputValue +
       (this.form.inputSpeed && this.treeIcon !== 'pulleys'
         ? ' (' + this.form.inputSpeed + 'RPM)'
         : ''),
        treeIcon: this.treeIcon,
        inputSpeed: this.form.inputSpeed, // 输入转速或转速
        outputSpeed: this.form.outputSpeed, // 输出转速
        treeRank: treeRank, // 位置
        mongodbId: '',
        smallPulleyCircumference: this.form.smallPulleyCircumference, // 输入皮带轮周长
        largePulleyCircumference: this.form.largePulleyCircumference, // 输出皮带轮周长
        beltLength: this.form.beltLength, // 皮带长度
        ratio: this.form.ratio, // 传动比
        table: tableData
      }
      if (this.editModel) {
        this.syncMongodbIds(this.selectedNode, data)
        console.log('提交数据', data)
        this.$emit('confirm', data)
        this.Reset()
      } else {
        this.$emit('confirm', data)
        this.Reset()
      }
    },
    // 把原有的树节点直接赋值给提交的树结构
    syncMongodbIds(selectedNode, dataNode) {
      // 确保两个节点都存在
      if (!selectedNode || !dataNode) return
      // 同步 mongodbId
      // selectedNode.mongodbId给dataNode.mongodbId
      dataNode.mongodbId = selectedNode.mongodbId
      dataNode.treeRank = selectedNode.treeRank
      dataNode.children = selectedNode.children
      // 如果两个节点都有子节点，递归处理每个子节点
      if (selectedNode.children && dataNode.children) {
        // 确保两个子节点数组长度相同
        const length = Math.min(selectedNode.children.length, dataNode.children.length);

        for (let i = 0; i < length; i++) {
          this.syncMongodbIds(selectedNode.children[i], dataNode.children[i]) // 递归处理子节点
          console.log(dataNode)
        }
      }
    },
    handleCancel() {
      this.$emit('cancel')
      this.Reset()
    }
  }
}
</script>

<style>
.dialog-footer {
  text-align: right;
}
</style>
