<template>
  <div id="app" class="chart-wrapper">
    <div>
      <!-- 控制倍频游标显示的按钮 -->
      <button @click="toggleCursors">
        {{ showCursors ? '隐藏倍频游标' : '显示倍频游标' }}
      </button>
    </div>
    <div id="chart" ref="chart" class="chart-container">
      <!-- 绑定 ref 用于图表初始化 -->
      <div ref="trendChartContainer" class="chart-item"></div>
      <div ref="spectrumChartContainer" class="chart-item"></div>
      <div ref="waveformChartContainer" class="chart-item"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      initialFrequency: 5, // 初始频率间隔为5Hz
      graphicXData: [5], // 第一条游标的初始位置为5Hz
      chart: null, // ECharts 实例
      showCursors: true // 控制倍频游标显示与否
    }
  },
  mounted() {
    this.chart = echarts.init(this.$refs.chart) // 初始化 ECharts
    this.drawLine() // 初始绘制图表
    this.chart.on('dataZoom', this.refreshGraphic) // 监听dataZoom事件
    window.addEventListener('resize', this.refreshGraphic) // 监听窗口resize事件
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.refreshGraphic) // 移除事件监听
  },
  methods: {
    // 切换倍频游标的显示状态
    toggleCursors() {
      this.showCursors = !this.showCursors
      this.updateCursorsVisibility() // 更新游标的显示或隐藏状态
    },

    // 计算游标位置
    calculateXData() {
      const newData = []
      for (let i = 0; i < 10; i++) {
        newData.push(this.graphicXData[0] * (i + 1)) // 按倍数递增，后续游标的位置为倍数
      }
      return newData
    },

    // 绘制图表
    drawLine() {
      const option = {
        backgroundColor: '#1f2d3d', // 背景色，适合大屏
        grid: {
          left: '2%',
          right: '2%',
          top: '10%',
          bottom: '10%',
          containLabel: true // 包含标签的区域
        },
        xAxis: {
          id: '1',
          type: 'value',
          min: 0,
          max: 100,
          interval: 5, // 每隔5Hz显示一个刻度
          axisLine: { lineStyle: { color: '#fff' }}, // 轴线颜色
          axisLabel: {
            color: '#ffffff', // 坐标轴标签颜色
            formatter: '{value} Hz'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#fff' }}, // 轴线颜色
          axisLabel: { color: '#ffffff' } // 坐标轴标签颜色
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#1f2d3d',
          borderColor: '#6a7985',
          borderWidth: 1,
          textStyle: { color: '#ffffff' },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        series: [
          {
            type: 'line',
            smooth: true, // 平滑曲线
            data: Array.from({ length: 100 }, () => Math.random() * 100), // 模拟Y轴数据
            lineStyle: { color: '#26a69a', width: 2 } // 线的样式
          }
        ]
      }

      this.chart.setOption(option)

      // 绘制可拖动的graphic和倍频标注
      this.refreshGraphic()
    },

    // 渲染可拖动的graphic和倍频标注
    refreshGraphic() {
      const chartHeight = this.chart.getHeight() - 80 // 获取图表的高度并减去顶部和底部空隙
      const graphic = []

      // 为每一个游标绘制可拖动的图形，并标注倍频信息
      this.calculateXData().forEach((item, index) => {
        const xPosition = this.chart.convertToPixel({ xAxisId: '1' }, item) // 将X轴坐标转换为像素

        // 添加实心圆点 (红色)
        graphic.push({
          type: 'circle',
          z: 101,
          shape: {
            cx: xPosition,
            cy: 80, // 圆点 Y 轴位置，偏移一定距离
            r: 4 // 圆点半径
          },
          style: {
            fill: '#ff0000' // 实心圆点颜色为红色
          },
          cursor: 'default', // 圆点不可拖动
          id: `cursor-circle-${index}` // 为每个游标点添加唯一ID
        })

        // 添加游标线 (红色)
        graphic.push({
          type: 'rect',
          z: 101,
          shape: {
            width: 2, // 更加细的游标线
            height: chartHeight * 0.85 // 将矩形高度设置为图表高度的85%
          },
          position: [xPosition, 80], // 使游标线紧接圆点向下延伸
          style: {
            fill: 'rgba(255, 0, 0, 0.8)' // 游标线颜色为红色
          },
          cursor: 'ew-resize', // 设置鼠标样式为左右拖动
          draggable: index === 0, // 仅第一条游标线可以拖动
          ondrag: (param) => {
            // 拖动时动态更新游标线位置
            if (index === 0) {
              this.onDrag(param)
            }
          },
          ondragend: (param) => {
            // 拖动结束后更新位置
            if (index === 0) {
              this.onDragend(param)
            }
          },
          id: `cursor-line-${index}` // 为每条游标线添加唯一ID
        })

        // 添加倍频标注
        graphic.push({
          type: 'text',
          z: 102,
          style: {
            text: `X${index + 1}`, // 标注倍频，如X1、X2、X3等
            x: xPosition + 10, // 文本位于游标右侧
            y: 30, // 文本位置靠上
            fill: '#ffffff',
            font: '14px Arial'
          },
          id: `cursor-text-${index}` // 为每个文本标注添加唯一ID
        })
      })

      this.chart.setOption({ graphic }) // 更新graphic
    },

    // 更新游标显示/隐藏状态
    updateCursorsVisibility() {
      const width = this.showCursors ? 2 : 0 // 控制游标线宽度，隐藏时为0，显示时为2
      const opacity = this.showCursors ? 1 : 0 // 通过透明度控制游标的显示与隐藏
      // console.log(width)
      // 更新每个游标元素的透明度和宽度
      this.calculateXData().forEach((_, index) => {
        this.chart.setOption({
          graphic: [
            { id: `cursor-circle-${index}`, style: { opacity }},
            { id: `cursor-line-${index}`, style: { opacity, width }},
            { id: `cursor-text-${index}`, style: { opacity }}
          ]
        })
      })
    },

    // 拖动时更新位置
    onDrag(param) {
      const position = param.target.x
      const newX = this.chart.convertFromPixel({ xAxisId: '1' }, position)
      this.graphicXData[0] = newX // 更新第一条游标的位置
      this.refreshGraphic() // 动态更新位置
    },

    // 拖动结束时更新所有游标的位置，并重新绘制图表
    onDragend(param) {
      const position = param.target.x
      const newX = this.chart.convertFromPixel({ xAxisId: '1' }, position)
      this.graphicXData[0] = newX // 更新第一条游标的位置
      this.drawLine() // 重新绘制所有游标
    }
  }
}
</script>

<style scoped>
.chart-wrapper {
  background-color: #1f2d3d; /* 适合大屏的背景色 */
  padding: 20px; /* 添加内边距 */
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* 高度设为全屏高度 */
}

.chart-container {
  width: 90%;
  height: 80vh; /* 图表容器的高度设为视口的80% */
}
</style>
