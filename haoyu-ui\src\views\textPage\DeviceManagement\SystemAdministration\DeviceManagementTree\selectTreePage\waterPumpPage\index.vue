<template>
  <div class="waterpump-component">
    <div class="header">
      <h2>设备信息配置</h2>
    </div>
    <!-- 分割线 -->
    <div class="divider" />
    <!-- 内容部分 -->
    <div class="content">
      <!-- 动态加载组件，传递 nodeData -->
      <component
        :is="currentComponent"
        v-if="currentComponent"
        :node-data="nodeData"
        @back="handleBack"
      />
    </div>
  </div>
</template>

<script>
import ConfigSetupComponent from './ConfigSetupComponent.vue'

export default {
  components: {
    ConfigSetupComponent // 引入新的组件
  },
  props: {
    nodeData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      currentComponent: 'ConfigSetupComponent' // 当前显示的组件，初始为 null
    }
  },
  mounted() {
    // 如果需要，可以在页面加载时默认显示一个组件
    // this.currentComponent = 'ConfigSetupComponent'
  },
  methods: {
    handleConfigSetup() {
      console.log('组态设置按钮点击')
      this.currentComponent = 'ConfigSetupComponent'
      // 这里添加组态设置的逻辑
    },
    // 执行返回操作
    handleBack() {
      this.currentComponent = null // 点击返回按钮时，将 currentComponent 置为 null
    }
  }
}
</script>

<style scoped>
.waterpump-component {
  height: 100%;
  padding: 20px;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #007bff;
  padding: 10px;
  border-radius: 6px;
  color: white;
}

h2 {
  margin: 0;
  font-size: 20px;
  color: #ffffff;
}

.config-button {
  background-color: #0056b3;
  border-color: #0056b3;
}

.divider {
  margin-top: 10px;
  border-bottom: 2px solid #ddd;
}

.content {
  height: 90%;
  margin-top: 10px;
  padding: 10px;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
