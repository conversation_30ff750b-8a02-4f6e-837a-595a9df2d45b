import request from '@/utils/request'

// 查询设备树列表
export function listDeviceManagement() {
    return request({
        url: '/deviceManagement/factorymanagement/list',
        method: 'get'
    })
}

// 获取设备一级组态
export function getDeviceConfiguration(parentId) {
    return request({
        url: `/deviceConfiguration/configuration/firstList/${parentId}`,
        method: 'get'
    })
}

// 获取设备测点
export function getDeviceMeasurePoint(ids) {
    return request({
        url: `/deviceManagement/factorymanagement/deviceMeasurePoint/list/${ids}`,
        method: 'get'
    })
}

// 获取设备测点与设备关系
export function getDeviceRelation(deviceId) {
    return request({
        url: `/measurePoint/measurePointManagement/withConfiguration/list`,
        method: 'get',
        params: {
            deviceId
        },
    })
}

// 修改设备测点与设备关系
export function updateDeviceRelation(data) {
    return request({
        url: `/measurePoint/measurePointManagement`,
        method: 'PUT',
        data: data,
    })
}