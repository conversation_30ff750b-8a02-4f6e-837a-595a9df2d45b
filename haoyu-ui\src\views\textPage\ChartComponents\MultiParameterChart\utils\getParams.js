/**
 * 获取图表参数
 * @param {string} params.ftype 图表类型
 * @param {string} params.pointId 点ID
 * @param {string} params.timePoint 时间点
 * @param {string} params.deviceId 设备ID
 * @param {string} params.fs [采样率，线频率]
 * @returns {Object} 参数对象
 */
export function getParams(ftype, device_id, point_id, time_point, timeSignalUnitId,fs) {
  return {
    device_id: String(device_id),

    point_id: String(point_id),

    time_point: time_point,

    ftype: ftype,

    input_type: 0,

    outer_type: timeSignalUnitId,

    cf: 2,

    fth: 0.02,

    band: [2],

    win: "hanning",

    ytype: 0,

    fs_lines: fs,

    fband: [],

    nperseg: "",

    noverlap: ""
  }
}
