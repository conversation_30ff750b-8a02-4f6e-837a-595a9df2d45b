import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import tree from './modules/tree'
import SettingConnect from './modules/SettingsConnect'
import selectedRowModule from './modules/selectedRowModule'
import dataStore from './modules/dataStore'
import measureData from './modules/measureData'
import chartSwitcher from './modules/chartSwitcher'
import chartLink from './modules/chartLink'
Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    tree,
    SettingConnect,
    selectedRowModule,
    dataStore,
    measureData,
    chartSwitcher,
    chartLink
  },
  getters
})

export default store
