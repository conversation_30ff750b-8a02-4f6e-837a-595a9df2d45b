// chartZoomMixin.js
export const chartZoomMixin = {
  data() {
    return {
      startPos: [0, 0],
      isDrawing: false,
      existingGraphics: [] // 新增：用于存储现有的 graphic 元素
    }
  },

  methods: {
    clearSelection() {
      if (!this.chart) return
      // 获取当前的 graphic 配置
      const model = this.chart.getModel();
      const currentGraphic = model.getComponent('graphic')?.option?.elements || [];
      // 过滤掉缩放选区的 graphic 元素（根据 id）
      // newGraphic 是当前的 graphic 元素，不包含 selectRect
      const newGraphic = currentGraphic.find(item => item.id === 'selectRect');
      if(newGraphic){
        newGraphic.$action = 'remove'
        this.chart.setOption({
          graphic: newGraphic
        })
      }
      /* this.chart.setOption({

        graphic: newGraphic
      }, {
        replaceMerge: ['graphic']
      }) */
      console.log('clearSelection');
    },

    drawSelection(start, end, shouldZoom = false) {
      if (!this.chart) return

      const [startX, startY] = start
      const [endX, endY] = end
        // 获取当前的所有 graphic 元素
      const model = this.chart.getModel();
      const currentGraphic = model.getComponent('graphic')?.graphicElements || [];
      // 过滤掉旧的 selectRect（如果存在）
      const existingGraphics = currentGraphic.filter(item => item.id !== 'selectRect');

      this.chart.setOption({
        graphic: [
          ...existingGraphics,
          {
          type: 'rect',
          id: 'selectRect',
          shape: {
            x: Math.min(startX, endX),
            y: Math.min(startY, endY),
            width: Math.abs(endX - startX),
            height: Math.abs(endY - startY)
          },
          style: {
            fill: 'rgba(0,0,0,0.2)',
            stroke: '#666',
            lineWidth: 1
          },
          silent: true,
          z: 100
        }]
      }/* , {
        merge: true
      } */)

      if (shouldZoom) {
        const grid = this.chart.getModel().getComponent('grid').coordinateSystem
        const gridRect = {
          x: grid.getRect().x,
          width: grid.getRect().width,
        }

        const clampedStartX = Math.max(gridRect.x, Math.min(gridRect.x + gridRect.width, Math.min(startX, endX)))
        const clampedEndX = Math.max(gridRect.x, Math.min(gridRect.x + gridRect.width, Math.max(startX, endX)))

        const startValue = this.chart.convertFromPixel({ xAxisIndex: 0 }, clampedStartX)
        const endValue = this.chart.convertFromPixel({ xAxisIndex: 0 }, clampedEndX)

        // 计算选区的中心点
        const centerValue = (startValue + endValue) / 2
        // 计算选区的宽度
        const selectionWidth = Math.abs(endValue - startValue)
        // 设置一个较小的边距比例（改为选区宽度的20%）
        const padding = selectionWidth * 0

        // 计算最终的缩放范围，确保选区在中心
        const finalStartValue = centerValue - selectionWidth/2 - padding
        const finalEndValue = centerValue + selectionWidth/2 + padding

        this.chart?.setOption({
          dataZoom: [{
            type: 'inside',
            disabled: false,
            zoomOnMouseWheel: true,
            moveOnMouseMove: false,
            preventDefaultMouseMove: true,
            zoomLock: false,
            throttle: 100,
            minSpan: 0.001,  // 统一minSpan值
            startValue: finalStartValue,
            endValue: finalEndValue
          }]
        })
      }
    },

    resetZoom(dataLength) {
      this.chart?.setOption({
        dataZoom: [{
          type: 'inside',
          disabled: false,
          zoomOnMouseWheel: true,
          moveOnMouseMove: false,
          preventDefaultMouseMove: true,
          zoomLock: false,
          throttle: 100,
          minSpan: 0.001,  // 统一minSpan值
          startValue: 0,
          endValue: dataLength - 1
        }]
      }, {
        replaceMerge: ['dataZoom']
      })
    },

    initZoomEvents(dataLength) {
      if (!this.chart) return

      // 初始化时设置缩放选项
      this.chart.setOption({
        dataZoom: [{
          type: 'inside',
          disabled: false,
          zoomOnMouseWheel: true,
          moveOnMouseMove: false,
          preventDefaultMouseMove: true,
          zoomLock: false,
          throttle: 100,
          minSpan: 0.0001,  // 最小缩放比例
          precision: 0.0001, // 设置更高的精度
          startValue: 0,
          endValue: dataLength - 1
        }]
      })

      // 阻止默认右键菜单
      this.chart.getZr().on('contextmenu', (e) => {
        e.event.preventDefault()
        e.event.stopPropagation()
      })

      // 鼠标按下事件
      this.chart.getZr().on('mousedown', (e) => {
        if (e.event?.button === 2) {
          const pointInPixel = [e.offsetX, e.offsetY]
          if (this.chart?.containPixel({ gridIndex: 0 }, pointInPixel)) {
            this.clearSelection()
            this.startPos = [e.offsetX, e.offsetY]
            this.isDrawing = true
            this.drawSelection(this.startPos, this.startPos)
          }
        }
      })

      // 鼠标移动事件
      this.chart.getZr().on('mousemove', (e) => {
        if (this.isDrawing) {
          const currentPos = [e.offsetX, e.offsetY]
          this.drawSelection(this.startPos, currentPos)
        }
      })

      // 鼠标松开事件
      this.chart.getZr().on('mouseup', (e) => {
        if (e.event?.button === 2) {
          this.isDrawing = false
          const endPos = [e.offsetX, e.offsetY]

          const distance = Math.sqrt(
            Math.pow(endPos[0] - this.startPos[0], 2) +
            Math.pow(endPos[1] - this.startPos[1], 2)
          )

          if (distance < 5) {
            this.clearSelection()
            this.resetZoom(dataLength)
            // 如果重置缩放也需要更新连接线，则调用回调
            if (typeof this.handleZoomFromMixin === 'function') {
              this.handleZoomFromMixin();
            }
          } else {
            this.drawSelection(this.startPos, endPos, true)

            setTimeout(() => {
              this.clearSelection();
              if (typeof this.handleZoomFromMixin === 'function') {
                this.handleZoomFromMixin(); // 通知 TimeDomainChart.vue 更新
              }
            }, 50);

          }
        }
      })

      // 鼠标离开事件
      this.chart.getZr().on('globalout', () => {
        if (this.isDrawing) {
          this.isDrawing = false
          this.clearSelection()
        }
      })

      // 双击重置事件
      this.chart.getZr().on('dblclick', () => {
        this.clearSelection()
        this.resetZoom(dataLength)
      })
    },

    disposeZoomEvents() {
      if (!this.chart) return
      const zr = this.chart.getZr()
      zr.off('contextmenu')
      zr.off('mousedown')
      zr.off('mousemove')
      zr.off('mouseup')
      zr.off('globalout')
      zr.off('dblclick')
    }
  },

  beforeDestroy() {
    this.disposeZoomEvents()
  }
}
