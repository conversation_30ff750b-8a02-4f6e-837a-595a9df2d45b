import request from '@/utils/request'

//获取指定设备所有模型id与名字
export function getDeviceModelIdAndName(deviceId) {
    return request({
        url: `/fileRelation/relation/deviceDetails/${deviceId}`,
        method: 'get'
    })
}



//获取指定设备下的所有测点
export function getDeviceMeasurePoint(ids) {
    return request({
        url: `/deviceManagement/factorymanagement/deviceMeasurePoint/list/${ids}`,
        method: 'get'
    })
}

//获取指定设备的绑定情况
export function getDeviceWithPoint(deviceId) {
    return request({
        url: `/fileRelation/relation/deviceWithPoint/list`,
        method: 'get',
        params: {
            deviceId: deviceId
        }
    })
}

//修改指定设备的绑定测点信息
export function UpdateDeviceWithPoint(data) {
    return request({
        url: '/deviceWithPoint/deviceWithPoint',
        method: 'put',
        data: data
    })
}

//新增模型和测点绑定记录
export function CreateDeviceWithPoint(data) {
    return request({
        url: '/deviceWithPoint/deviceWithPoint',
        method: 'post',
        data: data
    })
}

// 删除指定设备的绑定测点信息
export function deleteDeviceWithPoint(ids) {
    return request({
        url: `/deviceWithPoint/deviceWithPoint/${ids}`,
        method: 'delete'
    })
}