<!-- 时间范围选择器组件 -->
<template>
  <div class="time-range-selector">
    <el-date-picker
      v-model="dateRange"
      type="datetimerange"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      size="small"
      :clearable="false"
      @change="handleChange"
    />
  </div>
</template>

<script>
export default {
  name: 'TimeRangeSelector',

  props: {
    initialRange: {
      type: Object,
      default: () => ({
        start: null,
        end: null
      })
    }
  },

  data() {
    return {
      dateRange: null
    }
  },

  methods: {
    handleChange(range) {
      if (!range) return
      const [start, end] = range
      this.$emit('change', { start, end })
    }
  },

  created() {
    // 设置初始时间范围
    if (this.initialRange.start && this.initialRange.end) {
      this.dateRange = [this.initialRange.start, this.initialRange.end]
    } else {
      // 默认显示最近1小时
      const end = new Date()
      const start = new Date(end.getTime() - 60 * 60 * 1000)
      this.dateRange = [start, end]
      this.$emit('change', { start, end })
    }
  }
}
</script>

<style lang="scss" scoped>
.time-range-selector {
  display: flex;
  justify-content: center;

  :deep(.el-date-editor) {
    width: 100%;
  }
}
</style>
