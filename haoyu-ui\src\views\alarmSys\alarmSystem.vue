<template>
  <div class="alarm-system">
    <!-- 四区网格 -->
    <div class="grid-container" :class="{ 'fullscreen-mode': isFullscreen }">
      <!-- 1. 报警趋势（柱状图示例）-->
      <div class="grid-item trend" :class="{ 'fullscreen': currentFullscreen === 'trend', 'hidden': isFullscreen && currentFullscreen !== 'trend' }">
        <header style="display: flex; justify-content: space-between; align-items: center;">
          <span>报警趋势</span>
          <div class="header-controls" style="display: flex; align-items: center;">
            <el-date-picker
              v-model="trendDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              style="margin-right: 10px; width: 240px;"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleTrendDateChange"
            >
            </el-date-picker>
            <i :class="[currentFullscreen === 'trend' ? 'el-icon-close' : 'el-icon-full-screen']"
               style="font-size: 16px; cursor: pointer;"
               @click="toggleFullscreen('trend')"></i>
          </div>
        </header>
        <div ref="trendChart" class="chart" v-loading="trendChartLoading"></div>
      </div>

      <!-- 2. 报警分布（饼图示例）-->
      <div class="grid-item distribution" :class="{ 'fullscreen': currentFullscreen === 'distribution', 'hidden': isFullscreen && currentFullscreen !== 'distribution' }">
        <header style="display: flex; justify-content: space-between; align-items: center;">
          <span>报警分布</span>
          <i :class="[currentFullscreen === 'distribution' ? 'el-icon-close' : 'el-icon-full-screen']"
             style="font-size: 16px; cursor: pointer;"
             @click="toggleFullscreen('distribution')"></i>
        </header>
        <div ref="distChart" class="chart" v-loading="distChartLoading"></div>
      </div>

      <!-- 3. 未关闭报警 / 历史记录 -->
      <div class="grid-item unclosed" :class="{ 'fullscreen': currentFullscreen === 'unclosed', 'hidden': isFullscreen && currentFullscreen !== 'unclosed' }">
        <header style="display: flex; justify-content: space-between; align-items: center;">
          <span>报警信息</span>
          <i :class="[currentFullscreen === 'unclosed' ? 'el-icon-close' : 'el-icon-full-screen']"
             style="font-size: 16px; cursor: pointer;"
             @click="toggleFullscreen('unclosed')"></i>
        </header>
        <el-tabs v-model="alarmActiveTab" type="border-card" class="alarm-tabs">
          <!-- 未关闭报警 -->
          <el-tab-pane label="未关闭报警" name="unclosed">
            <el-table
              :data="unclosedAlarms"
              stripe
              border
              style="width: 100%;padding: 1px 1px 1px 1px;"
              v-loading="unclosedLoading"
              :empty-text="'暂无未处理的报警记录'"
              ref="unclosedTable"
              class="alarm-table"
              @row-click="handleUnclosedRowClick"
            >
              <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip />
              <el-table-column prop="channelName" label="测点名称"  show-overflow-tooltip />
              <el-table-column label="测点定义"  show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div>{{ row.originalRecord && row.originalRecord.definitionName || '-' }}</div>
                  <div v-if="row.originalRecord && row.originalRecord.measurementDefinition" class="definition-range">
                    {{ row.originalRecord.measurementDefinition.lowerLimitFrequency }}-
                    {{ row.originalRecord.measurementDefinition.upperLimitFrequency }}HZ
                    ({{ row.originalRecord.measurementDefinition.numberOfSpectralLines }}线)
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报警详情"  show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div>{{ row.originalRecord && row.originalRecord.warnMsg || row.type }}</div>
                  <div v-if="row.originalRecord" class="alarm-detail">
                    <span class="alarm-type">{{ row.originalRecord.warnType }}</span>
                    <span class="alarm-values">标准值: {{ row.originalRecord.normalValue || '-' }} / 当前值: {{ row.originalRecord.warnValue || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报警等级"  align="center">
                <template slot-scope="{ row }">
                  <el-tag :type="getAlarmLevelType(row.level)" effect="dark" size="mini">
                    {{ getAlarmLevelText(row.level) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="time" label="触发时间"  />
            </el-table>
          </el-tab-pane>

          <!-- 历史记录 -->
          <el-tab-pane label="历史记录" name="history" class="history-tab-pane">
            <div class="history-toolbar">
              <el-form :inline="true" :model="historyFilterForm" class="filter-form">
                <div class="filter-row">
                  <el-form-item label="处理状态">
                    <el-select v-model="historyFilterForm.status" placeholder="请选择处理状态" style="width: 120px;">
                      <el-option label="全部" value="all"></el-option>
                      <el-option label="未处理" value="unconfirmed"></el-option>
                      <el-option label="已处理" value="confirmed"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="时间范围" class="time-range-item">
                    <el-date-picker
                      v-model="historyFilterForm.dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 320px;">
                    </el-date-picker>
                  </el-form-item>
                  <div class="action-buttons-wrapper">
                    <el-button type="primary" size="mini" @click="searchAlarmHistory" icon="el-icon-search">查询</el-button>
                    <el-button size="mini" @click="resetHistoryFilter" icon="el-icon-refresh">重置</el-button>
                  </div>
                </div>
              </el-form>
            </div>
            <el-table
              :data="filteredHistoryAlarms"
              stripe
              border
              style="width: 100%; flex: 1; padding: 1px 1px 1px 1px;"
              height="100%"
              v-loading="loading"
              class="alarm-table history-table"
              ref="historyTable"
              @row-click="handleHistoryRowClick"
            >
              <el-table-column prop="deviceName" label="设备名称"  show-overflow-tooltip />
              <el-table-column prop="measurementName" label="测点名称"  show-overflow-tooltip />
              <el-table-column label="测点定义" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div>{{ row.definitionName || '-' }}</div>
                  <div v-if="row.measurementDefinition" class="definition-range">
                    {{ row.measurementDefinition.lowerLimitFrequency }}-
                    {{ row.measurementDefinition.upperLimitFrequency }}HZ
                    ({{ row.measurementDefinition.numberOfSpectralLines }}线)
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报警详情"  show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div>{{ row.warnMsg }}</div>
                  <div class="alarm-detail">
                    <span class="alarm-type">{{ row.warnType }}</span>
                    <span class="alarm-values">标准值: {{ row.normalValue || '-' }} / 当前值: {{ row.warnValue || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报警等级" align="center">
                <template slot-scope="{ row }">
                  <el-tag :type="getAlarmLevelType(row.warnLevel)" effect="dark" size="mini">
                    {{ getAlarmLevelText(row.warnLevel) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="处理状态"  align="center">
                <template slot-scope="{ row }">
                  <el-tag :type="row.isSolved === 1 ? 'success' : 'info'" effect="plain" size="mini">
                    {{ row.isSolved === 1 ? '已处理' : '未处理' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="warnTime" label="触发时间"  />
              <el-table-column prop="confirmTime" label="确认时间"  show-overflow-tooltip />
              <el-table-column label="操作"  align="center">
                <template slot-scope="{ row }">
                  <span class="action-buttons">
                    <el-button type="text" size="small" icon="el-icon-view" @click="handleDetails(row)">详情</el-button>
                    <el-button type="text" size="small" icon="el-icon-check" @click="handleConfirmHistoryAlarm(row)">处理</el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-container">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalAlarms">
              </el-pagination>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 4. 采集站异常 -->
      <div class="grid-item exceptions" :class="{ 'fullscreen': currentFullscreen === 'exceptions', 'hidden': isFullscreen && currentFullscreen !== 'exceptions' }">
        <header style="display: flex; justify-content: space-between; align-items: center;">
          <span>采集站异常</span>
          <i :class="[currentFullscreen === 'exceptions' ? 'el-icon-close' : 'el-icon-full-screen']"
             style="font-size: 16px; cursor: pointer;"
             @click="toggleFullscreen('exceptions')"></i>
        </header>
        <el-tabs
          v-model="activeTab"
          type="card"
          class="exception-tabs"
        >
          <!-- 采集站异常 -->
          <el-tab-pane label="采集站异常" name="station">
            <el-table
              :data="stationExceptions"
              stripe
              border
              style="width: 100%"
              height="100%"
              v-loading="loading"
            >
              <el-table-column prop="name" label="采集站名称" show-overflow-tooltip />
              <el-table-column prop="no" label="编号" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" show-overflow-tooltip />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 报警详情弹窗 -->
    <alarm-detail-dialog
      :visible.sync="detailDialogVisible"
      :detail="currentDetailItem"
      :showConfirmButton="alarmActiveTab !== 'unclosed'"
      @confirm="handleConfirmFromDetail"
    />

    <!-- 处理弹窗 -->
    <alarm-process-dialog
      :visible.sync="processDialogVisible"
      :alarmItem="currentProcessItem"
      @process-success="handleProcessSuccess"
    />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getWarnRecord } from '@/views/textPage/BigScreen/api/apiBigscreen'
import { getAlarmTrendData, getAlarmDistributionData } from './api/AlaTrend.js'
import AlarmDetailDialog from './AlarmDetailDialog.vue'
import AlarmProcessDialog from './AlarmProcessDialog.vue'

export default {
  name: 'AlarmSystem',
  components: {
    AlarmDetailDialog,
    AlarmProcessDialog
  },
  data() {
    return {
      trendDateRange: [], // 为报警趋势图添加日期范围, will be initialized in created()
      trendChartLoading: false, // + Add loading state for trend chart
      distChartLoading: false, // + Add loading state for distribution chart
      alarmList: [
        {
          deviceName: '设备A',
          channelName: '温度传感器',
          type: '温度异常',
          level: '1',
          time: '2024-01-20 10:30:00',
          status: 'unconfirmed'
        },
        {
          deviceName: '设备B',
          channelName: '振动传感器',
          type: '振动异常',
          level: '2',
          time: '2024-01-20 09:15:00',
          status: 'confirmed',
          confirmTime: '2024-01-20 10:25:00'
        }
      ],
      alarmActiveTab: 'unclosed',
      activeTab: 'station',
      activeDevices: [
        {
          name: '采集站A',
          no: '001',
          status: '异常'
        },
        {
          name: '采集站B',
          no: '002',
          status: '正常'
        }
      ],
      isFullscreen: false,
      currentFullscreen: null,

      // 图表实例引用
      trendChart: null,
      distChart: null,

      // 历史报警记录相关
      historyAlarmRecords: [],
      unclosedAlarmRecords: [],  // 未关闭的报警记录
      totalAlarms: 0,
      currentPage: 1,
      pageSize: 10,
      historyFilterForm: {
        status: 'all',
        dateRange: []
      },
      loading: false,
      unclosedLoading: false,  // 未关闭报警的加载状态

      // 详情弹窗相关
      detailDialogVisible: false,
      currentDetailItem: {},

      // 添加处理弹窗相关
      processDialogVisible: false,
      currentProcessItem: {},

      // 防止标签页切换误触发
      isTabSwitching: false,

      // 在data中添加resizeObserver
      resizeObserver: null,
    }
  },
  computed: {
    unclosedAlarms() {
      // 只保留每个设备的第一条未关闭报警（仅用API数据，不再回退本地模拟数据）
      if (this.unclosedAlarmRecords && this.unclosedAlarmRecords.length > 0) {
        const seen = new Set();
        return this.unclosedAlarmRecords.filter(item => {
          if (seen.has(item.deviceId || item.deviceName)) return false;
          seen.add(item.deviceId || item.deviceName);
          return true;
        });
      }
      return [];
    },
    historyAlarms() {
      // 只用API数据
      return this.historyAlarmRecords;
    },
    filteredHistoryAlarms() {
      let result = this.historyAlarmRecords;

      // 根据处理状态筛选
      if (this.historyFilterForm.status !== 'all') {
        const isProcessed = this.historyFilterForm.status === 'confirmed' ? 1 : 0; // 1表示已处理，0表示未处理
        result = result.filter(item => item.isSolved === isProcessed);
      }

      // 根据日期范围筛选
      if (this.historyFilterForm.dateRange && this.historyFilterForm.dateRange.length === 2) {
        const startDate = new Date(this.historyFilterForm.dateRange[0]);
        const endDate = new Date(this.historyFilterForm.dateRange[1]);
        endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间

        result = result.filter(item => {
          const warnTime = new Date(item.warnTime);
          return warnTime >= startDate && warnTime <= endDate;
        });
      }

      return result;
    },
    stationExceptions() {
      return this.activeDevices.filter(item => item.name.includes('采集站'))
    }
  },
  watch: {
    alarmActiveTab(newVal, oldVal) {
      // 标记为正在切换标签页
      this.isTabSwitching = true;

      // 当切换到未关闭报警标签时，加载最新数据
      if (newVal === 'unclosed') {
        this.fetchUnclosedAlarms();
      }

      // 200ms后重置标记
      setTimeout(() => {
        this.isTabSwitching = false;
        // 重新计算表格布局
        this.resizeAlarmTables();
      }, 200);
    }
  },
  created() { // + Add created hook to initialize trendDateRange
    this.trendDateRange = this.getDefaultTrendDateRange();
  },
  methods: {
    getDefaultTrendDateRange() { // + Helper method to get default date range (last 7 days)
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 6);
      return [formatDate(startDate), formatDate(endDate)];
    },
    async loadTrendChartData() { // + Method to load data and update trend chart
      if (!this.trendChart) {
        // Chart not initialized yet, initTrendChart will call this again
        return;
      }
      this.trendChartLoading = true;
      try {
        const params = {
          startTime: this.trendDateRange[0],
          endTime: this.trendDateRange[1]
        };
        const response = await getAlarmTrendData(params);

        if (response && response.rows && Array.isArray(response.rows)) {
          const apiData = response.rows;

          let categories = [];
          let seriesDataLevel1 = [];
          let seriesDataLevel2 = [];
          let seriesDataLevel3 = [];

          if (apiData.length > 0) {
            // Assuming 'createLocalDate' is the correct field for the date category.
            // Adjust to 'createdTime' if that's preferred.
            categories = apiData.map(item => item.createLocalDate || item.createdTime || '未知日期');
            seriesDataLevel1 = apiData.map(item => item.firstWarnValue || 0);
            seriesDataLevel2 = apiData.map(item => item.secondWarnValue || 0);
            // Accommodating the typo 'thiredWarnValue', ideally backend should fix to 'thirdWarnValue'
            seriesDataLevel3 = apiData.map(item => item.thiredWarnValue || item.thirdWarnValue || 0);
          } else {
             console.warn("API response for trend chart contains an empty rows array.");
          }

          if (categories.length > 0) {
            this.trendChart.setOption({
              xAxis: {
                data: categories
              },
              series: [
                { name: '一级警报', data: seriesDataLevel1 },
                { name: '二级警报', data: seriesDataLevel2 },
                { name: '三级警报', data: seriesDataLevel3 }
              ]
            });
          } else {
            // Handle case where apiData was empty or no valid dates were found
            this.$message.info('报警趋势：指定时间范围内无数据');
            this.trendChart.setOption({
              xAxis: { data: ['无数据'] },
              series: [
                { name: '一级警报', data: [] },
                { name: '二级警报', data: [] },
                { name: '三级警报', data: [] }
              ]
            });
          }
        } else {
          this.$message.error('获取报警趋势数据失败或格式不正确');
          if (this.trendChart) {
            this.trendChart.setOption({
              xAxis: { data: ['无数据'] },
              series: [ { name: '一级警报', data: [] }, { name: '二级警报', data: [] }, { name: '三级警报', data: [] } ]
            });
          }
        }
      } catch (error) {
        console.error('加载报警趋势图数据错误:', error);
        this.$message.error('加载报警趋势图数据出错');
        if (this.trendChart) {
            this.trendChart.setOption({
                xAxis: { data: ['加载失败'] },
                series: [ { name: '一级警报', data: [] }, { name: '二级警报', data: [] }, { name: '三级警报', data: [] } ]
            });
        }
      } finally {
        this.trendChartLoading = false;
      }
    },
    handleTrendDateChange() {
      console.log('Trend date range changed:', this.trendDateRange);
      // When date range changes, reload data for the trend chart
      if (this.trendDateRange && this.trendDateRange.length === 2) {
        this.loadTrendChartData(); // Call the new method to load data
      } else {
        // Handle case where date range might be cleared
        if (this.trendChart) {
          this.trendChart.setOption({
            xAxis: { data: ['请选择日期'] },
            series: [ { name: '一级警报', data: [] }, { name: '二级警报', data: [] }, { name: '三级警报', data: [] } ]
          });
        }
      }
    },
    toggleFullscreen(section) {
      // 标记为正在切换标签页，防止误触发弹窗
      this.isTabSwitching = true;

      if (this.currentFullscreen === section) {
        // 关闭全屏
        this.isFullscreen = false;
        this.currentFullscreen = null;
      } else {
        // 打开全屏
        this.isFullscreen = true;
        this.currentFullscreen = section;
      }

      // 延迟调整图表大小和表格布局，确保DOM已更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.resizeCharts();
          this.resizeAlarmTables();
        }, 50);
      });

      // 200ms后重置标记
      setTimeout(() => {
        this.isTabSwitching = false;
      }, 200);
    },
    resizeCharts() {
      if (this.trendChart && typeof this.trendChart.resize === 'function') {
        this.trendChart.resize();
      } else if (this.trendChart) {
        console.warn('trendChart instance exists but resize method is missing.');
      }

      if (this.distChart && typeof this.distChart.resize === 'function') {
        this.distChart.resize();
      } else if (this.distChart) {
        console.warn('distChart instance exists but resize method is missing.');
      }
    },
    getAlarmLevelType(level) {
      // 修改报警等级对应的类型，与报警趋势图颜色保持一致
      // 一级报警: #FFCC00 (黄色) -> warning
      // 二级报警: #E6A23C (橙色) -> warning
      // 三级报警: #F56C6C (红色) -> danger
      const map = { '1': 'warning', '2': 'warning', '3': 'danger' }
      return map[level] || 'info'
    },
    getAlarmLevelText(level) {
      const map = { '1': '一级警报', '2': '二级警报', '3': '三级警报' }
      return map[level] || '未知'
    },

    // 历史记录相关方法
    async fetchAlarmHistory() {
      this.loading = true;
      try {
        const res = await getWarnRecord(this.currentPage);
        if (res.code === 200) {
          this.totalAlarms = res.total;

          // 处理报警记录数据，保留完整的原始数据
          this.historyAlarmRecords = res.rows.map(item => {
            return {
              ...item,
              isSolved: item.isSolved || 0, // 是否已处理，0表示未处理，1表示已处理
              confirmTime: item.confirmTime || '', // 确认时间
              duration: item.duration || '', // 持续时间
              // 确保没有缺失的属性都有默认值
              deviceName: item.deviceName || item.deviceId || '未知设备',
              measurementName: item.measurementName || '未知测点',
              warnType: item.warnType || '未知类型',
              warnLevel: item.warnLevel || '2',
              warnMsg: item.warnMsg || '',
              normalValue: item.normalValue || '-',
              warnValue: item.warnValue || '-',
              definitionName: item.definitionName || '',
              // 测量定义可能是嵌套对象，确保不会出现undefined错误
              measurementDefinition: item.measurementDefinition || null
            };
          });

          // 分离出未处理的报警记录，使用同样的处理逻辑
          this.unclosedAlarmRecords = this.historyAlarmRecords
            .filter(item => item.isSolved === 0)
            .map(item => ({
              deviceName: item.deviceName || item.deviceId || '未知设备',
              channelName: item.measurementName || item.channelName || '未知通道',
              type: item.warnType || '未知类型',
              level: item.warnLevel || '2',
              time: item.warnTime || new Date().toLocaleString(),
              status: 'unconfirmed',
              id: item.id,
              originalRecord: item // 保留原始记录以便后续处理
            }));
        } else {
          this.$message.error('获取报警历史记录失败');
        }
      } catch (error) {
        console.error('获取报警历史记录错误:', error);
        this.$message.error('获取报警历史记录出错');
      } finally {
        this.loading = false;
        this.unclosedLoading = false;
      }
    },

    formatDuration(startTimeStr) {
      // 计算从报警开始到现在的持续时间
      const startTime = new Date(startTimeStr);
      const now = new Date();
      const diffMs = now - startTime;

      // 转换为天、小时、分钟
      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      // 格式化输出
      let result = '';
      if (days > 0) result += `${days}天`;
      if (hours > 0) result += `${hours}小时`;
      result += `${minutes}分钟`;

      return result;
    },

    searchAlarmHistory() {
      this.currentPage = 1;
      this.fetchAlarmHistory();
    },

    resetHistoryFilter() {
      this.historyFilterForm = {
        status: 'all',
        dateRange: []
      };
      this.searchAlarmHistory();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.fetchAlarmHistory();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchAlarmHistory();
    },

    handleConfirmHistoryAlarm(row) {
      // 打开处理弹窗，而不是简单的确认对话框
      this.currentProcessItem = row;
      this.processDialogVisible = true;
    },

    handleProcessSuccess(processedAlarm) {
      // 更新历史记录数据
      const index = this.historyAlarmRecords.findIndex(item => item.id === processedAlarm.id);
      if (index !== -1) {
        this.historyAlarmRecords.splice(index, 1, processedAlarm);
      }

      // 从未关闭列表中移除
      this.unclosedAlarmRecords = this.unclosedAlarmRecords.filter(item =>
        item.id !== processedAlarm.id
      );

      this.$message.success(`已处理报警：${processedAlarm.deviceName}`);
    },

    handleConfirm(row) {
      // 如果是API获取的数据，调用API进行确认处理
      if (row.id) {
        this.$confirm('确认处理该报警记录?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 这里应该调用实际的API处理报警确认
          // 模拟API处理成功
          row.status = 'confirmed';
          row.confirmTime = new Date().toLocaleString();

          // 从未关闭列表中移除
          this.unclosedAlarmRecords = this.unclosedAlarmRecords.filter(item => item.id !== row.id);

          // 如果存在原始记录，更新其状态
          if (row.originalRecord) {
            row.originalRecord.isSolved = 1;
            row.originalRecord.confirmTime = row.confirmTime;
          }

          this.$message.success(`已确认：${row.deviceName}`);
        }).catch(() => {
          // 取消操作
        });
      } else {
        // 本地模拟数据的处理方式
        row.status = 'confirmed';
        row.confirmTime = new Date().toLocaleString();
        this.$message.success(`已确认：${row.deviceName}`);
      }
    },
    handleDetails(row) {
      // 防止标签页切换时误触发
      if (this.isTabSwitching) return;

      this.currentDetailItem = { ...row }
      this.detailDialogVisible = true
    },
    handleConfirmFromDetail(row) {
      // 如果是来自未关闭报警的记录，不允许处理
      if (row.fromUnclosed) {
        return;
      }
      // 处理从详情弹窗中点击"处理报警"按钮
      this.handleConfirmHistoryAlarm(row);
    },
    handleDiagnose(row) {
      this.$message.info(`诊断：${row.name || row.deviceName}`)
    },
    async initTrendChart() { // Made async
      if (this.trendChart && typeof this.trendChart.dispose === 'function') {
        this.trendChart.dispose();
      }
      this.trendChart = null;

      if (!this.$refs.trendChart) {
        console.warn('趋势图DOM元素不存在');
        // Retry logic can be kept if this.$refs.trendChart might not be immediately available
        setTimeout(() => this.initTrendChart(), 100);
        return null;
      }

      if (this.$refs.trendChart.offsetHeight === 0 || this.$refs.trendChart.offsetWidth === 0) {
        console.warn('趋势图DOM元素尺寸为0');
        setTimeout(() => this.initTrendChart(), 100);
        return null;
      }

      // Initialize chart instance only if it doesn't exist
      // if (!this.trendChart) { // Removed this check as we dispose and nullify above
      try {
        this.trendChart = echarts.init(this.$refs.trendChart, null, {
          renderer: 'canvas',
          useDirtyRect: false
        });

        // Define a base option structure (without dynamic data)
        const baseOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(50,50,50,0.7)',
            textStyle: { color: '#fff' }
          },
          legend: {
            data: ['一级警报', '二级警报', '三级警报'],
            top: '2%',
            textStyle: { color: '#666', fontSize: 12 }
          },
          grid: {
            top: '15%', left: '3%', right: '4%', bottom: '10%', containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [], // Initial empty data, will be filled by loadTrendChartData
            axisLine: { lineStyle: { color: '#ccc' } },
            axisTick: { alignWithLabel: true },
            axisLabel: { color: '#666', fontSize: 12 }
          },
          yAxis: {
            type: 'value',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { lineStyle: { type: 'dashed', color: '#eee' } },
            axisLabel: { color: '#666', fontSize: 12 }
          },
          series: [
            { name: '一级警报', type: 'bar', stack: '报警', barWidth: '60%', data: [], itemStyle: { color: '#FFFF00' }, emphasis: { focus: 'series' }, animationDelay: idx => idx * 50 },
            { name: '二级警报', type: 'bar', stack: '报警', barWidth: '60%', data: [], itemStyle: { color: '#FF8000' }, emphasis: { focus: 'series' }, animationDelay: idx => idx * 50 + 100 },
            { name: '三级警报', type: 'bar', stack: '报警', barWidth: '60%', data: [], itemStyle: { color: 'red' }, emphasis: { focus: 'series' }, animationDelay: idx => idx * 50 + 200 }
          ]
        };
        this.trendChart.setOption(baseOption);
        this.trendChart.resize();
      } catch (error) {
        console.error('初始化趋势图表失败:', error);
        this.trendChart = null; // Ensure it's null if init fails
        return null;
      }
      // }
      // Load initial data once chart is initialized
      await this.loadTrendChartData(); // Await data loading
      return this.trendChart; // Return the instance
    },
    async initDistributionChart() { // Modified to be async and fetch data
      if (this.distChart && typeof this.distChart.dispose === 'function') {
        this.distChart.dispose();
      }
      this.distChart = null;

      if (!this.$refs.distChart) {
        console.warn('分布图DOM元素不存在');
        // Retry logic can be kept if this.$refs.distChart might not be immediately available
        setTimeout(() => this.initDistributionChart(), 100);
        return null;
      }

      if (this.$refs.distChart.offsetHeight === 0 || this.$refs.distChart.offsetWidth === 0) {
        console.warn('分布图DOM元素尺寸为0');
        setTimeout(() => this.initDistributionChart(), 100);
        return null;
      }

      this.distChartLoading = true;
      let chartInstance = this.distChart; // Use existing instance if available

      try {
        if (!chartInstance) {
          chartInstance = echarts.init(this.$refs.distChart, null, {
            renderer: 'canvas',
            useDirtyRect: false
          });
          this.distChart = chartInstance; // Store the new instance
        }

        const response = await getAlarmDistributionData();
        let sunburstData = [];

        if (response && response.code === 200 && response.data) {
          const apiData = response.data;
          const levelMapping = [
            { key: 'level0', name: '不报警', color: '#11EE3A' }, // Changed to Green for no alarm
            { key: 'level1', name: '一级报警', color: '#FFFF00' }, // Yellow
            { key: 'level2', name: '二级报警', color: '#FF8000' }, // Orange
            { key: 'level3', name: '三级报警', color: 'red' }  // Red
          ];

          levelMapping.forEach(level => {
            if (apiData[level.key] !== undefined && apiData[level.key] > 0) {
              sunburstData.push({
                name: level.name,
                value: apiData[level.key],
                itemStyle: { color: level.color }
              });
            }
          });

          if (sunburstData.length === 0) {
            // Handle case where all levels are 0 but API call was successful
            this.$message.info('报警分布：当前无报警数据');
            sunburstData.push({ name: '无数据', value: 1, itemStyle: { color: '#ccc' } });
          }
        } else {
          this.$message.error('获取报警分布数据失败或格式不正确');
          sunburstData.push({ name: '加载失败', value: 1, itemStyle: { color: '#ccc' } });
        }

        chartInstance.setOption({
          tooltip: {
            trigger: 'item',
            formatter: function (params) {
              // params.name is {b}, params.value is {c}, params.percent is {d} (as a number)
              if (params.value === undefined || params.percent === undefined) {
                return params.name; // Fallback for unusual data points
              }
              return params.name + ': ' + params.value + ' (' + params.percent + '%)';
            }
          },
          series: [
            {
              type: 'sunburst',
              data: sunburstData,
              radius: [0, '90%'],
              itemStyle: {
                borderRadius: 7,
                borderWidth: 2,
                borderColor: '#fff'
              },
              label: {
                show: true,
                formatter: '{b}\n{c}',
                rotate: 0 // Keep labels horizontal
              },
              emphasis: {
                focus: 'self',
                label: { show: true }
              }
            }
          ]
        }, true); // Added notMerge = true
        this.distChart.resize(); // Ensure resize after setting new options

      } catch (error) {
        console.error('初始化或加载报警分布图表数据错误:', error);
        this.$message.error('加载报警分布图表数据出错');
        if (chartInstance) {
            // Display an error state on the chart
            chartInstance.setOption({
                series: [{
                    type: 'sunburst',
                    data: [{ name: '加载出错', value: 1, itemStyle: { color: '#ccc' } }],
                    radius: [0, '90%'],
                    label: { show: true, formatter: '{b}' }
                }]
            });
        }
      } finally {
        this.distChartLoading = false;
      }
      return chartInstance;
    },
    showAlarmDetail(row) {
      // 防止标签页切换时误触发
      if (this.isTabSwitching) return;

      this.currentDetailItem = row
      this.detailDialogVisible = true
    },
    handleDetailConfirm(item) {
      // 处理确认逻辑
      this.confirmAlarm(item.id)
    },
    // 专门获取未关闭的报警记录
    async fetchUnclosedAlarms() {
      this.unclosedLoading = true;
      try {
        // 这里可以替换为实际获取未关闭报警的API
        // 示例中复用了getWarnRecord方法，实际使用时可能需要单独的API
        const res = await getWarnRecord(1, { status: 'unconfirmed' });
        if (res.code === 200) {
          // 处理未关闭报警记录数据
          this.unclosedAlarmRecords = res.rows.map(item => ({
            deviceName: item.deviceName || item.deviceId || '未知设备',
            channelName: item.measurementName || item.channelName || '未知通道',
            type: item.warnType || '未知类型',
            level: item.warnLevel || '2',
            time: item.warnTime || new Date().toLocaleString(),
            status: 'unconfirmed',
            id: item.id,
            originalRecord: item // 保留完整的原始记录以便展示详细信息
          }));
        } else {
          console.warn('获取未关闭报警记录失败，使用默认数据');
          // 失败时不显示错误，使用默认数据
        }
      } catch (error) {
        console.error('获取未关闭报警记录错误:', error);
        // 发生错误时不显示错误消息，使用默认数据
      } finally {
        this.unclosedLoading = false;
      }
    },
    // 重新计算表格布局，解决切换标签页后列宽不一致的问题
    resizeAlarmTables() {
      this.$nextTick(() => {
        if (this.$refs.unclosedTable) {
          this.$refs.unclosedTable.doLayout();
        }
        if (this.$refs.historyTable) {
          this.$refs.historyTable.doLayout();
        }
      });
    },
    // 窗口大小变化处理函数
    handleResize() {
      this.resizeCharts();
      this.resizeAlarmTables();
    },
    // 初始化图表方法
    async initCharts() { // Made async
      // 初始化并保存图表引用
      // Order matters if one depends on another, or for clarity
      try {
        this.trendChart = await this.initTrendChart();
        this.distChart = await this.initDistributionChart();
      } catch (error) {
        console.error("Error during chart initialization sequence:", error);
      }

      // 调整一次大小以确保正确显示
      this.$nextTick(() => {
        this.resizeCharts();
      });
    },
    // 未关闭报警表格行点击处理
    handleUnclosedRowClick(row, column, event) {
      // 如果正在切换标签页，不处理点击事件
      if (this.isTabSwitching) return;

      // 如果点击的是操作列按钮，不再触发行点击事件
      if (column && column.property === 'operation') return;
      if (event.target && (event.target.tagName === 'BUTTON' || event.target.tagName === 'I' ||
          event.target.closest('.el-button'))) return;

      // 处理点击事件，显示详情
      this.currentDetailItem = {
        ...row,
        deviceName: row.deviceName,
        measurementName: row.channelName,
        warnMsg: row.originalRecord ? row.originalRecord.warnMsg : row.type,
        warnTime: row.time,
        warnLevel: row.level,
        warnType: row.originalRecord ? row.originalRecord.warnType : '',
        normalValue: row.originalRecord ? row.originalRecord.normalValue : '-',
        warnValue: row.originalRecord ? row.originalRecord.warnValue : '-',
        definitionName: row.originalRecord ? row.originalRecord.definitionName : '-',
        measurementDefinition: row.originalRecord ? row.originalRecord.measurementDefinition : null,
        // 添加缺失的ID字段
        deviceId: row.originalRecord ? row.originalRecord.deviceId : '-',
        measurementId: row.originalRecord ? row.originalRecord.measurementId : '-',
        isSolved: 0,
        fromUnclosed: true  // 标记来源是未关闭报警表
      };
      this.detailDialogVisible = true;
    },

    // 历史记录表格行点击处理
    handleHistoryRowClick(row, column, event) {
      // 如果正在切换标签页，不处理点击事件
      if (this.isTabSwitching) return;

      // 如果点击的是操作列按钮，不再触发行点击事件
      if (column && column.property === 'operation') return;
      if (event.target && (event.target.tagName === 'BUTTON' || event.target.tagName === 'I' ||
          event.target.closest('.el-button'))) return;

      // 直接使用原有的handleDetails方法
      this.handleDetails(row);
    },
  },
  mounted() {
    // Initialize trendDateRange here if not in created, or ensure created() runs first
    // this.trendDateRange = this.getDefaultTrendDateRange(); // Already in created()

    // Delay initialization to ensure DOM is fully ready, especially for chart dimensions
    setTimeout(async () => { // setTimeout callback made async
      await this.initCharts(); // Await initCharts completion

      this.fetchAlarmHistory();
      this.fetchUnclosedAlarms(); // This might also benefit from awaiting if initCharts depends on its data, but for now, it is separate.
      // resizeAlarmTables might be better called after data is loaded and tables rendered.
      // For now, keep it here, but be mindful of its timing if table layout issues persist.
      this.resizeAlarmTables();
    }, 0); // Consider a slightly longer delay if 0 is not enough for complex DOMs

    // 创建ResizeObserver，监听容器大小变化
    this.resizeObserver = new ResizeObserver(this.handleResize);
    // 监视grid-container
    const gridContainer = this.$el.querySelector('.grid-container');
    if (gridContainer) {
      this.resizeObserver.observe(gridContainer);
    }

    // 窗口大小变化时重置图表和表格
    window.addEventListener('resize', this.handleResize);

    // 定时刷新持续时间
    this.durationTimer = setInterval(() => {
      if (this.alarmActiveTab === 'history') {
        this.$forceUpdate(); // 强制更新视图以刷新持续时间
      }
    }, 60000); // 每分钟刷新一次

    // 定时刷新未关闭报警列表
    this.unclosedAlarmTimer = setInterval(() => {
      if (this.alarmActiveTab === 'unclosed') {
        this.fetchUnclosedAlarms();
      }
    }, 60000); // 每分钟刷新一次

    // 初始状态下重置标签切换标志
    this.isTabSwitching = false;
  },
  beforeDestroy() {
    // 销毁图表实例，防止内存泄漏
    if (this.trendChart) {
      this.trendChart.dispose();
      this.trendChart = null;
    }

    if (this.distChart) {
      this.distChart.dispose();
      this.distChart = null;
    }

    // 清除ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    // 清除定时器
    if (this.durationTimer) {
      clearInterval(this.durationTimer);
      this.durationTimer = null;
    }

    if (this.unclosedAlarmTimer) {
      clearInterval(this.unclosedAlarmTimer);
      this.unclosedAlarmTimer = null;
    }

    // 移除resize监听器
    window.removeEventListener('resize', this.handleResize);
  }
}
</script>

<style scoped>
.alarm-system {
  position: absolute; /* 使用绝对定位固定在页面上 */
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  width: auto; /* 宽度自动计算 */
  height: auto; /* 高度自动计算 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0; /* 移除内边距，因为已经用定位控制了边距 */
  background: #f5f7fa;
  min-height: 0; /* 允许子元素溢出并滚动 */
  box-sizing: border-box;
  overflow: hidden; /* 防止整体溢出 */
}

.grid-container {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 5px;
  /* transition: all 0.3s ease; */
  min-height: 0; /* 允许容器缩小 */
  overflow: hidden;
  max-height: 100%; /* 限制最大高度 */
}

/* 全屏模式下的网格布局调整 */
.grid-container.fullscreen-mode {
  display: block;
}

/* 给每个格子加边框和阴影，确保下边界可见 */
.grid-item {
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0; /* 允许 flex 子元素收缩 */
  /* transition: all 0.3s ease; */
  height: 100%; /* 确保高度一致 */
  max-height: 100%; /* 限制最大高度 */
  box-sizing: border-box; /* 确保边框计算在内 */
}

/* 全屏状态 */
.grid-item.fullscreen {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  z-index: 10;
  height: calc(100vh - 8px);
}

/* 隐藏其他元素 */
.grid-item.hidden {
  display: none;
}

/* 标题栏 */
.grid-item > header {
  background: #fafafa;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 8px 8px 0 0;
  flex-shrink: 0; /* 防止标题栏被压缩 */
  height: 42px; /* 固定高度 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* 图表容器 */
.chart {
  flex: 1;
  min-height: 0;
  width: 100%;
  height: calc(100% - 42px); /* 减去头部高度 */
  overflow: hidden; /* 防止溢出 */
  box-sizing: border-box; /* 确保尺寸计算包含内边距和边框 */
}

/* 表格撑满高度 */
.el-table {
  height: 100%;
}

/* 不再需要单独的标题栏 */
.alarm-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 42px); /* 减去头部高度 */
  overflow: hidden;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

.alarm-tabs :deep(.el-tabs__content) {
  flex: 1;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

.alarm-tabs :deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

.exception-tabs {
  display: flex;
  flex-direction: column;
  height: calc(100% - 42px); /* 减去头部高度 */
  overflow: hidden;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

.exception-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: auto; /* 从hidden改为auto，允许滚动 */
  height: 100%;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

.exception-tabs :deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

/* 历史记录工具栏 */
.history-toolbar {
  padding: 5px 5px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0; /* 防止压缩 */
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 15px;
}

.filter-form .el-form-item__label {
  width: 70px;
  text-align: right;
  padding-right: 5px;
  white-space: nowrap;
}

.filter-form .el-form-item__content {
  display: flex;
  align-items: center;
}

.filter-row {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;
  padding: 5px 5px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
}

.time-range-item {
  display: flex;
  align-items: center;
  margin-right: 5px;
  flex: 1;
}

.action-buttons-wrapper {
  display: flex;
  margin-left: 10px;
  flex-shrink: 0;
  padding-left: 15px;
  border-left: 1px solid #e0e0e0;
  height: 32px;
  align-items: center;
}

.action-buttons-wrapper .el-button {
  margin-left: 8px;
}

/* 表格容器，让表格可滚动 */
.table-container {
  flex: 1;
  height: calc(100% - 110px);
  min-height: 0; /* 允许flex子元素溢出并滚动 */
  overflow: auto; /* 允许滚动 */
}

.pagination-container {
  padding: 10px 15px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #ebeef5;
  flex-shrink: 0; /* 防止压缩 */
}

/* 表格内容美化 */
:deep(.el-table) {
  font-size: 13px;
}

/* 报警表格通用样式 */
/* .alarm-table {
  table-layout: fixed;
} */

.alarm-table :deep(th.el-table__cell) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}

/* 历史记录表格样式 */
.history-table {
  flex: 1;
}

:deep(.el-tag) {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}

/* 操作列按钮样式 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-buttons .el-link {
  font-size: 12px;
  margin: 0 4px;
}

.action-buttons .el-link [class^="el-icon-"] {
  margin-right: 3px;
}

.process-btn {
  margin-left: 10px;
}

/* 参考alarmdialog.vue的报警详情样式 */
.alarm-detail {
  margin-top: 5px;
  font-size: 12px;
  color: #606266;
}

.alarm-type {
  margin-right: 10px;
  background-color: #f0f9eb;
  color: #67c23a;
  padding: 2px 5px;
  border-radius: 2px;
  display: inline-block;
}

.alarm-values {
  color: #e6a23c;
  display: inline-block;
}

.definition-range {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
  background-color: #f5f7fa;
  padding: 2px 4px;
  border-radius: 2px;
  display: inline-block;
}

/* 操作按钮样式优化 */
:deep(.el-button--text) {
  padding: 2px 6px;
}

:deep(.el-button--text:hover) {
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 2px;
}

/* 表格样式优化 */
:deep(.el-table .cell) {
  line-height: 20px;
  padding-top: 6px;
  padding-bottom: 6px;
}

:deep(.el-table .el-tag--mini) {
  height: 20px;
  line-height: 19px;
  padding: 0 6px;
}

:deep(.el-table--border td) {
  border-right: 1px solid #EBEEF5;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #FAFAFA;
}

.history-tab-pane {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* 确保子元素能够溢出滚动 */
}

/* 表格内容美化 */
:deep(.el-table) {
  font-size: 13px;
}

/* 添加这些额外的样式确保图表正确显示 */
.grid-item.trend, .grid-item.distribution {
  display: flex;
  flex-direction: column;
}

.grid-item.trend .chart, .grid-item.distribution .chart {
  flex: 1;
  overflow: hidden;
}

/* 鼠标指针样式 */
.alarm-table .el-table__row {
  cursor: pointer;
}

/* 鼠标悬停样式 */
.alarm-table .el-table__row:hover > td {
  background-color: #f0f7ff !important;
}
</style>
