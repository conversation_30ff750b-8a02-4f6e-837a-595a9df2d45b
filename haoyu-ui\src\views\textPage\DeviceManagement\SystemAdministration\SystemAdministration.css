.header {
  background-color: #f5f5f5;
  padding: 5px;
  border-bottom: 2px solid #dcdcdc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  text-align: center;
  box-sizing: border-box;
}

.SystemAdministration-body {
  display: flex;
  flex: 1;
  height: calc(100vh - 50px);
  box-sizing: border-box;
}

.SystemAdministration-layout {
  display: flex;
  flex-direction: column;
  height: 91vh;
}

.sidebar {
  width: 50px;
  background-color: #e0e0e0;
  overflow: auto;
}

.container_c {
  display: flex;
  flex-grow: 1;
  height: 100%;
}

.split-container {
  display: flex;
  width: 100%;
  height: 100%;
}

.left-container_l {
  width: 20%;
  overflow: auto;
}

.right-container_r {
  flex: 1;
  overflow: auto;
  background-color: #f9f9f9;
  border-left: 2px solid #dcdcdc;
  border-radius: 0 8px 8px 0;
  padding: 10px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.resizer {
  width: 8px;
  background: #ccc;
  cursor: ew-resize;
  height: 100%;
  border: 2px solid #a7a3a3;
}

.tooltip {
  position: absolute;
  background-color: white;
  border: 1px solid black;
  padding: 5px;
  display: none;
}
