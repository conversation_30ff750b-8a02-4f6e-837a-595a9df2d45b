<!-- 倒谱 -->
<template>
  <div class="cepstrum">
    <div class="top-container">{{ treePathText }} 倒谱图 </div>
    <!-- 工具栏 -->
    <div class="trend-toolbar">
      <tool-bar
        @tool-action="handleToolAction"
      />
    </div>
    <!-- ECharts 图表容器 -->
    <div ref="chartContainer" class="chart" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState, mapActions } from 'vuex'
import { getWaveform } from './data'
import { getParams } from '@/utils/graph_Interface'
import ToolBar from '../ChartsToolsBar/ToolBar'
export default {
  name: 'CepstrumComponent',
  components: {
    ToolBar
  },
  data() {
    return {
      audioData: null,
      cepstrumData: null,
      chartInstance: null,
      fftSize: 1024,
      fileAccept: '.wav',
      channel: 'left',
      smoothLine: true,
      lineColor: '#4985DF',
      lineWidth: 2,
      defaultCepstrumData: Array.from({ length: 256 }, (_, i) => Math.sin(i / 10) * Math.exp(-i / 50)),
      x:[],
      y:[]
    }
  },
  computed: {
    ...mapState('tree', ['treePathText']),
    ...mapState('dataStore', ['device_id', 'point_id', 'time_point'])
  },
  watch: {
    '$store.state.tree.selectedTreeNode.id': {
      async handler(newNodeId) {
        console.log('节点ID变化:', newNodeId);
        if (!newNodeId) return
        await this.getCepstrumData()
        if (this.$store.state.dataStore && this.$store.state.dataStore.length > 0) {
          if (!this.chartInstance) {
            // this.initChart()
            this.updateChart()
          } else {
            await this.getCepstrumData()
            this.updateChart()
          }
        }
      }
    },
    'time_point': {
      async handler(newTimePoint) {
        if (!newTimePoint) return

        const { device_id, point_id } = this.$store.state.dataStore;
        if (!device_id || !point_id) return;
        await this.getCepstrumData()
        // this.initChart()
        this.updateChart()
      }
    },
  },
  async mounted() {
    await this.checkDataStore()
    await this.getCepstrumData()
    // 确保 DOM 加载完毕后再绘制图表
    this.$nextTick(() => {

      this.initChart()
    })
    const resizeObserver = new ResizeObserver(() => {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    });
    // 监听图表容器
    resizeObserver.observe(this.$refs.chartContainer);
    // window.addEventListener('resize', this.resizeChart)
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    //
    async getCepstrumData() {
      try {
        const dataStore = this.$store.state.dataStore
        if (!dataStore.device_id && !dataStore.point_id && !dataStore.time_point) {
          return
        }
        const params = await getParams(5) // 假设倒谱的 ftype 为 5
        console.log('倒谱图获取到的参数--->', params);
        const result = await getWaveform(params)
        this.data = result.data.x.map((xValue, index) => [xValue, result.data.y[index]])
        this.loading = false
        this.updateChart();
      } catch (error) {
        this.error = '数据加载失败'
        this.loading = false
      }
    },
    async checkDataStore() {
      const dataStore = this.$store.state.dataStore
      if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
        this.loading = true
        return new Promise(resolve => {
          const interval = setInterval(() => {
            if (this.$store.state.dataStore && this.$store.state.dataStore.device_id) {
              clearInterval(interval)
              this.loading = false
              resolve()
            }
          }, 100)
        })
      }
    },
    initChart() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
      this.chartInstance = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },
    updateChart() {
      if (!this.chartInstance) return

      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '5%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none',
              title: {
                zoom: '区域缩放',
                back: '还原缩放'
              }
            },
            restore: { title: '还原' },
            saveAsImage: { title: '保存' }
          }
        },
        xAxis: {
          type: 'value',
          name: 'Quefrency',
          axisLine: {
            symbol: 'none',
            lineStyle: {
              type: 'solid'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: 'Amplitude',
          axisLine: {
            symbol: 'none',
            lineStyle: {
              type: 'solid'
            }
          }
        },
        series: [
          {
            data: this.data,
            type: 'line',
            symbol: false,
            showSymbol: false,
            smooth: this.smoothLine,
            lineStyle: {
              color: this.lineColor,
              width: this.lineWidth
            },
            large: true
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          }
        ]
      }

      this.chartInstance.setOption(option)
    },
    // 计算倒谱
    calculateCepstrum(timeData, samplingRate) {
      const n = timeData.length;

      // 1. 计算FFT
      const fft = this.computeFFT(timeData);

      // 2. 计算功率谱
      const powerSpectrum = fft.map(x => Math.abs(x) * Math.abs(x));

      // 3. 取对数
      const logPowerSpectrum = powerSpectrum.map(x => Math.log(x + 1e-10));

      // 4. 计算IFFT得到倒谱
      const cepstrum = this.computeIFFT(logPowerSpectrum);

      // 5. 只取实部并转换为quefrency域
      const quefrencyScale = Array.from({length: n}, (_, i) => i / samplingRate);
      const cepstrumMagnitude = cepstrum.map(x => Math.abs(x));

      return {
        quefrency: quefrencyScale,
        magnitude: cepstrumMagnitude
      };
    },
  }
}
</script>

<style scoped>
.cepstrum {
  /* text-align: center; */
  background: #f5f5f5;
}

.trend-toolbar {
  background: #f5f5f5;
  height: 30px;
}
.top-container {
  background: #f5f5f5;
}
.controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.file-input {
  border-radius: 4px;
  /* border: 1px solid #ddd; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

.calculate-btn {
  background-color: #5470c6;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.calculate-btn:disabled {
  background-color: #bbb;
  cursor: not-allowed;
}

.calculate-btn:hover:not(:disabled) {
  background-color: #355ab2;
  transform: translateY(-2px);
}

.chart {
  width: 100%;
  height: 95%;
  margin: 0 auto;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.chart:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
</style>

