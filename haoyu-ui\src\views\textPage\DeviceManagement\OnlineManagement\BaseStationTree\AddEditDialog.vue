<template>
  <el-dialog
    :title="mode === 'add' ? '新增采集站' : '编辑采集站'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :modal="false"
    width="90%"
    @close="handleClose"
  >
    <el-tabs type="border-card">
      <el-tab-pane label="基本配置" style="height: 70vh;">
        <BaseConfig
          ref="BaseConfigRef"
          :form.sync="form"
          :visible="visible"
          :vibration-channels.sync="vibrationChannels"
          :mode="mode"
          :company="company"
          @enable-save="enableSave"
        />
      </el-tab-pane>
      <el-tab-pane label="采集站策略" style="height: 70vh;">
        <MeasurementDefinition ref="MeasurementDefinitionRef" :mode="mode" :company="company" :visible="visible" />
      </el-tab-pane>
    </el-tabs>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <!-- <el-button type="primary" @click="handleSubmit">保存</el-button> -->
      <el-button :loading="loading" type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import MeasurementDefinition from './MeasurementDefinition.vue'
import BaseConfig from './BaseConfig.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'AddEditDialog',
  components: {
    MeasurementDefinition,
    BaseConfig
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      form: {
        name: '',
        location: '',
        policyName: '',
        policyDescription: '',
        channel1: '',
        channel2: '',
        stationType: '',
        stationCode: '',
        serverName: '',
        collectionMethod: '',
        company: ''
      },
      loading: false,
      // 是否允许点击保存
      canSave: false,
      vibrationChannels: [],
      company: '' // 初始化 company 值
    }
  },

  computed: {
    ...mapGetters('SettingConnect', ['selectedNodes']) // 使用命名空间 'SettingConnect' 的 mapGetters
  },
  created() {
    console.log('Created lifecycle: Company ID from Vuex:', this.$store.state.SettingConnect.companyId)
    this.initializeData()
  },
  mounted() {
    this.company = this.$store.state.SettingConnect.companyId || '1' // 从 Vuex 获取 companyId 的默认值
    console.log('Mounted lifecycle: Company ID:', this.company);
  },
  methods: {
    ...mapActions('SettingConnect', ['clearBaseStationNode']),
    // 允许保存
    enableSave(data) {
      this.canSave = data // 如果接收到 true，则允许保存
      // console.log('enableSave', data)
    },
    initializeData() {
      this.company = this.companyId || '' // 使用 computed 属性直接从 Vuex 获取 companyId
    },
    handleSubmit() {
      console.log('BaseConfigRef:', this.$refs.BaseConfigRef)

      // 将 MeasurementDefinition 中的数据传递给 BaseConfig
      if (this.$refs.MeasurementDefinitionRef) {
        const measurementForm = this.$refs.MeasurementDefinitionRef.form;
        if (measurementForm) {
          this.$refs.BaseConfigRef.form.collectTime = measurementForm.samplingTime || '2';
          this.$refs.BaseConfigRef.form.gap = measurementForm.samplingValue || '5';
          this.$refs.BaseConfigRef.form.wave_gap = measurementForm.waveform || '30';
          console.log('已将采集站策略数据传递给基本配置:', this.$refs.BaseConfigRef.form);
        }
      }

      // 如果 BaseConfigRef 不存在，直接退出
      if (!this.$refs.BaseConfigRef) {
        console.error('BaseConfigRef is undefined')
        return;
      }
      // 开启 loading 状态
      this.loading = true;
      // 调用 BaseConfig 的提交方法
      this.$refs.BaseConfigRef.submitForm()
        .then(() => {
          console.log('保存完成，触发上层事件与关闭弹框');
          this.$emit('submit', { ...this.form, mode: this.mode, company: this.company });
          this.$store.dispatch('SettingConnect/confirmDialog');
          this.handleClose();
        })
        .catch(error => {
          console.error('保存失败', error);
          this.$message.error('保存失败，请重试');
        })
        .finally(() => {
          // 请求结束，关闭 loading
          this.loading = false;
        });
    },
    handleClose() {
      this.canSave = false
      this.clearBaseStationNode()
      // console.log(this.$store.state.SettingConnect.BaseStationNode)

      // 重置测量定义组件的表单数据
      if (this.$refs.MeasurementDefinitionRef) {
        this.$refs.MeasurementDefinitionRef.resetForm();
      }

      this.$emit('close')
      this.resetForm()
    },
    resetForm() {
      this.form = {
        name: '',
        location: '',
        policyName: '',
        policyDescription: '',
        channel1: '',
        channel2: '',
        stationType: '',
        stationCode: '',
        serverName: '',
        collectionMethod: '',
        company: ''
      }
      this.company = this.$store.state.SettingConnect.companyId || '' // 重置时再次从 Vuex 获取默认值
      console.log(this.company)
    }
  }
}
</script>

<style scoped>
@import './AddEditDialog.css';

/* .tree-container { */
  /* max-height: 15em; 使用百分比 */
  /* overflow-y: auto; */
/* } */
/* 覆盖 el-dialog__body 样式 */
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
::v-deep .el-form-item--medium .el-form-item__label {
    font-size: 12px;
    line-height: 36px;
    padding-bottom: 0;
}
::v-deep .el-dialog__header {
    padding: 20px;
    padding-bottom: 0px;
}
::v-deep .el-tabs--border-card > .el-tabs__content {
    padding: 0;
}

</style>
