.basic-config {
  display: flex;
  justify-content: space-between;
}

.BaseConfig-left-container {
  width: 20%;
}
.BaseConfig-right-container{
  width: 80%;
}
.BaseConfig-left-container {
  border-right: 1px solid #ddd;
  padding-right: 20px;
}

.right-container {
  padding-left: 20px;
}

.BaseConfig-tree-container {
  height: 28vh;
  overflow-y: auto;
}

.compact-form {
  margin-bottom: 20px;
}

.compact-form-item {
  margin-bottom: 10px;
}

.small-input {
  width: 100%;
}
.section-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px; /* 标题和列表之间的距离 */
}

.node-list-container {
  width: 100%;
  height: 15%; /* 保持容器高度为 10% */
  background-color: #f9f9f9; /* 背景颜色 */
  border: 1px solid #ddd; /* 边框样式 */
  border-radius: 4px; /* 边框圆角 */
  padding: 10px; /* 内边距 */
  margin-bottom: 20px; /* 和下方内容的距离 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微的阴影 */
  display: flex;
  flex-direction: column;
}

.node-list {
  flex-grow: 1; /* 占据容器剩余空间 */
  overflow-y: auto; /* 内容超出时显示滚动条 */
  list-style: none; /* 去除默认的列表样式 */
  padding: 0; /* 去除默认的内边距 */
  margin: 0; /* 去除默认的外边距 */
  display: flex; /* 使用 Flexbox 布局 */
  flex-wrap: wrap; /* 当空间不足时换行 */
  gap: 10px; /* 项目之间的间距 */
}

.node-item {
  display: flex;
  height: 50%;
  align-items: center; /* 垂直居中对齐 */
  padding: 0px; /* 内边距 */
  padding-left: 10px;
  border: 1px solid #ddd; /* 边框样式 */
  border-radius: 4px; /* 边框圆角 */
  background-color: #ffffff; /* 背景颜色 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 轻微的阴影 */
}

.node-item span {
  margin-right: 10px; /* 文本和按钮之间的间距 */
  font-size: 14px; /* 字体大小 */
  color: #333; /* 字体颜色 */
}

.node-buttom {
  padding: 5px;
  font-size: small;
}
