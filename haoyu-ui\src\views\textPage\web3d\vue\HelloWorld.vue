<template>
  <div class="MainTitle">
    <h1>{{ msg }}</h1>
  </div>
</template>

<script>

export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  mounted() {
    // 在组件挂载之后进行 DOM 操作
    const overlap = document.querySelector('.MainTitle');
    overlap.innerHTML = overlap.textContent
      .split('')
      .map((c) => `<span>${c}</span>`)
      .join('');
},

  
}
</script>

<style scoped>
@import "../css/FontStyle.css";

</style>
