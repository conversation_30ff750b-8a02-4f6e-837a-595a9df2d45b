import { message } from 'ant-design-vue'
export default {
  // 处理右击菜单的点击事件
  handleMenuClick(action, context, treeIcon) {
    // console.log('handleMenuClick:', action)
    // console.log(treeIcon)
    switch (action) {
      case 'copy':
        this.copyNodeToClipboard(context) // 调用复制节点方法
        break
      case 'cut':
        this.cutNode(context) // 调用剪切节点方法
        break
      case 'paste':
        this.pasteNodeAndOverwrite(context) // 调用粘贴并覆盖节点方法
        break
      case 'delete':
        this.deleteNode(context) // 调用删除方法
        break
      case 'newDevice':
        this.addNewDevice(context) // 调用添加新设备方法
        break
      case 'newNode':
        context.PointsTitle = '新建测点'
        this.addNewNode(context) // 调用添加测量点方法
        break
      case 'newDefinition':
        context.PointsTitle = '新建测量定义'
        this.addNewNode(context, false) // 调用添加测量定义
        break
      case 'newArea':
        // 在显示新建节点对话框前关闭所有遮罩层
        context.stopComponentLoading()
        context.newNodeDialogVisible = true // 显示新建节点对话框
        break
      case 'characteristicFrequency':
        this.setCharacteristicFrequency(context) // 调用设置设备特征频率方法
        break
      case 'spectrumRangeSettings':
        this.setSpectrumRangeSettings(context) // 调用设置频谱显示范围方法
        break
      case 'exportFile':
        this.exportFile(context) // 调用导出文件方法
        break
      case 'importExcel':
        this.importExcel(context) // 调用导入Excel方法
        break
      case 'reserve':
        this.reserve(context) // 调用预留方法
        break
      case 'edit':
        this.EditNode(context, treeIcon) // 调用预留方法
        break
      case 'uploadImage':
        this.handleImageUpload(context) // 添加上传图片处理方法
        break
      default:
        message.error(`未知操作: ${action}`) // 显示提示框
    }
  },
  // 复制节点到剪贴板
  copyNodeToClipboard(context) {
    if (context.currentNode) {
      const nodeData = context.deepClone(context.currentNode)
      context.clipboard = nodeData
      message.success('节点已复制到剪贴板')
    }
  },
  // 剪切节点
  cutNode(context) {
    if (context.currentNode) {
      context.clipboard = context.deepClone(context.currentNode)
      context.deleteNode(context.treeData, context.currentNode.key)
      message.success('节点已剪切到剪贴板')
    }
  },
  // 粘贴
  pasteNodeAndOverwrite(context) {
    if (context.clipboard && context.currentNode) {
      const newNodeData = context.deepClone(context.clipboard)
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
      context.currentNode.children.push(newNodeData)
      message.success('节点数据已粘贴')
    } else {
      message.error('没有可粘贴的数据或未选中目标节点')
    }
  },
  // 删除
  deleteNode(context) {
    // console.log('deleteNode:', context.currentNode.id)
    context.deleteTreeItem(context.currentNode.id)
  },
  // 新建设备
  addNewDevice(context) {
    context.newDeviceDialogVisible = true
    context.getDeviceModel()
    if (context.currentNode) {
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
    }
  },
  // 新建测点
  addNewNode(context, isEdit) {
    context.newMeasuringPointsDialogVisible = true
    if (context.inAction === 'newNode') {
      context.currentView = 'sensorConfigForm'
      context.updatePointsTreeData()
    } else if (context.inAction === 'newDefinition') {
      context.currentView = 'vibration'
      context.updatePointsTreeData(isEdit)
    }
    if (context.currentNode) {
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
    }
  },

  // 新建设备
  addNewFactory(context) {
    context.newMeasuringPointsDialogVisible = true
    if (context.currentNode) {
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
    }
  },
  //  设置设备特征频率
  setCharacteristicFrequency(context) {
    console.log('设置设备特征频率')
    // 实现设置设备特征频率逻辑
  },
  // 设置频谱显示范围
  setSpectrumRangeSettings(context) {
    console.log('设置频谱显示范围')
    // 实现设置频谱显示范围逻辑
  },
  // 导出文件
  exportFile(context) {
    console.log('导出文件')
    // 实现导出文件逻辑
  },
  // 实现导入Excel逻辑
  importExcel(context) {
    console.log('导入Excel')
    // 实现导入Excel逻辑
  },
  // 实现预留功能逻辑
  reserve(context) {
    console.log('预留功能')
    // 实现预留功能逻辑
  },
  // 实现编辑功能
  EditNode(context, treeIcon) {
    switch (treeIcon) {
      case 'company':
        context.handleEditClick()
        break

      case 'factory':
        console.log('Handling factory icon')
        break

      case 'grading':
        context.handleEditClick()
        console.log('Handling grading icon')
        break

      case 'waterPump':
        context.newDeviceDialogVisible = true
        context.getDeviceData(context.currentNode.id)
        if (context.currentNode) {
          if (!context.currentNode.children) {
            context.currentNode.children = []
          }
        }
        break

      case 'bearing':
        // 打开编辑测点弹窗
        context.currentPointId = context.currentNode.id
        context.editMeasurementPointVisible = true
        break

      case 'measureDef':
        // console.log(treeIcon)
        // 下面这部分转移到treeicon为测量定义下面
        /* context.PointsTitle = '编辑测量定义'
        context.inAction = 'newDefinition' */
        /* context.editMeasurement().then(() => {
          context.newMeasuringPointsDialogVisible = true
          context.currentView = 'vibration'
          context.updatePointsTreeData(true)
        }) */
        /* context.editMeasurement() */
        /* this.addNewNode(context, true) */
        context.editMeasurementDefineVisible = true

        break
      case 'value':
        console.log(treeIcon)
        context.PointsTitle = '编辑测量定义'
        context.inAction = 'newDefinition'
        context.editMeasurement()
        this.addNewNode(context, true)
        break

      default:
        /* context.PointsTitle = '编辑测量定义'
        context.inAction = 'newDefinition'
        context.editMeasurement()
        this.addNewNode(context, true) */
        console.log('Unknown icon')
        break
    }
  },
  // 添加处理上传图片的方法
  handleImageUpload(context) {
    // 添加节点检查
    if (!context.currentNode || !context.currentNode.id) {
      context.$message.error('请先选择要上传图片的节点')
      return
    }

    // 检查节点类型
    if (context.currentNode.treeIcon !== 'company' && context.currentNode.treeIcon !== 'grading') {
      context.$message.error('只能为公司或厂区节点上传图片')
      return
    }

    // 缓存当前节点信息
    context.uploadNodeInfo = {
      id: context.currentNode.id,
      treeIcon: context.currentNode.treeIcon
    }
    context.uploadImageDialogVisible = true
  }
}
