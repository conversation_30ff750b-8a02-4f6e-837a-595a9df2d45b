import AiChat from './index.vue'
import Vue from 'vue'

export default {
  install(Vue) {
    // 创建构造器
    const AiChatConstructor = Vue.extend(AiChat)

    // 创建一个div
    const aiChatDiv = document.createElement('div')
    document.body.appendChild(aiChatDiv)

    // 创建实例并挂载
    const aiChatInstance = new AiChatConstructor().$mount()
    aiChatDiv.appendChild(aiChatInstance.$el)

    // 将实例暴露到全局
    Vue.prototype.$aiChat = aiChatInstance
  }
}

