<template>
  <div class="dashboard-container" :class="{ fullscreen: isFullscreen }" ref="dashboardContainer">
    <header class="dashboard-header">
      <h1><span id="current-time">{{ currentTime }}</span>工业互联网数据分析平台</h1>
      <button class="switch-button" @click="switchToBigScreen">设备监控中心</button>
    </header>

    <main class="dashboard-main">
      <div class="left-panel">
        <div class="chart-item">
          <div ref="lineChartMain" id="line-chart-main"></div>
        </div>
        <div class="chart-item">
          <div ref="barChartResource" id="bar-chart-resource"></div>
        </div>
        <div class="chart-item">
          <div ref="pieChartStatus" id="pie-chart-status"></div>
        </div>
      </div>

      <div class="middle-panel">
        <div ref="chinaMapChart" id="china-map-chart"></div>
      </div>

      <div class="right-panel">
        <div class="chart-item" id="realtime-alerts-panel">
          <h3>实时报警</h3>
          <div id="realtime-alerts-list" class="alerts-list-container">
            <div v-for="alert in realtimeAlerts" :key="alert.id" :class="['alert-item', alert.level]">
              <span class="timestamp">{{ formatAlertTime(alert.timestamp) }}</span>
              <strong class="type">[{{ alert.type }}]</strong>
              <span class="message">{{ alert.message }}</span>
            </div>
          </div>
        </div>
        <div class="chart-item" id="historical-alerts-panel">
          <h3>历史报警</h3>
          <div id="historical-alerts-list" class="alerts-list-container">
            <template v-if="historicalAlerts.length > 0">
              <div v-for="alert in historicalAlerts" :key="alert.id" :class="['alert-item', alert.level]">
                <div class="alert-header">
                  <span class="timestamp">{{ formatAlertTime(alert.timestamp) }}</span>
                  <span class="status" :class="alert.isSolved === '已解决' ? 'solved' : 'unsolved'">{{ alert.isSolved }}</span>
                </div>
                <div class="alert-content">
                  <strong class="type">[{{ alert.type }}级报警]</strong>
                  <span class="device-name">{{ alert.deviceName }}</span>
                </div>
                <span class="message">{{ alert.message }}</span>
              </div>
            </template>
            <div v-else class="no-data-message">
              <i class="no-data-icon">!</i>
              <p>暂无历史报警数据</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getWarnRecord } from '@/views/textPage/BigScreen/api/apiBigscreen'
import wsService from '@/utils/alarmSocket/websocket'

export default {
  name: 'IndexBigscreen',
  data() {
    return {
      currentTime: '',
      allCharts: [],
      realtimeAlerts: [],
      historicalAlerts: [],
      MAX_REALTIME_ALERTS: 7,
      MAX_HISTORICAL_ALERTS: 100,
      lineMainData: [],
      timeIntervals: [],
      historyAlertTimer: null,
      isFullscreen: false
    }
  },
  mounted() {
    this.initTimeUpdate()
    this.initCharts()
    this.fetchHistoricalAlerts()
    this.startHistoricalAlertTimer()
    this.initWebSocket()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 清理定时器
    this.timeIntervals.forEach(interval => clearInterval(interval))
    if (this.historyAlertTimer) {
      clearInterval(this.historyAlertTimer)
    }
    window.removeEventListener('resize', this.handleResize)
    // 销毁图表实例
    this.allCharts.forEach(chart => {
      if (chart && typeof chart.dispose === 'function') {
        chart.dispose()
      }
    })
    // 断开WebSocket连接
    wsService.close()
  },
  methods: {
    initTimeUpdate() {
      const updateTime = () => {
        const now = new Date()
        const year = now.getFullYear()
        const month = (now.getMonth() + 1).toString().padStart(2, '0')
        const day = now.getDate().toString().padStart(2, '0')
        const hours = now.getHours().toString().padStart(2, '0')
        const minutes = now.getMinutes().toString().padStart(2, '0')
        const seconds = now.getSeconds().toString().padStart(2, '0')
        this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }
      updateTime()
      this.timeIntervals.push(setInterval(updateTime, 1000))
    },
    async initCharts() {
      this.initLineChartMain()
      this.initBarChartResource()
      this.initPieChartStatus()
      await this.initChinaMapChart()
    },
    initChart(ref, option, theme = 'dark') {
      const chartDom = this.$refs[ref]
      if (!chartDom) return null

      const chartInstance = echarts.init(chartDom, theme)
      chartInstance.setOption({
        ...option,
        backgroundColor: 'transparent',
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '20%',
          containLabel: true,
          ...(option.grid || {})
        }
      })
      this.allCharts.push(chartInstance)
      return chartInstance
    },
    initLineChartMain() {
      let base = +new Date(2023, 0, 1)
      let oneDay = 24 * 3600 * 1000
      this.lineMainData = [[base, Math.random() * 300]]
      for (let i = 1; i < 200; i++) {
        let now = new Date((base += oneDay))
        this.lineMainData.push([+now, Math.round((Math.random() - 0.5) * 20 + this.lineMainData[i - 1][1])])
      }

      const lineOptionMain = {
        title: { text: '核心指标趋势' },
        tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
        xAxis: { type: 'time', splitLine: { show: false }, axisLine: { lineStyle: { color: '#2c5a89' } } },
        yAxis: { type: 'value', boundaryGap: [0, '100%'], splitLine: { lineStyle: { color: '#1a3a66' } }, axisLine: { lineStyle: { color: '#2c5a89' } } },
        series: [{
          name: '指标值',
          type: 'line',
          showSymbol: false,
          smooth: true,
          data: this.lineMainData,
          lineStyle: { color: '#00ffff', width: 2 },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 255, 255, 0.3)' },
              { offset: 1, color: 'rgba(0, 255, 255, 0)' }
            ])
          }
        }]
      }

      const lineChartMain = this.initChart('lineChartMain', lineOptionMain)
      if (lineChartMain) {
        this.timeIntervals.push(setInterval(() => {
          let now = new Date()
          this.lineMainData.shift()
          this.lineMainData.push([+now, Math.round((Math.random() - 0.5) * 20 + this.lineMainData[this.lineMainData.length - 1][1])])
          lineChartMain.setOption({ series: [{ data: this.lineMainData }] })
        }, 2000))
      }
    },
    initBarChartResource() {
      const barOptionResource = {
        title: { text: '资源分配' },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        xAxis: { type: 'category', data: ['CPU', '内存', '磁盘', '网络', 'GPU'], axisTick: { alignWithLabel: true }, axisLine: { lineStyle: { color: '#2c5a89' } } },
        yAxis: { type: 'value', splitLine: { lineStyle: { color: '#1a3a66' } }, axisLine: { lineStyle: { color: '#2c5a89' } } },
        series: [{
          name: '使用率',
          type: 'bar',
          barWidth: '60%',
          data: [75, 60, 85, 50, 30],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00bfff' },
              { offset: 1, color: '#0077c2' }
            ])
          }
        }]
      }
      this.initChart('barChartResource', barOptionResource)
    },
    initPieChartStatus() {
      const pieOptionStatus = {
        title: { text: '服务状态' },
        tooltip: { trigger: 'item' },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: { color: '#ccc' },
          itemGap: 5,
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8
        },
        series: [{
          name: '状态',
          type: 'pie',
          radius: ['45%', '70%'],
          center: ['60%', '55%'],
          avoidLabelOverlap: false,
          itemStyle: { borderRadius: 5, borderColor: '#0a1931', borderWidth: 1 },
          label: { show: false, position: 'center' },
          emphasis: {
            label: { show: true, fontSize: '16', fontWeight: 'bold', formatter: '{b}\n{d}%' }
          },
          labelLine: { show: false },
          data: [
            { value: 1048, name: '正常', itemStyle: { color: '#00bcd4' } },
            { value: 735, name: '警告', itemStyle: { color: '#ff9800' } },
            { value: 580, name: '错误', itemStyle: { color: '#f44336' } },
            { value: 484, name: '离线', itemStyle: { color: '#607d8b' } }
          ]
        }]
      }
      this.initChart('pieChartStatus', pieOptionStatus)
    },
    async initChinaMapChart() {
      try {
        // 尝试加载用户提供的GeoJSON数据源
        let geoJson = null;
        let errorMessage = '';

        try {
          // 使用用户提供的新数据源
          const response = await fetch('https://geojson.cn/api/china/1.6.2/china.json');
          if (response.ok) {
            geoJson = await response.json();
            console.log('成功加载geojson.cn的中国地图数据');
          } else {
            errorMessage = `geojson.cn数据源请求失败: ${response.status}`;
            console.error(errorMessage);
          }
        } catch (err) {
          errorMessage = `geojson.cn数据源访问出错: ${err.message}`;
          console.error(errorMessage);
        }

        // 如果无法加载地图数据，使用简化版渲染
        if (!geoJson) {
          console.warn('使用简化版地图渲染');
          // 使用简单的边界框
          const mockChinaGeoJson = {
            type: "FeatureCollection",
            features: []
          };

          geoJson = mockChinaGeoJson;
        }

        // 注册地图
        echarts.registerMap('china', geoJson);

        const geoCoordMap = {
          '宁波北仑区': [121.844, 29.9],
          '北京': [116.46, 39.92], '上海': [121.48, 31.22], '广州': [113.23, 23.16], '深圳': [114.07, 22.62],
          '成都': [104.06, 30.67], '武汉': [114.31, 30.52], '西安': [108.95, 34.27], '杭州': [120.19, 30.26],
          '重庆': [106.54, 29.59], '乌鲁木齐': [87.68, 43.77], '拉萨': [91.11, 29.97], '哈尔滨': [126.63, 45.75],
          '沈阳': [123.43, 41.80], '兰州': [103.73, 36.03], '昆明': [102.73, 25.04], '南宁': [108.33, 22.84],
          '海口': [110.35, 20.02], '南京': [118.78, 32.04], '长沙': [112.94, 28.23], '合肥': [117.27, 31.86],
          '南昌': [115.89, 28.68], '福州': [119.30, 26.08], '济南': [117.00, 36.65], '石家庄': [114.48, 38.03],
          '郑州': [113.65, 34.76], '天津': [117.20, 39.13], '太原': [112.53, 37.87], '呼和浩特': [111.65, 40.82],
          '银川': [106.27, 38.47], '西宁': [101.74, 36.56], '台北': [121.52, 25.05]
        }
        const centerCity = '宁波北仑区'
        const targetCities = Object.keys(geoCoordMap).filter(city => city !== centerCity);

        const convertData = (data) => {
          const res = []
          for (let i = 0; i < data.length; i++) {
            const dataItem = data[i]
            const fromCoord = geoCoordMap[dataItem[0].name]
            const toCoord = geoCoordMap[dataItem[1].name]
            if (fromCoord && toCoord) {
              res.push({
                fromName: dataItem[0].name,
                toName: dataItem[1].name,
                coords: [fromCoord, toCoord],
                value: dataItem[1].value
              })
            }
          }
          return res
        }

        const lineData = targetCities.map(city => {
          return [{ name: centerCity }, { name: city, value: Math.floor(Math.random() * 100) + 50 }]
        })

        // 创建虚拟的边界框 (如果没有成功加载GeoJSON时会用到)
        const chinaBoundary = [
          [73, 18], [73, 53.5], [135, 53.5], [135, 18], [73, 18]
        ];

        const mapOption = {
          backgroundColor: 'transparent',
          title: { text: '昊宇检测', left: 'center', top: 10, textStyle: { color: '#00ffff', fontSize: 20 } },
          tooltip: {
            trigger: 'item',
            formatter: function (params) {
              if (params.seriesType === 'lines') {
                return params.data.fromName + ' -> ' + params.data.toName + '<br/>流量: ' + params.data.value
              } else if (params.seriesType === 'effectScatter' || params.seriesType === 'scatter') {
                return params.name + '<br/>坐标: ' + params.value.slice(0, 2).map(v => v.toFixed(2)).join(', ')
              }
              return params.name
            }
          },
          geo: {
            map: 'china',
            roam: true,
            zoom: 1.2,
            center: [104.0, 34.5],
            label: { show: false, color: '#ccc' },
            emphasis: { label: { show: true, color: '#fff' }, itemStyle: { areaColor: '#2a333d' } },
            itemStyle: { areaColor: 'rgba(16, 46, 87, 0.7)', borderColor: '#0A53E9', borderWidth: 1 },
            regions: [{ name: '南海诸岛', itemStyle: { opacity: 0, label: { show: false } } }]
          },
          series: [
            // 如果未能加载GeoJSON，则显示虚拟边界
            ...(geoJson.features && geoJson.features.length ? [] : [{
              name: '中国边界',
              type: 'line',
              coordinateSystem: 'geo',
              zlevel: 1,
              animation: false,
              lineStyle: {
                color: '#0A53E9',
                width: 2,
                opacity: 0.6
              },
              data: chinaBoundary.map(coord => {
                return {
                  value: coord
                };
              }),
              symbol: 'none'
            }]),
            {
              name: '连接线',
              type: 'lines',
              zlevel: 2,
              effect: {
                show: true,
                period: 6,
                trailLength: 0.7,
                color: '#fff',
                symbolSize: 3
              },
              lineStyle: {
                normal: {
                  color: '#00ffff',
                  width: 0.8,
                  curveness: 0.2
                }
              },
              data: convertData(lineData)
            },
            {
              name: '起点',
              type: 'effectScatter',
              coordinateSystem: 'geo',
              zlevel: 2,
              rippleEffect: {
                brushType: 'stroke',
                scale: 5
              },
              label: {
                normal: {
                  show: true,
                  position: 'right',
                  formatter: '{b}',
                  color: '#ffffff',
                  fontSize: 14,
                  fontWeight: 'bold',
                  textShadow: '0 0 5px rgba(0,0,0,1)'
                }
              },
              symbolSize: 20,
              itemStyle: {
                normal: {
                  color: '#f4e925'
                }
              },
              data: [{
                name: centerCity,
                value: geoCoordMap[centerCity]
              }]
            },
            {
              name: '城市',
              type: 'scatter',
              coordinateSystem: 'geo',
              zlevel: 2,
              label: {
                normal: {
                  show: true,
                  position: 'right',
                  formatter: '{b}',
                  color: '#00ffff'
                }
              },
              symbolSize: 8,
              itemStyle: {
                normal: {
                  color: '#00ffff'
                }
              },
              data: targetCities.map(city => {
                return {
                  name: city,
                  value: geoCoordMap[city]
                }
              })
            }
          ]
        }

        // 确保地图容器有明确的边界和背景
        if (this.$refs.chinaMapChart) {
          this.$refs.chinaMapChart.style.border = '1px solid #1f4f89';
          this.$refs.chinaMapChart.style.background = 'rgba(16, 46, 87, 0.1)';
          this.$refs.chinaMapChart.style.position = 'relative';
        }

        const chinaMapChart = this.initChart('chinaMapChart', mapOption)
        if (chinaMapChart) {
          this.allCharts.push(chinaMapChart)

          // 显示消息
          if (this.$refs.chinaMapChart) {
            const loadingElement = document.createElement('div');
            loadingElement.style.cssText = 'position:absolute;top:10px;left:10px;color:#00ffff;font-size:12px;background:rgba(0,0,0,0.5);padding:4px 8px;border-radius:4px;z-index:10;';
            loadingElement.textContent = geoJson.features && geoJson.features.length ?
              '地图数据加载成功' : '已显示简化版地图';
            this.$refs.chinaMapChart.appendChild(loadingElement);

            // 5秒后移除消息
            setTimeout(() => {
              loadingElement.remove();
            }, 5000);
          }
        }
      } catch (error) {
        console.error("加载中国地图数据失败:", error)
        if (this.$refs.chinaMapChart) {
          this.$refs.chinaMapChart.innerHTML = `
            <div style="display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:20px;text-align:center;">
              <p style="color:#00ffff;margin-bottom:15px;">地图显示失败</p>
              <p style="color:#cccccc;font-size:12px;margin-bottom:15px;">
                错误信息: ${error.message}
              </p>
              <button id="retry-load-map" style="padding:5px 15px;background:rgba(0,122,255,0.3);color:#00ffff;border:1px solid #00ffff;border-radius:4px;cursor:pointer;margin-top:10px;">
                重试加载地图
              </button>
            </div>
          `;

          // 添加点击事件
          setTimeout(() => {
            const retryButton = document.getElementById('retry-load-map');
            if (retryButton) {
              retryButton.addEventListener('click', () => {
                this.$refs.chinaMapChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;"><p style="color:#00ffff;">正在重新加载地图...</p></div>';
                setTimeout(() => this.initChinaMapChart(), 500);
              });
            }
          }, 0);
        }
      }
    },
    formatAlertTime(date) {
      return new Date(date).toLocaleTimeString('zh-CN', { hour12: false })
    },
    initWebSocket() {
      const wsUrl = process.env.VUE_APP_WS_URL || ''
      if(wsUrl != ''){
        wsService.connect(wsUrl)
      }

      wsService.onAlarmNotification((alarmData) => {
        this.realtimeAlerts.unshift(alarmData)
        if (this.realtimeAlerts.length > this.MAX_REALTIME_ALERTS) {
          this.realtimeAlerts.pop()
        }
      })
    },
    handleResize() {
      this.allCharts.forEach(chart => {
        if (chart && typeof chart.resize === 'function') {
          chart.resize()
        }
      })
    },
    switchToBigScreen() {
      this.$router.push('/index')
    },
    startHistoricalAlertTimer() {
      // 确保先清除之前的定时器
      if (this.historyAlertTimer) {
        clearInterval(this.historyAlertTimer)
      }

      // 设置新的定时器
      this.historyAlertTimer = setInterval(() => {
        this.fetchHistoricalAlerts()
      }, 60000) // 每分钟刷新一次
    },
    async fetchHistoricalAlerts() {
      try {
        console.log('发起历史报警数据请求...')
        // 调用正确的API函数，传入页码（从1开始）
        const response = await getWarnRecord(1)

        if (response && response.code === 200 && response.rows) {
          // 将API返回的数据转换为组件需要的格式
          this.historicalAlerts = response.rows.map(alert => {
            // 根据报警级别确定样式类名
            let levelClass = 'info'
            if (alert.warnLevel === '1') levelClass = 'warning'
            if (alert.warnLevel === '2') levelClass = 'warning'
            if (alert.warnLevel === '3') levelClass = 'critical'

            return {
              id: alert.id,
              timestamp: new Date(alert.warnTime),
              level: levelClass,
              type: alert.warnLevel,
              deviceName: alert.deviceName,
              message: alert.warnMsg,
              isSolved: alert.isSolved === 1 ? '已解决' : '未解决'
            }
          })

          // 按时间降序排序
          this.historicalAlerts.sort((a, b) => b.timestamp - a.timestamp)

          // 限制显示数量
          if (this.historicalAlerts.length > this.MAX_HISTORICAL_ALERTS) {
            this.historicalAlerts = this.historicalAlerts.slice(0, this.MAX_HISTORICAL_ALERTS)
          }
        } else {
          console.error('获取历史报警数据失败: 返回数据格式不正确', response)
          this.historicalAlerts = []
        }
      } catch (error) {
        console.error('获取历史报警数据失败:', error)
        this.historicalAlerts = []
      }
    },
    toggleFullScreen() {
      this.isFullscreen = !this.isFullscreen;
      const el = this.$refs.dashboardContainer;
      if (this.isFullscreen) {
        el.requestFullscreen?.().catch(err => console.error(err));
      } else {
        document.exitFullscreen?.().catch(err => console.error(err));
      }
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 15px;
  box-sizing: border-box;
  background-color: #142e54;
}

.dashboard-header {
  padding: 10px 20px;
  text-align: center;
  background-color: rgba(19, 41, 79, 0.5);
  border-bottom: 2px solid #1f4f89;
  margin-bottom: 15px;
  flex-shrink: 0;
  position: relative;
}

.dashboard-header h1 {
  margin: 0;
  font-size: 2em;
  color: #00ffff;
  letter-spacing: 2px;
  text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff;
  text-align: center;
}

#current-time {
  font-size: 0.8em;
  color: #7DF9FF;
  margin-right: 15px;
}

.dashboard-main {
  flex-grow: 1;
  display: flex;
  gap: 15px;
  overflow: hidden;
}

.left-panel, .right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  max-height: calc(100vh - 80px);
}

.middle-panel {
  flex: 2.5;
  display: flex;
  background-color: rgba(25, 54, 101, 0.6);
  border: 1px solid #1f4f89;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 0 15px rgba(0, 122, 255, 0.3) inset, 0 0 5px rgba(0, 122, 255, 0.2);
}

#china-map-chart {
  width: 100%;
  height: 100%;
}

.chart-item {
  background-color: rgba(25, 54, 101, 0.6);
  border: 1px solid #1f4f89;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 0 15px rgba(0, 122, 255, 0.3) inset, 0 0 5px rgba(0, 122, 255, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  min-height: 180px;
  flex: 1;
}

.chart-item h3 {
  margin: 0 0 8px 0;
  padding-bottom: 5px;
  font-size: 1em;
  color: #7DF9FF;
  text-align: center;
  border-bottom: 1px solid rgba(31, 79, 137, 0.5);
  flex-shrink: 0;
}

.alerts-list-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 5px 0;
}

.alert-item {
  padding: 8px 10px;
  margin-bottom: 8px;
  background-color: rgba(19, 41, 79, 0.7);
  border-radius: 3px;
  font-size: 0.85em;
  border-left: 3px solid #ffc107;
  line-height: 1.4;
}

.alert-item:last-child {
  margin-bottom: 0;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
}

.alert-content {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.device-name {
  color: #b4e0ff;
  margin-left: 5px;
}

.alert-item.critical { border-left-color: #f44336; }
.alert-item.warning { border-left-color: #ff9800; }
.alert-item.info { border-left-color: #03a9f4; }

.alert-item .timestamp {
  font-size: 0.9em;
  color: #88a8d1;
}

.alert-item .status {
  font-size: 0.8em;
  padding: 1px 6px;
  border-radius: 10px;
  font-weight: bold;
}

.status.solved {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.status.unsolved {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.alert-item .message {
  color: #e0e0e0;
  display: block;
  word-break: break-word;
}

.alert-item .type {
  font-weight: bold;
  color: #b0d0ff;
}

::-webkit-scrollbar { width: 6px; height: 6px; }
::-webkit-scrollbar-thumb { background: #1f4f89; border-radius: 3px; }
::-webkit-scrollbar-track { background: #0a1931; }

.switch-button {
  background-color: rgba(0, 122, 255, 0.3);
  border: 1px solid #00ffff;
  color: #00ffff;
  padding: 5px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.3s ease;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.switch-button:hover {
  background-color: rgba(0, 122, 255, 0.5);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #88a8d1;
  padding: 20px;
  text-align: center;
}

.no-data-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(3, 169, 244, 0.2);
  color: #03a9f4;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.dashboard-container.fullscreen {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  padding: 0 !important;
  margin: 0 !important;
  z-index: 9999 !important;
}
</style>
