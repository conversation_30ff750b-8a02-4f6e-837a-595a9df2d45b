<!-- src/components/ModelLoaderDialog.vue -->
<template>
    <el-dialog title="载入模型" :visible.sync="dialogVisible" width="65%">
        <div style="display: flex;">
            <!-- 左边3D场景 -->
            <div ref="objViewer_test" class="model-viewer" style="width: 48%; height: 400px;"></div>

            <!-- 右边选择框 -->
            <div style="width: 52%; padding-left: 20px;">
                <el-form>
                    <el-form-item label="选择模型来源">
                        <el-select v-model="modelSource" placeholder="请选择" style="width: 180px;">
                            <el-option label="下载线上模型" value="online"></el-option>
                            <el-option label="选择本地模型" value="local"></el-option>
                            <!-- <el-option label="上传图片生成3D模型" value="python"></el-option> -->
                        </el-select>
                    </el-form-item>

                    <!-- 根据选择显示不同的输入框 -->
                    <el-form-item v-if="modelSource === 'online'" label="请选择模型">
                        <!-- 模型列表标题和刷新按钮 -->
                        <div class="model-list-header">
                            <span>模型列表</span>
                            <el-button 
                                size="mini" 
                                icon="el-icon-refresh" 
                                @click="fetchModelList" 
                                :loading="modelListLoading">刷新</el-button>
                        </div>
                        
                        <!-- 模型列表展示区域 -->
                        <div class="model-list-container" v-loading="modelListLoading">
                            <div v-if="modelList.length === 0" class="no-models">
                                <span>暂无可用模型</span>
                            </div>
                            <div v-else v-for="(model, index) in modelList" 
                                :key="model.id"
                                class="model-item"
                                @click="handleModelClick(model)">
                                <div class="model-card">
                                    <div class="model-image">
                                        <img v-if="model.backgroundImageUrl" :src="model.backgroundImageUrl" alt="模型预览">
                                        <div v-else class="no-image">无预览图</div>
                                    </div>
                                    <div class="model-name">{{ model.fileNote || '模型' + (index + 1) }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="input-group">
                            <el-input v-model="modelUrl" placeholder="请输入模型ID"></el-input>
                            <el-button type="primary" @click="IdGetRelationList">下载并载入</el-button>
                        </div>
                    </el-form-item>

                    <el-form-item v-if="modelSource === 'local'" label="上传本地模型">
                        <el-upload
                            class="upload-demo"
                            action=""
                            :auto-upload="false"
                            :on-change="uploadLocalModel"
                            :show-file-list="true">
                            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                            <div slot="tip" class="el-upload__tip">请上传OBJ格式的3D模型文件</div>
                        </el-upload>
                    </el-form-item>

                    <el-form-item v-if="modelSource === 'python'" label="上传图片">
                        <input type="file" @change="uploadLocalPhoto" ref="fileInput"/>
                    </el-form-item>
                </el-form>
            </div>

        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="confirmLoadModel(false)">取消</el-button>
            <el-button type="primary" @click="confirmLoadModel(true)">确认</el-button>
        </span>
    </el-dialog>
</template>

<script>
import axios from 'axios';
import * as THREE from 'three';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { UploadFile, GetImgObjList, DownloadImgObjList, DeleteItem, ChunkFile, MergeFile, GetRelationList, getDemoModelList, uploadImageToURL } from '@/api/haoyu-system/web3d_api/web_3dapi.js';
import { getProcessImageToObjUrl } from '@/config/api.config'
export default {
    props: {
        dialogVisible: {
            type: Boolean,
            required: true
        },
        canCreate: {  // 添加 canCreate prop
            type: Boolean,
            required: true
        }
    },

    data() {
        return {
            modelList: [], // 存储模型列表
            modelListLoading: false, // 加载状态
            deviceImages: [
                '连轴齿轮.png',
                '整个轮毂.png',
                '离心泵.png',
                '单个齿轮.png',
                '齿轮箱.png'
            ],
            animationId: null,
            color: '#ffffff', // 初始颜色
            modelSource: 'online', // 选择模型来源
            modelUrl: '', // 线上模型URL
            localFile: null, // 本地上传的模型文件
            DialogScene: null, // 场景
            DialogCamera: null, // 相机
            renderer: null, // 渲染器
            controls: null, // 控制器
            models: [], // 存储多个模型
            selectedModelIndex: null, // 当前选中的模型索引
            ObjUrl: '',
            pageSize: 1,
            pageNum: 1,
            // 模型
            selectedFile: null,
            objFileUrl: null,
            // 新增属性
            scale: 1, // 默认缩放
            rotationX: 0, // 默认旋转X
            rotationY: 0, // 默认旋转Y
            rotationZ: 0, // 默认旋转Z
        };
    },

    created() {
        getProcessImageToObjUrl().then(url => {
            console.log('图片转3dURL:', url);
            this.healthCheckUrl = url; // 将URL保存到data中
        }).catch(error => {
            console.error('获取图片转3dURL失败:', error);
        });
        
        // 获取模型列表
        this.fetchModelList();
    },

    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.$nextTick(() => {
                    this.initObjViewer();
                    // 当对话框打开时刷新模型列表
                    this.fetchModelList();
                });
            } else {
                this.disposeViewer();
            }
        }
    },


    methods: {
        // 获取模型列表
        async fetchModelList() {
            this.modelListLoading = true;
            try {
                const response = await getDemoModelList();
                console.log('getDemoModelList响应:', response);
                if (response.code === 200 && response.rows) {
                    this.modelList = response.rows || [];
                    
                    // 处理模型列表数据
                    this.modelList.forEach(model => {
                        // 确保uploadFiles存在
                        if (!model.uploadFiles) {
                            model.uploadFiles = [];
                        }
                        
                        // 如果有fileRelation但没有uploadFiles，尝试解析fileRelation
                        if (model.fileRelation && model.uploadFiles.length === 0) {
                            console.log(`模型 ${model.id} 有fileRelation但没有uploadFiles`);
                        }
                    });
                    
                    console.log('模型列表获取成功并处理完成:', this.modelList);
                } else {
                    // this.$message.warning('获取模型列表失败');
                    console.error('获取模型列表失败:', response);
                }
            } catch (error) {
                // this.$message.error('获取模型列表出错');
                console.error('获取模型列表出错:', error);
            } finally {
                this.modelListLoading = false;
            }
        },

        // 处理模型点击
        async handleModelClick(model) {
            console.log('选择的模型:', model);
            
            // 设置模型ID用于标识
            this.modelUrl = model.id;
            
            // 检查模型是否有关联的文件
            if (!model.fileRelation && (!model.uploadFiles || model.uploadFiles.length === 0)) {
                this.$message.warning('该模型没有关联的文件');
                return;
            }
            
            // 清空场景中现有的模型
            this.clearExistingModels();
            
            // 提示用户
            this.$message.info(`正在加载模型${model.fileNote || model.id}，请稍候...`);
            
            try {
                let fileIds = [];
                
                // 优先检查uploadFiles数组
                if (model.uploadFiles && model.uploadFiles.length > 0) {
                    // 从uploadFiles中提取ID
                    fileIds = model.uploadFiles.map(file => file.id.toString());
                    console.log('从uploadFiles中获取的文件ID列表:', fileIds);
                } else if (model.fileRelation) {
                    // 如果没有uploadFiles但有fileRelation，则从fileRelation中提取ID
                    fileIds = model.fileRelation.split(',').filter(id => id.trim() !== '');
                    console.log('从fileRelation中获取的文件ID列表:', fileIds);
                }
                
                if (fileIds.length === 0) {
                    this.$message.warning('该模型没有有效的关联文件ID');
                    return;
                }
                
                // 存储所有下载任务的Promise
                const downloadPromises = [];
                
                // 遍历文件ID列表并下载每个文件
                for (const fileId of fileIds) {
                    if (fileId && !isNaN(fileId)) {  // 确保ID是有效的数字
                        // console.log(`正在下载模型文件 ID: ${fileId}`);
                        downloadPromises.push(
                            this.DownloadAndLoading(fileId)
                                .catch(error => {
                                    console.error(`模型文件 ${fileId} 下载失败:`, error);
                                    return null;  // 返回null表示这个下载失败了
                                })
                        );
                    }
                }
                
                // 等待所有下载完成
                const results = await Promise.all(downloadPromises);
                console.log('所有模型文件下载完成', results);
                
                // 检查是否有成功下载的模型
                const successfulDownloads = results.filter(result => result !== null);
                if (successfulDownloads.length > 0) {
                    this.$message.success(`成功下载 ${successfulDownloads.length} 个模型文件`);
                } else {
                    this.$message.warning('没有成功下载任何模型文件');
                }
            } catch (error) {
                console.error('处理模型下载时出错:', error);
                // this.$message.error('模型下载失败');
            }
        },

        // 清除场景中已有的模型
        clearExistingModels() {
            // 如果有模型，从场景中移除并释放资源
            if (this.models && this.models.length > 0) {
                console.log(`清除场景中的 ${this.models.length} 个已有模型`);
                
                this.models.forEach(model => {
                    if (model && this.DialogScene) {
                        this.DialogScene.remove(model);
                        model.traverse(child => {
                            if (child.isMesh) {
                                if (child.material) {
                                    if (Array.isArray(child.material)) {
                                        child.material.forEach(material => material.dispose());
                                    } else {
                                        child.material.dispose();
                                    }
                                }
                            }
                        });
                    }
                });
                
                // 清空数组
                this.models = [];
                this.selectedModelIndex = null;
                
                // 回收资源
                if (this.renderer) {
                    this.renderer.renderLists.dispose();
                }
            }
        },

        // 添加图片点击处理方法
        handleImageClick(imageName) {
            console.log('点击的图片名称:', imageName);
        },

        rotateModel() {
        if (this.selectedModelIndex !== null) {
            const model = this.models[this.selectedModelIndex];
            if (model) {
                model.rotation.x -= Math.PI / 2;
            }
        }
        },

        changeColor() {
            if (this.selectedModelIndex !== null) {
                const model = this.models[this.selectedModelIndex];
                if (model) {
                    model.traverse((child) => {
                        if (child.isMesh) {
                            child.material.color.set(this.color);
                        }
                    });
                }
            }
        },

        // 初始化3D场景
        initObjViewer() {
            const container = this.$refs.objViewer_test;

            this.DialogScene = new THREE.Scene();
            this.DialogScene.background = new THREE.Color(0xeeeeee);

            // 设置相机
            this.DialogCamera = new THREE.PerspectiveCamera(
                75,
                container.offsetWidth / container.offsetHeight,
                0.1,
                1000
            );
            this.DialogCamera.position.set(0, 2, 5);

            // 设置渲染器
            this.renderer = new THREE.WebGLRenderer({ antialias: true });
            this.renderer.setSize(container.offsetWidth, container.offsetHeight);
            this.renderer.shadowMap.enabled = true;
            container.appendChild(this.renderer.domElement);

            // 添加光照和地面
            this.initLightingAndGround();

            // 添加OrbitControls
            this.controls = new OrbitControls(this.DialogCamera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;

            // 渲染循环
            const animate = () => {
                this.animationId = requestAnimationFrame(animate);
                this.controls.update();
                this.renderer.render(this.DialogScene, this.DialogCamera);
            };
            animate();

            // 处理窗口大小变化
            window.addEventListener('resize', this.onWindowResize);
        },

        async IdGetRelationList() {
            try {
                // 如果modelUrl为空，提示用户
                if (!this.modelUrl) {
                    this.$message.warning('请先选择或输入模型ID');
                    return;
                }
                
                // 清空场景中现有的模型
                this.clearExistingModels();
                
                const response = await GetRelationList(this.modelUrl);
                console.log("GetRelationList响应:", response);
                
                if (response.code === 200 && response.data) {
                    // 存储所有下载任务的Promise
                    const downloadPromises = [];
                    
                    // 遍历所有的键值对
                    for (const [key, value] of Object.entries(response.data)) {
                        if (key && !isNaN(key)) {  // 确保key是有效的数字
                            console.log(`正在下载模型 ID: ${key}`);
                            downloadPromises.push(
                                this.DownloadAndLoading(key)
                                    .catch(error => {
                                        console.error(`模型 ${key} 下载失败:`, error);
                                        return null;  // 返回null表示这个下载失败了
                                    })
                            );
                        }
                    }
                    
                    // 等待所有下载完成
                    const results = await Promise.all(downloadPromises);
                    console.log('所有模型下载完成', results);
                    
                    // 检查是否有成功下载的模型
                    const successfulDownloads = results.filter(result => result !== null);
                    if (successfulDownloads.length > 0) {
                        this.$message.success(`成功下载 ${successfulDownloads.length} 个模型`);
                    } else {
                        this.$message.warning('没有成功下载任何模型');
                    }
                }
            } catch (error) {
                console.error('获取关联列表失败:', error);
                // this.$message.error('获取模型列表失败');
            }
        },

        async DownloadAndLoading(id) {
            try {
                const response = await DownloadImgObjList(id);
                const blob = new Blob([response], { type: 'obj/binary' });
                const url = URL.createObjectURL(blob);
                this.ObjUrl = url;
                await this.loadModel(this.ObjUrl);
                return url;  // 返回成功下载的URL
            } catch (error) {
                console.error('Error downloading OBJ:', error);
                // this.$message.error(`模型 ${id} 下载失败`);
                throw error;  // 抛出错误以便上层处理
            }
        },

        // 处理本地文件上传
        uploadLocalModel(file) {
            if (!file) return;
            
            // 清空场景中现有的模型
            this.clearExistingModels();
            
            // 获取文件对象
            const localFile = file.raw;
            
            if (localFile) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const contents = e.target.result;
                    const blob = new Blob([contents], { type: 'application/octet-stream' });
                    const url = URL.createObjectURL(blob);
                    this.loadModel(url);
                    this.ObjUrl = url;
                };
                reader.readAsArrayBuffer(localFile);
            }
        },

        // 处理本地文件上传
        uploadLocalPhoto(event) {
            // 清空场景中现有的模型
            this.clearExistingModels();
            
            this.localFile = event.target.files[0];
            this.processImage();
        },

        async processImage() {
            const formData = new FormData();
            formData.append('image', this.localFile);

            try {
                const response = await axios.post(
                // 'http://192.168.31.35:12121/process-image-to-obj/',
                    this.healthCheckUrl + '/process-image-to-obj/',
                formData,
                {
                    responseType: 'json',
                }
                );

                const responseData = response.data;
                this.objFileUrl = responseData.file_url;
                this.modelUrl = responseData.obj_id;

                console.log('obj_id:', this.modelUrl);
                this.DownloadAndLoading(this.modelUrl);
            } catch (error) {
                console.error('处理图片时出错:', error);
            }
            },

        loadModel(ObjUrl) {
            return new Promise((resolve, reject) => {
                // console.log("ObjUrl", ObjUrl);
                const loader = new OBJLoader();
                loader.load(
                    ObjUrl,
                    (object) => {
                        // 获取所有的子网格
                        const meshes = [];
                        object.traverse((child) => {
                            if (child.isMesh) {
                                meshes.push(child);
                            }
                        });

                        // 为每个网格创建单独的模型
                        meshes.forEach((mesh, index) => {
                            console.log("mesh", mesh);
                            const modelGroup = new THREE.Group();
                            
                            // 克隆网格并添加到新的组中
                            const clonedMesh = mesh.clone();
                            
                            // 创建新材质
                            const material = new THREE.MeshStandardMaterial({
                                color: this.color,
                                roughness: 0.3,
                                metalness: 0.8,
                            });
                            
                            clonedMesh.material = material;
                            clonedMesh.castShadow = true;
                            clonedMesh.receiveShadow = true;
                            
                            modelGroup.add(clonedMesh);
                            
                            // 设置组的变换
                            modelGroup.scale.set(this.scale, this.scale, this.scale);
                            modelGroup.position.set(0, 0, 0);
                            modelGroup.rotation.set(
                                THREE.MathUtils.degToRad(this.rotationX),
                                THREE.MathUtils.degToRad(this.rotationY),
                                THREE.MathUtils.degToRad(this.rotationZ)
                            );
                            
                            // 设置模型名称和URL
                            modelGroup.name = mesh.name || `模型${this.models.length + 1}`;
                            clonedMesh.name = mesh.name || `模型${this.models.length + 1}`;
                            modelGroup.userData.url = ObjUrl;  // 保存URL信息
                            
                            this.DialogScene.add(modelGroup);
                            this.models.push(modelGroup);
                        });

                        if (this.selectedModelIndex === null && this.models.length > 0) {
                            this.selectedModelIndex = 0;
                        }

                        // console.log('模型加载完成，共创建', this.models.length, '个模型');
                        resolve(object);
                    },
                    (xhr) => {
                        console.log(`模型加载进度: ${(xhr.loaded / xhr.total) * 100}%`);
                    },
                    (error) => {
                        console.error('加载模型时出错:', error);
                        // this.$message.error('模型加载失败');
                        reject(error);
                    }
                );
            });
        },

        disposeViewer() {
            // 取消动画
            if (this.animationId) {
                cancelAnimationFrame(this.animationId);
                this.animationId = null;
            }

            // 清理模型资源
            if (this.models) {
                this.models.forEach(model => {
                    if (model && this.DialogScene) {
                        this.DialogScene.remove(model);
                        model.traverse(child => {
                            if (child.isMesh) {
                                // 不要立即释放几何体，因为可能已经传递给接收方
                                // 仅释放材质
                                if (child.material) {
                                    if (Array.isArray(child.material)) {
                                        child.material.forEach(material => material.dispose());
                                    } else {
                                        child.material.dispose();
                                    }
                                }
                            }
                        });
                    }
                });
            }

            // 清理渲染器
            if (this.renderer) {
                this.renderer.dispose();
                this.renderer.forceContextLoss();
                if (this.renderer.domElement && this.renderer.domElement.parentNode) {
                    this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
                }
                this.renderer.domElement = null;
                this.renderer = null;
            }

            // 清理其他THREE.js对象
            if (this.DialogScene) {
                this.DialogScene.traverse(object => {
                    if (object.isMesh && object !== this.models) {
                        if (object.geometry) object.geometry.dispose();
                        if (object.material) {
                            if (Array.isArray(object.material)) {
                                object.material.forEach(material => material.dispose());
                            } else {
                                object.material.dispose();
                            }
                        }
                    }
                });
                this.DialogScene = null;
            }

            // 重置引用
            this.DialogCamera = null;
            this.controls = null;
            
            // 重置文件输入
            if (this.$refs.fileInput) {
                this.$refs.fileInput.value = '';
            }
            this.localFile = null;
            this.ObjUrl = '';
            
            // 触发垃圾回收
            setTimeout(() => {
                console.log('资源已清理，触发垃圾回收');
            }, 200);
        },



        initLightingAndGround() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
            this.DialogScene.add(ambientLight);

            // 方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 10, 7.5);
            directionalLight.castShadow = true;
            this.DialogScene.add(directionalLight);

            // 地面
            const planeGeometry = new THREE.PlaneGeometry(100, 100);
            const planeMaterial = new THREE.MeshStandardMaterial({ color: 0x808080, roughness: 0.5 });
            const plane = new THREE.Mesh(planeGeometry, planeMaterial);
            plane.rotation.x = -Math.PI / 2;
            plane.position.y = -1;
            plane.receiveShadow = true;
            plane.isGround = true;
            this.DialogScene.add(plane);
        },

        onWindowResize() {
            const container = this.$refs.objViewer_test;
            if (!container || !this.DialogCamera || !this.renderer) return;
            this.DialogCamera.aspect = container.offsetWidth / container.offsetHeight;
            this.DialogCamera.updateProjectionMatrix();
            this.renderer.setSize(container.offsetWidth, container.offsetHeight);
        },

        async confirmLoadModel(isConfirmed) {
            console.time('confirmLoadModel');
            try {
                if (isConfirmed && this.models.length > 0) {
                    // 收集所有模型的信息和几何体数据
                    const modelDataTransfer = this.models.map(model => {
                        // 基本信息
                        const modelInfo = {
                            name: model.name,
                            position: model.position.clone(),
                            rotation: model.rotation.clone(),
                            scale: model.scale.clone(),
                            meshes: []
                        };
                        
                        // 提取网格信息
                        model.traverse(child => {
                            if (child.isMesh) {
                                // 直接传递几何体引用以避免重新加载
                                const meshData = {
                                    name: child.name,
                                    geometry: child.geometry,  // 直接传递几何体的引用
                                    material: {
                                        color: child.material.color.getHexString(),
                                        opacity: child.material.opacity,
                                        roughness: child.material.roughness || 0.3,
                                        metalness: child.material.metalness || 0.8
                                    }
                                };
                                modelInfo.meshes.push(meshData);
                            }
                        });
                        
                        return modelInfo;
                    });

                    console.log(`准备传递 ${modelDataTransfer.length} 个模型数据`);
                    
                    // 发送所有模型信息，包括特殊标记表示这是内存传输
                    this.$emit('model-loaded', {
                        models: modelDataTransfer,
                        url: this.ObjUrl,
                        directTransfer: true  // 标记为直接传输几何体数据
                    });
                    
                    console.log('模型数据已发送');
                }
            } catch (error) {
                console.error('处理模型数据时出错:', error);
                // this.$message.error('处理模型数据失败');
            } finally {
                console.timeEnd('confirmLoadModel');
                
                // 关闭对话框
                this.$emit('update:dialogVisible', false);
                this.$emit('update:canCreate', false);
                
                // 延迟清理资源，确保接收方有足够时间处理
                setTimeout(() => {
                    this.disposeViewer();
                    this.models = [];
                    this.selectedModelIndex = null;
                }, 100);
            }
        },
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.onWindowResize);
        if (this.renderer) {
            this.renderer.dispose();
        }
    },
};
</script>

<style scoped>
.model-viewer {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    width: 100%;
    height: 400px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    background-color: #f8f9fa;
}

.model-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.model-list-header span {
    font-size: 14px;
    font-weight: 500;
    color: #606266;
}

.model-list-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

.no-models {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: #909399;
    font-size: 14px;
}

.model-item {
    cursor: pointer;
    transition: all 0.3s;
}

.model-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.model-card {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    height: 150px;
    background-color: white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.03);
}

.model-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.model-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
    font-size: 14px;
    background-color: #f5f7fa;
}

.model-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0px;
    font-size: 13px;
    color: #fff;
    background: rgba(0, 0, 0, 0.6);
    z-index: 2;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.model-file-info {
    display: none;
}

.input-group {
    display: flex;
    margin-top: 10px;
}

.input-group .el-input {
    margin-right: 10px;
}

.el-upload__tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

.image-item {
    cursor: pointer;
    transition: transform 0.2s;
    text-align: center;
}

.image-item:hover {
    transform: scale(1.05);
}

.image-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.image-name {
    margin-top: 8px;
    font-size: 12px;
    color: #606266;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.example-image-upload {
    margin-bottom: 10px;
}

.example-image-preview {
    margin-top: 10px;
    width: 100%;
    height: 150px;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

.example-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
</style>
