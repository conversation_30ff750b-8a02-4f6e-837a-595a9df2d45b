<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    badge: {
      type: [Number, String],
      default: null
    }
  },
  render(h, context) {
    const { icon, title ,badge} = context.props
    /* console.log('MenuItem props:', { icon, title, badge }); // 打印属性 */
    const vnodes = []

    // 创建一个容器来包裹图标和徽章
    const iconContainer = []
    if (icon) {
      if (icon.includes('el-icon-')) {
        iconContainer.push(<i class={icon} style="display: inline-block;"/>)
      } else {
        iconContainer.push(<svg-icon icon-class={icon} style="display: inline-block;"/>)
      }
      
      if (badge !== null && badge !== undefined) {
        iconContainer.push(<el-badge value={badge} class="menu-badge" />)
      }
      
      vnodes.push(<span class="icon-container">{iconContainer}</span>)
    }

    if (title) {
      if (title.length > 5) {
        vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)
      } else {
        vnodes.push(<span slot='title'>{(title)}</span>)
      }
    }
    return vnodes
  }
}
</script>
<style scoped>
.icon-container {
  position: relative;
  display: inline-block;
}

.menu-badge {
  position: absolute;
  top: -8px;
  right: -5px;
  transform: scale(0.8);  /* 整体缩小徽章 */
  font-weight: bolder;
}

.menu-badge :deep(.el-badge__content) {
  background-color: #ff0000;
  border: none;
  font-size: 12px;  /* 设置字体大小 */
  padding: 0 4px;  /* 调整内边距使徽章更紧凑 */
  height: 16px;  /* 调整高度 */
  line-height: 16px;  /* 调整行高使文字垂直居中 */
  min-width: 16px;  /* 确保圆形徽章的最小宽度 */
}
</style>