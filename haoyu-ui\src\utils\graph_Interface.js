// 这个文件主要用来构成我们的图谱接口的参数
// import dataStore from '@/store'  // 直接导入store实例
import request from '@/utils/request'
import store from '@/store'  // 导入store实例


// 创建一个函数来获取参数
export async function getParams(ftype) {
  try {
    // 从store获取数据
    const device_id = store.state.dataStore.device_id;
    const point_id = store.state.dataStore.point_id;
    const time_point = store.state.dataStore.time_point;
    // console.log(`从store获取的数据:id->${deviceId},pointid->${pointId},timepoint->${timePoint}`);
    const select_id = store.state.tree.selectedTreeNode.id
    // console.log(select_id);
    // 只有在有pointId时才发起请求

    /* if (!device_id&&!point_id&&!time_point) {
      return
    } */
    if (!select_id) {
      console.log('没有选中点')
      return
    }

    // 发起请求获取额外数据
    /* const result = await request({
      url: `/measureDefinition/measureDefinition/${select_id}`,
      method: 'get'
    }) */
    // 从store中获取缓存的数据
    const cachedData = store.getters['measureData/getMeasureData'](select_id);

    // const { timeSignalUnitId, upperLimitFrequency,numberOfSpectralLines } = result.data
    // console.log('result->',result.data);
    // const fs = [Number(upperLimitFrequency),Number(numberOfSpectralLines)]

    // 返回构建的参数对象
/*     return {
      "device_id": String(device_id),
      "point_id": String(point_id),
      "time_point": time_point,
      "ftype": ftype,
      "input_type": 0,
      "outer_type": timeSignalUnitId,
      "cf": 2,
      "fth": 0.02,
      "band": [2],
      "win": "hanning",
      "ytype": 0,
      "fs_lines": fs,
      "fband": [],
      "nperseg": "",
      "noverlap": ""
    } */
      if (cachedData) {
        // 如果缓存中有数据，直接使用缓存的值
        const { timeSignalUnitId, upperLimitFrequency, numberOfSpectralLines } = cachedData;
        const fs = [Number(upperLimitFrequency), Number(numberOfSpectralLines)];

        // 返回构建的参数对象
        return {
          device_id: String(device_id),
          point_id: String(point_id),
          time_point: time_point,
          ftype: ftype,
          input_type: 0,
          outer_type: timeSignalUnitId,
          cf: 2,
          fth: 0.02,
          band: [2],
          win: "hanning",
          ytype: 0,
          fs_lines: fs,
          fband: [],
          nperseg: "",
          noverlap: ""
        };
      } else {
        // 如果缓存中没有数据，发起请求获取数据
        const result = await store.dispatch('measureData/fetchMeasureData', select_id);
        const { timeSignalUnitId, upperLimitFrequency, numberOfSpectralLines } = result;
        const fs = [Number(upperLimitFrequency), Number(numberOfSpectralLines)];

        // 返回构建的参数对象
        return {
          device_id: String(device_id),
          point_id: String(point_id),
          time_point: time_point,
          ftype: ftype,
          input_type: 0,
          outer_type: timeSignalUnitId,
          cf: 2,
          fth: 0.02,
          band: [2],
          win: "hanning",
          ytype: 0,
          fs_lines: fs,
          fband: [],
          nperseg: "",
          noverlap: ""
        };
      }
    } catch (error) {
    console.error('构建参数失败:', error)
    throw error
  }

}
