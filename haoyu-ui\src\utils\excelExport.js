import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

export default class ExcelExporter {
  static exportToExcel(data, fileName = 'export.xlsx') {
    // 创建一个工作簿
    const wb = XLSX.utils.book_new();

    // 遍历数据对象，为每个属性创建一个工作表
    Object.entries(data).forEach(([sheetName, sheetData]) => {
      const ws = XLSX.utils.json_to_sheet(sheetData);
      XLSX.utils.book_append_sheet(wb, ws, sheetName);
    });

    // 生成二进制字符串
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

    // 将二进制字符串转换为 Blob
    const blob = new Blob([this.s2ab(wbout)], { type: 'application/octet-stream' });

    // 保存文件
    saveAs(blob, fileName);
  }

  static s2ab(s) {
    const buf = new ArrayBuffer(s.length);
    const view = new Uint8Array(buf);
    for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
  }

  static importFromExcel(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });

          const result = {};
          workbook.SheetNames.forEach(sheetName => {
            const worksheet = workbook.Sheets[sheetName];
            result[sheetName] = XLSX.utils.sheet_to_json(worksheet);
          });

          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = (error) => reject(error);
      reader.readAsArrayBuffer(file);
    });
  }
}
