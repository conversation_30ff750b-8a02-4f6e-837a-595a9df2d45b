<template>
    <div>
      <!-- <h1 class="MainTitle">设备现场照片</h1> -->
      <el-upload
        ref="upload"
        action="#"
        list-type="picture-card"
        :auto-upload="true"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :file-list="fileList">
        <i slot="default" class="el-icon-plus"></i>
        <div slot="file" slot-scope="{ file }">
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
              <i class="el-icon-download"></i>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </el-upload>

      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
      <!-- <div class="block">
        <el-pagination
            layout="prev, pager, next"
            :total="100">
        </el-pagination>
     </div> -->
    </div>
</template>

<script>
import { UploadFile, GetImgObjList, DownloadImgObjList ,DeleteItem } from '@/api/haoyu-system/web3d_api/web_3dapi.js';
import { chunkAndMergeFile } from '../js/ChunkAndMergeFile.js';
// import "../index.js"

export default {
data() {
    return {
    dialogImageUrl: '',
    dialogVisible: false,
    disabled: false,
    fileList: [],
    download_list:[],
    uploadData:[],
    pageSize: 5, // 每页显示的图片数量
    pageNum: 1 // 当前页码
    };
},

created() {
    // 在组件创建时获取图片列表
    this.fetchImageList();
},
watch(){

},
mounted() {

},

methods: {

    fetchImageList() {
        const locationInfo = 'some-location-info'; // Provide the required locationInfo value
        GetImgObjList('png', this.pageSize, this.pageNum, locationInfo).then((response) => {
            this.download_list = response.rows;
            this.download_list.forEach((item) => {
                if (item.fileName.endsWith('.png') || item.fileName.endsWith('.jpg') || item.fileName.endsWith('.jpeg')) {
                    this.DownloadAndLoading(item.id);
                }
            });
        }).catch((error) => {
            this.$message.error('获取图片列表失败');
        });
    },


    DownloadAndLoading(id) {
        // 调用下载 API，获取二进制数据流
        DownloadImgObjList(id).then((response) => {
            // 创建 Blob 对象
            const blob = new Blob([response], { type: 'image/png' }); // 根据实际类型调整 MIME 类型
            const url = URL.createObjectURL(blob); // 创建 Blob URL

            console.log('IMGresponse',response);

            this.fileList.push({
            name: `image_${id}.png`, // 给图片一个名称
            url: url, // 使用 Blob URL 显示图片
            status: 'success',
            uid: id // 使用唯一的 ID 作为 uid
        });

        // 不要立即撤销 URL，在组件销毁时再处理
        }).catch((error) => {
            console.error('下载图片时出错:', error);
            this.$message.error('图片下载失败');
        });
    },


    handleRemove(file) {
        console.log("删除",file);
        this.fileList = this.fileList.filter((f) => f.uid !== file.uid);
        this.fileList = []
        // this.fetchImageList();
    },

    handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
    },

    handleDownload(file) {
        console.log(file);
    },

    beforeUpload(file) {
        console.log(this.fileList.length)
        const isAllowedType = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'].includes(file.type);

        if (!isAllowedType) {
            this.$message.error('只能上传 PNG, JPEG, JPG, GIF 格式的图片');
            return false;
        }

        if (this.fileList.length >= this.pageSize) {
            const excess = this.fileList.length - this.pageSize + 1;
            this.fileList.splice(0, excess);
        }

        if (this.fileList.length < this.pageSize) {
            console.log("upload")
        }

        chunkAndMergeFile(file)

        return false; // Prevent default upload behavior
    },


    beforeDestroy() {
        // 在组件销毁时释放所有的 Blob URL
        this.fileList.forEach(file => {
            if (file.url.startsWith('blob:')) {
            URL.revokeObjectURL(file.url);
            }
        });
    },

    handleSuccess(response, file) {
        this.$message.success('图片上传成功');
    }
}
};
</script>
<style>
@import '../css/FontStyle.css'
</style>
