<template>
  <el-dialog
    :visible.sync="visible"
    :title="isEdit ? '编辑轴承库' : '新建轴承库'"
    :close-on-click-modal="false"
    width="50%"
    @close="handleCancel"
  >
    <div class="bearing-library-form">
      <el-form ref="bearingForm" :model="bearingForm" label-width="100px">
        <!-- 上部分容器 -->
        <div class="upper-container">
          <el-form ref="bearingForm" :model="bearingForm" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="制造厂商">
                  <el-input v-model="bearingForm.manufacturer" size="mini" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="型号">
                  <el-input v-model="bearingForm.model" size="mini" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="描述">
                  <el-input v-model="bearingForm.description" size="mini" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 下部分容器 -->
        <div class="lower-container">
          <el-tabs v-model="activeTab">
            <!-- 内部尺寸标签页 -->
            <el-tab-pane label="内部尺寸" name="innerSize">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="滚柱数目(N)">
                    <el-input
                      v-model="bearingForm.numberOfBalls"
                      size="mini"
                      :disabled="isMainFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="滚珠直径(d)">
                    <el-input
                      v-model="bearingForm.ballDiameter"
                      size="mini"
                      :disabled="isMainFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="节圆直径(D)">
                    <el-input
                      v-model="bearingForm.pitchDiameter"
                      size="mini"
                      :disabled="isMainFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="接触角">
                    <el-input
                      v-model="bearingForm.contactAngle"
                      size="mini"
                      :disabled="isMainFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="BPFO">
                    <el-input
                      v-model="bearingForm.bpfo"
                      size="mini"
                      :disabled="isBpcFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="BPFI">
                    <el-input
                      v-model="bearingForm.bpfi"
                      size="mini"
                      :disabled="isBpcFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="FTF">
                    <el-input
                      v-model="bearingForm.ftf"
                      size="mini"
                      :disabled="isBpcFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="BSF">
                    <el-input
                      v-model="bearingForm.bsf"
                      size="mini"
                      :disabled="isBpcFieldsDisabled"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 外部尺寸标签页 -->
            <el-tab-pane label="外部尺寸" name="outerSize">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="外圆直径">
                    <el-input v-model="bearingForm.outerDiameter" size="mini" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="内圆直径">
                    <el-input v-model="bearingForm.outerDiameter" size="mini" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="宽度">
                    <el-input v-model="bearingForm.width" size="mini" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form>

    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleCalculate">计算</el-button>
      <el-button type="primary" @click="handleSubmit">{{ isEdit ? '保存编辑' : '创建轴承' }}</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addBearing,
  updateBearing,
  deleteBearing
} from '@/api/haoyu-system/SystemAdministration/Configuration.js'
export default {
  name: 'BearingLibraryDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataList: {
      type: Object,
      default() {
        return {
          id: null, // 轴承id
          // 基本信息
          manufacturer: '', // 制造厂商
          model: '', // 型号
          numberOfBalls: '', // 滚动体数目
          description: '', // 描述

          // 尺寸
          innerDiameter: '', // 内圆直径
          outerDiameter: '', // 外圆直径
          pitchDiameter: '', // 节圆直径
          contactAngle: '', // 接触角，真/假
          width: '', // 宽度

          // 故障频率参数
          bpfo: '', // BPFO
          bpfi: '', // BPFI
          ftf: '', // FTF
          bsf: '' // BSF
        }
      }
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      bearingForm: {}, // 初始化表单数据
      activeTab: 'innerSize', // 默认激活的标签页
      isBpcFieldsDisabled: false, // 控制 BPCO, BPCI, FTCI, BSC 的禁用状态
      isMainFieldsDisabled: false // 控制主输入框组的禁用状态
    }
  },
  watch: {
    dataList: {
      immediate: true,
      handler(newVal) {
        this.initializeForm(newVal) // 每次 dataList 变化时调用初始化方法
      }
    },
    isEdit: {
      immediate: true,
      handler() {
        this.initializeForm(this.dataList) // 每次 isEdit 变化时调用初始化方法
      }
    },
    // 监听主输入框组的变化
    'bearingForm.numberOfBalls'(val) {
      this.checkMainFields()
    },
    'bearingForm.ballDiameter'(val) {
      this.checkMainFields()
    },
    'bearingForm.pitchDiameter'(val) {
      this.checkMainFields()
    },
    'bearingForm.contactAngle'(val) {
      this.checkMainFields()
    },

    // 监听 BPCO, BPCI, FTCI, BSC 的变化
    'bearingForm.bpfo'(val) {
      this.checkBpcFields()
    },
    'bearingForm.bpfi'(val) {
      this.checkBpcFields()
    },
    'bearingForm.ftf'(val) {
      this.checkBpcFields()
    },
    'bearingForm.bsf'(val) {
      this.checkBpcFields()
    }
  },
  mounted() {
    // if (this.isEdit) {
    this.initializeForm(this.dataList)
    // }
  },
  methods: {
  // 检查主字段（滚子/滚柱数目等）的状态
    checkMainFields() {
      // console.log('checkMainFields')
      if (
        this.bearingForm.numberOfBalls === '' &&
        this.bearingForm.ballDiameter === '' &&
        this.bearingForm.pitchDiameter === '' &&
        this.bearingForm.contactAngle === ''
      ) {
        // 如果主字段的任何一个不为空，解锁所有输入框
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = false
      } else if (
        this.bearingForm.bpfo !== '' &&
        this.bearingForm.bpfi !== '' &&
        this.bearingForm.ftf !== '' &&
        this.bearingForm.bsf !== '' &&
        this.bearingForm.numberOfBalls !== '' &&
        this.bearingForm.ballDiameter !== '' &&
        this.bearingForm.pitchDiameter !== '' &&
        this.bearingForm.contactAngle !== ''
      ) {
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = true
      } else if (
        this.bearingForm.bpfo === '' &&
        this.bearingForm.bpfi === '' &&
        this.bearingForm.ftf === '' &&
        this.bearingForm.bsf === '' &&
        this.bearingForm.numberOfBalls === '' &&
        this.bearingForm.ballDiameter === '' &&
        this.bearingForm.pitchDiameter === '' &&
        this.bearingForm.contactAngle === ''
      ) {
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = false
      } else {
        // console.log('进入都不是空的情况')
        this.isBpcFieldsDisabled = true
      }
    },

    // 检查 BPCO, BPCI, FTCI, BSC 的状态
    checkBpcFields() {
      // console.log('进入checkBpcFields')
      if (
        this.bearingForm.bpfo === '' &&
        this.bearingForm.bpfi === '' &&
        this.bearingForm.ftf === '' &&
        this.bearingForm.bsf === ''
      ) {
        // 如果 BPCO, BPCI, FTCI, BSC 的任何一个不为空，解锁所有输入框
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = false
      } else if (
        this.bearingForm.bpfo !== '' &&
        this.bearingForm.bpfi !== '' &&
        this.bearingForm.ftf !== '' &&
        this.bearingForm.bsf !== '' &&
        this.bearingForm.numberOfBalls !== '' &&
        this.bearingForm.ballDiameter !== '' &&
        this.bearingForm.pitchDiameter !== '' &&
        this.bearingForm.contactAngle !== ''
      ) {
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = true
      } else if (
        this.bearingForm.bpfo === '' &&
        this.bearingForm.bpfi === '' &&
        this.bearingForm.ftf === '' &&
        this.bearingForm.bsf === '' &&
        this.bearingForm.numberOfBalls === '' &&
        this.bearingForm.ballDiameter === '' &&
        this.bearingForm.pitchDiameter === '' &&
        this.bearingForm.contactAngle === ''
      ) {
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = false
      } else {
        // 如果 BPC 字段都为空，则保持主字段禁用状态
        this.isMainFieldsDisabled = true
      }
    },
    // 监测是不是编辑模式用来更新表单
    initializeForm(data) {
      this.checkMainFields()
      if (this.isEdit) {
        // console.log('编辑模式 - 传入的新 dataList:', data) // 编辑模式下打印传入的数据
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = false
        this.bearingForm = { ...data } // 编辑时使用传入的数据
      } else {
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = false
        // console.log('新建模式 - 重置表单') // 新建模式下打印日志
        this.resetForm() // 新建时使用空表单数据
      }
    },
    // 重新赋值给传入的表单(覆盖)
    resetForm() {
      // 初始化新建表单的数据
      this.bearingForm = {
        id: null,
        manufacturer: '',
        model: '',
        numberOfBalls: '',
        description: '',
        innerDiameter: '',
        outerDiameter: '',
        pitchDiameter: '',
        contactAngle: '',
        ballDiameter: '',
        width: '',
        bpfo: '',
        bpfi: '',
        ftf: '',
        bsf: ''
      }
    },
    handleCancel() {
      this.isMainFieldsDisabled = false
      this.isBpcFieldsDisabled = false
      this.$emit('cancel')
    },
    handleCalculate() {
    // 检查主输入框是否都有值
      if (
        this.bearingForm.numberOfBalls !== '' &&
        this.bearingForm.ballDiameter !== '' &&
        this.bearingForm.pitchDiameter !== '' &&
        this.bearingForm.contactAngle !== ''
      ) {
        // 如果主输入框都有值
        this.isMainFieldsDisabled = false // 禁用主输入框
        this.isBpcFieldsDisabled = true // 解锁 BPCO, BPCI, FTCI, BSC 输入框
      } else {
        // 如果主输入框有空的值，保留输入框可编辑状态
        this.isMainFieldsDisabled = false
        this.isBpcFieldsDisabled = true
      }

      // 继续执行计算逻辑
      this.calculateEigenvalues()
    },
    calculateEigenvalues() {
      console.log(this.bearingForm)
      // 确保表单中的数据都被转换成数字类型
      this.bearingForm.numberOfBalls = Number(this.bearingForm.numberOfBalls)
      this.bearingForm.ballDiameter = Number(this.bearingForm.ballDiameter)
      this.bearingForm.pitchDiameter = Number(this.bearingForm.pitchDiameter)
      this.bearingForm.contactAngle = Number(this.bearingForm.contactAngle)

      // 打印每个字段的值以确保它们是有效的数字
      console.log('numberOfBalls:', this.bearingForm.numberOfBalls)
      console.log('ballDiameter:', this.bearingForm.ballDiameter)
      console.log('pitchDiameter:', this.bearingForm.pitchDiameter)
      console.log('contactAngle:', this.bearingForm.contactAngle)

      // 角度值打印
      // console.log('角度值', Math.cos(Math.PI / 4))

      // 检查是否转换为有效数字（非 NaN）
      if (
        isNaN(this.bearingForm.numberOfBalls) ||
        isNaN(this.bearingForm.ballDiameter) ||
        isNaN(this.bearingForm.pitchDiameter) ||
        isNaN(this.bearingForm.contactAngle)
      ) {
        console.error('Some inputs are invalid. Please check the form values.')
        return
      }

      // 进行计算
      this.bearingForm.bpfo = (this.bearingForm.numberOfBalls / 2) *
                              (1 - (this.bearingForm.ballDiameter / this.bearingForm.pitchDiameter) * Math.cos(this.bearingForm.contactAngle * Math.PI / 180)).toFixed(4)

      this.bearingForm.bpfi = (this.bearingForm.numberOfBalls / 2) *
                              (1 + (this.bearingForm.ballDiameter / this.bearingForm.pitchDiameter) * Math.cos(this.bearingForm.contactAngle * Math.PI / 180)).toFixed(4)

      this.bearingForm.ftf = (1 / 2) *
                            (1 - (this.bearingForm.ballDiameter / this.bearingForm.pitchDiameter) * Math.cos(this.bearingForm.contactAngle * Math.PI / 180)).toFixed(4)

      this.bearingForm.bsf = (1 / 2) *
                            (this.bearingForm.pitchDiameter / this.bearingForm.ballDiameter) *
                            (1 - Math.pow((this.bearingForm.ballDiameter / this.bearingForm.pitchDiameter) * Math.cos(this.bearingForm.contactAngle * Math.PI / 180), 2)).toFixed(4)
    },
    // 提交轴承信息
    handleSubmit() {
      if (!this.isEdit) {
        addBearing(this.bearingForm).then(res => {
          // console.log(res)
          this.$message.success('新增成功轴承')
          this.$emit('confirm', this.bearingForm) // 返回修改后的表单数据
        })
      } else {
        updateBearing(this.bearingForm).then(res => {
          // console.log(res)
          this.$message.success('成功更新轴承信息')
          this.$emit('confirm', this.bearingForm) // 返回修改后的表单数据
        })
      }
      this.handleCancel()
    }
  }
}
</script>

<style scoped>
.bearing-library-form {
  padding: 10px;
}
</style>
