// 基础配置
export const baseOption = {
  backgroundColor: 'transparent',
  series: [{
    type: 'gauge',
    radius: '90%',
    startAngle: 90,
    endAngle: -270,
    pointer: {
      show: false
    },
    progress: {
      show: true,
      overlap: false,
      roundCap: true,
      clip: false,
      itemStyle: {
        color: '#0084ff'
      }
    },
    axisLine: {
      lineStyle: {
        width: 8,
        color: [[1, 'rgba(0,132,255,0.1)']]
      }
    },
    splitLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: false
    },
    detail: {
      offsetCenter: [0, 0],
      formatter: function(value) {
        return value + (this.unit || '');
      },
      color: '#0084ff',
      fontSize: 20,
      fontWeight: 'bold'
    },
    title: {
      fontSize: 14,
      color: '#999',
      offsetCenter: [0, '30%']
    }
  }]
};

// 创建图表配置
export const createChartOptions = () => {
  const option1 = {...baseOption};
  option1.series[0].data = [{
    value: 1990,
    name: '运行时长',
    unit: 'h'
  }];

  const option2 = {...baseOption};
  option2.series[0].data = [{
    value: 0,
    name: '急性反应次数',
    unit: '次'
  }];

  const option3 = {...baseOption};
  option3.series[0].data = [{
    value: 99,
    name: '完整度指数',
    unit: '%'
  }];

  const option4 = {...baseOption};
  option4.series[0].data = [{
    value: 1990,
    name: '本次运行时长',
    unit: 'h'
  }];

  return [option1, option2, option3, option4];
};

// 初始化图表
// charts.js
export function initCharts(echarts) {
  const charts = [];
  const chartConfigs = [
      { title: '累计时长:', value: 1990, unit: 'h' },
      { title: '超出预警次数:', value: 0, unit: '次' },
      { title: '设备XX率:', value: 99, unit: '%' },
      { title: '本次运行时长:', value: 1990, unit: 'h' }
  ];

  for(let i = 1; i <= 4; i++) {
      const chart = echarts.init(document.getElementById(`chart${i}`));
      const config = chartConfigs[i-1];

      const option = {
          title: {
              text: config.title,
              left: 'left',
              textStyle: {
                color: '#ffffff',
                fontSize: 14  // 减小标题字体
              }
          },
          series: [{
              type: 'gauge',
              startAngle: 90,
              endAngle: -270,
              pointer: {
                  show: false
              },
              progress: {
                  show: true,
                  overlap: false,
                  roundCap: true,
                  clip: false,
                  itemStyle: {
                    color: '#1a98ff',
                  }
              },
              axisLine: {
                  lineStyle: {
                      width: 10,
                      color: [[1, 'rgba(255,255,255,0.2)']]
                  }
              },
              splitLine: {
                  show: false
              },
              axisTick: {
                  show: false
              },
              axisLabel: {
                  show: false
              },
              data: [{
                  value: config.value,
                  /* name: config.unit, */
                  title: {
                      offsetCenter: ['0%', '0%']
                  },
                  detail: {
                      offsetCenter: ['0%', '20%']
                  }
              }],
              title: {
                  fontSize: 14
              },
              detail: {
                  width: 40,
                  height: 14,
                  fontSize: 12,
                  color: '#fff',
                  formatter: '{value} ' + config.unit
              },
          }]
      };

      chart.setOption(option);
      charts.push(chart);
  }
  return charts;
}

export function updateChartData(charts, newData) {
  // 更新图表数据的逻辑
  charts.forEach((chart, index) => {
      if (newData[index]) {
          chart.setOption({
              series: [{
                  data: newData[index]
              }]
          });
      }
  });
}

export function disposeCharts(charts) {
  // 销毁图表，避免内存泄漏
  charts.forEach(chart => {
      chart && chart.dispose();
  });
}
export default {
  baseOption,
  createChartOptions,
  initCharts,
  updateChartData,
  disposeCharts
};
