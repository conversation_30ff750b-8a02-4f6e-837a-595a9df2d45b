.bearing-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 1080px;
    height: 700px;
    z-index: 2000 !important;
    display: flex;
    flex-direction: column;
    .dialog-header {
        padding: 16px 24px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;
        user-select: none;
        flex-shrink: 0;
        background: #f5f7fa;
        span {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
        }
        .close-btn {
            border: none;
            background: none;
            font-size: 20px;
            color: #909399;
            cursor: pointer;
            padding: 0;
            &:hover {
                color: #409EFF;
            }
        }
    }
    .bearing-content {
        padding: 24px;
        flex: 1;
        overflow: auto;
        .gear-table-container {
            margin-top: 0;
            .gearbox-card {
                background: #fff;
                border-radius: 4px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                margin-bottom: 20px;
                .gearbox-header {
                    padding: 16px 24px;
                    background: #f5f7fa;
                    border-radius: 4px 4px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: pointer;
                    transition: all 0.3s;
                    &:hover {
                        background: #e6e8eb;
                    }
                    .header-left {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        .gearbox-icon {
                            font-size: 20px;
                            color: #409EFF;
                        }
                        .gearbox-title {
                            font-size: 15px;
                            font-weight: 500;
                            color: #303133;
                        }
                    }
                    .header-right {
                        display: flex;
                        align-items: center;
                        gap: 16px;
                        .speed-input {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            .speed-label {
                                color: #606266;
                                font-size: 13px;
                                white-space: nowrap;
                            }
                            .el-input {
                                 ::v-deep .el-input__inner {
                                    height: 24px;
                                    line-height: 24px;
                                    font-size: 13px;
                                    text-align: center;
                                }
                                 ::v-deep .el-input-group__append {
                                    padding: 0 8px;
                                    background-color: #f5f7fa;
                                    color: #606266;
                                    font-size: 12px;
                                }
                            }
                        }
                    }
                }
                .gearbox-detail {
                    padding: 20px 24px;
                    .gearbox-overview {
                        display: flex;
                        gap: 32px;
                        margin-bottom: 20px;
                        padding: 16px 24px;
                        background: #f5f7fa;
                        border-radius: 4px;
                        flex-wrap: wrap;
                        .overview-item {
                            display: flex;
                            align-items: center;
                            gap: 10px;
                            min-width: 200px;
                            .label {
                                color: #606266;
                                font-size: 14px;
                                white-space: nowrap;
                            }
                            .value {
                                color: #303133;
                                font-weight: 500;
                                font-size: 15px;
                            }
                            .el-input {
                                width: 120px !important;
                                 ::v-deep .el-input__inner {
                                    height: 28px;
                                    line-height: 28px;
                                    text-align: center;
                                }
                                 ::v-deep .el-input-group__append {
                                    padding: 0 8px;
                                    background-color: #f5f7fa;
                                    color: #606266;
                                    font-size: 12px;
                                }
                            }
                        }
                    }
                    .gear-detail-table {
                        margin-bottom: 20px;
                         ::v-deep .el-table {
                            .el-table__header-wrapper {
                                th {
                                    background: #f5f7fa;
                                    color: #606266;
                                    font-weight: 500;
                                    padding: 12px 0;
                                    font-size: 14px;
                                }
                            }
                            .el-table__body-wrapper {
                                td {
                                    padding: 12px 0;
                                }
                            }
                        }
                    }
                    .selected-frequencies {
                        padding: 16px 24px;
                        background: #f5f7fa;
                        border-radius: 4px;
                        .frequency-title {
                            font-size: 14px;
                            margin-bottom: 12px;
                        }
                        .frequency-tag {
                            margin-right: 12px;
                            margin-bottom: 12px;
                            font-size: 13px;
                            padding: 6px 12px;
                        }
                    }
                }
            }
        }
    }
    .dialog-footer {
        padding: 16px 24px;
        border-top: 1px solid #e4e7ed;
        text-align: right;
        flex-shrink: 0;
        background: #f5f7fa;
        .el-button {
            padding: 10px 20px;
            font-size: 14px;
            &+.el-button {
                margin-left: 12px;
            }
        }
    }
    .speed-calc-content {
        padding: 20px 0;
        .el-form {
            .el-form-item {
                margin-bottom: 20px;
                .el-radio-group {
                    .el-radio {
                        margin-right: 20px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
                .el-select {
                    width: 100%;
                }
            }
        }
    }
}

.gear-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    .gear-type {
        font-size: 13px;
        color: #606266;
    }
}

.frequency-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px 8px;
    .frequency-row {
        display: flex;
        align-items: center;
        gap: 8px;
        .frequency-label {
            color: #606266;
            font-size: 13px;
            min-width: 60px;
        }
        .frequency-value {
            color: #409EFF;
            font-weight: 500;
        }
        .frequency-unit {
            color: #909399;
            font-size: 12px;
        }
    }
}

.multiple-selection {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px;
    .multiple-row {
        display: flex;
        align-items: center;
        gap: 8px;
        .multiple-label {
            color: #606266;
            font-size: 13px;
            min-width: 60px;
        }
    }
}

.no-gear {
    color: #909399;
    font-size: 14px;
}

 ::v-deep .el-select {
    width: 120px;
}

.operation-tips {
    line-height: 1.8;
    font-size: 14px;
    color: #606266;
}

.operation-tips p {
    margin: 5px 0;
}