<template>
  <div>
    <input 
      v-model="websiteUrl" 
      placeholder="请输入网站 URL，例如：https://www.example.com" 
      @keyup.enter="openWeb"
      style="margin-right: 10px; padding: 5px; width: 300px;"
    />
    <button @click="openWeb">打开网站</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      websiteUrl: '', // 用于存储用户输入的 URL
    };
  },
  methods: {
    openWeb() {
      let url = this.websiteUrl.trim();

      // 如果用户没有输入协议，则默认添加 'https://'
      if (url && !/^https?:\/\//i.test(url)) {
        url = 'https://' + url;
      }

      // 简单的验证 URL 是否有效
      try {
        new URL(url);
        window.open(url, '_blank'); // 在新窗口中打开
      } catch (e) {
        alert('请输入有效的 URL 地址！');
      }
    },
  },
};
</script>

<style>
/* 简单的样式调整 */
/* div {
  display: flex;
  align-items: center;
  margin: 20px;
} */

input {
  font-size: 14px;
}

button {
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
}
</style>
