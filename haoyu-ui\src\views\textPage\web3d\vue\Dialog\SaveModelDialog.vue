<template>
  <el-dialog
    title="保存为示例模型"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :before-close="handleClose">
    <div class="save-model-form">
      <el-form :model="form" ref="modelForm" :rules="rules" label-width="100px">
        <el-form-item label="模型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模型名称"></el-input>
          <div class="field-tip">此名称将作为模型的主要标识</div>
        </el-form-item>
        
        <el-form-item label="模型描述" prop="note">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入模型描述"
            v-model="form.note">
          </el-input>
          <div class="field-tip">描述模型的用途、特点等信息</div>
        </el-form-item>
        
        <el-form-item label="模型图片" prop="image">
          <el-upload
            class="model-image-uploader"
            action="#"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="handleImageChange">
            <img v-if="imageUrl" :src="imageUrl" class="model-image">
            <i v-else class="el-icon-plus model-image-uploader-icon"></i>
          </el-upload>
          <div class="field-tip">请上传一张模型预览图片 (必填)</div>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { uploadImageToURL } from '@/api/haoyu-system/web3d_api/web_3dapi.js';

export default {
  name: 'SaveModelDialog',
  
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      form: {
        name: '',
        note: '',
        image: null
      },
      rules: {
        name: [
          { required: true, message: '请输入模型名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        image: [
          { required: true, message: '请上传模型图片', trigger: 'change' }
        ]
      },
      imageUrl: '',
      loading: false
    };
  },
  
  methods: {
    // 处理图片上传
    handleImageChange(file) {
      // 校验文件类型和大小
      const isImage = file.raw.type.indexOf('image/') !== -1;
      const isLt2M = file.size / 1024 / 1024 < 2;
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return;
      }
      
    //   if (!isLt2M) {
    //     this.$message.error('图片大小不能超过 2MB!');
    //     return;
    //   }
      
      // 将文件存储到表单中并显示预览
      this.form.image = file.raw;
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imageUrl = e.target.result;
      };
      reader.readAsDataURL(file.raw);
    },
    
    // 关闭对话框
    handleClose() {
      this.resetForm();
      this.$emit('update:dialogVisible', false);
    },
    
    // 确认保存
    async handleConfirm() {
      try {
        // 表单验证
        await this.$refs.modelForm.validate();
        
        this.loading = true;
        
        let imageUrl = '';
        
        // 上传图片
        if (this.form.image) {
          const response = await uploadImageToURL(this.form.image);
          if (response.code === 200) {
            imageUrl = response.url;
          } else {
            throw new Error('图片上传失败');
          }
        }
        
        // 提交信息
        this.$emit('confirm', {
          name: this.form.name,
          note: this.form.note,
          backgroundImageUrl: imageUrl
        });
        
        // 重置表单
        this.resetForm();
      } catch (error) {
        if (error.message) {
          this.$message.error(error.message);
        }
        console.error('保存模型信息出错:', error);
      } finally {
        this.loading = false;
      }
    },
    
    // 重置表单
    resetForm() {
      if (this.$refs.modelForm) {
        this.$refs.modelForm.resetFields();
      }
      this.form = {
        name: '',
        note: '',
        image: null
      };
      this.imageUrl = '';
    }
  }
};
</script>

<style scoped>
.save-model-form {
  padding: 0 20px;
}

.model-image-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.model-image-uploader .el-upload:hover {
  border-color: #409EFF;
}

.model-image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 150px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

.model-image {
  width: 150px;
  height: 150px;
  display: block;
  object-fit: cover;
}

.field-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style> 