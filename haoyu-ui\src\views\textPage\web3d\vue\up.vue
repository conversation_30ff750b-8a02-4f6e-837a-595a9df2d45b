<template>
    <div>
      <el-upload
        action="#"
        list-type="picture-card"
        :auto-upload="false"
        :on-remove="handleRemove"
        :on-change="handleUploadChange">
        <i slot="default" class="el-icon-plus"></i>

        <div slot="file" slot-scope="{ file }">
          <img
            class="el-upload-list__item-thumbnail"
            :src="file.url"
            alt="图片预览"
          >
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="handlePictureCardPreview(file)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span
              v-if="!disabled"
              class="el-upload-list__item-download"
              @click="handleDownload(file)">
              <i class="el-icon-download"></i>
            </span>
            <span
              v-if="!disabled"
              class="el-upload-list__item-delete"
              @click="handleRemove(file)">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </el-upload>

      <!-- 图片展示区，显示下载的图片 -->
      <div v-if="dialogImageUrls.length > 0">
        <img v-for="(url, index) in dialogImageUrls" :key="index" :src="url" alt="下载的图片" style="width: 100%; margin-bottom: 10px;">
      </div>
      <div v-else>
        <p>没有下载的图片</p>
      </div>
    </div>
  </template>

  <script>
  import { Upload } from 'element-ui';
  import { UploadFile, GetImgObjList, DownloadImgObjList ,DeleteItem } from '@/api/haoyu-system/web3d_api/web_3dapi.js';

  export default {
    data() {
      return {
        dialogImageUrls: [], // 存储下载的图片 URL
        uploadData: [], // 存储上传的文件信息
      };
    },

    mounted() {
      this.get_imglist(); // 初始化时获取图片列表
    },

    components: {
      'el-upload': Upload,
    },

    methods: {
      // 获取图片列表
      get_imglist() {
        get_img_obj_list().then(response => {
          console.log('获取图片列表成功', response.rows);
          this.uploadData = response.rows; // 假设返回的数据是一个包含文件信息的数组
          this.downloadAndShowImages(); // 在获取图片列表后调用下载并展示图片
        }).catch(error => {
          console.error('获取图片列表失败', error);
        });
      },

      // 处理上传的图片
      handleUploadChange(file, fileList) {
        const formData = new FormData();
        formData.append('file', file.raw);

        upload_file(formData).then(response => {
          console.log('上传成功', response);
          file.url = response.data.url; // 假设返回的数据中包含文件 URL
        }).catch(error => {
          console.error('上传失败', error);
        });

        this.downloadAndShowImages()
      },

      // 处理删除文件
      handleRemove(file) {
        console.log('删除文件:', file);
        // 根据业务需求，你可以在此处理删除请求
      },

      downloadAndShowImages() {
    this.uploadData.forEach(item => {
      const fileName = item.fileName.toLowerCase();
      if (fileName.endsWith('.png') || fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
        console.log('需要下载的文件:', fileName);

        // 下载图片数据，确保 responseType 为 'blob'
        download_img_obj_list(item.id, { responseType: 'blob' }).then(response => {

          const blob = new Blob([response], { type: response.type || 'image/png' });

          // 生成图片 URL
          const imageUrl = URL.createObjectURL(blob);

          // 将生成的图片 URL 加入到 dialogImageUrls 数组
          this.dialogImageUrls.push(imageUrl);

          // 调试信息：生成的图片 URL
          console.log('生成的图片 URL:', imageUrl);
          console.log('当前的 dialogImageUrls:', this.dialogImageUrls);

        }).catch(error => {
          console.error('图片下载失败:', error);
        });
      }
    });
  }




    }
  };
  </script>
