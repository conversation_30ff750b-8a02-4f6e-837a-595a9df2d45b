<!-- 指标选择器组件 -->
<template>
  <div class="indicator-selector">
    <div class="indicator-container">
      <div class="nav-tabs">
        <el-tabs v-model="currentType" @tab-click="handleTypeChange">
          <el-tab-pane
            v-for="(indicators, type) in indicatorConfig"
            :key="type"
            :label="indicatorConfig[type].label"
            :name="type">
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="indicator-list">
        <el-checkbox-group v-model="selectedIndicators" @change="handleChange">
          <el-checkbox
            v-for="item in currentIndicators"
            :key="item.value"
            :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <div class="footer">
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'IndicatorSelector',

  props: {
    initialSelected: {
      type: Array,
      default: () => []
    },
    measurePointIds: {
      type: String,
      required: true
    },
    timeRange: {
      type: Object,
      required: true,
      validator: prop => {
        return prop.start && prop.end
      }
    }
  },

  data() {
    return {
      currentType: 'time',
      selectedIndicators: [],
      indicatorConfig: {
        time: {
          label: '时域指标',
          originType: 'wave',
          options: [
            { value: 'accPeak', label: '加速度峰值' },
            { value: 'accPpeak', label: '加速度峰峰值' },
            { value: 'kurt', label: '峭度指标' },
            { value: 'accrms', label: '加速度有效值' },
            { value: 'enValue', label: '包络' },
            { value: 'velrms', label: '速度有效值'},
            { value: 'disrms', label: '位移有效值'},
            { value: 'disppk', label: '位移峰峰值'},

          ]
        },
        sample: {
          label: '采样值指标',
          originType: 'all',
          options: [
            { value: 'temperature', label: '温度'},
            { value: 'accPeak', label: '加速度峰值' },
            { value: 'accPpeak', label: '加速度峰峰值' },
            { value: 'kurt', label: '峭度指标' },
            { value: 'accrms', label: '加速度有效值' },
            { value: 'enValue', label: '包络' },
            { value: 'velrms', label: '速度有效值'},
            { value: 'disrms', label: '位移有效值'},
            { value: 'disppk', label: '位移峰峰值'},
          ]
        },
/*         feature: {
          label: '特征值指标',
          originType: '',
          options: [
            { value: 'freq_main', label: '主频' },
            { value: 'freq_amp', label: '主频幅值' },
            { value: 'freq_energy', label: '频段能量' },
            { value: 'spectrum', label: '频谱' }
          ]
        } */
      }
    }
  },

  computed: {
    currentIndicators() {
      return this.indicatorConfig[this.currentType]?.options || []
    }
  },

  methods: {
    // 处理指标类型变更
    handleTypeChange() {
      // 切换类型时保持已选指标
      this.selectedIndicators = this.selectedIndicators.filter(key =>
        this.indicatorMap[this.currentType].includes(key)
      )
    },

    // 处理指标选择变更
    handleChange(value) {
      this.selectedIndicators = value
      this.$emit('change', value)
    },

    // 处理确认按钮点击
    async handleConfirm() {
      // 格式化时间
      const formatDate = (date) => {
        try {
          // 确保输入是一个Date对象
          const dateObj = date instanceof Date ? date : new Date(date);

          // 检查是否是有效的日期对象
          if (isNaN(dateObj.getTime())) {
            console.error('无效的日期:', date);
            return ''; // 返回空字符串作为默认值
          }

          const pad = (num) => String(num).padStart(2, '0');
          return `${dateObj.getFullYear()}-${pad(dateObj.getMonth() + 1)}-${pad(dateObj.getDate())} ${pad(dateObj.getHours())}:${pad(dateObj.getMinutes())}:${pad(dateObj.getSeconds())}`;
        } catch (error) {
          console.error('日期格式化错误:', error);
          return ''; // 返回空字符串作为默认值
        }
      };

      // 确保时间范围有效
      if (!this.timeRange || !this.timeRange.start || !this.timeRange.end) {
        this.$message.error('时间范围无效，请重新选择');
        return;
      }

      // 构建查询对象
      const queryObj = {
        ids: this.measurePointIds,
        time: `${formatDate(this.timeRange.start)},${formatDate(this.timeRange.end)}`,
        type: this.selectedIndicators.join(','),
        originType: this.indicatorConfig[this.currentType].originType
      };

      // 验证查询参数是否有效
      if (!queryObj.time.includes(',') || queryObj.time.startsWith(',') || queryObj.time.endsWith(',')) {
        this.$message.error('时间格式无效，请重新选择时间范围');
        return;
      }

      console.log('Query 参数:', queryObj);
      try {
        const response = await request({
          url: '/data/eigenvalues/tender/list/more',
          method: 'get',
          params: queryObj
        });
        console.log('获取到的数据:', response);
        this.$emit('change', response);
        this.$message.success('获取数据成功');
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error(' 请选择一台设备 ');
      }
    }
  },

  created() {
    // 直接使用传入的初始选中值
    if (Array.isArray(this.initialSelected)) {
      this.selectedIndicators = this.initialSelected
    }
  }
}
</script>
<style lang="scss" scoped>
.indicator-selector {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

  .indicator-container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .nav-tabs {
      padding: 0 15px;
      ::v-deep .el-tabs__header {
        margin-bottom: 15px;
      }

      ::v-deep .el-tabs__nav {
        width: 100%;
        display: flex;
        justify-content: space-around;
      }

      ::v-deep .el-tabs__item {
        flex: 1;
        text-align: center;
        color: #606266;

        &.is-active {
          color: #409EFF;
        }
      }
    }

    .indicator-list {
      padding: 10px 0;
      background-color: #f5f7fa;
      border-radius: 4px;
      height: calc(100% - 120px);
      overflow-y: auto;

      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 0 15px;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      ::v-deep .el-checkbox {
        margin: 0;
        .el-checkbox__input.is-checked {
          .el-checkbox__inner {
            background-color: #409EFF;
            border-color: #409EFF;
          }
        }
        .el-checkbox__label {
          color: #606266;
        }
      }
    }

    .footer {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 0;
      background-color: #fff;
      border-top: 1px solid #ebeef5;

      .el-button {
        width: 120px;
      }
    }
  }
}
</style>

