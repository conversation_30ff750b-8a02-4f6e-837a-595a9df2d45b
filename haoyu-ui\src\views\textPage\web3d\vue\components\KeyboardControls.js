import * as THREE from 'three';

export default class KeyboardControls {
    constructor(options = {}) {
        this.keyStates = {};
        this.keyFlags = 0; // 使用位掩码存储按键状态，比对象查找更快
        this.KEY_LEFT = 1; // 0001
        this.KEY_RIGHT = 2; // 0010
        this.KEY_UP = 4; // 0100
        this.KEY_DOWN = 8; // 1000
        this.KEY_Q = 16; // 10000
        this.KEY_E = 32; // 100000
        this.KEY_R = 64; // 1000000
        this.KEY_F = 128; // 10000000
        this.KEY_T = 256; // 100000000
        this.KEY_G = 512; // 1000000000
        this.KEY_Y = 1024; // 10000000000
        this.KEY_H = 2048; // 100000000000

        this.selectedModelIndices = [];
        this.models = [];
        this.moveSpeed = options.moveSpeed || 0.1;
        this.rotateSpeed = options.rotateSpeed || 0.5;
        this.inputStep = options.inputStep || 0.5;

        // 绑定方法到实例
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        this.updateModelMovement = this.updateModelMovement.bind(this);

        // 添加事件监听
        window.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keyup', this.handleKeyUp);
    }

    setModels(models) {
        this.models = models;
    }

    setSelectedIndices(indices) {
        this.selectedModelIndices = indices;
    }

    setInputStep(step) {
        this.inputStep = step;
    }

    handleKeyDown(event) {
        // 如果当前焦点在输入框中或没有选中的模型，不处理键盘事件
        if (event.target.tagName === 'INPUT' || this.selectedModelIndices.length === 0) return;

        event.preventDefault();
        this.keyStates[event.key] = true;

        // 更新位掩码
        switch (event.key) {
            case 'ArrowLeft':
            case 'a':
                this.keyFlags |= this.KEY_LEFT;
                break;
            case 'ArrowRight':
            case 'd':
                this.keyFlags |= this.KEY_RIGHT;
                break;
            case 'ArrowUp':
            case 'w':
                this.keyFlags |= this.KEY_UP;
                break;
            case 'ArrowDown':
            case 's':
                this.keyFlags |= this.KEY_DOWN;
                break;
            case 'q':
                this.keyFlags |= this.KEY_Q;
                break;
            case 'e':
                this.keyFlags |= this.KEY_E;
                break;
            case 'r':
                this.keyFlags |= this.KEY_R;
                break;
            case 'f':
                this.keyFlags |= this.KEY_F;
                break;
            case 't':
                this.keyFlags |= this.KEY_T;
                break;
            case 'g':
                this.keyFlags |= this.KEY_G;
                break;
            case 'y':
                this.keyFlags |= this.KEY_Y;
                break;
            case 'h':
                this.keyFlags |= this.KEY_H;
                break;
        }
    }

    handleKeyUp(event) {
        // 如果当前焦点在输入框中或没有选中的模型，不处理键盘事件
        if (event.target.tagName === 'INPUT' || this.selectedModelIndices.length === 0) return;

        this.keyStates[event.key] = false;

        // 更新位掩码
        switch (event.key) {
            case 'ArrowLeft':
            case 'a':
                this.keyFlags &= ~this.KEY_LEFT;
                break;
            case 'ArrowRight':
            case 'd':
                this.keyFlags &= ~this.KEY_RIGHT;
                break;
            case 'ArrowUp':
            case 'w':
                this.keyFlags &= ~this.KEY_UP;
                break;
            case 'ArrowDown':
            case 's':
                this.keyFlags &= ~this.KEY_DOWN;
                break;
            case 'q':
                this.keyFlags &= ~this.KEY_Q;
                break;
            case 'e':
                this.keyFlags &= ~this.KEY_E;
                break;
            case 'r':
                this.keyFlags &= ~this.KEY_R;
                break;
            case 'f':
                this.keyFlags &= ~this.KEY_F;
                break;
            case 't':
                this.keyFlags &= ~this.KEY_T;
                break;
            case 'g':
                this.keyFlags &= ~this.KEY_G;
                break;
            case 'y':
                this.keyFlags &= ~this.KEY_Y;
                break;
            case 'h':
                this.keyFlags &= ~this.KEY_H;
                break;
        }
    }

    updateModelMovement() {
        if (this.selectedModelIndices.length === 0 || this.keyFlags === 0) return false;

        // 预计算所有需要的值
        const moveStep = this.moveSpeed * (this.inputStep || 0.01);
        const rotXPos = this.keyFlags & this.KEY_R ? THREE.MathUtils.degToRad(this.rotateSpeed) : 0;
        const rotXNeg = this.keyFlags & this.KEY_F ? -THREE.MathUtils.degToRad(this.rotateSpeed) : 0;
        const rotYPos = this.keyFlags & this.KEY_T ? THREE.MathUtils.degToRad(this.rotateSpeed) : 0;
        const rotYNeg = this.keyFlags & this.KEY_G ? -THREE.MathUtils.degToRad(this.rotateSpeed) : 0;
        const rotZPos = this.keyFlags & this.KEY_Y ? THREE.MathUtils.degToRad(this.rotateSpeed) : 0;
        const rotZNeg = this.keyFlags & this.KEY_H ? -THREE.MathUtils.degToRad(this.rotateSpeed) : 0;

        // 计算移动量
        const moveX = ((this.keyFlags & this.KEY_RIGHT) ? moveStep : 0) -
            ((this.keyFlags & this.KEY_LEFT) ? moveStep : 0);
        const moveY = ((this.keyFlags & this.KEY_UP) ? moveStep : 0) -
            ((this.keyFlags & this.KEY_DOWN) ? moveStep : 0);
        const moveZ = ((this.keyFlags & this.KEY_Q) ? moveStep : 0) -
            ((this.keyFlags & this.KEY_E) ? moveStep : 0);

        // 计算旋转量
        const rotX = rotXPos + rotXNeg;
        const rotY = rotYPos + rotYNeg;
        const rotZ = rotZPos + rotZNeg;

        // 如果没有变化，快速返回
        if (moveX === 0 && moveY === 0 && moveZ === 0 &&
            rotX === 0 && rotY === 0 && rotZ === 0) {
            return false;
        }

        // 只进行一次循环，应用所有变更
        let hasMovement = false;
        for (let i = 0; i < this.selectedModelIndices.length; i++) {
            const index = this.selectedModelIndices[i];
            const model = this.models[index];
            if (!model) continue;

            // 应用位置变化（如果有）
            if (moveX !== 0 || moveY !== 0 || moveZ !== 0) {
                model.position.x += moveX;
                model.position.y += moveY;
                model.position.z += moveZ;
                hasMovement = true;
            }

            // 应用旋转变化（如果有）
            if (rotX !== 0 || rotY !== 0 || rotZ !== 0) {
                model.rotation.x += rotX;
                model.rotation.y += rotY;
                model.rotation.z += rotZ;
                hasMovement = true;
            }
        }

        return hasMovement;
    }

    moveModel(direction, step) {
        if (this.selectedModelIndices.length === 0) return;

        const moveStep = step || this.inputStep;
        let dx = 0,
            dy = 0,
            dz = 0;

        // 根据方向确定移动量
        switch (direction) {
            case 'left':
                dx = -moveStep;
                break;
            case 'right':
                dx = moveStep;
                break;
            case 'up':
                dy = moveStep;
                break;
            case 'down':
                dy = -moveStep;
                break;
            case 'forward':
                dz = moveStep;
                break;
            case 'backward':
                dz = -moveStep;
                break;
            default:
                return; // 无效方向，直接返回
        }

        // 一次性应用变化到所有选中模型
        for (let i = 0; i < this.selectedModelIndices.length; i++) {
            const model = this.models[this.selectedModelIndices[i]];
            if (model) {
                model.position.x += dx;
                model.position.y += dy;
                model.position.z += dz;
            }
        }
    }

    cleanup() {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('keyup', this.handleKeyUp);
        this.keyStates = {};
        this.keyFlags = 0;
        this.selectedModelIndices = [];
        this.models = [];
    }
}