## 开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/Ruoyi-Vue

# 进入项目目录
cd Ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 安装快捷键依赖
npm install hotkeys-js

# 安装分割页面依赖
npm install vue-splitpane

# 安装ant design依赖（适配vue2）
npm i --save ant-design-vue@1.7.2

# 如果Node.js版本太高或者不匹配可以先执行以下命令再启动服务
$env:NODE_OPTIONS="--openssl-legacy-provider"

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```