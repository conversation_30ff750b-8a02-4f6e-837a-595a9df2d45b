<template>
  <div class="planetary-axis-container" :class="{ 'selected': selectedNode }">
    <!-- 标题和提交按钮区域 -->
    <div class="header-section">
      <div class="title">
        <svg-icon icon-class="planetaryShaftSys" class="title-icon"/>
        <span>行星轴系配置</span>
        <el-tag v-if="selectedNode" type="success" size="mini" class="selected-tag">
          当前选中: {{ selectedNode.deviceName }}
        </el-tag>
      </div>
      <el-button type="primary" size="mini" @click="handleSubmit" class="submit-btn">
        {{ editModel ? '更新信息' : '提交' }}
      </el-button>
    </div>

    <!-- 输入类型提示区域 -->
    <div class="input-type-section" v-if="formData.planetaryType">
      <el-alert
        :title="getInputTypeTitle"
        type="success"
        :description="getInputTypeDescription"
        show-icon
        :closable="false"
      >
      </el-alert>
    </div>

    <!-- 主要内容区域 -->
    <el-form ref="planetaryGearForm" :model="form" :rules="rules" label-width="120px" class="main-form">
      <!-- 齿轮箱信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <i class="el-icon-setting"></i>
          <span>基本信息</span>
        </div>
        <div class="card-content">
          <!-- 齿轮箱名称 -->
          <el-form-item label="齿轮箱名称" prop="gearbox">
            <el-input
              v-model="form.gearbox"
              placeholder="请输入齿轮箱名称"
              size="mini"
              class="custom-input"
            />
          </el-form-item>

          <!-- 转速输入部分 -->
          <el-form-item :label="formLabel.speed">
            <el-input-number
              v-model="form.speed"
              :min="0"
              :max="100000"
              :step="100"
              controls-position="right"
              size="mini"
              class="custom-input-number"
            />
            <span class="unit">rpm</span>
          </el-form-item>
        </div>
      </div>

      <!-- 齿轮参数卡片 -->
      <div class="info-card">
        <div class="card-header">
          <i class="el-icon-data-analysis"></i>
          <span>齿轮参数</span>
        </div>
        <div class="card-content">
          <!-- 行星轮个数 -->
          <el-form-item :label="formLabel.planetaryGearCount" prop="planetaryGearCount" class="gear-count">
            <el-input-number
              v-model="form.planetaryGearCount"
              :min="0"
              :max="99"
              :step="1"
              controls-position="right"
              size="mini"
              class="custom-input-number"
            >
              <template slot="prepend">
                <i class="el-icon-star-on"></i>
              </template>
            </el-input-number>
            <el-tooltip content="行星轮数量通常在3-8个之间" placement="right">
              <i class="el-icon-question" style="margin-left: 8px; color: #909399;"></i>
            </el-tooltip>
          </el-form-item>

          <!-- 齿轮参数网格 -->
          <div class="gear-params-grid">
            <!-- 行星轮齿数 -->
            <el-form-item :label="formLabel.planetaryGearTeeth" prop="planetaryGearTeeth">
              <el-input
                v-model="form.planetaryGearTeeth"
                placeholder="请输入行星轮齿数"
                size="mini"
                class="custom-input"
              >
                <template slot="prepend">
                  <i class="el-icon-refresh"></i>
                </template>
              </el-input>
            </el-form-item>

            <!-- 内齿圈齿数 -->
            <el-form-item :label="formLabel.ringGearTeeth" prop="ringGearTeeth">
              <el-input
                v-model="form.ringGearTeeth"
                placeholder="请输入内齿圈齿数"
                size="mini"
                class="custom-input"
              >
                <template slot="prepend">
                  <i class="el-icon-circle-plus"></i>
                </template>
              </el-input>
            </el-form-item>

            <!-- 太阳轮齿数 -->
            <el-form-item :label="formLabel.sunGearTeeth" prop="sunGearTeeth">
              <el-input
                v-model="form.sunGearTeeth"
                placeholder="请输入太阳轮齿数"
                size="mini"
                class="custom-input"
              >
                <template slot="prepend">
                  <i class="el-icon-sunny"></i>
                </template>
              </el-input>
            </el-form-item>
          </div>
        </div>
      </div>

      <!-- 计算结果卡片 -->

    </el-form>
  </div>
</template>

<script>
import {
  batchAddGroupComponent,
  batchUpdateGroupComponent
} from '@/api/haoyu-system/SystemAdministration/Configuration.js'

export default {
  props: {
    tableDataTree: {
      type: [Object, Array], // 接受 Object 或 Array 类型
      required: true
    },
    formData: {
      type: Object,
      default: null
    },
    deviceId: {
      type: Number,
      default: null
    },
    selectedNode: {
      type: Object,
      default: null
    },
    editModel: {
      type: Boolean,
      default: false
    },
    parentNode: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      formLabel: {
        gearbox: '齿轮箱名称',
        planetaryGearCount: '行星轮数量',
        planetaryGearTeeth: '行星轮齿数',
        ringGearTeeth: '内齿圈齿数',
        sunGearTeeth: '太阳轮齿数',
        speed: '输入轴转速'
      },
      form: {
        gearbox: '',
        planetaryGearCount: 3,
        planetaryGearTeeth: 20,
        ringGearTeeth: 60,
        sunGearTeeth: 20,
        speed: 0
      },
      calculatedResults: {
        planetaryGearFreq: null,
        planetaryMeshFreq: null,
        sunGearFreq: null,
        carrierFreq: null,
        ringPassFreq: null,
        meshFreq: null
      },
      rules: {
        speed: [{ required: true, message: '请输入转速', trigger: 'blur' }],
        gearbox: [{ required: true, message: '请输入齿轮箱名称', trigger: 'blur' }],
        planetaryCarrier: [{ required: true, message: '请输入行星轮个数', trigger: 'blur' }],
        planetaryGearCount: [{ required: true, message: '请输入行星轮个数', trigger: 'blur' }],
        planetaryGearTeeth: [{ required: true, message: '请输入一级行星轮齿数', trigger: 'blur' }],
        ringGearTeeth: [{ required: true, message: '请输入一级内齿圈齿数', trigger: 'blur' }],
        sunGearTeeth: [{ required: true, message: '请输入一级太阳轮齿数', trigger: 'blur' }]
      }
    }
  },
  watch: {
    formData: {
      immediate: true,
      handler(newData) {
        if (!newData) return;
        
        if (this.editModel && newData.table && newData.table.tableData) {
          // 编辑模式下的处理
          const tableData = newData.table.tableData;
          this.form = { 
            ...this.form, 
            ...tableData
          };
        } else if (newData.tableData) {
          // 新建模式下的处理
          const tableData = newData.tableData;
          this.form = { 
            ...this.form, 
            ...tableData
          };
        }
      }
    }
  },
  created() {
    console.log(this.formData);
    if (this.editModel && this.formData.table && this.formData.table.tableData) {
      const tableData = this.formData.table.tableData;
      this.form = { 
        ...this.form, 
        ...tableData
      };
    } else if (this.formData && this.formData.tableData) {
      const tableData = this.formData.tableData;
      this.form = { 
        ...this.form, 
        ...tableData
      };
    }
    console.log(this.form);
  },
  methods: {
    handleSubmit() {
      this.$refs.planetaryGearForm.validate((valid) => {
        if (valid) {
          if (this.editModel) {
            const data = this.formData
            data.deviceName = this.form.gearbox
            data.title = this.form.gearbox
            // 合并更新表单数据，保留其他字段并确保输入轴转速被保存
            data.table.tableData = {
              ...data.table.tableData,
              ...this.form,
              speed: Number(this.form.speed),
              inputShaftSpeed: Number(this.form.speed)
            }
            // 获取要遍历的子节点数组，优先使用 data.children，无效则回退到 selectedNode.children
            const updateChildren = Array.isArray(data.children)
              ? data.children
              : Array.isArray(this.selectedNode && this.selectedNode.children)
                ? this.selectedNode.children
                : []
            // 遍历子节点，更新子节点及其子节点的deviceName和title
            updateChildren.forEach((child) => {
              if (child.deviceName.includes('一级行星架')) {
                // 更新 child.children 中的所有 deviceName 和 title
                child.children.forEach((subChild) => {
                  subChild.deviceName = this.formLabel.planetaryGearTeeth + this.form.planetaryGearTeeth + '齿'
                  subChild.title = this.formLabel.ringGearTeeth + this.form.planetaryGearTeeth + '齿'
                })
              }
              if (child.deviceName.includes('内齿轮轴')) {
                // 更新 child.children 中的所有 deviceName 和 title
                child.children.forEach((subChild) => {
                  subChild.deviceName = this.formLabel.ringGearTeeth + this.form.ringGearTeeth + '齿'
                  subChild.title = this.formLabel.ringGearTeeth + this.form.ringGearTeeth + '齿'
                })
              }
              if (child.deviceName.includes('太阳轮轴')) {
                // 更新 child.children 中的所有 deviceName 和 title
                child.children.forEach((subChild) => {
                  subChild.deviceName = this.formLabel.sunGearTeeth + this.form.sunGearTeeth + '齿'
                  subChild.title = this.formLabel.sunGearTeeth + this.form.sunGearTeeth + '齿'
                })
              }
            })
            console.log('提交表单数据', data)
            batchUpdateGroupComponent(data).then(() => {
              this.$emit('submit')
            })
          } else {
            // 表单重构
            const data = this.generateData()
            console.log(data)
            batchAddGroupComponent(data).then(() => {
              this.$emit('submit')
            })
          }
        } else {
          console.log('表单验证失败')
        }
      })
    },
    // 生成表单数据
    generateData() {
      const children = [];
      const type = this.formData.planetaryType;
      const speed = Number(this.form.speed);
      
      // 根据 planetaryGearCount 来动态生成行星轮
      for (let i = 0; i < this.form.planetaryGearCount; i++) {
        children.push({
          id: '',
          parentId: this.deviceId,
          deviceName: this.formLabel.planetaryGearCount,
          treeRank: i,
          treeIcon: 'noDeleteAxis',
          mongodbId: '',
          children: [
            {
              id: '',
              parentId: this.deviceId,
              deviceName: this.formLabel.planetaryGearTeeth + this.form.planetaryGearTeeth + '齿',
              treeIcon: 'noDeletePlanetaryShaftSys',
              mongodbId: '',
              treeRank: 0,
              children: [],
              table: {
                type: '行星轮',
                teethCount: this.form.planetaryGearTeeth,
                inputSpeed: type === '1-1' ? speed : this.calculatePlanetarySpeed(speed, type)
              }
            }
          ],
          table: {}
        });
      }

      // 内齿圈节点
      children.push({
        id: '',
        parentId: this.deviceId,
        deviceName: '内齿轮轴',
        treeIcon: 'noDeleteAxis',
        treeRank: children.length,
        mongodbId: '',
        children: [
          {
            id: '',
            parentId: this.deviceId,
            deviceName: this.formLabel.ringGearTeeth + this.form.ringGearTeeth + '齿',
            treeIcon: 'noDeletePlanetaryShaftSys',
            mongodbId: '',
            children: [],
            treeRank: 0,
            table: {
              type: '内齿圈',
              teethCount: this.form.ringGearTeeth,
              inputSpeed: 0 // 内齿圈固定，转速为0
            }
          }
        ],
        table: {}
      });

      // 太阳轮节点
      children.push({
        id: '',
        parentId: this.deviceId,
        deviceName: '太阳轮轴',
        treeIcon: 'noDeleteAxis',
        treeRank: children.length + 1,
        mongodbId: '',
        children: [
          {
            id: '',
            parentId: this.deviceId,
            deviceName: this.formLabel.sunGearTeeth + this.form.sunGearTeeth + '齿',
            treeIcon: 'noDeletePlanetaryShaftSys',
            mongodbId: '',
            children: [],
            treeRank: 0,
            table: {
              type: '太阳轮',
              teethCount: this.form.sunGearTeeth,
              inputSpeed: type === '1-2' ? speed : this.calculateSunSpeed(speed, type)
            }
          }
        ],
        table: {}
      });

      let treeRank = this.selectedNode ? 
        (this.selectedNode.children ? this.selectedNode.children.length : 0) : 
        this.tableDataTree.length;

      // 根据输入类型生成不同的啮合频率计算公式
      let meshFrequencyFormulas = {};
      if (type === '1-1') {
        // 行星架输入式(风电型)
        meshFrequencyFormulas = {
          // 行星齿转频N2
          planetaryGearFreq: 'N2 = (n_c/60) * (Z_r + Z_s) / Z_p',
          // 行星齿啮合转频
          planetaryMeshFreq: 'f_mesh_p = N2 * Z_p',
          // 太阳轮转频N3
          sunGearFreq: 'N3 = n_c * (1 + Z_r/Z_s) / 60',
          // 齿圈故障通过频率
          ringPassFreq: 'f_r_pass = n_c * N / 60',
          // 啮合频率
          meshFreq: 'f_mesh = n_c * Z_s / 60',
          // 行星架转频
          carrierRotateFreq: 'f_c = n_c / 60',
          description: `
            其中：
            N2 - 行星轮转频
            f_mesh_p - 行星轮啮合频率
            N3 - 太阳轮转频
            f_r_pass - 齿圈故障通过频率
            f_mesh - 啮合频率
            f_c - 行星架转频
            n_c - 行星架转速(rpm)
            Z_s - 太阳轮齿数
            Z_r - 内齿圈齿数
            Z_p - 行星轮齿数
            N - 行星轮个数
          `
        };
      } else if (type === '1-2') {
        // 太阳轮输入式(水泥窑型)
        meshFrequencyFormulas = {
          // 行星齿转频N2
          planetaryGearFreq: 'N2 = (n_s/60) * Z_s / Z_p',
          // 行星齿啮合转频
          planetaryMeshFreq: 'f_mesh_p = N2 * Z_p',
          // 行星架转频N1
          carrierFreq: 'N1 = n_s / (60 * (1 + Z_r/Z_s))',
          // 齿圈故障通过频率
          ringPassFreq: 'f_r_pass = N1 * N',
          // 啮合频率
          meshFreq: 'f_mesh = n_s * Z_s / 60',
          // 太阳轮转频
          sunRotateFreq: 'f_s = n_s / 60',
          description: `
            其中：
            N2 - 行星轮转频
            f_mesh_p - 行星轮啮合频率
            N1 - 行星架转频
            f_r_pass - 齿圈故障通过频率
            f_mesh - 啮合频率
            f_s - 太阳轮转频
            n_s - 太阳轮转速(rpm)
            Z_s - 太阳轮齿数
            Z_r - 内齿圈齿数
            Z_p - 行星轮齿数
            N - 行星轮个数
          `
        };
      }

      // 重构表单数据
      let submitTable = this.formData;
      submitTable.tableData = {
        ...this.form,
        inputShaftSpeed: speed, // 确保设置输入轴转速
        inputShaftType: type === '1-1' ? 'carrier' : 'sun', // 根据类型设置输入轴类型
        meshFrequencyFormulas: meshFrequencyFormulas, // 设置啮合频率计算公式
        // 添加转速信息
        speed: speed,
        carrierSpeed: type === '1-1' ? speed : 0,
        sunSpeed: type === '1-2' ? speed : this.calculateSunSpeed(speed, type),
        ringSpeed: 0
      };

      // 最终的数据对象
      const data = {
        id: this.selectedNode ? this.selectedNode.id : '',
        parentId: this.deviceId,
        deviceName: this.form.gearbox,
        treeRank: treeRank,
        treeIcon: 'gearbox',
        mongodbId: '',
        children: children,
        table: submitTable,
        gearboxType: this.formData.gearboxType
      };

      return data;
    },
    // 计算太阳轮转速（行星架输入模式下）
    calculateSunSpeed(carrierSpeed, type) {
      if (type !== '1-1') return 0;
      
      const Zs = Number(this.form.sunGearTeeth);
      const Zr = Number(this.form.ringGearTeeth);
      
      // 行星架输入模式下，太阳轮转速 = 行星架转速 * (1 + Zr/Zs)
      return carrierSpeed * (1 + Zr/Zs);
    },
    
    // 计算行星轮转速（太阳轮输入模式下）
    calculatePlanetarySpeed(sunSpeed, type) {
      if (type !== '1-2') return 0;
      
      const Zs = Number(this.form.sunGearTeeth);
      const Zr = Number(this.form.ringGearTeeth);
      const Zp = Number(this.form.planetaryGearTeeth);
      
      // 太阳轮输入模式下，行星架转速 = 太阳轮转速 * Zs/(Zs+Zr)
      const carrierSpeed = sunSpeed * (Zs/(Zs+Zr));
      
      // 行星轮转速 = 行星架转速 * (Zr/Zp)
      return carrierSpeed * (Zr/Zp);
    },
    // 计算啮合频率
    calculateFrequencies() {
      // 获取表单数据
      const speed = parseFloat(this.form.speed); // 输入转速(rpm)
      const Zp = parseFloat(this.form.planetaryGearTeeth); // 行星轮齿数
      const Zr = parseFloat(this.form.ringGearTeeth); // 内齿圈齿数
      const Zs = parseFloat(this.form.sunGearTeeth); // 太阳轮齿数
      const N = parseFloat(this.form.planetaryGearCount); // 行星轮个数

      // <<< 添加日志：检查输入值和模式 >>>
      console.log('--- Calculating Frequencies ---');
      console.log('Input Type (planetaryType):', this.formData ? this.formData.planetaryType : 'formData not available');
      console.log('Inputs:', { speed, Zp, Zr, Zs, N });
      // <<< 结束日志添加 >>>

      // 检查输入是否有效
      if (isNaN(speed) || isNaN(Zp) || isNaN(Zr) || isNaN(Zs) || isNaN(N) || Zp <= 0 || Zr <= 0 || Zs <= 0 || N <= 0) {
        this.$message.warning('请输入有效的正数参数');
        // 清空结果
         Object.keys(this.calculatedResults).forEach(key => {
            this.calculatedResults[key] = null;
         });
        return;
      }
      if (Zs + Zp > Zr || Zs + Zr <= Zp || Zr <= Zs ) {
         this.$message.warning('齿数不满足行星齿轮传动要求 (e.g., Zs + Zp < Zr)');
         // 清空结果
         Object.keys(this.calculatedResults).forEach(key => {
            this.calculatedResults[key] = null;
         });
         return;
      }


      // 清空之前的计算结果
      Object.keys(this.calculatedResults).forEach(key => {
        this.calculatedResults[key] = null;
      });

      // 根据输入类型计算不同的啮合频率
      if (this.formData && this.formData.planetaryType === '1-1') { // 增加 formData 检查
        console.log('Entering Mode 1-1 Calculation (Planetary Carrier Input)'); // <<< 添加日志
        // 行星架输入模式(风电型 - 假设内齿圈固定)
        const nc = speed;
        const fc = nc / 60;

        // 太阳轮转频N3 (Hz)
        const fs_calculated = fc * (1 + Zr / Zs); // <<< 使用临时变量
        console.log(`Mode 1-1 Intermediate: fc=${fc.toFixed(4)}, Zr=${Zr}, Zs=${Zs}, Calculated fs (N3)=${fs_calculated.toFixed(4)}`); // <<< 添加日志
        this.calculatedResults.sunGearFreq = fs_calculated.toFixed(3); // N3

        // 行星轮相对行星架的转频 (Hz)
        const fp_rel = fc * (Zr / Zp);
        // 注意：N2通常指行星轮绝对转频，计算较复杂。这里计算相对转频。
        // 或者可以理解为行星轮与太阳轮啮合的相对频率 / Zp?
        // 暂时用相对转频表示 N2，或者可以考虑改为 f_p_rel
        this.calculatedResults.planetaryGearFreq = fp_rel.toFixed(3); // N2 (relative)

        // 啮合频率 (Hz) - 太阳轮/行星轮 或 内齿圈/行星轮
        const fMesh = fc * Zr; // 也等于 fs * Zs
        this.calculatedResults.meshFreq = fMesh.toFixed(3); // 啮合频率
        // 行星齿啮合转频: 通常指整体啮合频率
        this.calculatedResults.planetaryMeshFreq = fMesh.toFixed(3);

        // 齿圈故障通过频率 (Hz) - 行星轮通过内齿圈某点的频率
        const fRPass = fc * N;
        this.calculatedResults.ringPassFreq = fRPass.toFixed(3);

      } else if (this.formData && this.formData.planetaryType === '1-2') { // 增加 formData 检查
        console.log('Entering Mode 1-2 Calculation (Sun Gear Input)'); // <<< 添加日志
        // 太阳轮输入模式(水泥窑型 - 假设行星架固定)
        const ns = speed; // 太阳轮转速(rpm)
        const fs = ns / 60; // 太阳轮频率(Hz)

        // 行星架转频N1 (Hz)
        const fc = 0; // 行星架固定
        this.calculatedResults.carrierFreq = fc.toFixed(3); // N1

        // 行星轮转频N2 (Hz) - 行星架固定时，相对转频即绝对转频
        const fp = fs * (Zs / Zp);
        this.calculatedResults.planetaryGearFreq = fp.toFixed(3); // N2

        // 啮合频率 (Hz) - 太阳轮/行星轮 或 内齿圈/行星轮
        const fMesh = fs * Zs; // 也等于 |fr * Zr| , fr = -fs * Zs / Zr
        this.calculatedResults.meshFreq = fMesh.toFixed(3); // 啮合频率
        // 行星齿啮合转频: 通常指整体啮合频率
        this.calculatedResults.planetaryMeshFreq = fMesh.toFixed(3);

        // 齿圈故障通过频率 (Hz) - 行星轮通过齿圈某点的频率 (相对固定行星架)
        // 如果是指行星轮通过固定机架上某点的频率，则为 N * fp
        const fPass = fp * N;
        this.calculatedResults.ringPassFreq = fPass.toFixed(3);
        // <<< 确保这里没有错误地修改 sunGearFreq >>>
        console.log('Mode 1-2: Sun Gear Freq (N3) is NOT calculated in this mode.');

      } else {
          console.error('Could not determine calculation mode. formData.planetaryType:', this.formData ? this.formData.planetaryType : 'N/A'); // <<< 添加错误日志
      }

      console.log('Calculated Results Object:', JSON.parse(JSON.stringify(this.calculatedResults))); // <<< 添加日志：查看最终结果对象
      this.$message.success('计算完成');
    },
  },
  computed: {
    getInputTypeTitle() {
      const type = this.formData.planetaryType;
      if (type === '1-1') {
        return '行星架输入';
      } else if (type === '1-2') {
        return '太阳轮输入';
      }
      return '';
    },
    getInputTypeDescription() {
      const type = this.formData.planetaryType;
      if (type === '1-1') {
        return '行星架作为输入轴，太阳轮作为输出轴，内齿圈固定。';
      } else if (type === '1-2') {
        return '太阳轮作为输入轴，内齿圈作为输出轴，行星架固定。';
      }
      return '';
    }
  }
}
</script>

<style lang="scss" scoped>
.planetary-axis-container {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &.selected {
    border-color: #409EFF;
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
  }

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      .title-icon {
        margin-right: 8px;
        font-size: 20px;
      }

      .selected-tag {
        margin-left: 12px;
        font-size: 12px;
      }
    }

    .submit-btn {
      margin-left: auto;
    }
  }

  .input-type-section {
    margin-bottom: 20px;
    
    .el-alert {
      border-radius: 4px;
    }
    
    :deep(.el-alert__title) {
      font-size: 16px;
      font-weight: bold;
    }
    
    :deep(.el-alert__description) {
      margin: 8px 0 0 30px;
      color: #67c23a;
      font-size: 14px;
    }
  }

  .main-form {
    .info-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      margin-bottom: 20px;
      overflow: hidden;

      .card-header {
        background-color: #f5f7fa;
        padding: 12px 20px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 8px;
          color: #409EFF;
        }

        span {
          font-weight: 600;
          color: #303133;
        }
        
        .calculate-btn {
          margin-left: auto;
          color: #409EFF;
          
          &:hover {
            opacity: 0.8;
          }
        }
      }

      .card-content {
        padding: 20px;
      }
    }
    
    // 计算结果样式
    .calculation-results {
      .results-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
      }
      
      .result-item {
        background-color: #f8f9fa;
        padding: 12px 16px;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .result-label {
          font-weight: 500;
          color: #606266;
        }
        
        .result-value {
          font-weight: 600;
          color: #409EFF;
          
          .unit {
            color: #909399;
            font-size: 12px;
            margin-left: 2px;
          }
        }
      }
    }

    .speed-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .gear-params-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .custom-input {
      .el-input__inner {
        border-radius: 4px;
      }

      .el-input-group__prepend {
        background-color: #f5f7fa;
        color: #409EFF;
      }
    }

    .custom-input-number {
      width: 180px;
    }

    .unit {
      margin-left: 8px;
      color: #909399;
    }

    .gear-count {
      max-width: 300px;
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .planetary-axis-container {
    padding: 10px;

    .gear-params-grid {
      grid-template-columns: 1fr;
    }

    .speed-inputs {
      grid-template-columns: 1fr;
    }
  }
}
</style>
