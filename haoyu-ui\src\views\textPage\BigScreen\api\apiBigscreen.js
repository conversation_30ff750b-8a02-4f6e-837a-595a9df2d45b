import request from '@/utils/request'

export function getNextLevel(ids) {
    return request({
        url: `/deviceManagement/factorymanagement/deviceMeasurePoint/list/${ids}`,
        method: 'get'
    })
}

export function getAllCompany(ids) {
    return request({
        url: `/deviceManagement/factorymanagement/company/list`,
        method: 'get'
    })
}

export function getNextLevelImformation(id) {
    return request({
        url: `/deviceManagement/factorymanagement/list/${id}`,
        method: 'get'
    })
}

export function getWarnRecord(id) {
    return request({
        url: `/dataEngine/warnRecord/list?pageNum=${id}&pageSize=${100}`,
        method: 'get',
    })
}

export function geDeviceMeasureDefine(id) {
    return request({
        url: `/deviceManagement/factorymanagement/list/device/${id}`,
        method: 'get',
    })
}

export function getfactoryImage(id) {
    return request({
        url: `/factory/factoryImage/factory/${id}`,
        method: 'get',
    })
}

export function getDeviceImageList(deviceId) {
    const url = `/deviceDetailsImage/image/device/${deviceId}`
    return request({
        url,
        method: 'get',
    })
}