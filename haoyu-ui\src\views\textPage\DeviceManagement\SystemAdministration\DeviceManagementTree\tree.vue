<template>
  <div ref="treeContainer" class="tree-container" @click="handleContainerClick" @contextmenu.prevent="handleRightClick">
    <!-- 搜索框 -->
    <div class="content-wrapper" style="max-height: 85vh;">
      <!-- 搜索框和搜索按钮 -->
      <div class="search-container">
        <a-input-search
          v-model="searchQuery"
          class="search-input"
          placeholder="搜索节点..."
          @search="performSearch"
        />
        <a-button
          type="default"
          shape="circle"
          icon="reload"
          @click="Reset"
        />
      </div>
      <!-- 树 -->
      <a-tree
        v-if="treeData && treeData.length > 0"
        ref="tree"
        show-icon
        show-line
        show-leaf-icon="false"
        :selected-keys="[selectedKey]"
        :icon="getIcon"
        :tree-data="treeData"
        :expanded-keys="expandedKeys"
        class="custom-tree"
        @dblclick="handleDoubleClick"
        @rightClick="handleRightClickOnNode"
        @select="handleSelect"
        @expand="handleExpand"
      >
        <!-- 定义树节点的标题模板 -->
        <template #title="{ title, dataRef }">
          <span v-if="dataRef.treeIcon === 'measureDef' && dataRef.title.includes('Hz)')">
            {{ formatMeasureDefTitle(dataRef) }}
          </span>
          <span v-else>{{ title }}</span>
        </template>
      </a-tree>
    </div>
    <!-- 修改节点名称 -->
    <a-modal
      v-model="dialogVisible"
      :mask-closable="false"
      title="修改节点名称"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <a-input v-model="dialogTitle" />
    </a-modal>

    <!-- 新建厂区 -->
    <el-dialog
      :visible.sync="newNodeDialogVisible"
      title="新建厂区"
      :modal-append-to-body="true"
      append-to-body
      @close="handleNewNodeCancel"
    >
      <el-input v-model="newNodeTitle" placeholder="请输入区域名称" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleNewNodeCancel">取消</el-button>
        <el-button type="primary" @click="handleNewNodeOk">确定</el-button>
      </span>
    </el-dialog>

    <!-- 新建设备 -->
    <el-dialog
      :visible.sync="newDeviceDialogVisible"
      title="设备信息"
      width="800px"
      :modal="false"
    >
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item
          v-for="(item, index) in formItems"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          :rules="item.rules"
        >
          <el-input v-model="form[item.prop]" :placeholder="item.placeholder" size="mini" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleTermSettings">词条设置</el-button>
        <el-button type="success" @click="handleOk">保存信息</el-button>
        <el-button type="default" @click="handleCancel_imformation_dialog">取消保存</el-button>
      </span>
    </el-dialog>
    <!-- 词条设置弹窗 -->
    <el-dialog
      :visible.sync="termSettingsDialogVisible"
      :close-on-click-modal="false"
      title="设备信息-词条设置"
      @close="handleTermSettingsCancel"
    >
      <!-- 添加按钮 -->
      <div style="margin-bottom: 10px;">
        <el-button
          type="primary"
          size="mini"
          @click="addNewRow"
        >
          添加
        </el-button>
      </div>
      <el-table
        :data="formItems"
        border
        style="width: 100%"
      >
        <el-table-column
          prop="label"
          label="文本标签"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.label"
              :disabled="scope.row.lock"
              placeholder="请输入新的标签文本"
              @input="updateProp(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否锁定"
          width="100"
        >
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.lock"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="是否必填项"
          width="100"
        >
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.isRequired"
              @change="handleLockChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              type="danger"
              @click="deleteRow(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleTermSettingsOk">确定</el-button>
        <el-button type="default" @click="handleTermSettingsCancel">取消</el-button>
      </span>
    </el-dialog>
    <!-- 新建测点 -->
    <div>
      <el-dialog
        :title="PointsTitle"
        :visible.sync="newMeasuringPointsDialogVisible"
        width="80%"
        :close-on-click-modal="false"
        :modal-append-to-body="false"
        @close="handleCancel"
      >
        <div class="Point-container">
          <!-- 左边部分 -->
          <div class="Point-left">
            <el-button
              size="mini"
              @click="handleDeletePointTree"
            >删除</el-button>
            <!-- <h3 class="Point-title">传感器信息</h3> -->
            <div class="Point-left-top">
              <!-- 上部分容器 - 测量定义树 -->
              <a-tree
                ref="treePointRef"
                :tree-data="PointsTreeData"
                :accordion="false"
                :expand-action="'click'"
                :expandedKeys="PointsexpandedKeys"
                :selectedKeys="selectedPointKeys"
                :icon-render="renderIcon"
                @expand="handleNodeExpand"
                @select="handleNodeClick"
              />
            </div>
            <div class="Point-left-bottom">
              <!-- 下部分容器 - 其他内容 -->
              <div ref="PointleftBottomContainer" class="Point-left-bottom">
                <!-- 振动值、温度、用户定义的触发元素 -->
                <div class="trigger-container">
                  <!-- <el-button :disabled="isPointsDisabled" class="trigger-button" type="text" @click="VibrationValueDialog">振动值</el-button> -->
                  <!-- <el-button :disabled="isPointsDisabled" class="trigger-button" type="text">包络</el-button> -->
                  <!-- <el-button :disabled="isPointsDisabled" class="trigger-button" type="text">温度</el-button> -->
                  <!-- <el-button :disabled="!isPointsDisabled" class="trigger-button" type="text">测点选择</el-button> -->
                  <el-button :disabled="isPointsDisabled" class="trigger-button" @click="confirmVibrationValue('加速度')" type="text">加速度</el-button>
                  <el-button :disabled="isPointsDisabled" class="trigger-button" @click="confirmVibrationValue('速度')" type="text">速度</el-button>
                  <el-button :disabled="isPointsDisabled" class="trigger-button" @click="confirmVibrationValue('位移')" type="text">位移</el-button>
                  <el-button :disabled="isPointsDisabled" class="trigger-button" @click="confirmVibrationValue('包络')" type="text">包络</el-button>
                  <el-button :disabled="isPointsDisabled" class="trigger-button" type="text">用户定义</el-button>

                </div>
              </div>
            </div>
          </div>

          <!-- 右边部分 -->
          <div class="Point-right">
            <!-- 动态组件 -->
            <component
              v-if="currentFormData"
              :is="currentViewComponent"
              ref="componentRef"
              style="height: 100%; position: relative;"
              :form-data="currentFormData"
              @update="updateTreeTitle"
            />
          </div>
        </div>

        <span slot="footer" class="dialog-footer">
          <!-- <el-button @click="startComponentLoading">开启遮罩层</el-button>
          <el-button @click="stopComponentLoading">关闭遮罩层</el-button> -->
          <el-button @click="handleMeasuringPointsCancel">取消</el-button>
          <el-button type="primary" @click="handleMeasuringPointsSave">保存</el-button>
          <el-button type="primary" @click="textTable">测试表单</el-button>
        </span>
      </el-dialog>

    </div>
    <!-- 振动值弹窗 -->
    <!-- <el-dialog
      title="振动值设置"
      :visible.sync="VibrationValueDialogVisible"
      width="30%"
      @close="VibrationValuehandleClose"
    >
      <el-form>
        <el-form-item label="选择振动值">
          <el-radio-group v-model="selectedVibrationValue">
            <el-radio label="加速度">加速度</el-radio>
            <el-radio label="速度">速度</el-radio>
            <el-radio label="位移">位移</el-radio>
            <el-radio label="包络">包络</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="VibrationValueDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmVibrationValue">确定</el-button>
      </div>
    </el-dialog> -->
    <!-- 右击事件组件 -->
    <RightClickMenu ref="rightClickMenu" :container-rect="containerRect" @menu-click="handleMenuClick" />
    <!-- 上传图片对话框 -->
    <el-dialog
      title="上传公司图片"
      :visible.sync="uploadImageDialogVisible"
      width="400px"
      @close="handleDialogClose"
    >
      <el-upload
        ref="upload"
        class="upload-demo"
        action="#"
        :auto-upload="false"
        :show-file-list="true"
        :on-change="handleFileChange"
        accept="image/*"
      >
        <el-button size="small" type="primary">选择图片</el-button>
        <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleImageUpload">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑测点弹窗 -->
    <EditMeasrumentPoint
      :visible.sync="editMeasurementPointVisible"
      :pointId="currentPointId"
    />
  </div>
</template>

<script>
import { Tree, Input, Modal, message } from 'ant-design-vue'
import RightClickMenu from '@/views/textPage/RightClickMenu/RightClickMenu.vue'
import hotkeys from 'hotkeys-js'
import rightClickHandlers from './rightClickHandlers'
import { listDeviceManagement } from '@/api/haoyu-system/deviceManagement/deviceManagement.js'
import {
  addFactory,
  delTreeNode,
  deviceConfigData,
  batchAddDevice,
  addDevicePoint,
  updateTreeData,
  getDeviceTemplateData,
  getTemplateData,
  updateTemplateData,
  // batchUpdateDevice,
  getMeasurementDefine,
  getMeasurePointInfo,
  batchAddMeasurementDefine,
  batchDelMeasurementDefine,
  updateDevicePoint,
  batchUpdateDevicePoint
} from '@/api/haoyu-system/SystemAdministration/SystemAdministration'

import {
  getBaseStationDeviceList
} from '@/api/haoyu-system/OnlineManagement/BaseStationTree'

// import pinyin from 'pinyin-pro'
import { mapState, mapActions } from 'vuex'

import SensorConfigForm from './SensorConfigForm/SensorConfigForm.vue'
import VibrationForm from './VibrationForm/VibrationForm.vue'
import { uploadCompanyImage } from '@/api/system/company'
import LeftSidebar from '@/views/textPage/LeftSidebar/LeftSidebar.vue'
import EditMeasrumentPoint from './edit/EditMeasrumentPoint.vue'
export default {
  components: {
    'a-tree': Tree,
    'a-input': Input,
    'a-modal': Modal,
    RightClickMenu,
    SensorConfigForm,
    VibrationForm,
    LeftSidebar,
    EditMeasrumentPoint,
  },
  data() {
    return {
      containerRect: {}, // 用于存储容器边界信息
      StagingArea: [], // 只需要拿来给转速用把id给他（后续需要优化）
      DeletePointNode: [], // 缓存编辑时候的要删除的数据（id）
      mainUseEdit: false, // 在当前页面使用看看是否正在编辑功能(和传入的右键编辑inAction区分开)
      isPointsDisabled: true, // 判断是否将振动点等功能禁用
      PointsexpandedKeys: [],
      selectedPointKeys: [],
      deletedNodeIds: [], // 用于存储被删除的节点 id
      PointloadingInstance: null, // 用于保存遮罩层的实例
      PointisLoading: false, // 标识是否正在加载
      // 振动值弹窗
      VibrationValueDialogVisible: false,
      selectedVibrationValue: '加速度', // 存储选择的振动值
      // 默认测点弹窗文本
      PointsTitle: '新建测点',
      // 选择的测量定义树的节点信息
      currentDefPoints: null,
      inAction: '', // 右击事件点击要进行的动作
      currentView: 'sensor', // 默认视图
      // 存储已修改的测量定义，用于批量提交
      modifiedMeasureDefinitions: [],
      // 测量定义表单的字段
      sensorFormData: {
        serialNumber: '', // 测点编号
        name: '', // 测点名称
        speed: '', // 设备转速
        instrument: '3',
        vibrationDirection: '', // 振动方向
        sensorDirection: 1, // 传感器类型
        collectorName: '', // 传感器名称
        collectorModel: '' // 传感器的型号
      },
      // 振动值控制的字段
      vibrationFormData: {
        measureDefineName: '加速度', // 名称
        timeSignalUnitId: '', // 时间信号单位
        spectrumUnitId: '', // 频谱单位
        upperLimitFrequency: '', // 上限频率HZ
        lowerLimitFrequency: '', // 下限频率HZ
        windowsFunctionId: '', // 窗函数
        numberOfSpectralLines: '', // 谱线数
        envelopeFrequency: '', // 包络频率
        fastFourierTransformTypeId: '', // 快速傅里叶变化类型
        averageType: '', // 平均类型
        averageValue: '', // 平均次数
        dataOverlapRate: '', // 数据重叠率
        firstAlarmValue: '', // 一级报警值
        secondAlarmValue: '', // 二级报警值
        thirdAlarmValue: '', // 一级报警值
        forthAlarmValue: '' // 二级报警值
      },
      defaultExpandedKeys: [],
      // 新建测点的树数据
      PointsTreeData: [],
      treeData: [],
      // 编辑词条弹窗
      termSettingsDialogVisible: false,
      // 输入框的值
      formItems: [],
      // 设备信息暂存区
      formItemsStagingArea: [],
      // 设备信息表单
      form: {},
      newMeasuringPointsForm: {
        title: ''
      },
      // 控制模态框的显示状态
      dialogVisible: false,
      // 设备信息弹窗
      newDeviceDialogVisible: false,
      // 新建测量点对话框
      newMeasuringPointsDialogVisible: false,
      // 存储当前编辑节点的标题
      dialogTitle: '',
      // 新建下一区域弹窗
      newNodeDialogVisible: false,
      // 新建下一区域标题
      newNodeTitle: '',
      // 存储当前编辑的节点对象
      nodeToEdit: null,
      // 剪切板数据
      clipboard: null,
      // 当前右击的节点
      currentNode: null,
      cachedNodeId: null,  // 添加这一行,缓存的节点
      searchQuery: '', // 搜索框的值
      // tree的真实数据
      RealTreeData: [],
      // 搜索筛选节点数据
      filteredData: [],
      // 高亮中的点
      heightLight: '',
      sensorFormDataname: '',
      sensorFormDataserialNumber: '',
      uploadImageDialogVisible: false,
      imageFile: null,
      editMeasurementPointVisible: false, // 控制编辑测点弹窗的显示
      currentPointId: '', // 当前编辑的测点ID
      uploadNodeInfo: null, // 添加这个属性用于缓存上传时的节点信息
      expandedKeys: [] // 使用 expandedKeys 替代 localExpandedKeys
    }
  },

  computed: {
    ...mapState('tree', ['treeDataSend', 'selectedKey', 'treePathText']),
    filteredTreeData() {
      return this.filteredData.length ? this.filteredData : this.treeData
    },
    // 动态加载子组件
    currentViewComponent() {
      return this.currentView === 'vibration' ? 'VibrationForm' : 'SensorConfigForm'
    },
    // 给子组件传递不同的ref
    dynamicRefName() {
      // 根据 currentViewComponent 生成唯一的 ref 名称
      return `${this.currentView}Ref`
    },
    // 传递给加载子组件内容
    currentFormData() {
      // console.log('vibrationFormData:', this.vibrationFormData) // 打印 currentView
      const formData = this.currentView === 'vibration' ? this.vibrationFormData : this.sensorFormData
      // console.log('Returned formData:', formData) // 打印返回的数据
      return formData
    }
  },
  watch: {
    // 监听 sensorDirection 的变化，动态更新 PointsTreeData
    'sensorFormData.sensorDirection'(newVal) {
      this.updatePointsTreeData()
    },
    // 监听下拉框的选择方向动态更新树
    'sensorFormData.vibrationDirection'(newVal) {
      this.updatePointsTreeData()
    },
    // 监听测点名称变化，更新树显示
    'sensorFormData.name'(newVal) {
      this.sensorFormDataname = newVal
      this.updatePointsTreeData()
    },
    // 监听测点编号变化，更新树显示
    'sensorFormData.serialNumber'(newVal) {
      this.sensorFormDataserialNumber = newVal
      this.updatePointsTreeData()
    },
    'vibrationFormData.name'(newVal) {
      this.updatePointsTreeData()
    },
    // 监听测量定义树是否是空去开启或者关闭遮罩层
    // 深度监听整个 PointsTreeData
    'currentDefPoints': {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (!newVal) {
          this.stopComponentLoading()
          return
        }

        // 只有在测量定义节点且数据未加载时显示遮罩层
        if (newVal.treeIcon === 'measureDef') {
          const isDataLoaded = newVal.measureDefinitions &&
                             Object.keys(newVal.measureDefinitions).length > 0

          if (!isDataLoaded) {
            this.startComponentLoading()
          } else {
            this.stopComponentLoading()
          }
        }
      }
    },
    // 移除 PointsTreeData 的 watch，因为遮罩层控制已经在 currentDefPoints 中处理
    'selectedPointKeys': {
      immediate: true,
      handler(newVal) {
        // 只在没有选中节点时显示遮罩层
        if (newVal.length === 0 && this.currentView === 'vibration') {
          this.startComponentLoading()
        }
      }
    },
    'inAction'(action) {
      if (this.inAction === 'newNode') {
        this.isPointsDisabled = true
      } else {
        this.isPointsDisabled = false
      }
    },
    'formData.numberOfSpectralLines': {
      immediate: true,
      handler(newVal) {
        // 确保数据类型为数字
        this.$emit('update:formData', {
          ...this.formData,
          numberOfSpectralLines: Number(newVal)
        })
      }
    }
  },
  created() {
    // 从 store 中恢复状态
    this.treeData = this.$store.state.tree.treeDataSend
    this.defaultExpandedKeys = this.$store.state.tree.expandedKeys // 使用 defaultExpandedKeys
    this.expandedKeys = this.$store.state.tree.expandedKeys
    this.heightLight = this.$store.state.tree.selectedKey
    this.getTreelist()
  },
  mounted() {
    this.updatePointsTreeData()
    this.startPointLoading()
    // 绑定快捷键
    hotkeys('ctrl+c', (event) => {
      event.preventDefault()
      this.copyNodeToClipboard(this.currentNode)
    })
    hotkeys('ctrl+v', (event) => {
      event.preventDefault()
      this.pasteNodeAndOverwrite()
    })
    hotkeys('ctrl+x', (event) => {
      event.preventDefault()
      this.cutNode()
    })

    // 修改事件监听
    this.$root.$on('expand-all', () => {
      // 强制更新展开状态
      const allKeys = this.getAllKeys(this.treeData)
      this.$nextTick(() => {
        this.expandedKeys = []
        this.$nextTick(() => {
          this.expandedKeys = allKeys
          // 强制树组件重新渲染
          this.$refs.tree && this.$refs.tree.$forceUpdate()
        })
      })
    })

    this.$root.$on('collapse-all', () => {
      this.$nextTick(() => {
        this.expandedKeys = []
        // 强制树组件重新渲染
        this.$refs.tree && this.$refs.tree.$forceUpdate()
      })
    })
  },
  beforeDestroy() {
    // 解绑快捷键
    hotkeys.unbind('ctrl+c')
    hotkeys.unbind('ctrl+v')
    hotkeys.unbind('ctrl+x')
    this.stopComponentLoading()
    // 在组件销毁前保存状态到 store
    this.$store.dispatch('tree/setTreeData', this.treeData)
    this.$store.dispatch('tree/setSelectedKey', this.heightLight)

    // 解绑全局事件监听
    this.$root.$off('expand-all')
    this.$root.$off('collapse-all')
    this.$store.dispatch('tree/setExpandedKeys', this.defaultExpandedKeys)
  },
  methods: {
    ...mapActions('tree', ['setTreeData', 'setSelectedKey', 'moveNode', 'setSelectedParentKey', 'setTreePathText']),
    // 递归遍历树形结构，找到目标节点并删除，同时存储 id
    deleteNodeByKey(treeData, key) {
      for (let i = 0; i < treeData.length; i++) {
        const node = treeData[i]

        // 检查当前节点是否是目标节点
        if (node.key === key) {
          // 检查节点 id 是否存在，不为空时存储 id
          if (node.id) {
            this.deletedNodeIds.push(node.id)
          }

          // 删除该节点
          treeData.splice(i, 1)
          return true // 删除成功，退出递归
        }

        // 如果当前节点有子节点，递归查找
        if (node.children && node.children.length > 0) {
          const result = this.deleteNodeByKey(node.children, key)
          if (result) {
            return true // 找到并删除，退出递归
          }
        }
      }
      return false // 未找到目标节点
    },

    handleCancel_imformation_dialog(){
      this.newDeviceDialogVisible = false
    },
    // 处理删除操作
    handleDeletePointTree() {
      if (!this.currentDefPoints || !this.currentDefPoints.key) {
        console.error('currentDefPoints 中未找到 key')
        return
      }
      // 获取当前节点的 id
      const nodeId = this.currentDefPoints.id
      const nodeKey = this.currentDefPoints.key  // 使用 key 而不是 id 来查找节点
      console.log('要删除的节点ID:', nodeId)
      console.log('要删除的节点Key:', nodeKey)

      // console.log(this.currentDefPoints.treeIcon)
      if (this.currentDefPoints.treeIcon !== 'measureDef') {
        console.error('不是测量定义无法删除')
        return
      }

      const targetKey = this.currentDefPoints.key // 获取要删除节点的 key
      const parentChildren = this.findParentNode(this.PointsTreeData, targetKey).children // 找到父节点的 children
      const currentIndex = parentChildren.findIndex(node => node.key === targetKey) // 获取当前节点的索引

      if (currentIndex === -1) {
        console.log(`未找到 key 为 ${targetKey} 的节点`)
        return
      }

      // 删除当前节点
      parentChildren.splice(currentIndex, 1)

      // 将删除的节点 ID 添加到 deletedNodeIds 数组
      if (nodeId) {
        this.deletedNodeIds.push(nodeId)  // 存储 id 用于后续的删除操作
        console.log('删除的节点 ID:', this.deletedNodeIds)
      }

      // 判断删除后的选择逻辑
      if (parentChildren.length > 0) {
        let nextKey = null

        if (currentIndex < parentChildren.length) {
          // 如果下一个节点存在，选择下一个节点
          nextKey = parentChildren[currentIndex].key
        } else if (currentIndex - 1 >= 0) {
          // 如果下一个节点不存在，选择上一个节点
          nextKey = parentChildren[currentIndex - 1].key
        }

        if (nextKey) {
          // 查找下一个或上一个节点
          const nextNode = this.findNodeByKey(this.PointsTreeData, nextKey)

          if (nextNode) {
            // 选中下一个或上一个节点，并触发点击事件
            this.selectedPointKeys = [nextKey]
            console.log(nextNode)
            // 将找到的节点对象传递给 handleNodeClick
            this.handleNodeClick(this.selectedPointKeys, { node: { dataRef: nextNode }})
          } else {
            console.error('未找到下一个节点')
          }
        }
      } else {
        // 如果没有节点可选，提示用户
        this.selectedPointKeys = []
        this.$message.info('所有节点已被删除')
      }
    },
    // 递归查找父节点
    findParentNode(treeData, targetKey) {
      if (!treeData) return null
      for (const node of treeData) {
        if (node.children && node.children.find(child => child.key === targetKey)) {
          return node // 找到目标节点的父节点
        }
        if (node.children) {
          const parentNode = this.findParentNode(node.children, targetKey)
          if (parentNode) return parentNode
        }
      }
      return null
    },
    // 启动遮罩层*（未成功待修改）
    startPointLoading() {
      // 如果已经有加载实例，避免重复启动
      if (this.PointisLoading) return
      console.log(this.$refs)
      // 检查 ref 是否有效，防止 target 丢失
      if (!this.$refs.PointleftBottomContainer) {
        // console.error('Container not found!')
        return
      }

      // 启动遮罩层
      this.loadingInstance = this.$loading({
        lock: false,
        text: '正在加载...', // 可选的加载文字
        spinner: ' ', // 移除加载图标
        background: 'rgba(0, 0, 0, 0.5)', // 背景遮罩
        target: this.$refs.PointleftBottomContainer // 遮罩层应用到 Point-left-bottom 容器
      })

      this.isLoading = true
    },

    // 关闭遮罩层
    stopPointLoading() {
      if (this.loadingInstance) {
        this.PointloadingInstance.close()
        this.PointloadingInstance = null // 清空实例，允许再次启动
        this.PointisLoading = false
      }
    },
    // 通过 $emit 触发子组件的事件
    startComponentLoading() {
      if (this.$refs.componentRef && this.$refs.componentRef.startLoading) {
        this.$nextTick(() => {
          this.$refs.componentRef.startLoading({
            target: '.Point-right', // 限定遮罩层范围
            fullscreen: false
          })
        })
      }
    },
    // 通过 ref 调用子组件的 stopLoading 方法
    stopComponentLoading() {
      if (this.$refs.componentRef && this.$refs.componentRef.stopLoading) {
        this.$refs.componentRef.stopLoading() // 调用子组件的 stopLoading 方法
      } else {
        // console.error('子组件未找到或没有 stopLoading 方法')
      }
    },
    // 查看测点信息
    viewPointsMessage(id) {
      getMeasurePointInfo(id).then(res => {
        this.sensorFormData.serialNumber = res.data.sensorCoding || '' // 测点编号
        this.sensorFormData.name = res.data.sensorName || '' // 测点名称
        this.sensorFormData.speed = res.data.deviceRpm || '' // 设备转速
        this.sensorFormData.sensorDirection = Number(res.data.sensorType) || '' // 传感器类型
        this.sensorFormData.vibrationDirection = Number(res.data.sensorDirection) || '' // 振动方向
        this.sensorFormData.id = res.data.id || ''
        this.sensorFormData.deviceId = res.data.deviceId || ''
        this.sensorFormData.sensorTypeName = res.data.sensorTypeName || ''
        this.sensorFormData.measurePointRelation = res.data.measurePointRelation || ''
        this.sensorFormData.collectorModel = res.data.collectorModel || ''
        this.sensorFormData.collectorName = res.data.collectorName || ''
      })
    },
    // 振动值弹窗
    VibrationValueDialog() {
      this.VibrationValueDialogVisible = true // 打开弹窗
    },
    VibrationValuehandleClose() {
      console.log('关闭弹窗')
      this.selectedVibrationValue = '' // 重置选择
    },
    // 振动弹窗插入树节点
    confirmVibrationValue(selected) {
      // 假设要插入的数据结构
      this.vibrationFormData = {
        ...this.vibrationFormData,
        fastFourierTransformTypeId: '1' // 设置FFT类型默认值为1(无)
      }
      console.log('selected', selected)
      this.selectedVibrationValue = selected
      const newVibrationData = {
        id: null, // 新建节点的话id为空
        key: '', // 这里我们将生成唯一 key
        status: 'connected', // 示例状态，可以根据实际情况调整
        title: `${this.selectedVibrationValue}(2-1000HZ)(800线)`, // 使用已选���的振动值
        treeIcon: 'measureDef', // 图标，可以根据需求调整
        children: [], // 默认空 children
        measureDefinitions: {
          measureDefineName: '', // 名称
          timeSignalUnitId: 1, // 时间信号单位
          spectrumUnitId: 1, // 频谱单位
          upperLimitFrequency: '', // 上限频率HZ
          lowerLimitFrequency: '', // 下限频率HZ
          windowsFunctionId: 1, // 窗函数
          numberOfSpectralLines: '', // 谱线数
          envelopeFrequency: 1, // 包络频率
          fastFourierTransformTypeId: '', // 快速傅里叶变化类型
          averageType: '', // 平均类型
          averageValue: '', // 平均次数
          dataOverlapRate: '', // 数据重叠率
          firstAlarmValue: '', // 一级报警值
          secondAlarmValue: '', // 二级报警值
          thirdAlarmValue: '', // 一级报警值
          forthAlarmValue: '' // 二级报警值
        }
      }
      if (this.selectedVibrationValue === '加速度') {
        newVibrationData.measureDefinitions.measureDefineName = this.selectedVibrationValue
        newVibrationData.measureDefinitions.timeSignalUnitId = 2
        newVibrationData.measureDefinitions.spectrumUnitId = 2
      } else if (this.selectedVibrationValue === '速度') {
        newVibrationData.measureDefinitions.measureDefineName = this.selectedVibrationValue
        newVibrationData.measureDefinitions.timeSignalUnitId = 1
        newVibrationData.measureDefinitions.spectrumUnitId = 1
      } else if (this.selectedVibrationValue === '包络') {
        newVibrationData.measureDefinitions.measureDefineName = this.selectedVibrationValue
        newVibrationData.measureDefinitions.timeSignalUnitId = 4
        newVibrationData.measureDefinitions.spectrumUnitId = 4
        newVibrationData.measureDefinitions.isUnitDisabled = true // 添加禁用标记
      }
      console.log('this.PointsTreeData ==> ', this.PointsTreeData)

      // 找到目标插入位置
      const targetNode = this.PointsTreeData[0].children[1].children

      // 生成唯一 key
      const newKey = targetNode.length > 0
        ? `${targetNode[targetNode.length - 1].key}-1` // 基于最后一个子节点的 key 生成新 key
        : `${this.PointsTreeData[0].children[1].key}-0` // 如果没有子节点，生成新的 key

      // 设置新节点的 key
      newVibrationData.key = newKey

      // 将新节点插入到目标节点
      targetNode.push(newVibrationData)

      // 打印插入后的数据
      // console.log(this.PointsTreeData[0].children[1].children)

      // 关闭弹窗
      this.VibrationValueDialogVisible = false
      // console.log(this.$refs.treePointRef)
      // const dataRef = newVibrationData
      this.$nextTick(() => {
        this.expandParentNode(newKey) // 展开父节点
        this.highlightNode(newKey) // 手动调用节点点击事件
        // 构造 info 对象，并传递 dataRef 到 node 下
        this.handleNodeClick([newKey], { node: { dataRef: newVibrationData }})
      })
    },
    // 递归展开父节点
    expandParentNode(newKey) {
      const parentKey = newKey.split('-').slice(0, -1).join('-') // 获取父节点的 key
      if (!this.PointsexpandedKeys.includes(parentKey)) {
        this.PointsexpandedKeys.push(parentKey) // 将父节点 key 添加到 expandedKeys 中
        console.log(this.PointsexpandedKeys)
      }
    },

    // 手动高亮选中的节点
    highlightNode(key) {
      this.selectedPointKeys = [key] // 设置选中节点的 key
    },

    // 获取指定设备数据
    getDeviceData(id) {
      getTemplateData(id).then(res => {
        this.formItemsStagingArea = res
        this.formItems = res.formItems
        this.form = res.form
      })
    },
    // 获取设备模板数据
    getDeviceModel() {
      getDeviceTemplateData().then(res => {
        // 存储表单模板
        this.formItemsStagingArea = res
        this.formItems = res.formItems
        this.form = res.form
      })
    },
    // 编辑测量定义(获取信息)
    editMeasurement() {
      console.log('当前节点id为：' + this.currentNode.id)
      getMeasurementDefine(this.currentNode.id).then(res => {
        console.log(res.data)
        this.vibrationFormData = res.data
      })
    },
    // 递归查找指定节点的父级及父级的父级
    findGrandParentNode(treeData, targetId) {
      let parentNode = null // 父级节点
      let grandParentNode = null // 祖父级节点

      const findNode = (nodes, parent = null, grandParent = null) => {
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i]

          if (node.id === targetId) {
            // 找到了目标节点
            parentNode = parent
            grandParentNode = grandParent
            return
          }

          // 如果有子节点，继续递归查找
          if (node.children && node.children.length > 0) {
            findNode(node.children, node, parent)
          }
        }
      }

      // 开始递归查找
      findNode(treeData)

      // 返回找到的父级及祖父级
      return {
        parentNode,
        grandParentNode
      }
    },
    // 获取测点树的信息
    getBaseStationDeviceTree(isEdit) {
      if (!this.currentNode || !this.currentNode.id) {
        // console.log('currentNode is not selected or is invalid')
        return  // 提前退出，避免后续报错
      }
      const result = this.findGrandParentNode(this.treeData, this.currentNode.id)
      if (result.parentNode && isEdit) {
        const data = [{}]
        data[0] = result.parentNode
        this.PointsTreeData = data
        // 确保 PointsTreeData[0] 和其子节点存在
        if (this.PointsTreeData[0] && this.PointsTreeData[0].children[1]) {
          // console.log(this.PointsTreeData[0].children)
          this.PointsTreeData[0].children.forEach(node => {
            if (node.id) {
              this.DeletePointNode.push(node.id) // 将每个节点的 id 存入数组中
              // console.log('待删除节点的 ID',this.DeletePointNode)
            }
          })
        }
        const keysWithChildren = this.getKeysWithChildren(this.PointsTreeData)
        this.handleNodeExpand(keysWithChildren)
      } else {
        // console.log('未找到祖父节点')
        getBaseStationDeviceList(this.currentNode.id).then(res => {
          const data = [{}]
          data[0] = res.data
          this.PointsTreeData = data
          this.StagingArea = data
          console.log(this.PointsTreeData)
          this.PointsTreeData[0].children.forEach(node => {
            if (node.id) {
              this.DeletePointNode.push(node.id) // 将每个节点的 id 存入数组中
              // console.log('存储的删除id', this.DeletePointNode)
            }
          })
          const keysWithChildren = this.getKeysWithChildren(this.PointsTreeData)
          this.handleNodeExpand(keysWithChildren)

          // console.log('Keys with children:', this.keysWithChildren)
        })
      }
    },
    // 递归提取有子节点的 key
    getKeysWithChildren(nodes) {
      const keys = []
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          keys.push(node.key) // 如果有子节点，则存储该节点的 key
          keys.push(...this.getKeysWithChildren(node.children)) // 递归处理子节点
        }
      })
      return keys
    },
    // 更新测点的数据，测点名称测点轴向等
    updatePointsTreeData(sensorDirection) {
      // console.log(this.PointsTreeData)
      // console.log(this.vibrationFormData) // 打印确保数据有效
      if (this.inAction === 'newDefinition') {
        console.log(sensorDirection)
        if (sensorDirection) {
          this.getBaseStationDeviceTree(true)
        } else {
          this.getBaseStationDeviceTree(false)
        }
      } else {
        this.getBaseStationDeviceTree(true)
        // console.log(this.StagingArea)
        const baseTreeData = [
          {
            id: 3,
            title: `${this.sensorFormData.name || this.sensorFormDataname || ''}-${this.sensorFormData.serialNumber || this.sensorFormDataserialNumber || ''}`, // 动态添加测点名称和编号
            key: '0-0',
            treeIcon: 'bearing',
            status: 'disconnected',
            children: [
              {
                id: (this.StagingArea && this.StagingArea[0] && this.StagingArea[0].children && this.StagingArea[0].children[0])
                  ? this.StagingArea[0].children[0].id
                  : '', // 如果 StagingArea 结构不完整，则赋值为空字符串
                title: `转速(${this.sensorFormData.speed}RPM)`, // 显示设备的转速
                key: '0-0-0',
                treeIcon: 'value',
                status: 'disconnected',
                children: []
              }
            ]
          }
        ]
        // 判断 sensorDirection 的值，动态设置 baseTreeData 的 children
        if (this.sensorFormData.sensorDirection === 1 || this.sensorFormData.sensorDirection === 2) {
          // 判断是不是测量定义
          if (this.inAction === 'newDefinition') {
            baseTreeData[0].children.push({
              title: this.getVibrationTitle(this.sensorFormData.vibrationDirection),
              key: '0-0-1-0',
              treeIcon: 'measureDef',
              status: 'disconnected',
              children: [],
              measureDefinitions: {
                measureDefineName: '', // 名称
                timeSignalUnitId: '2', // 时间信号单位
                spectrumUnitId: 2, // 频谱单位
                upperLimitFrequency: '2', // 上限频率HZ
                lowerLimitFrequency: '10000', // 下限频率HZ
                windowsFunctionId: 1, // 窗函数
                numberOfSpectralLines: 1600, // 谱线数
                envelopeFrequency: '1', // 包络频率
                fastFourierTransformTypeId: 1, // 快速傅里叶变化类型
                averageType: '1', // 平均类型
                averageValue: '100', // 平均次数
                dataOverlapRate: '50', // 数据重叠率
                firstAlarmValue: '1', // 一级报警值
                secondAlarmValue: '2', // 二级报警值
                thirdAlarmValue: '3', // 一级报警值
                forthAlarmValue: '4' // 二级报警值
              }
            })
          } else {
            baseTreeData[0].children.push({
              id: null,
              title: this.getVibrationTitle(this.sensorFormData.vibrationDirection),
              key: '0-0-1-0',
              treeIcon: 'value',
              status: 'disconnected',
              children: [],
              measureDefinitions: this.vibrationFormData
            })
          }
        } else if (this.sensorFormData.sensorDirection === 3 || this.sensorFormData.sensorDirection === 4) {
          baseTreeData[0].children.push(
            {
              id: null,
              title: '纵向振动值(V)',
              key: '0-0-1-0',
              treeIcon: '',
              status: 'disconnected',
              children: []
            },
            {
              id: null,
              title: '水平向振动值(H)',
              key: '0-0-1-1',
              treeIcon: '',
              status: 'disconnected',
              children: []
            },
            {
              id: null,
              title: '轴向振动值(A)',
              key: '0-0-1-2',
              treeIcon: '',
              status: 'disconnected',
              children: []
            }
          )
        }
        // baseTreeData[0].children.push(baseTreeData)
        // }
        this.PointsTreeData = baseTreeData
        // console.log(this.PointsTreeData)
      }
    },
    // 辅助方法，用于获取振动方向的标题
    getVibrationTitle(direction) {
      switch (direction) {
        case 1:
          return '垂直向振动值(V)'
        case 2:
          return '水平向振动值(H)'
        case 3:
          return '轴向振动值(A)'
        default:
          return '未知振动值'
      }
    },
    // 测试表格按钮
    textTable() {
      console.log(this.currentFormData)
    },
    handleFormUpdate() {
      // 处理表单数据更新
      console.log('Form data updated', this.currentFormData)
    },
    // 处理节点点击事件
    async handleNodeClick(selectedKeys, info) {
      console.log('点击节点的id:', info.node.dataRef.id)
      console.log('点击节点的treeIcon:', info.node.dataRef.treeIcon)

      // 保存当前节点的测量定义数据，避免切换后数据丢失
      if (this.currentDefPoints && this.currentDefPoints.treeIcon === 'measureDef' && this.vibrationFormData) {
        // 查找是否已存在相同ID的测量定义
        const existingIndex = this.modifiedMeasureDefinitions.findIndex(
          item => item.id === this.currentDefPoints.id
        )

        // 构建测量定义对象
        const measureDefObject = {
          id: this.currentDefPoints.id,
          title: this.currentDefPoints.title,
          treeIcon: this.currentDefPoints.treeIcon,
          status: this.currentDefPoints.status || 'disconnected',
          key: this.currentDefPoints.key,
          measureDefinitions: { ...this.vibrationFormData }
        }

        // 如果已存在则更新，否则添加
        if (existingIndex !== -1) {
          this.modifiedMeasureDefinitions[existingIndex] = measureDefObject
        } else {
          this.modifiedMeasureDefinitions.push(measureDefObject)
        }

        console.log('已保存当前测量定义:', JSON.stringify(measureDefObject, null, 2))
        console.log('所有已修改的测量定义:', this.modifiedMeasureDefinitions.length)
      }

      this.selectedPointKeys = selectedKeys

      // 立即设置视图类型
      this.currentView = info.node.dataRef.treeIcon === 'measureDef' ? 'vibration' : 'sensor'

      // 如果是测量定义节点，先显示遮罩层并加载数据
      if (info.node.dataRef.treeIcon === 'measureDef') {
        this.startComponentLoading()

        try {
          // 如果有ID，则从服务器获取数据
          if (info.node.dataRef.id) {
            const response = await getMeasurementDefine(info.node.dataRef.id)
            if (response && response.data) {
              this.vibrationFormData = {
                ...response.data,
                timeSignalUnitId: Number(response.data.timeSignalUnitId),
                spectrumUnitId: Number(response.data.spectrumUnitId),
                fastFourierTransformTypeId: Number(response.data.fastFourierTransformTypeId),
                dataOverlapRate: Number(response.data.dataOverlapRate),
                windowsFunctionId: Number(response.data.windowsFunctionId),
                envelopeFrequency: Number(response.data.envelopeFrequency),
                numberOfSpectralLines: Number(response.data.numberOfSpectralLines)
              }

              // 同时更新PointsTreeData中对应节点的measureDefinitions
              this.updatePointsTreeNodeMeasureDef(info.node.dataRef.key, this.vibrationFormData)
            }
          } else if (info.node.dataRef.measureDefinitions) {
            // 如果节点本身包含数据，直接使用
            this.vibrationFormData = {
              ...info.node.dataRef.measureDefinitions,
              timeSignalUnitId: Number(info.node.dataRef.measureDefinitions.timeSignalUnitId),
              spectrumUnitId: Number(info.node.dataRef.measureDefinitions.spectrumUnitId),
              fastFourierTransformTypeId: Number(info.node.dataRef.measureDefinitions.fastFourierTransformTypeId),
              dataOverlapRate: Number(info.node.dataRef.measureDefinitions.dataOverlapRate),
              windowsFunctionId: Number(info.node.dataRef.measureDefinitions.windowsFunctionId),
              envelopeFrequency: Number(info.node.dataRef.measureDefinitions.envelopeFrequency),
              numberOfSpectralLines: Number(info.node.dataRef.measureDefinitions.numberOfSpectralLines)
            }
          }

          // 更新当前节点信息
          this.currentDefPoints = {
            ...info.node.dataRef,
            measureDefinitions: this.vibrationFormData
          }

          // 确保数据加载完成后关闭遮罩层
          this.$nextTick(() => {
            this.stopComponentLoading()
          })
        } catch (error) {
          console.error('加载测量定义数据失败:', error)
          this.$message.error('加载测量定义数据失败')
          this.stopComponentLoading()
        }
      } else if (info.node.dataRef.treeIcon === 'bearing') {
        if (this.inAction === 'newNode') {
          this.currentView = 'sensor'
        }
        this.currentDefPoints = info.node.dataRef
      }
    },
    // 新增方法：更新PointsTreeData中指定key节点的测量定义数据
    updatePointsTreeNodeMeasureDef(key, measureDef) {
      const updateNodeData = (nodes) => {
        if (!nodes) return false

        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].key === key) {
            // 更新节点的measureDefinitions数据
            nodes[i].measureDefinitions = { ...measureDef }

            // 同步更新节点标题，确保显示最新的谱线数
            const newTitle = `${measureDef.measureDefineName} (${measureDef.lowerLimitFrequency}-${measureDef.upperLimitFrequency}HZ) (${measureDef.numberOfSpectralLines}线)`
            nodes[i].title = newTitle

            console.log('已更新PointsTreeData中的节点测量定义数据')
            return true
          }
          if (nodes[i].children && nodes[i].children.length > 0) {
            if (updateNodeData(nodes[i].children)) {
              return true
            }
          }
        }
        return false
      }

      updateNodeData(this.PointsTreeData)

      // 强制刷新树视图
      this.$nextTick(() => {
        if (this.$refs.treePointRef) {
          this.$refs.treePointRef.$forceUpdate()
        }
      })
    },
    // 处理节点展开事件
    handleNodeExpand(expandedKeys, info) {
      // console.log('Node expanded:', expandedKeys, info)
      this.PointsexpandedKeys = expandedKeys
    },
    // 渲染自定义图标
    renderIcon(h, { icon, expanded }) {
      // 返回一个包含图标的 <span> 元素
      return h('span', [icon])
    },

    // 更新树的一级节点标题
    updateTreeTitle(formData) {
      // 如果传入了表单数据，则处理更新
      if (formData) {
        // 如果是从 SensorConfigForm 组件接收的数据
        if (formData.name !== undefined && formData.serialNumber !== undefined) {
          // 更新测点名称和编号
          this.sensorFormDataname = formData.name
          this.sensorFormDataserialNumber = formData.serialNumber
          // 更新树显示
          this.updatePointsTreeData()
          return
        }

        // 如果是从 VibrationForm 组件接收的数据
        // 确保谱线数是数字类型
        formData.numberOfSpectralLines = Number(formData.numberOfSpectralLines)

        // 更新当前的表单数据
        this.vibrationFormData = { ...formData }

        // 保存到修改列表中
        if (this.currentDefPoints && this.currentDefPoints.treeIcon === 'measureDef') {
          // 构建测量定义对象
          const measureDefObject = {
            id: this.currentDefPoints.id,
            title: this.currentDefPoints.title,
            treeIcon: this.currentDefPoints.treeIcon,
            status: this.currentDefPoints.status || 'disconnected',
            key: this.currentDefPoints.key,
            measureDefinitions: { ...this.vibrationFormData }
          }

          // 更新标题
          const newTitle = `${formData.measureDefineName} (${formData.lowerLimitFrequency}-${formData.upperLimitFrequency}HZ) (${formData.numberOfSpectralLines}线)`
          measureDefObject.title = newTitle

          // 查找是否已存在
          const existingIndex = this.modifiedMeasureDefinitions.findIndex(
            item => item.key === this.currentDefPoints.key
          )

          // 如果已存在则更新，否则添加
          if (existingIndex !== -1) {
            this.modifiedMeasureDefinitions[existingIndex] = measureDefObject
          } else {
            this.modifiedMeasureDefinitions.push(measureDefObject)
          }

          console.log('已保存修改的测量定义:', this.modifiedMeasureDefinitions.length)

          // 更新当前节点标题
          this.currentDefPoints.title = newTitle

          // 同时更新在PointsTreeData中的节点标题
          this.updatePointsTreeNodeTitle(this.currentDefPoints.key, newTitle)

          // 强制刷新树视图
          this.$nextTick(() => {
            if (this.$refs.treePointRef) {
              this.$refs.treePointRef.$forceUpdate()
            }
            console.log('树视图已强制刷新', this.currentDefPoints.key, newTitle)
          })
        }
      }
    },

    // 新增方法：更新PointsTreeData中指定key的节点标题
    updatePointsTreeNodeTitle(key, newTitle) {
      const updateNodeTitle = (nodes) => {
        if (!nodes) return false

        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].key === key) {
            nodes[i].title = newTitle
            // 确保measureDefinitions中的值也被更新
            if (nodes[i].measureDefinitions) {
              nodes[i].measureDefinitions.numberOfSpectralLines = this.vibrationFormData.numberOfSpectralLines
              nodes[i].measureDefinitions.measureDefineName = this.vibrationFormData.measureDefineName
              nodes[i].measureDefinitions.lowerLimitFrequency = this.vibrationFormData.lowerLimitFrequency
              nodes[i].measureDefinitions.upperLimitFrequency = this.vibrationFormData.upperLimitFrequency
            }
            return true
          }
          if (nodes[i].children && nodes[i].children.length > 0) {
            if (updateNodeTitle(nodes[i].children)) {
              return true
            }
          }
        }
        return false
      }

      // 更新PointsTreeData中的节点标题
      updateNodeTitle(this.PointsTreeData)

      console.log('已更新PointsTreeData中节点标题:', newTitle)
    },

    // 搜索能
    performSearch() {
      if (!this.searchQuery) {
        this.filteredData = []
        return
      }
      const searchNodes = (nodes, query) => {
        return nodes
          .map(node => {
            const children = node.children ? searchNodes(node.children, query) : []
            // 如果当前节点匹配查询，返回该节点及其所有子节点
            if (node.title.includes(query)) {
              return {
                ...node,
                children: node.children || []
              }
            }
            // 如果子节点中有匹配的节点，返回该节点及其匹配的子节点
            if (children.length) {
              return {
                ...node,
                children
              }
            }
            return null
          })
          .filter(node => node)
      }

      this.filteredData = searchNodes(this.treeData, this.searchQuery)
      this.treeData = this.filteredData
    },
    // 获取树的列表
    getTreelist() {
      listDeviceManagement().then(res => {
        this.RealTreeData = res.data
        this.treeData = res.data

        // 对获取的树数据进行处理，确保测量定义节点标题正确显示
        this.processTreeNodes(this.treeData)

        // 如果有展开状态，需要重新应用
        if (this.expandedKeys.length > 0) {
          const keys = [...this.expandedKeys]
          this.$nextTick(() => {
            this.expandedKeys = []
            this.$nextTick(() => {
              this.expandedKeys = keys
              // 强制树组件重新渲染
              this.$refs.tree && this.$refs.tree.$forceUpdate()
            })
          })
        }
      })
    },
    // 新增方法：递归处理树节点，确保测量定义节点标题格式正确
    processTreeNodes(nodes) {
      if (!nodes) return

      nodes.forEach(node => {
        // 如果是测量定义节点，确保标题包含谱线数
        if (node.treeIcon === 'measureDef' && node.measureDefinitions) {
          const { measureDefineName, lowerLimitFrequency, upperLimitFrequency, numberOfSpectralLines } = node.measureDefinitions

          // 只有当这些值都存在时才更新标题
          if (measureDefineName && lowerLimitFrequency !== undefined && upperLimitFrequency !== undefined && numberOfSpectralLines !== undefined) {
            node.title = `${measureDefineName} (${lowerLimitFrequency}-${upperLimitFrequency}HZ) (${numberOfSpectralLines}线)`
          }
        }

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          this.processTreeNodes(node.children)
        }
      })
    },
    // 重置
    Reset() {
      this.treeData = this.RealTreeData
      this.PointsTreeData = []
      this.getDeviceModel()
      this.setTreeData(this.RealTreeData)
      // 重置当前选中的节点
      this.currentDefPoints = null
      // 重置已修改的测量定义数组
      this.modifiedMeasureDefinitions = []
      this.sensorFormData = {
        serialNumber: '',
        name: '',
        speed: '',
        instrument: '',
        vibrationDirection: '',
        sensorDirection: '',
        collectorName: '', // 传感器名称
        collectorModel: '' // 传感器的型号
      }
      this.vibrationFormData = {
        measureDefineName: '', // 名称
        timeSignalUnitId: '', // 时间信号单位
        spectrumUnitId: '', // 频谱单位
        upperLimitFrequency: '', // 上限频率HZ
        lowerLimitFrequency: '', // 下限频率HZ
        windowsFunctionId: '', // 窗函数
        numberOfSpectralLines: '', // 谱线数
        envelopeFrequency: '', // 包络频率
        fastFourierTransformTypeId: '', // 快速傅里叶变化类型
        averageType: '', // 平均类型
        averageValue: '', // 平均次数
        dataOverlapRate: '', // 数据重叠率
        firstAlarmValue: '', // 一级报警值
        secondAlarmValue: '', // 二级报警值
        thirdAlarmValue: '', // 一级报警值
        forthAlarmValue: '' // 二级报警值
      }
      this.mainUseEdit = false
    },
    // 获取Icon
    getIcon(props) {
      // console.log(props)
      const { treeIcon, status } = props.dataRef

      let iconClass = treeIcon
      // 判断icon状态
      switch (status) {
        // 预警状态
        case 'warning':
          iconClass = `${treeIcon}-warningValue`
          break
        // 危险状态
        case 'danger':
          iconClass = `${treeIcon}-dangerValue`
          break
        // 连接状态
        case 'connected':
          iconClass = `${treeIcon}-connectedData`
          break
        // 未连接状态
        case 'disconnected':
          iconClass = `${treeIcon}-disconnectedData`
          break
        default:
          iconClass = treeIcon
      }
      // 返回icon的DOM元素
      return <svg-icon icon-class={iconClass} style='width: 1.5em; height: 1.5em;'/>
    },
    // 用于更新Icon状态
    updatetreeIcon(key, newStatus) {
      const updateStatus = (data) => {
        return data.map(item => {
          if (item.key === key) {
            return { ...item, status: newStatus }
          }
          if (item.children) {
            return { ...item, children: updateStatus(item.children) }
          }
          return item
        })
      }

      this.treeData = updateStatus(this.treeData)
    },
    // 当状态改变时调用这个函数更新图标
    someConditionFunction() {
      this.updatetreeIcon('0-0', 'warning')
    },
    // 获取当前选中节点信息
    handleSelect(keys, event) {
      const newSelectedKey = keys[0]
      // 如果没有选中任何节点，直接返回
      if (keys.length === 0) {
        this.heightLight = null
        this.setSelectedKey(null)
        this.currentNode = null
        return
      }
      // 保持高亮状态
      if (keys.length === 0) {
        // console.log(this.heightLight)
        this.setSelectedKey(this.heightLight)
      } else {
        this.heightLight = keys[0]
        this.setSelectedKey(newSelectedKey)
      }

      // 设置当前节点为选中节点
      if (event.selectedNodes.length > 0) {
        this.currentNode = this.findNodeByKey(this.treeData, newSelectedKey)
        console.log(this.currentNode)
        this.updateTreePathText(this.currentNode)

        // 切换右边节点信息组件
        this.handleNodeAction(this.currentNode)
      } else {
        this.currentNode = null
      }
    },
    // 包装的处理方法，根据 treeIcon 字段触发不同的操作
    handleNodeAction(node) {
      const actionMap = {
        'factory': this.handleFactoryAction,
        'grading': this.handleGradingAction,
        'waterPump': this.handleWaterPumpAction,
        'bearing': this.handleBearingAction,
      }

      const action = actionMap[node.treeIcon]
      // console.log(action)
      if (action) {
        action.call(this, node)
      } else {
        console.log('未定义的 treeIcon 操作')
      }
    },

    // 处理 factory 类型的操作(公司)
    handleFactoryAction(node) {
      // console.log('选中了 Factory 类型的节点:', node)
      this.$emit('switch-component', 'FactoryComponent', node)
    },

    // 处理 grading 类型的操作(厂区)
    handleGradingAction(node) {
      // console.log('选中了 Grading 类型的节点:', node)
      this.$emit('switch-component', 'GradingComponent', node)
    },

    // 处理 waterPump 类型的操作(设备)
    handleWaterPumpAction(node) {
      // console.log('选中了 Water Pump 类型的节点:', node)
      this.$emit('switch-component', 'WaterPumpComponent', node)
    },

    // 处理 bearing 类型的作(轴承)
    handleBearingAction(node) {
      // console.log('选中了 Bearing 类型的节点:', node)
      this.$emit('switch-component', 'BearingComponent', node)
    },

    updateTreePathText(node) {
      let path = []
      const findPath = (nodes, currentPath) => {
        nodes.forEach(n => {
          const newPath = [...currentPath, n.title]
          if (n.key === node.key) {
            path = newPath
          } else if (n.children) {
            findPath(n.children, newPath)
          }
        })
      }
      findPath(this.treeData, [])
      const newPathText = path.join('/')
      this.setTreePathText(newPathText)
    },
    // 双击节点时打开弹窗，并设置当前编辑节点的信息
    // handleDoubleClick(event, node) {
    handleDoubleClick(event, node) {
      this.nodeToEdit = node.dataRef
      console.log(this.nodeToEdit)
      this.dialogTitle = this.findNodeByKey(this.treeData, this.nodeToEdit.key).title
      this.dialogVisible = true
    },
    handleEditClick() {
      this.nodeToEdit = this.currentNode
      this.dialogTitle = this.findNodeByKey(this.treeData, this.nodeToEdit.key).title
      this.dialogVisible = true
    },
    // 获取节点所有信息
    findNodeByKey(data, key) {
      let foundNode = null
      const findNode = (data) => {
        data.forEach((item) => {
          if (item.key === key) {
            foundNode = item
          } else if (item.children) {
            findNode(item.children)
          }
        })
      }
      findNode(data)
      return foundNode
    },
    // 点击模态框的"确定"按钮时，更新节点标题并关闭弹窗
    handleDialogOk() {
      if (this.nodeToEdit) {
        // console.log(this.nodeToEdit.dataRef)
        this.updateNodeTitle(this.nodeToEdit.id, this.dialogTitle)
        this.nodeToEdit = null
        this.dialogVisible = false
      }
    },
    // 点击模态框的"取消"按钮时，关闭弹窗
    handleDialogCancel() {
      this.nodeToEdit = null
      this.dialogVisible = false
    },
    // 细化厂区结构的确定
    handleNewNodeOk() {
      // 确保关闭所有可能的遮罩层
      this.stopComponentLoading()

      if (!this.currentNode) {
        this.$message.error('请先选择一个节点')
        return
      }

      if (!this.newNodeTitle || this.newNodeTitle.trim() === '') {
        this.$message.error('请输入区域名称')
        return
      }

      // 构建新节点数据
      const newAreaList = {
        id: this.currentNode.id,
        treeIcon: 'grading',
        title: this.newNodeTitle.trim()
      }

      // 调用添加接口
      addFactory(newAreaList).then(res => {
        this.getTreelist()
        this.newNodeTitle = ''
        this.newNodeDialogVisible = false
        this.$message.success('新节点已添加')
      }).catch(error => {
        console.error('添加节点失败:', error)
        this.$message.error('新节点添加失败')
      })
    },
    // 新建下一区域的取消
    handleNewNodeCancel() {
      // 确保关闭所有可能的遮罩层
      this.stopComponentLoading()
      this.newNodeTitle = ''
      this.newNodeDialogVisible = false
    },
    // 新建设备信息确定
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 从 formItems 中找到设备名称和设备编号对应的 prop
          const namePropItem = this.formItems.find(item => item.label === '名称')
          const numberPropItem = this.formItems.find(item => item.label === '设备编号')
          // 从 form 中获取设备名称和设备编号的值
          const deviceName = namePropItem ? this.form[namePropItem.prop] : ''
          const deviceNumber = numberPropItem ? this.form[numberPropItem.prop] : ''

          // 组合 title
          const deviceTitle = `${deviceNumber}_${deviceName} `

          const deviceData = {
            id: this.currentNode.id, // 父节点id
            treeIcon: 'waterPump', // 图标
            status: 'disconnected', // 状态
            title: deviceTitle // 组合后的 title
          }
          if (this.formItemsStagingArea.mysql_id === '' || this.formItemsStagingArea.mysql_id === undefined) {
            // 获取新建出来的设备的mysql_id
            addFactory(deviceData).then(res => {
              const SettingData = {
                mysql_id: res.data,
                version: this.formItemsStagingArea.version,
                formItems: this.formItems,
                form: this.form
              }
              deviceConfigData(SettingData).then(res => {
                this.getTreelist()
                this.$message.success('新设备已添加成功')
                this.expandedKeys.push(this.currentNode.key)
                this.Reset()
              })
            })
          } else {
            // 以下是编辑状态下的保存
            const SettingData = {
              mysql_id: this.formItemsStagingArea.mysql_id,
              version: this.formItemsStagingArea.version,
              formItems: this.formItems,
              form: this.form
            }
            console.log(this.currentNode)
            const DeviceChangeData = {
              id: this.currentNode.id,
              title: deviceTitle
            }
            // 先更改树名称
            updateTreeData(DeviceChangeData).then(() => {
              deviceConfigData(SettingData).then(res => {
                this.getTreelist()
                this.$message.success('设备更新成功')
                this.expandedKeys.push(this.currentNode.key)
                this.Reset()
              })
            })
          }
          this.newDeviceDialogVisible = false
          // this.currentNode.children.push({ title: '新建设备', key: `${this.currentNode.key}-newDevice`, treeIcon: 'bearing' })
          // 执行保存操作，例如调用 API 提交表单
        } else {
          console.log('表单验证失败，无法保存')
        }
      })
    },
    // 新建设备信息取消
    handleCancel() {
      this.uploadImageDialogVisible = false  // 添加这行来关闭弹窗
      this.clearUpload()
    },

    // 词条设置
    handleTermSettings() {
      this.termSettingsDialogVisible = true
    },
    // 新建设备确定
    handleTermSettingsOk() {
      // 数据验证
      const invalidItems = this.formItems.filter(item => !item.label.trim())
      if (invalidItems.length) {
        this.$message.error('请填写所有标签文本！')
        return
      }
      // 重新获取模板
      getDeviceTemplateData().then(res => {
        // 存储表单模板
        this.formItemsStagingArea = res
      })
      // 文本项赋值
      this.formItemsStagingArea.formItems = this.formItems
      updateTemplateData(this.formItemsStagingArea).then(res => {
        this.$message.success('词条设置成功')
        this.termSettingsDialogVisible = false
      }).catch(() => {
        this.$message.error('词条设置失败')
      })
    },
    // 删除
    deleteRow(index) {
      this.$confirm('确认删除此行吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formItems.splice(index, 1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 添加新的设备文本信息
    addNewRow() {
      this.formItems.push({
        label: '',
        prop: '',
        placeholder: '请输入',
        lock: false,
        isRequired: false, // 默认值
        rules: []
      })
    },
    // 更新 prop 字段（暂时用时间戳表示）
    updateProp(row) {
      if (row.label) {
        // 生成当前时间戳
        row.prop = Date.now().toString()
        row.placeholder = '请输入' + row.label
        row.rules[0].message = row.label + '不能为空'
        console.log(row)
      } else {
        row.prop = ''
      }
    },
    handleLockChange(row) {
      // console.log(row)
      if (row.isRequired) {
        row.rules = [
          {
            required: 'true',
            message: '序列号不能为空',
            trigger: 'blur'
          }] // 设置必填
      } else if (!row.isRequired) {
        row.rules = [] // 设置必填
      }
    },
    // 词条设置取消
    handleTermSettingsCancel() {
      this.getDeviceModel()
      this.termSettingsDialogVisible = false
    },
    // 新建测量点确定(暂时无法分辨是那个组件提交需要更改方法)
    handleMeasuringPointsSave() {
      // this.$nextTick 确保在执行 validate 方法之前，DOM 中的 additionalInfoForm 元素已经渲染完成。
      console.log("新建测点保存时的currentNode ==> ",this.currentNode );

      this.$nextTick(() => {
        // 下面两个判断均查看提交的表单是否有效(必填项是否都填了)
        const dynamicComponentRef = this.$refs.componentRef.$refs.SensorConfigFormRef // 获取组建的ref
        // console.log(this.$refs)
        if (dynamicComponentRef) {
          dynamicComponentRef.validate((valid) => {
            if (valid) {
              // 使用缓存的节点ID
              const nodeId = this.cachedNodeId || (this.currentNode && this.currentNode.id);
              this.PointsTreeData[0].id = nodeId // 父节点赋值
              // 将节点信息赋值给data
              const data = this.PointsTreeData[0]
              // 重新赋值给内定的数据
              const FormData = this.sensorFormData
              console.log('表单赋值数据-->',FormData);

              // console.log(this.inAction)
              if (this.mainUseEdit) {
                // 筛选删除的数组
                if (data && data.children && data.children.length > 0) {
                  data.children.forEach(child => {
                    const index = this.DeletePointNode.indexOf(child.id)
                    if (index !== -1) {
                      // 从 DeletePointNode 数组中移除
                      this.DeletePointNode.splice(index, 1)
                    }
                  })
                }
                // console.log(JSON.stringify(data))
                batchUpdateDevicePoint(data).then(res => {
                  const measurePointData = {
                    id: this.currentNode.id, // 新建的时候发送测点id
                    sensorCoding: FormData.serialNumber, // 测点编号
                    sensorName: FormData.name, // 测点名称
                    deviceRpm: FormData.speed, // 设备转速
                    sensorType: FormData.sensorDirection, // 传感器类型
                    deviceId: nodeId, // 设备id
                    sensorDirection: FormData.vibrationDirection // 振动方向
                  }
                  // updateDevicePoint是更改测点的请求方法
                  updateDevicePoint(measurePointData).then(res => {
                    // 编辑的时候删除节点
                    if (this.inAction === 'newNode' && this.DeletePointNode.length > 0) {
                      delTreeNode(this.DeletePointNode)
                      this.getTreelist()
                    }
                    this.getTreelist()
                    this.$message.success('测量点已保存成功')
                    this.cachedNodeId = null; // 清除缓存的ID
                    this.expandedKeys.push(nodeId)
                    //清理状态
                    this.selectedPointKeys = [];
                    this.currentDefPoints = null;
                    this.cachedNodeId = null; // 清除缓存的ID
                    this.Reset();
                  })
                })
              } else {
                batchAddDevice(JSON.stringify(data)).then(res => {
                  const measurePointData = {
                    id: res.data, // 新建的时候发送测点id
                    sensorCoding: FormData.serialNumber, // 测点编号
                    sensorName: FormData.name, // 测点名称
                    deviceRpm: FormData.speed, // 设备转速
                    //传感器名称
                    collectorName: FormData.collectorName,
                    //传感器型号
                    collectorModel: FormData.collectorModel,
                    sensorType: FormData.sensorDirection, // 传感器类型
                    deviceId: nodeId, // 设备id
                    sensorDirection: FormData.vibrationDirection // 振动方向
                  }
                  addDevicePoint(measurePointData).then(res => {
                    if (res.code === 200) {
                      this.getTreelist();
                      this.$message.success('新测量点已添加成功');
                      this.expandedKeys.push(this.currentNode.key);
                    } else {
                      // 如果响应不是 200，弹出失败提
                      this.$message.error('添加测量点失败，请稍后重试');
                    }
                  }).catch(error => {
                    // 捕获任何错误并提示
                    // this.$message.error('添加测量点失败，请稍后重试');
                    console.error('添加测量点请求失败:', error);
                  });
                })
              }
              this.newMeasuringPointsDialogVisible = false
              this.Reset()
            }
          })
        } else {
          // 保存当前正在编辑的测量定义
          if (this.currentDefPoints && this.currentDefPoints.treeIcon === 'measureDef') {
            // 当前测量定义对象
            const currentMeasureDef = {
              id: this.currentDefPoints.id,
              title: this.currentDefPoints.title,
              treeIcon: this.currentDefPoints.treeIcon,
              status: this.currentDefPoints.status || 'disconnected',
              key: this.currentDefPoints.key,
              measureDefinitions: { ...this.vibrationFormData }
            }

            // 查找是否已存在
            const existingIndex = this.modifiedMeasureDefinitions.findIndex(
              item => item.key === this.currentDefPoints.key
            )

            // 如果已存在则更新，否则添加
            if (existingIndex !== -1) {
              this.modifiedMeasureDefinitions[existingIndex] = currentMeasureDef
            } else {
              this.modifiedMeasureDefinitions.push(currentMeasureDef)
            }
          }

          const nodeId = this.cachedNodeId || (this.currentNode && this.currentNode.id);

          // 打印要提交的测量定义表单数据
          console.log('当前测量定义表单数据:', JSON.stringify(this.vibrationFormData, null, 2));
          console.log('已修改的测量定义总数:', this.modifiedMeasureDefinitions.length);

          // 构建提交到接口的数据结构
          let submitData = [];

          // 根据父节点ID进行分组
          const groupedByParent = {};
          this.modifiedMeasureDefinitions.forEach(def => {
            // 从树结构中找到父节点ID
            const parentNode = this.findParentNode(this.PointsTreeData, def.key);
            const parentId = parentNode ? parentNode.id : nodeId;

            if (!groupedByParent[parentId]) {
              groupedByParent[parentId] = {
                id: parentId,
                title: parentNode ? parentNode.title : '未知节点',
                treeIcon: 'bearing',
                status: 'disconnected',
                children: []
              };
            }

            // 添加子节点
            groupedByParent[parentId].children.push({
              id: def.id,
              title: def.title,
              treeIcon: def.treeIcon,
              status: def.status,
              measureDefinitions: def.measureDefinitions
            });
          });

          // 将分组后的数据转换为数组
          submitData = Object.values(groupedByParent);

          console.log('提交到接口的数据结构:', JSON.stringify(submitData, null, 2));

          // 如果没有修改过任何测量定义，构建默认的提交数据
          if (submitData.length === 0 && this.PointsTreeData && this.PointsTreeData.length > 0) {
            const dataRemove = [...this.PointsTreeData[0].children]
            dataRemove.splice(0, 1)
            submitData = dataRemove;
          }

          batchAddMeasurementDefine(submitData).then(res => {
            if (this.deletedNodeIds.length > 0) {
              const ids = this.deletedNodeIds.join(',') // 将数组元素用逗号拼接成字符串 "1,2"
              console.log('要删除的ID:', ids);

              batchDelMeasurementDefine(ids).then(res => {
                this.$message.success('已删除不需要的测量定义')
              })
            }
            this.$message.success('测量定义保存成功')

            // 保存当前选中节点和展开状态，用于在树刷新后恢复
            const currentSelectedKey = this.selectedPointKeys.length > 0 ? this.selectedPointKeys[0] : null
            const currentExpandedKeys = [...this.expandedKeys]

            // 加入当前节点ID到展开节点列表
            const nodeId = this.cachedNodeId || (this.currentNode && this.currentNode.id)
            if (nodeId && !currentExpandedKeys.includes(nodeId)) {
              currentExpandedKeys.push(nodeId)
            }

            // 获取最新的树数据
            this.getTreelist()

            // 在数据加载完成后恢复选中和展开状态
            this.$nextTick(() => {
              // 如果有之前选中的节点，重新选中它
              if (currentSelectedKey) {
                this.selectedPointKeys = [currentSelectedKey]

                // 查找新树数据中对应的节点，确保显示正确的数据
                const findNodeInTree = (key, nodes) => {
                  if (!nodes) return null
                  for (const node of nodes) {
                    if (node.key === key) {
                      return node
                    }
                    if (node.children && node.children.length > 0) {
                      const found = findNodeInTree(key, node.children)
                      if (found) return found
                    }
                  }
                  return null
                }

                // 找到树中匹配的节点
                const matchedNode = findNodeInTree(currentSelectedKey, this.treeData)
                if (matchedNode && matchedNode.treeIcon === 'measureDef') {
                  // 需要手动触发节点点击以更新表单
                  this.handleNodeClick([currentSelectedKey], { node: { dataRef: matchedNode } })
                }
              }

              // 恢复展开的节点
              this.expandedKeys = currentExpandedKeys
            })

            this.newMeasuringPointsDialogVisible = false

            // 清空修改状态和缓存
            this.currentDefPoints = null
            this.modifiedMeasureDefinitions = []
            this.Reset()
          }).catch((error) => {
            console.error('保存测量定义失败:', error)
            this.$message.error('测量定义保存失败')
          })
        }
      })
    },
    // 新建测量点取消
    handleMeasuringPointsCancel() {
      this.newMeasuringPointsDialogVisible = false
      this.selectedPointKeys = []
      this.PointsexpandedKeys = []
      this.cachedNodeId = null; // 清除缓存的ID
      this.Reset()
      this.getTreelist()
    },
    // 更新节点标题的方法
    updateNodeTitle(id, title) {
      const updataList = {
        id: id,
        title: title
      }
      updateTreeData(updataList).then(res => {
        this.getTreelist()
        this.$message.success('节点标题已更新')
      })
    },
    // 右击树节点时触发的事件，显示右击菜单
    handleRightClick(event) {
      /* if (!this.$refs.rightClickMenu.$el.contains(event.target)) {
        this.$refs.rightClickMenu.show(event, this.getContextMenuOptions())
      } */
      // 阻止默认右键菜单
      event.preventDefault()
      event.stopPropagation()

      // 如果是右键菜单本身,不做处理
      if (this.$refs.rightClickMenu?.$el?.contains(event.target)) {
        return
      }

      // 如果没有选中节点或者点击的是空白区域,不显示右键菜单
      if (!this.currentNode) {
        return
      }

      // 确保先隐藏任何已经存在的菜单
      this.$refs.rightClickMenu?.hide?.()

      // 获取菜单选项
      const menuOptions = this.getContextMenuOptions()

      // 确保有选项时才显示菜单
      if (menuOptions && menuOptions.length > 0) {
        this.$nextTick(() => {
          this.$refs.rightClickMenu?.show?.(event, menuOptions)
        })
      }
    },
    // 右击事件获取节点信息
    handleRightClickOnNode({ event, node }) {
      // 检查是否是测量定义节点，如果是则阻止右键菜单
      if (node && node.dataRef.treeIcon === 'measureDef') {
        event.preventDefault()
        event.stopPropagation()
        return // 直接返回，不显示右键菜单
      }
      // 获取父组件容器的边界
      const container = this.$refs.treeContainer

      if (container) {
        // 获取父组件容器的边界，转换为普通对象
        const domRect = container.getBoundingClientRect()
        this.containerRect = {
          top: domRect.top,
          left: domRect.left,
          width: domRect.width,
          height: domRect.height,
          bottom: domRect.bottom,
          right: domRect.right
        }
      }
      // console.log(this.containerRect)
      if (node) {
        console.log('右击获取到的节点数据:', node.dataRef)
        this.currentNode = this.findNodeByKey(this.treeData, node.eventKey)
        console.log("currentNode ==> ",this.currentNode);

      }
    },
    // handleMenuClick方法,this指向组件中的方法
    handleMenuClick(action) {
      this.inAction = action

      // 缓存当前节点ID
      if(this.currentNode && this.currentNode.id) {
        this.cachedNodeId = this.currentNode.id;
      }

      if (this.inAction === 'edit') {
        // console.log(this.currentNode.treeIcon)
        rightClickHandlers.handleMenuClick(action, this, this.currentNode.treeIcon)
      } else {
        rightClickHandlers.handleMenuClick(action, this)
      }
    },
    // 右击事件
    getContextMenuOptions() {
      if (!this.currentNode) return []

      // 通用菜单
      const commonOptions = [
        { label: '编辑', action: 'edit', shortcut: 'ENTER' },
        { label: '删除', action: 'delete', shortcut: 'Del' }
      ]

      // 如果是公司节点或厂区节点,添加上传图片选项
      if (this.currentNode.treeIcon === 'company' || this.currentNode.treeIcon === 'grading') {
        commonOptions.push({
          label: '上传图片',
          action: 'uploadImage'
        })
      }

      const level = this.getNodeLevel(this.currentNode.treeIcon)

      // console.log('等级', level)
      switch (level) {
        case 1: // 公司
          return [
            { label: '新建', subMenu: [
              { label: '细化厂区结构', action: 'newArea', icon: 'el-icon-folder-opened' },
              { label: '设备', action: 'newDevice', icon: 'el-icon-setting' }
            ] },
            ...commonOptions
          ]
        case 2: // 厂区
          return [
            { label: '新建', subMenu: [
              { label: '细化厂区结构', action: 'newArea', icon: 'el-icon-folder-opened' },
              { label: '设备', action: 'newDevice', icon: 'el-icon-setting' }
            ] },
            ...commonOptions
          ]
        case 3: // 设备
          return [
            { label: '新建', subMenu: [
              { label: '测量点', action: 'newNode', icon: 'el-icon-view' }
            ] },
            ...commonOptions
          ]
        case 4: // 测点
          return [
            { label: '新建', subMenu: [
              { label: '测量定义', action: 'newDefinition', icon: 'el-icon-edit' }
            ] },
            ...commonOptions
          ]
        default:
          return commonOptions
      }
    },
    // 计算树的等级
    getNodeLevel(treeIcon) {
      // console.log(treeIcon)
      // 根据不同的 nodeType 返回相应的等级
      switch (treeIcon) {
        case 'company':
          return 1
        case 'grading':
          return 2
        case 'waterPump':
          return 3
        case 'bearing':
          return 4
        default:
          return 0 // 或者根据需求设置一个默认等级
      }
    },

    // 深度克隆节点数据，排除循环引用
    deepClone(data) {
      return JSON.parse(JSON.stringify(data, (key, value) => {
        if (key === '_isVue') return undefined
        return value
      }))
    },

    // 复制节点到剪贴板
    copyNodeToClipboard() {
      if (this.currentNode) {
        const nodeData = this.deepClone(this.currentNode)
        this.clipboard = nodeData
        message.success('节点已复制到剪贴板')
      }
    },
    // 剪切节点
    cutNode() {
      if (this.currentNode) {
        this.clipboard = this.deepClone(this.currentNode)
        this.deleteNode(this.treeData, this.currentNode.key)
        message.success('节点已剪切到剪贴板')
      }
    },
    // 粘贴
    pasteNodeAndOverwrite() {
      if (this.clipboard && this.currentNode) {
        const newNodeData = this.deepClone(this.clipboard)
        if (!this.currentNode.children) {
          this.currentNode.children = []
        }
        this.currentNode.children.push(newNodeData)
        message.success('节点数据已粘贴')
      } else {
        message.error('没有可粘贴的数据或未选中目标节点')
      }
    },
    deleteTreeItem(id) {
      this.$modal.confirm('是否删除节点').then(() => {
        console.log(this.inAction)
        delTreeNode(id).then(res => {
          this.getTreelist()
          this.$message.success('删除成功')
        })
      }).catch(() => {})
    },

    expandAll() {
      const allKeys = this.getAllKeys(this.treeData)
      this.$nextTick(() => {
        this.expandedKeys = []
        this.$nextTick(() => {
          this.expandedKeys = allKeys
          // 强制树组件重新渲染
          this.$refs.tree && this.$refs.tree.$forceUpdate()
        })
      })
    },

    collapseAll() {
      this.$nextTick(() => {
        this.expandedKeys = []
        // 强制树组件重新渲染
        this.$refs.tree && this.$refs.tree.$forceUpdate()
      })
    },

    getAllKeys(data) {
      let keys = []
      if (Array.isArray(data)) {
        data.forEach(item => {
          keys.push(item.key)
          if (item.children) {
            keys = keys.concat(this.getAllKeys(item.children))
          }
        })
      }
      return keys
    },
    // 更新当前展开的节点键，以确保用户手动展开或收缩某个节点时状态能够保持。
    handleExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    },
    handleContainerClick(event) {
      // 检查点击是否发生在树节点或对话框中
      const isTreeNode = event.target.closest('.ant-tree-node-content-wrapper') ||
                        event.target.closest('.ant-tree-title') ||
                        event.target.closest('.ant-tree-switcher')
      const isDialog = event.target.closest('.el-dialog') ||  // 检查是否点击在对话框内
                      event.target.closest('.el-input')    // 检查是否点击在输入框内

      // 如果点击的不是树节点且不在对话框内，则清除选中状态
      if (!isTreeNode && !isDialog) {
        this.setSelectedKey(null)
        this.heightLight = null
        this.currentNode = null
        // 如果右键菜单是显示状态，也需要隐藏
        if (this.$refs.rightClickMenu) {
          this.$refs.rightClickMenu.hide()
        }
      }
    },
    // 处理文件选择
    handleFileChange(file) {
      this.imageFile = file.raw
    },

    // 处理图片上传
    handleImageUpload() {
      if (!this.imageFile) {
        this.$message.error('请选择要上传的图片')
        return
      }

      // 使用缓存的节点信息
      if (!this.uploadNodeInfo || !this.uploadNodeInfo.id) {
        this.$message.error('请先选择要上传图片的公司节点')
        return
      }

      const formData = new FormData()
      formData.append('img', this.imageFile)
      formData.append('id', this.uploadNodeInfo.id)

      uploadCompanyImage(formData).then(response => {
        if (response.code === 200) {
          this.$message.success('图片上传成功')
          this.uploadImageDialogVisible = false
          this.clearUpload()
          this.getTreelist()
        } else {
          this.$message.error(response.msg || '图片上传失败')
        }
      }).catch(error => {
        console.error('上传失败:', error)
        this.$message.error('图片上传失败')
      })
    },
    // 处理对话框关闭
    handleDialogClose() {
      this.clearUpload()
    },

    // 清除上传文件和缓存信息
    clearUpload() {
      this.imageFile = null
      this.uploadNodeInfo = null  // 清除缓存的节点信息
      // 清除上传组件的文件列表
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    },
    // 在打开新建厂区对话框的方法中添加检查
    openNewNodeDialog() {
      if (!this.currentNode) {
        this.$message.error('请先选择一个节点')
        return
      }
      this.newNodeDialogVisible = true
    },
    // 添加这两个方法来处理左侧边栏的展开/收起事件
    handleExpandAll() {
      const allKeys = this.getAllKeys(this.treeData)
      this.expandedKeys = allKeys
    },

    handleCollapseAll() {
      this.expandedKeys = []
    },
    handleMeasurementPointRefresh() {
      // 如果有当前选中的测量定义节点
      if (this.currentNode && this.currentNode.treeIcon === 'measureDef') {
        // 重新获取该节点的测量定义数据
        getMeasurementDefine(this.currentNode.id).then(res => {
          if (res.code === 200 && res.data) {
            // 更新当前节点的测量定义数据
            this.currentNode.measureDefinitions = res.data;

            // 根据最新的测量定义数据更新节点标题
            const updatedTitle = this.formatMeasureDefTitle({
              ...this.currentNode,
              measureDefinitions: res.data
            });

            // 更新节点标题在树中的显示
            this.updateNodeTitleInTree(this.currentNode.key, updatedTitle);
          }
        }).finally(() => {
          // 无论成功与否，都重新获取整个树数据
          this.getTreelist();
        });
      } else {
        // 如果没有当前选中的测量定义节点，直接刷新树
        this.getTreelist();
      }
    },

    // 更新树中指定节点的标题
    updateNodeTitleInTree(key, newTitle) {
      const updateNodeTitle = (nodes) => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].key === key) {
            nodes[i].title = newTitle;
            return true;
          }
          if (nodes[i].children && nodes[i].children.length > 0) {
            if (updateNodeTitle(nodes[i].children)) {
              return true;
            }
          }
        }
        return false;
      };

      // 更新树数据中的节点标题
      updateNodeTitle(this.treeData);

      // 强制树组件重新渲染
      this.$nextTick(() => {
        this.$refs.tree && this.$refs.tree.$forceUpdate();
      });
    },

    // 格式化测量定义节点的标题，确保显示最新的谱线数
    formatMeasureDefTitle(dataRef) {
      // 如果节点有保存的测量定义数据，使用它来格式化标题
      if (dataRef.measureDefinitions) {
        const title = dataRef.title;
        // 提取标题中的基本信息（不包括谱线数）
        const measureType = title.split('(')[0].trim(); // 如"加速度"
        const frequencyRange = title.match(/\(([^)]+)\)/); // 提取频率范围如"2-10000HZ"

        if (frequencyRange && frequencyRange[1]) {
          // 获取最新的谱线数
          const numberOfLines = dataRef.measureDefinitions.numberOfSpectralLines ||
                              dataRef.measureDefinitions.numberOfLines ||
                              title.match(/\((\d+)线\)/)?.[1] || "";

          // 返回格式化后的标题
          return `${measureType} (${frequencyRange[1]}) (${numberOfLines}线)`;
        }
      }

      // 如果无法解析或没有必要的数据，则返回原始标题
      return dataRef.title;
    }
  }
}
</script>

<style scoped>
@import './tree.css'
</style>
