<template>
  <div style="height: 100%;">
    <!-- <el-dialog
      :visible.sync="visible"
      title="轴承弹窗"
      :close-on-click-modal="false"
      width="60%"
      @close="handleCancel"
    > -->
    <div style="height: 90%;">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm">
          <el-form-item label="制造厂商">
            <el-select
              v-model="queryParams.manufacturer"
              placeholder="请选择制造厂商"
              clearable
              filterable
              allow-create
              default-first-option
              style="width: 200px;">
              <el-option
                v-for="manufacturer in manufacturers"
                :key="manufacturer"
                :label="manufacturer"
                :value="manufacturer"
              />
            </el-select></el-form-item>

          <el-form-item label="型号">
            <el-input v-model="queryParams.model" placeholder="请输入型号" style="width: 200px;" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="handleAdd">添加</el-button>
          </el-form-item>
          <el-form-item>

            <el-dropdown size="mini" type="primary" placement="bottom" trigger="hover">
              <el-button type="primary" size="mini">
                本地轴承
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <!-- 加上.native可以把自定义事件转变为原生的DOM事件，即给父元素的子节点们都加上这个click事件 -->
                <el-dropdown-item @click.native="handleCreateBearing">新建本地轴承</el-dropdown-item>
                <el-dropdown-item @click.native="handleEditBearing">编辑本地轴承</el-dropdown-item>
                <el-dropdown-item @click.native="handleDeleteBearing">删除本地轴承</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

          </el-form-item>

        </el-form>
      </div>
      <!-- 表格内容 -->
      <el-table
        :data="bearingLibraryData"
        height="90%"
        style="width: 100%;"
        border
        highlight-current-row
        @current-change="handleBearingLibraryRowChange"
        @row-dblclick="handleAdd"
      >
        <el-table-column prop="manufacturer" label="制造厂商" />
        <el-table-column prop="model" label="型号" />
        <el-table-column prop="numberOfBalls" label="滚动体数目" />
        <el-table-column prop="bpfo" label="BPFO" />
        <el-table-column prop="bpfi" label="BPFI" />
        <el-table-column prop="bsf" label="BSF" />
        <el-table-column prop="ftf" label="FTF" />
      </el-table>
    </div>
    <!-- 分页 -->
    <pagination
      v-show="total>0"
      style="padding: 0;"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- </el-dialog> -->
    <!-- 轴承信息弹窗 -->
    <el-dialog
      :visible.sync="showBearingInfoDialog"
      title="轴承信息"
      :close-on-click-modal="false"
      width="60%"
      @close="handleBearingInfoCancel"
    >
      <div>
        <!-- 上部分内容 -->
        <div class="dialog-section">
          <div class="section-title">基本信息</div>
          <el-form ref="bearingForm" :model="bearingForm" label-width="150px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="制造厂商">
                  <el-input v-model="bearingForm.manufacturer" size="mini" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="型号">
                  <el-input v-model="bearingForm.model" size="mini" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="滚动体数目">
                  <el-input v-model="bearingForm.numberOfBalls" size="mini" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="名称">
                  <el-input v-model="bearingForm.description" size="mini" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="内圈或外圈">
                  <el-select v-model="bearingForm.rotationAngle" size="mini" placeholder="请选择">
                    <el-option label="内圈旋转" value="0" />
                    <el-option label="外圈旋转" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 下部分内容 -->
        <div class="dialog-section">
          <div class="section-title">轴承数据</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="leftSection">
                <div class="section-subtitle">尺寸:</div>
                <el-form ref="bearingDataForm" :model="bearingForm" label-width="100px">
                  <el-form-item label="内圆直径">
                    <el-input v-model="bearingForm.innerDiameter" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="外圆直径">
                    <el-input v-model="bearingForm.outerDiameter" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="节圆直径">
                    <el-input v-model="bearingForm.pitchCircleDiameter" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="接触角">
                    <el-input v-model="bearingForm.contactAngle" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="宽度">
                    <el-input v-model="bearingForm.width" size="mini" disabled />
                  </el-form-item>
                </el-form>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="rightSection">
                <div class="section-subtitle">故障频率参数:</div>
                <el-form ref="bearingDataForm" :model="bearingForm" label-width="100px">
                  <el-form-item label="BPFO">
                    <el-input v-model="bearingForm.bpfo" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="BPFI">
                    <el-input v-model="bearingForm.bpfi" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="FTF">
                    <el-input v-model="bearingForm.ftf" size="mini" disabled />
                  </el-form-item>
                  <el-form-item label="BSF">
                    <el-input v-model="bearingForm.bsf" size="mini" disabled />
                  </el-form-item>
                </el-form>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleBearingInfoSubmit">提交</el-button>
        <el-button @click="handleBearingInfoCancel">关闭</el-button>
      </span>
    </el-dialog>
    <div>
      <!-- 轴承库新建/编辑弹窗 -->
      <bearing-library-dialog
        :visible.sync="showBearingDialog"
        :data-list="bearingForm"
        :is-edit="isEdit"
        @cancel="showBearingDialog = false"
        @confirm="handleBearingLibraryConfirm"
      />
    </div>
  </div>
</template>

<script>
import BearingLibraryDialog from './BearingLibraryDialog.vue'
import {
  getBearingLibrary,
  getBearingManufacturer,
  deleteBearing
} from '@/api/haoyu-system/SystemAdministration/Configuration.js'
export default {
  components: {
    BearingLibraryDialog
  },
  props: {
    visible: Boolean,
    dataList: {
      type: Array,
      default: () => []
    },
    bearingDescriptionData: {
      type: [Object, Array],
      default: null
    },
    editModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showBearingDialog: false, // 控制弹窗的显示
      isEdit: false, // 标志是新建还是编辑
      filterForm: {
        manufacturer: '',
        model: ''
      },
      manufacturers: [], // 存储后端给到的制造厂商列表
      showBearingLibraryDialog: false, // 轴承库弹窗属性
      // 轴承库表格数据
      bearingLibraryData: [],
      bearingLibraryTotalItems: 0, // 轴承库总记录数
      bearingLibraryCurrentPage: 1, // 当前页码
      bearingLibraryPageSize: 10, // 每页条数
      total: 1,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        manufacturer: undefined,
        model: undefined,
        status: undefined
      },
      showBearingInfoDialog: false,
      // 填入的轴承信息
      bearingForm: {
        id: null, // 轴承id
        // 基本信息
        manufacturer: '', // 制造厂商
        model: '', // 型号
        numberOfBalls: '', // 滚动体数目
        description: '', // 描述
        rotationAngle: '0', // 内圈或者外圈
        // 尺寸
        innerDiameter: '', // 内圆直径
        outerDiameter: '', // 外圆直径
        pitchCircleDiameter: '', // 节圆直径
        contactAngle: '', // 接触角，真/假
        width: '', // 宽度

        // 故障频率参数
        bpfo: '', // BPFO
        bpfi: '', // BPFI
        ftf: '', // FTF
        bsf: '' // BSF
      }
    }
  },
  watch: {
    bearingDescriptionData: {
      handler(newVal, oldVal) {
        // 监听到 bearingDescriptionData 变化时执行以下逻辑
        if (this.editModel && newVal.table) {
          this.bearingForm = newVal ? newVal.table : null
          this.showBearingInfoDialog = true
          // console.log(this.bearingForm)
        }
      },
      deep: true // 如果你需要深度监听对象内部的变化
    },
    editModel: {
      handler(newVal, oldVal) {
        // 监听到 bearingDescriptionData 变化时执行以下逻辑
        if (this.bearingDescriptionData && newVal) {
          this.bearingForm = this.bearingDescriptionData ? this.bearingDescriptionData.table : null
          this.showBearingInfoDialog = true
          // console.log(this.bearingForm)
        }
      },
      deep: true // 如果你需要深度监听对象内部的变化
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    if (this.editModel && this.bearingDescriptionData.table) {
      this.bearingForm = this.bearingDescriptionData.table
      this.showBearingInfoDialog = true
      console.log(this.bearingForm)
    }
  },
  methods: {
    getList() {
      getBearingLibrary(this.queryParams).then(res => {
        this.total = res.total
        this.bearingLibraryData = res.rows
        console.log(res.rows)
        // console.log(res)
      })
      getBearingManufacturer().then(res => {
        this.manufacturers = res.rows
      })
    },
    handleBearingLibraryConfirm(data) {
      this.getList()
      // 处理弹窗确认后的数据
      console.log('确认的数据', data)
    },
    // 新建轴承
    handleCreateBearing() {
      this.showBearingDialog = true
      this.isEdit = false
      // 你的新建逻辑
    },

    // 编辑轴承
    handleEditBearing() {
      this.showBearingDialog = true
      this.isEdit = true
      // 你的编辑逻辑
    },

    // 删除轴承
    handleDeleteBearing() {
      this.$modal.confirm('是否确认删除此轴承？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteBearing(this.bearingForm.id).then(() => {
            this.getList()
            this.$message.success('删除成功')
          })
        })
        .catch(() => {
          // 点击取消后不做任何操作
          console.log('取消删除')
        })
    },
    // 处理表格中当前选中的行
    handleCurrentChange(val) {
      // 这里 val 是选中的行数据
      this.bearingForm = val
      // console.log(this.bearingForm) // 打印选中的行数据
      this.$emit('handle-current-change', val) // 发出事件，传递选中的行数据
    },

    // 处理添加按钮点击事件
    handleAdd() {
      this.showBearingInfoDialog = true // 显示轴承信息弹窗
      this.$emit('add') // 发出添加事件
    },

    // 处理打开轴承库按钮点击事件
    handleOpenBearingLibrary() {
      this.showBearingLibraryDialog = true // 显示轴承库弹窗
      this.$emit('open-bearing-library') // 发出打开轴承库事件
    },

    // 处理打开本地轴承按钮点击事件
    handleOpenLocalBearings() {
      this.$emit('open-local-bearings') // 发出打开本地轴承事件
    },

    // 处理关闭按钮点击事件
    handleCancel() {
      this.$emit('cancel') // 发出关闭事件
    },

    // 处理轴承信息提交按钮点击事件
    handleBearingInfoSubmit() {
      if (this.editModel) {
        let TreeData = this.bearingDescriptionData
        TreeData.title = this.bearingForm.description
        const dataToSubmit = { ...this.bearingForm } // 收集要提交的数据
        TreeData.table = dataToSubmit // 向 TreeData.table 赋值
        console.log(TreeData)
        TreeData.deviceName = TreeData.title
        this.$emit('submit-bearing-info', TreeData) // 发出提交轴承信息事件，传递数据
        this.$emit('cancel')
        this.showBearingInfoDialog = false // 隐藏轴承信息弹窗
      } else {
        const dataToSubmit = { ...this.bearingForm } // 收集要提交的数据
        this.$emit('submit-bearing-info', dataToSubmit) // 发出提交轴承信息事件，传递数据
        this.$emit('cancel')
        this.showBearingInfoDialog = false // 隐藏轴承信息弹窗
      }
    },

    // 处理轴承信息弹窗关闭按钮点击事件
    handleBearingInfoCancel() {
      this.$emit('cancel')
      this.showBearingInfoDialog = false // 隐藏轴承信息弹窗
    },

    // 处理轴承库表格中当前选中的行
    handleBearingLibraryRowChange(val) {
      this.bearingForm = val
      console.log('轴承库当前选中行', val) // 打印选中的轴承库行数据
    },

    // 处理轴承库分页变化事件
    handleBearingLibraryPageChange(page) {
      this.bearingLibraryCurrentPage = page // 设置当前页码
      // 在这里你可以请求新的轴承库数据
    },

    // 处理轴承库弹窗关闭按钮点击事件
    handleBearingLibraryCancel() {
      this.showBearingLibraryDialog = false // 隐藏轴承库弹窗
    },

    handleSearch() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleAddToLocal() {
      // 处理添加到本地逻辑
      console.log('添加到本地')
      // 在这里你可以将选中的轴承添加到本地存储
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
  margin-top: 6px;
}

/* 只隐藏 class 为 custom-table 的 el-table 表头 */
.custom-table::v-deep .el-table__header {
  display: none;
}

.bearing-dialog .el-dialog__body {
  padding: 20px;
  background-color: #f9f9f9;
}

.dialog-content {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dialog-section {
  margin-bottom: 20px;
}

.form-section .el-form-item {
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
}

.leftSection, .rightSection {
  padding: 10px;
  background-color: #f1f1f1;
  border-radius: 4px;
}

.dialog-content {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dialog-section {
  margin-bottom: 20px;
}

.leftSection, .rightSection {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 10px;
  font-size: 12px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #409EFF; /* Title underline */
  padding-bottom: 10px; /* Space between title and content */
}

.section-subtitle {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: semi-bold;
  color: #555;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 15px;
}
</style>
