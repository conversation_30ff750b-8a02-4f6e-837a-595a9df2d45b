<!-- 编辑测点 -->
<template>
  <div class="edit-measurement-point">
    <el-dialog
      title="编辑测点"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :before-close="handleClose"
      :modal="false"
      v-dialogDrag
    >
      <el-form
        ref="measurePointForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        size="small"
      >
        <el-form-item label="测点编号" prop="serialNumber">
          <el-input v-model="formData.serialNumber" placeholder="请输入测点编号"></el-input>
        </el-form-item>

        <el-form-item label="测点名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入测点名称"></el-input>
        </el-form-item>

        <el-form-item label="设备转速" prop="speed">
          <el-input v-model.number="formData.speed" placeholder="请输入设备转速">
            <template slot="append">RPM</template>
          </el-input>
        </el-form-item>

        <el-form-item label="传感器名称" prop="collectorName">
          <el-input v-model="formData.collectorName" placeholder="请输入传感器名称"></el-input>
        </el-form-item>

        <el-form-item label="传感器型号" prop="collectorModel">
          <el-input v-model="formData.collectorModel" placeholder="请输入传感器型号"></el-input>
        </el-form-item>

        <el-form-item label="传感器类型" prop="instrument">
          <el-select v-model="formData.instrument" placeholder="请选择传感器类型">
            <el-option label="加速度传感器" value="1"></el-option>
            <el-option label="速度传感器" value="2"></el-option>
            <el-option label="位移传感器" value="3"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="振动方向" prop="sensorDirection">
          <el-select v-model="formData.sensorDirection" placeholder="请选择振动方向">
            <el-option label="垂直(V)" :value="1"></el-option>
            <el-option label="水平(H)" :value="2"></el-option>
            <el-option label="轴向(A)" :value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMeasurePointInfo, updateDevicePoint } from '@/api/haoyu-system/SystemAdministration/SystemAdministration'

export default {
  name: 'EditMeasurementPoint',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pointId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      formData: {
        serialNumber: '', // 测点编号
        name: '', // 测点名称
        speed: '', // 设备转速
        instrument: '3', // 仪器类型
        sensorDirection: 1, // 传感器类型
        collectorName: '', // 传感器名称
        collectorModel: '', // 传感器型号
        pointLocation: [] // 测点位置坐标
      },
      rules: {
        serialNumber: [
          { required: true, message: '请输入测点编号', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入测点名称', trigger: 'blur' }
        ],
        speed: [
          { required: true, message: '请输入设备转速', trigger: 'blur' },
          { type: 'number', message: '转速必须为数字', trigger: 'blur' }
        ],
        instrument: [
          { required: true, message: '请选择传感器类型', trigger: 'change' }
        ],
        sensorDirection: [
          { required: true, message: '请选择振动方向', trigger: 'change' }
        ]
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.pointId) {
        this.getPointInfo()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    // 获取测点信息
    async getPointInfo() {
      if (!this.pointId) return

      this.loading = true
      try {
        const res = await getMeasurePointInfo(this.pointId)
        if (res.code === 200 && res.data) {
          const data = res.data
          console.log('API返回的原始数据:', data)

          // 确保sensorDirection是数字类型（振动方向）
          const sensorDirection = data.vibrationDirection ? parseInt(data.vibrationDirection) : 1

          this.formData = {
            serialNumber: data.sensorCoding || data.serialNumber || '',
            name: data.sensorName || data.name || '',
            speed: Number(data.deviceRpm) || data.speed || '',
            instrument: data.sensorType || data.instrument || '1',
            sensorDirection: sensorDirection,
            collectorName: data.collectorName || '',
            collectorModel: data.collectorModel || '',
            pointLocation: Array.isArray(data.pointLocation) ? data.pointLocation : []
          }

          console.log('处理后的表单数据:', this.formData)
        } else {
          this.$modal.msgError(res.msg || '获取测点信息失败')
        }
      } catch (error) {
        console.error('获取测点信息失败:', error)
        this.$modal.msgError('获取测点信息失败')
      } finally {
        this.loading = false
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.measurePointForm.validate(valid => {
        if (!valid) return

        // 构建提交的数据，按照API期望的格式
        const submitData = {
          id: this.pointId,
          sensorCoding: this.formData.serialNumber, // 测点编号
          sensorName: this.formData.name, // 测点名称
          deviceRpm: this.formData.speed, // 设备转速
          sensorType: this.formData.instrument, // 传感器类型
          sensorDirection: this.formData.sensorDirection, // 振动方向
          collectorName: this.formData.collectorName, // 传感器名称
          collectorModel: this.formData.collectorModel // 传感器型号
        }

        // 发送更新请求
        this.$modal.loading('正在保存...')
        updateDevicePoint(submitData).then(res => {
          this.$modal.closeLoading()
          if (res.code === 200) {
            this.$modal.msgSuccess('保存成功')
            this.handleClose()
            this.$emit('refresh')
          } else {
            this.$modal.msgError(res.msg || '保存失败')
          }
        }).catch(err => {
          this.$modal.closeLoading()
          this.$modal.msgError('保存失败')
          console.error(err)
        })

        // 注释掉模拟API调用
        // setTimeout(() => {
        //   this.$modal.closeLoading()
        //   this.$modal.msgSuccess('保存成功')
        //   this.handleClose()
        //   this.$emit('refresh')
        // }, 1000)
      })
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.$refs.measurePointForm && this.$refs.measurePointForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-measurement-point {
  .el-dialog {
    border-radius: 8px;

    .el-dialog__header {
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
    }

    .el-dialog__body {
      padding: 20px;
      max-height: 60vh;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 15px 20px;
      border-top: 1px solid #eee;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
