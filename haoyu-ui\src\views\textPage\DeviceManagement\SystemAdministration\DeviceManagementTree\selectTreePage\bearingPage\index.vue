<template>
  <div class="bearing-component">
    <h2>轴承详情</h2>
    <p>这里展示与轴承相关的信息。</p>
    <p>轴承编号: {{ nodeData.id }}</p>
    <p>轴承类型: {{ nodeData.type }}</p>
    <p>轴承寿命: {{ nodeData.lifetime }} 小时</p>
  </div>
</template>

<script>
export default {
  props: {
    nodeData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
.bearing-component {
  padding: 20px;
  background-color: #f7f1e3;
  border: 1px solid #e0b084;
  border-radius: 4px;
}
</style>
