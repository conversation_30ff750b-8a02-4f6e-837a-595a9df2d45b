<template>
  <div>
    <h3 class="Point-title">测量点</h3>

    <el-form ref="SensorConfigFormRef" :model="formData" label-width="150px">
      <!-- 传感器序列号 -->
      <el-form-item label="测点编号" prop="serialNumber" :rules="[{ required: true, message: '请输入传感器序列号', trigger: 'blur' }]">
        <el-input v-model="formData.serialNumber" placeholder="请输入测点编号" size="mini" @input="handleInput" />
      </el-form-item>

      <!-- 传感器名称 -->
      <el-form-item label="测点名称" prop="name" :rules="[{ required: true, message: '请输入传感器名称', trigger: 'blur' }]">
        <el-input v-model="formData.name" placeholder="请输入测点名称" size="mini" @input="handleInput" />
      </el-form-item>

      <!-- 设备转速 -->
      <el-form-item label="设备转速" prop="speed" :rules="[{ required: true, message: '请输入设备转速', trigger: 'blur' }]">
        <el-input v-model="formData.speed" placeholder="请输入设备转速" size="mini" @input="handleInput" />
      </el-form-item>

      <el-form-item label="传感器名称" prop="collectorName" :rules="[{ required: true, message: '请输入传感器名称', trigger: 'blur' }]">
        <el-input v-model="formData.collectorName" placeholder="请输入传感器名称" size="mini" />
      </el-form-item>

      <el-form-item label="传感器型号" prop="collectorModel" :rules="[{ required: true, message: '请输入传感器型号', trigger: 'blur' }]">
        <el-input v-model="formData.collectorModel" placeholder="请输入传感器型号" size="mini" />
      </el-form-item>
      <!-- 仪器类型 -->
      <el-form-item
        label="传感器类型"
        prop="sensorDirection"
        :rules="[{ required: true, message: '请选择传感器类型', trigger: 'change' }]"
      >
        <el-radio-group ref="radio" v-model="formData.sensorDirection" @change="handleSensorDirectionChange">
          <el-radio
            v-for="sensor in SensorList"
            :key="sensor.id"
            :label="sensor.id"
          >{{ sensor.label }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 动态下拉框 -->
      <el-form-item
        v-if="showDropdown"
        label="振动方向"
        prop="vibrationDirection"
      >
        <el-select v-model="formData.vibrationDirection" size="mini" placeholder="请选择振动方向">
          <el-option
            v-for="option in vibrationDirectionOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getAllSensor } from '@/api/haoyu-system/SystemAdministration/SystemAdministration.js'
export default {
  name: 'SensorConfigForm',
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showDropdown: true,
      // 传感器类型列表
      SensorList: [],
      vibrationDirectionOptions: [
        { label: '垂直向振动值(V)', value: 1 },
        { label: '水平振动值(H)', value: 2 },
        { label: '轴向振动值(A)', value: 3 }
      ]
    }
  },
  created() {
    this.getAllSensorOption()
  },
  mounted() {
    this.handleSensorDirectionChange(this.value)
    this.$refs.radio.$children.forEach((item) => {
      item.$refs.radio.removeAttribute('aria-hidden')
    })
  },
  methods: {
    // 获取所有传感器类型单选框选项
    getAllSensorOption() {
      getAllSensor().then(res => {
        this.SensorList = res.rows.map(item => ({
          id: item.id,
          label: item.sensorType
        }))
        this.handleSensorDirectionChange(this.formData.sensorDirection)
      })
    },
    handleInput() {
      // 将当前表单数据传递给父组件，以便实时更新树显示
      this.$emit('update', this.formData)
    },
    handleSensorDirectionChange(value) {
      // 将当前表单数据传递给父组件，以便实时更新树显示
      this.$emit('update', this.formData)
      // 根据选择的仪器类型决定是否显示下拉框
      if (value === 1 || value === 2) { // 单轴仪器的类型值
        this.showDropdown = true
      } else {
        this.showDropdown = false
      }
    }
  }
}
</script>

<style scoped>
.Point-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700; /* 加粗字体 */
  color: #37474f; /* 更深的字体颜色 */
  text-align: center;
  border-bottom: 3px solid #90a4ae; /* 更深的边框颜色 */
  padding-bottom: 10px;
  text-transform: uppercase; /* 全部大写，增加工业感 */
}
</style>
