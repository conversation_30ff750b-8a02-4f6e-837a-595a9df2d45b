import request from '@/utils/request'

export function getHealthValue(url, params) {
    return request({
        url: url,
        method: 'POST',
        data: params,
        timeout: 0,
        validateStatus: function() {
            return true
        }
    })
}

// 获取测量定义信息
export function getAIisTrained(id) {
    return request({
        url: `/measureDefinition/measureDefinition/${id}`,
        method: 'get',
    })
}

// 获取测量点信息
export function getHaveTraining() {
    return request({
        url: `/measurePoint/measurePointManagement/list`,
        method: 'get',
        params: {
            "trained": "training"
        }
    })
}

// 更新测量定义信息
export function updateAIisTrained(params) {
    return request({
        url: `/measureDefinition/measureDefinition`,
        method: 'put',
        data: params
    })
}

// 获取测量点信息
export function getMeasurePointManagement(id) {
    return request({
        url: `/measurePoint/measurePointManagement/${id}`,
        method: 'get'
    })
}

// 更新测量点信息
export function UpdataMeasurePointManagement(params) {
    return request({
        url: `/measurePoint/measurePointManagement`,
        method: 'put',
        data: params
    })
}