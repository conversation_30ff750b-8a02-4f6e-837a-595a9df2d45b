import request from '@/utils/request'

export function getTrend(id) {
    return request({
        url: `/data/eigenvalues/tender/list/${id}`,
        method: 'get'
    })
}

export function getChildImformation(id) {
    return request({
        url: `/deviceManagement/factorymanagement/list/${id}`,
        method: 'get'
    })
}

export function getMeasureDefinition(id) {
    return request({
        url: `/measurePoint/measurePointManagement/definition/${id}`,
        method: 'get'
    })
}

// 获取测量点信息
export function getMeasurePointManagement(id) {
    return request({
        url: `/measurePoint/measurePointManagement/${id}`,
        method: 'get'
    })
}

export function UpdataMeasurePointManagement(params) {
    return request({
        url: `/measurePoint/measurePointManagement`,
        method: 'put',
        data: params
    })
}

export function getWarn(params) {
    return request({
        url: `/dataEngine/warnRecord/list`,
        method: 'get',
        params: params
    })
}

export function getLatestTime(id, waveState) {
    const url = `/waveForm/waveFormDataInfo/list/definePoint/${id}`
    return request({
        url,
        method: 'get',
        params: {
            pageNum: 1,
            pageSize: 1,
            waveState: waveState,
        }
    })
}

//新增设备图片
export function NewLatestTime(id, imagePath) {
    const formData = new FormData()
    formData.append('id', id)
    formData.append('img', imagePath)

    return request({
        url: '/deviceDetailsImage/image/upload',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

//修改设备图片
export function UpdateLatestTime(id, imagePath) {
    const formData = new FormData()
    formData.append('id', id)
    formData.append('img', imagePath)

    return request({
        url: '/deviceDetailsImage/image/upload',
        method: 'put',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

// 获取指定设备列表
export function getDeviceImageList(deviceId) {
    const url = `/deviceDetailsImage/image/device/${deviceId}`
    return request({
        url,
        method: 'get',
    })
}

//获取所有设备图片列表
export function getAllDeviceImageList() {
    const url = `/deviceDetailsImage/image/list`
    return request({
        url,
        method: 'get',
    })
}


export function get2DImageList(params = {}) {
    const requestParams = {
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 10000
    }

    // 只在有值时添加可选参数
    if (params.imageType !== undefined) {
        requestParams.imageType = params.imageType
    }
    if (params.imageNote !== undefined) {
        requestParams.imageNote = params.imageNote
    }

    return request({
        url: `/file/imagesResources/list`,
        method: 'get',
        params: requestParams
    })
}

//删除2D图片
export function delete2DImage(id) {
    return request({
        url: `/file/imagesResources/${id}`,
        method: 'delete'
    })
}

//根据id修改2D图片
export function update2DImage(params) {
    return request({
        url: `/file/imagesResources`,
        method: 'put',
        data: params
    })
}

//新增2D图片，不带id
export function add2DImage(params) {
    return request({
        url: `/file/imagesResources`,
        method: 'post',
        data: params
    })
}

//获取类型
export function getImageTypeList() {
    return request({
        url: '/file/imagesType/list/all',
        method: 'get'
    })
}
//新增类型
export function addImageType(params) {
    return request({
        url: '/file/imagesType',
        method: 'post',
        data: params
    })
}
//根据id修改类型
export function updateImageType(params) {
    return request({
        url: '/file/imagesType',
        method: 'put',
        data: params
    })
}
//删除类型
export function deleteImageType(id) {
    return request({
        url: `/file/imagesType/${id}`,
        method: 'delete'
    })
}


export function uploadImageToURL(file) {
    const formData = new FormData();
    formData.append('file', file);

    return request({
        url: '/common/upload',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: formData
    });
}