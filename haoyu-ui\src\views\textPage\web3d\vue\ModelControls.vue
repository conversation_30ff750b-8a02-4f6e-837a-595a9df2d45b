<template>
  <div class="model-controls">
    <label for="colorPicker">选择模型颜色:</label>
    <input 
      type="color" 
      id="colorPicker" 
      v-model="localColor" 
      @input="onColorChange" 
    />

    <label for="opacitySlider">模型透明度:</label>
    <input 
      type="range" 
      id="opacitySlider" 
      v-model="localOpacity" 
      min="0" 
      max="1" 
      step="0.01" 
      @input="onOpacityChange" 
    />
    <span>{{ (localOpacity * 100).toFixed(0) }}%</span>
  </div>
</template>

<script>
export default {
  name: 'ModelControls',
  props: {
    color: {
      type: String,
      default: '#ff0000'
    },
    opacity: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      localColor: this.color,
      localOpacity: this.opacity,
    };
  },
  watch: {
    color: {
      handler(newVal) {
        // 如果是 rgba 格式，转换为 hex
        if (newVal.startsWith('rgba')) {
          const rgba = newVal.match(/rgba\((\d+),(\d+),(\d+),([\d.]+)\)/);
          if (rgba) {
            const [_, r, g, b] = rgba;
            this.localColor = this.rgbToHex(parseInt(r), parseInt(g), parseInt(b));
            this.localOpacity = parseFloat(rgba[4]);
          }
        } else {
          this.localColor = newVal;
        }
      },
      immediate: true
    },
    opacity(newVal) {
      this.localOpacity = newVal;
    }
  },
  methods: {
    rgbToHex(r, g, b) {
      return '#' + [r, g, b].map(x => {
        const hex = x.toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      }).join('');
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    onColorChange() {
      const rgb = this.hexToRgb(this.localColor);
      if (rgb) {
        const rgba = `rgba(${rgb.r},${rgb.g},${rgb.b},${this.localOpacity})`;
        this.$emit('color-change', rgba);
        this.$emit('opacity-change', this.localOpacity);
      }
    },
    onOpacityChange() {
      this.$emit('opacity-change', this.localOpacity);
      // 同时触发颜色变化以更新 rgba
      this.onColorChange();
    },
  },
};
</script>

<style scoped>
.model-controls {
  /* 你的样式 */
}
</style>
