<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="alertTitle"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    @closed="handleClosed"
    center
    custom-class="alarm-dialog-wrapper"
  >
    <div class="alarm-dialog-content">
      <!-- 报警表格 - 添加高度来启用滚动 -->
      <el-table
        :data="alarmRecords"
        style="width: 100%"
        height="450"
        v-loading="loading && pageNum === 1"
        element-loading-text="加载中..."
        @row-click="handleRowClick"
        :row-class-name="tableRowClassName"
        v-infinite-scroll="loadMore"
        infinite-scroll-disabled="noMore || loading"
        infinite-scroll-distance="10"
        ref="alarmTable"
        highlight-current-row
      >
        <el-table-column prop="warnTime" label="时间" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>{{ formatDate(scope.row.warnTime) }}</div>
            <div>{{ formatTime(scope.row.warnTime) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="measurementName" label="测点" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="报警详情" min-width="220" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>{{ scope.row.warnMsg }}</div>
            <div v-if="scope.row.warnType" class="alarm-detail">
              <span class="alarm-type">{{ scope.row.warnType }}</span>
              <span class="alarm-values">标准值: {{ scope.row.normalValue || '-' }} / 当前值: {{ scope.row.warnValue || '-' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="warnLevel" label="级别" width="90" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="getAlarmLevelTag(scope.row.warnLevel)"
              effect="dark"
              size="mini"
            >
              {{ getAlarmLevelText(scope.row.warnLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.resolved ? 'success' : 'warning'"
              effect="dark"
              size="mini"
            >
              {{ scope.row.resolved ? '已解决' : '未解决' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 加载更多提示 -->
      <div v-if="noMore && !loading" class="load-all-text">
        已加载全部数据
      </div>
      <div v-if="loading && pageNum > 1" class="loading-more-text">
        加载更多数据中...
      </div>

      <!-- 滚动提示指示器 -->
      <div v-if="hasMoreToScroll" class="scroll-indicator">
        <i class="el-icon-arrow-down"></i>
        <span>向下滚动查看更多</span>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="refresh-info">
        <span v-if="autoRefresh">
          <i class="el-icon-refresh refresh-icon rotating"></i>
          自动刷新中
        </span>
        <span v-else>
          <i class="el-icon-refresh refresh-icon"></i>
          自动刷新已暂停
        </span>
        <!-- <el-switch
          v-model="autoRefresh"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="handleAutoRefreshChange"
        ></el-switch> -->
      </div>
      <div>
        <el-button @click="closeDialog">关闭</el-button>
        <el-button type="primary" @click="goToAlarmSystem">
          <i class="el-icon-view"></i>
          去查看报警
        </el-button>
      </div>
    </div>

    <!-- 报警详情的弹窗 -->
    <el-dialog
      width="650px"
      title="报警详情"
      :visible.sync="detailDialogVisible"
      append-to-body
      center
      custom-class="detail-dialog-wrapper"
    >
      <div v-if="selectedAlarm" class="alarm-detail-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备名称" :span="2">{{ selectedAlarm.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="测点名称">{{ selectedAlarm.measurementName }}</el-descriptions-item>
          <el-descriptions-item label="定义名称">{{ selectedAlarm.definitionName }}</el-descriptions-item>
          <el-descriptions-item label="报警时间" :span="2">{{ selectedAlarm.warnTime }}</el-descriptions-item>
          <el-descriptions-item label="报警类型">{{ selectedAlarm.warnType }}</el-descriptions-item>
          <el-descriptions-item label="报警级别">
            <el-tag :type="getAlarmLevelTag(selectedAlarm.warnLevel)" effect="dark">
              {{ getAlarmLevelText(selectedAlarm.warnLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标准值">{{ selectedAlarm.normalValue }}</el-descriptions-item>
          <el-descriptions-item label="报警值">{{ selectedAlarm.warnValue }}</el-descriptions-item>
          <el-descriptions-item label="报警消息" :span="2">{{ selectedAlarm.warnMsg }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ selectedAlarm.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="测点ID">{{ selectedAlarm.measurementId }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedAlarm.measurementDefinition" class="measurement-definition-info">
          <h4>测点定义详情</h4>
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="测点定义名称" :span="2">{{ selectedAlarm.measurementDefinition.measureDefineName }}</el-descriptions-item>
            <el-descriptions-item label="频率范围">{{ selectedAlarm.measurementDefinition.lowerLimitFrequency }}-{{ selectedAlarm.measurementDefinition.upperLimitFrequency }}HZ</el-descriptions-item>
            <el-descriptions-item label="谱线数">{{ selectedAlarm.measurementDefinition.numberOfSpectralLines }}线</el-descriptions-item>
            <el-descriptions-item label="一级报警值">{{ selectedAlarm.measurementDefinition.firstAlarmValue }}</el-descriptions-item>
            <el-descriptions-item label="二级报警值">{{ selectedAlarm.measurementDefinition.secondAlarmValue }}</el-descriptions-item>
            <el-descriptions-item label="三级报警值">{{ selectedAlarm.measurementDefinition.thirdAlarmValue }}</el-descriptions-item>
            <el-descriptions-item label="四级报警值">{{ selectedAlarm.measurementDefinition.forthAlarmValue }}</el-descriptions-item>
            <el-descriptions-item label="包络频率" v-if="selectedAlarm.measurementDefinition.envelopeFrequency">{{ selectedAlarm.measurementDefinition.envelopeFrequency }}</el-descriptions-item>
            <el-descriptions-item label="窗函数ID" v-if="selectedAlarm.measurementDefinition.windowsFunctionId">{{ selectedAlarm.measurementDefinition.windowsFunctionId }}</el-descriptions-item>
            <el-descriptions-item label="FFT类型ID" v-if="selectedAlarm.measurementDefinition.fastFourierTransformTypeId">{{ selectedAlarm.measurementDefinition.fastFourierTransformTypeId }}</el-descriptions-item>
            <el-descriptions-item label="平均类型" v-if="selectedAlarm.measurementDefinition.averageType">{{ selectedAlarm.measurementDefinition.averageType }}</el-descriptions-item>
            <el-descriptions-item label="平均值" v-if="selectedAlarm.measurementDefinition.averageValue">{{ selectedAlarm.measurementDefinition.averageValue }}</el-descriptions-item>
            <el-descriptions-item label="数据重叠率" v-if="selectedAlarm.measurementDefinition.dataOverlapRate">{{ selectedAlarm.measurementDefinition.dataOverlapRate }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { getWarnRecord } from '@/views/textPage/BigScreen/api/apiBigscreen'

export default {
  name: 'AlarmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    alertTitle: {
      type: String,
      default: '报警详情'
    },
    timestamp: {
      type: String,
      default: ''
    },
    statusText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      message: [],
      alarmRecords: [], // 从接口获取的报警记录
      dialogVisible: this.visible,
      activeTab: 'details',
      detailDialogVisible: false,
      loading: false,
      pageSize: 100,
      pageNum: 1,
      noMore: false,
      total: 0,
      selectedAlarm: null, // 选中的报警详情
      autoRefresh: true, // 自动刷新控制
      refreshInterval: 30, // 刷新间隔（秒）
      refreshCountdown: 30, // 倒计时
      refreshTimer: null, // 刷新定时器
      lastKnownIds: new Set(), // 记录已知的报警ID
      resolvedIds: new Set(), // 记录已解决的报警ID
      hasMoreToScroll: false // 是否有更多内容可以滚动
    }
  },
  computed: {
    // 判断是否有未解决的报警
    hasUnresolvedAlarms() {
      return this.alarmRecords.some(alarm => !alarm.resolved);
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        // 重置分页并加载报警记录
        this.resetPagination();
        this.fetchAlarmRecords();
        // 启动自动刷新
        this.startAutoRefresh();
      } else {
        // 清除刷新定时器
        this.stopAutoRefresh();
      }
    }
  },
  methods: {
    // 新增明确的关闭弹窗方法
    closeDialog() {
      console.log('关闭弹窗');
      this.dialogVisible = false;
      this.$emit('update:visible', false);
      this.$emit('close');
      this.stopAutoRefresh();
    },
    handleClose() {
      this.closeDialog();
    },
    handleClosed() {
      this.$emit('closed');
      // 重置状态
      this.resetPagination();
      this.alarmRecords = [];
      this.stopAutoRefresh();
    },
    // 重置分页
    resetPagination() {
      this.pageNum = 1;
      this.noMore = false;
      this.total = 0;
      this.lastKnownIds.clear();
      this.resolvedIds.clear();
    },
    // 启动自动刷新
    startAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
      }

      this.refreshCountdown = this.refreshInterval;

      this.refreshTimer = setInterval(() => {
        if (this.refreshCountdown <= 1) {
          this.refreshData();
          this.refreshCountdown = this.refreshInterval;
        } else {
          this.refreshCountdown--;
        }
      }, 1000);
    },
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    // 处理自动刷新开关变化
    handleAutoRefreshChange(value) {
      if (value) {
        this.startAutoRefresh();
        this.refreshData(); // 立即刷新一次
      } else {
        this.stopAutoRefresh();
      }
    },
    // 无感刷新数据
    async refreshData() {
      if (this.loading || !this.dialogVisible) return;

      try {
        // 只获取第一页进行比较
        const response = await getWarnRecord(1);

        if (response && response.rows) {
          const currentScrollTop = this.$refs.alarmTable ? this.$refs.alarmTable.$el.querySelector('.el-table__body-wrapper').scrollTop : 0;

          // 处理新数据
          const newRecords = response.rows;
          const currentIds = new Set(this.alarmRecords.map(item => item.id));

          // 找出新增加的报警
          const addedRecords = newRecords.filter(item => !currentIds.has(item.id));

          // 更新现有记录的状态（保留用户操作的resolved状态）
          const updatedRecords = this.alarmRecords.map(existingItem => {
            const updatedRecord = newRecords.find(newItem => newItem.id === existingItem.id);
            if (updatedRecord) {
              return {
                ...updatedRecord,
                resolved: this.resolvedIds.has(existingItem.id) || existingItem.resolved
              };
            }
            return existingItem;
          });

          // 组合最终的显示数据：新增的记录 + 更新后的记录
          if (addedRecords.length > 0) {
            // 将新记录默认设置为未解决
            const newFormattedRecords = addedRecords.map(item => ({
              ...item,
              resolved: false
            }));

            // 将新记录放在前面
            this.alarmRecords = [...newFormattedRecords, ...updatedRecords];

            // 更新总计数
            this.total = response.total || 0;

            // 在不影响用户查看的情况下提示新报警
            if (this.dialogVisible && newFormattedRecords.length > 0) {
              this.$notify({
                title: '新的报警',
                message: `收到${newFormattedRecords.length}个新的报警信息`,
                type: 'warning',
                duration: 3000
              });
            }
          } else {
            // 没有新记录，仅更新现有记录状态
            this.alarmRecords = updatedRecords;
          }

          // 更新lastKnownIds集合
          this.lastKnownIds = new Set(this.alarmRecords.map(item => item.id));

          // 恢复滚动位置
          this.$nextTick(() => {
            if (this.$refs.alarmTable) {
              this.$refs.alarmTable.$el.querySelector('.el-table__body-wrapper').scrollTop = currentScrollTop;
            }
          });
        }
      } catch (error) {
        console.error('刷新报警记录失败:', error);
        // 静默处理错误，避免频繁弹窗打扰用户
      }
    },
    // 加载报警记录
    async fetchAlarmRecords() {
      this.loading = true;
      try {
        const response = await getWarnRecord(this.pageNum);
        if (response && response.rows) {
          const newRecords = response.rows.map(item => ({
            ...item,
            resolved: this.resolvedIds.has(item.id) || false
          }));

          if (this.pageNum === 1) {
            // 第一页，直接赋值
            this.alarmRecords = newRecords;
            // 重置已知ID集合
            this.lastKnownIds = new Set(newRecords.map(item => item.id));
          } else {
            // 追加数据 - 过滤掉可能重复的记录
            const existingIds = new Set(this.alarmRecords.map(item => item.id));
            const uniqueNewRecords = newRecords.filter(item => !existingIds.has(item.id));

            if (uniqueNewRecords.length > 0) {
              this.alarmRecords = [...this.alarmRecords, ...uniqueNewRecords];
              // 更新已知ID集合
              uniqueNewRecords.forEach(item => this.lastKnownIds.add(item.id));
            }
          }

          this.total = response.total || 0;
          this.noMore = this.alarmRecords.length >= this.total;

          // 检查是否需要显示滚动指示器
          this.$nextTick(() => {
            this.checkScrollIndicator();
          });
        }
      } catch (error) {
        console.error('获取报警记录失败:', error);
        this.$message.error('获取报警记录失败');
      } finally {
        this.loading = false;
      }
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },
    // 格式化时间
    formatTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },
    // 获取报警级别标签类型
    getAlarmLevelTag(level) {
      const levelMap = {
        '1': 'warning',  // 一级警报
        '2': 'warning',  // 二级警报
        '3': 'danger',   // 三级警报
        '4': 'danger'    // 四级警报
      };
      return levelMap[level] || 'info';
    },
    // 获取报警级别文本
    getAlarmLevelText(level) {
      const levelMap = {
        '1': '一级警报',
        '2': '二级警报',
        '3': '三级警报',
        '4': '三级警报'  // 与三级报警保持一致
      };
      return levelMap[level] || '未知';
    },
    // 表格行样式
    tableRowClassName({row}) {
      return row.resolved ? 'resolved-row' : '';
    },
    // 处理行点击事件
    handleRowClick(row) {
      // 显示详情弹窗
      this.selectedAlarm = row;
      this.detailDialogVisible = true;
    },
    // 加载更多数据（无限滚动）
    loadMore() {
      if (this.noMore || this.loading) return;

      this.pageNum++;
      this.fetchAlarmRecords();
    },
    // 手动刷新
    manualRefresh() {
      this.refreshCountdown = this.refreshInterval;
      this.refreshData();
    },
    // 检查是否显示滚动指示器
    checkScrollIndicator() {
      if (this.$refs.alarmTable) {
        const tableBody = this.$refs.alarmTable.$el.querySelector('.el-table__body-wrapper');
        if (tableBody) {
          // 如果内容高度大于可视区域高度，则显示滚动指示器
          this.hasMoreToScroll = tableBody.scrollHeight > tableBody.clientHeight &&
                                tableBody.scrollTop < (tableBody.scrollHeight - tableBody.clientHeight);

          // 添加滚动事件监听
          tableBody.addEventListener('scroll', this.handleTableScroll);
        }
      }
    },

    // 处理表格滚动事件
    handleTableScroll() {
      if (this.$refs.alarmTable) {
        const tableBody = this.$refs.alarmTable.$el.querySelector('.el-table__body-wrapper');
        if (tableBody) {
          // 当滚动到底部时隐藏指示器
          this.hasMoreToScroll = tableBody.scrollHeight > tableBody.clientHeight &&
                                tableBody.scrollTop < (tableBody.scrollHeight - tableBody.clientHeight - 20);
        }
      }
    },

    // 跳转到报警系统页面
    goToAlarmSystem() {
      this.closeDialog();
      this.$router.push('/alarmSys');
    }
  },
  mounted() {
    // 确保弹窗显示时正确居中
    if (this.visible) {
      this.$nextTick(() => {
        window.dispatchEvent(new Event('resize'));
        this.fetchAlarmRecords();
        this.startAutoRefresh();

        // 初始化滚动指示器
        setTimeout(() => {
          this.checkScrollIndicator();
        }, 500);
      });
    }
  },
  updated() {
    // 组件更新后检查滚动指示器
    this.$nextTick(() => {
      this.checkScrollIndicator();
    });
  },
  beforeDestroy() {
    // 组件销毁前清除定时器和事件监听
    this.stopAutoRefresh();

    // 移除滚动事件监听
    if (this.$refs.alarmTable) {
      const tableBody = this.$refs.alarmTable.$el.querySelector('.el-table__body-wrapper');
      if (tableBody) {
        tableBody.removeEventListener('scroll', this.handleTableScroll);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-dialog-content {
  padding: 10px 0;
  min-height: 200px;

  .alarm-header {
    margin-bottom: 20px;

    .alert-status {
      display: flex;
      align-items: center;

      .status-tag {
        margin-right: 10px;
        padding: 5px 10px;

        i {
          margin-right: 5px;
        }
      }

      .alert-id {
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .alarm-detail {
    margin-top: 5px;
    font-size: 12px;
    color: #606266;

    .alarm-type {
      margin-right: 10px;
      background-color: #f0f9eb;
      color: #67c23a;
      padding: 2px 5px;
      border-radius: 2px;
      display: inline-block;
    }

    .alarm-values {
      color: #e6a23c;
      display: inline-block;
    }
  }

  .load-all-text, .loading-more-text {
    text-align: center;
    color: #909399;
    font-size: 12px;
    padding: 10px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .refresh-info {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;

    .refresh-icon {
      margin-right: 5px;
    }

    .rotating {
      animation: rotate 2s linear infinite;
    }

    .el-switch {
      margin-left: 10px;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.alarm-detail-info {
  h4 {
    margin: 20px 0 10px;
    font-weight: bold;
    color: #303133;
    font-size: 14px;
  }

  .measurement-definition-info {
    margin-top: 20px;
  }
}

// 增加一些 Element UI 样式的覆盖
:deep(.el-dialog__header) {
  padding: 20px;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #EBEEF5;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.resolved-row) {
  background-color: #f0f9eb;
  color: #67c23a;
}

/* 新增报警行高亮动画 */
.new-alarm-row {
  animation: newAlarmHighlight 3s ease-in-out;
}

@keyframes newAlarmHighlight {
  0% {
    background-color: rgba(255, 193, 7, 0.3);
  }
  100% {
    background-color: transparent;
  }
}

/* 滚动指示器样式 */
.scroll-indicator {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  padding: 5px 15px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10;
  animation: bounce 2s infinite;

  i {
    margin-right: 5px;
    font-weight: bold;
  }

  span {
    font-size: 12px;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-2px);
  }
}
</style>

<style>
/* 全局样式，确保弹窗居中 */
.alarm-dialog-wrapper {
  margin: 0 auto !important;
}

.alarm-dialog-wrapper .el-dialog {
  margin-top: 15vh !important;
}

.confirm-dialog-wrapper, .detail-dialog-wrapper {
  margin: 0 auto !important;
}

.confirm-dialog-wrapper .el-dialog, .detail-dialog-wrapper .el-dialog {
  margin-top: 30vh !important;
}

/* 表格样式优化 */
.alarm-dialog-wrapper .el-table {
  max-width: 100%;
  margin: 0 auto;
}

.alarm-dialog-wrapper .el-table__body-wrapper {
  overflow-y: auto;
  overflow-x: hidden;
}

.alarm-dialog-wrapper .el-table .cell {
  word-break: break-word;
  line-height: 20px;
  white-space: pre-wrap;
}

/* 详情弹窗样式 */
.detail-dialog-wrapper .el-descriptions-item__label {
  width: 100px;
  background-color: #f5f7fa;
  white-space: nowrap;
}

.detail-dialog-wrapper .el-descriptions-item__content {
  padding: 8px 10px;
  word-break: break-word;
}

.detail-dialog-wrapper .el-descriptions {
  margin-bottom: 20px;
}

/* 高亮显示当前选中行 */
.el-table__row.current-row {
  background-color: #e6f1fc !important;
}
</style>

