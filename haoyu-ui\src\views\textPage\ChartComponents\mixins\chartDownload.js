import html2canvas from 'html2canvas'

export const chartDownloadMixin = {
  methods: {
    async downloadChart(prefix = 'chart') {
      try {
        const chartContainer = this.$refs.chart
        const parentContainer = chartContainer.parentElement // 获取父容器

        // 获取所有工具栏元素
        const toolbars = parentContainer.querySelectorAll('.spectrum-toolbar, .trend-toolbar');

        // 暂时隐藏所有工具栏
        toolbars.forEach(toolbar => {
          if (toolbar) {
            toolbar.style.display = 'none';
          }
        });

        // 创建截图
        const canvas = await html2canvas(parentContainer, {
          backgroundColor: '#ffffff',
          scale: 2,
          logging: false,
          useCORS: true
        })

        // 恢复所有工具栏显示
        toolbars.forEach(toolbar => {
          if (toolbar) {
            toolbar.style.display = 'flex';
          }
        });

        canvas.toBlob((blob) => {
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url

          // 使用时间戳作为文件名
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
          const fileName = `${this.treePathText || prefix}-${timestamp}.png`
          link.download = fileName

          document.body.appendChild(link)
          link.click()

          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }, 'image/png')

      } catch (error) {
        console.error('下载图表失败:', error)
        this.$message.error('下载图表失败')
      }
    }
  }
}
