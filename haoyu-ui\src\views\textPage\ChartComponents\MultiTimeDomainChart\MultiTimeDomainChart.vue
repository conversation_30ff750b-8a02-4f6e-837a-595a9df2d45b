<!-- 多时域波形图 -->
<template>
  <div class="mtcontainer">
    <!-- 工具栏 -->
    <div ref="chart" class="chart" :class="{ loading: loading }" />
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import { getWaveform } from './data'
import { getParams } from '@/utils/graph_Interface'
import ToolBar from '../ChartsToolsBar/ToolBar.vue'

export default {
  components: {
    ToolBar
  },
  data() {
    return {
      chartInstance: null,
      waveformDataList: [], //存储多条波形数据
      loading: false,
      error: null,
      requestCache: new Map(), // 添加请求缓存
      pendingRequests: new Map() // 添加待处理请求队列
    }
  },
  computed: {
    ...mapState('dataStore',['selectedRowsData'])
  },
  watch:{
    selectedRowsData: {
      immediate: true,
      handler:'handleSelectedRowsDataChange'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      window.addEventListener('resize', this.resizeChart)
      // 初始化时如果没有选中数据，确保图表为空
      if (!this.selectedRowsData || this.selectedRowsData.length === 0) {
        this.waveformDataList = [];
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null;
    }
    this.waveformDataList = [];

    // 清理缓存和待处理请求
    this.requestCache.clear();
    this.pendingRequests.clear();
    console.log('🧹 MultiTimeDomainChart: 清理所有缓存和待处理请求');
  },
  methods: {
    initChart() {
      if (!this.$refs.chart) {
        return;
      }

      if (this.chartInstance) {
        this.chartInstance.dispose();
      }

      this.chartInstance = echarts.init(this.$refs.chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          }
        },
        dataZoom: [{
          type: 'inside',
          xAxisIndex: Array.from({ length: this.waveformDataList.length }, (_, i) => i)
        }]
      }

      this.chartInstance.setOption(option)
    },
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    },
    async handleSelectedRowsDataChange(newData) {
      if (!this.chartInstance) {
        this.initChart();
      }

      if (!newData || newData.length === 0) {
        this.waveformDataList = [];
        this.chartInstance.clear();
        return;
      }

      try {
        this.loading = true;

        // 为每个数据行创建唯一的缓存键
        const waveformPromises = newData.map(async rowData => {
          const cacheKey = `${rowData.device_id}_${rowData.point_id}_${rowData.time_point}`;

          // 检查缓存
          if (this.requestCache.has(cacheKey)) {
            console.log(`📦 使用缓存的波形数据: ${cacheKey}`);
            return this.requestCache.get(cacheKey);
          }

          // 检查是否有相同的请求正在进行
          if (this.pendingRequests.has(cacheKey)) {
            console.log(`⏳ 等待进行中的波形数据请求: ${cacheKey}`);
            return await this.pendingRequests.get(cacheKey);
          }

          // 创建新的请求
          const requestPromise = this.fetchWaveformData(rowData, cacheKey);
          this.pendingRequests.set(cacheKey, requestPromise);

          try {
            const result = await requestPromise;
            return result;
          } finally {
            this.pendingRequests.delete(cacheKey);
          }
        });

        const results = await Promise.all(waveformPromises);
        this.waveformDataList = results.map(result => ({
          data: result.data.x.map((x, index) => [x, result.data.y[index]])
        }));

        this.updateChart();
      } catch (error) {
        console.error('获取波形数据失败:', error);
        this.error = '数据加载失败';
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取单个波形数据
     * @private
     */
    async fetchWaveformData(rowData, cacheKey) {
      console.log(`🌐 发起波形数据请求: ${cacheKey}`);

      try {
        const params = await getParams(1);
        params.device_id = String(rowData.device_id);
        params.point_id = String(rowData.point_id);
        params.time_point = rowData.time_point;

        const result = await getWaveform(params);

        // 缓存结果（设置3分钟过期时间）
        this.requestCache.set(cacheKey, result);
        setTimeout(() => {
          this.requestCache.delete(cacheKey);
          console.log(`🗑️ 清理过期波形缓存: ${cacheKey}`);
        }, 3 * 60 * 1000); // 3分钟

        console.log(`✅ 波形数据请求完成: ${cacheKey}`);
        return result;

      } catch (error) {
        console.error(`❌ 波形数据请求失败: ${cacheKey}`, error);
        throw error;
      }
    },

    updateChart() {
      if (!this.chartInstance || !this.waveformDataList.length) return;

      const seriesCount = this.waveformDataList.length;
      const grids = [];
      const yAxis = [];
      const xAxis = [];
      const series = [];

      // 优化布局计算
      const gap = seriesCount > 1 ? 1.5 : 0; // 图表间隙百分比
      const totalGapHeight = (seriesCount - 1) * gap;
      const gridHeight = (98 - totalGapHeight) / seriesCount; // 使用98%避免边界问题

      // 创建所有子图表的索引数组
      const xAxisIndexes = Array.from({ length: seriesCount }, (_, i) => i);

      // 设置缩放控件
      const dataZoom = [
        {
          type: 'inside',
          xAxisIndex: xAxisIndexes
        }
      ];

      this.waveformDataList.forEach((waveform, index) => {
        // 计算数据的最小值和最大值
        const xValues = waveform.data.map(point => point[0]);
        const minX = Math.min(...xValues);
        const maxX = Math.max(...xValues);

        // 计算当前图表的top位置
        const topPosition = 1 + (gridHeight + gap) * index;

        grids.push({
          height: `${gridHeight}%`,
          top: `${topPosition}%`,
          left: '2%',
          right: '2%',
          bottom: '1%',
          containLabel: true
        });

        yAxis.push({
          gridIndex: index,
          type: 'value',
          scale: true
        });

        xAxis.push({
          gridIndex: index,
          type: 'value',
          scale: true,
          boundaryGap: false, // 设置为 false
          min: minX,          // 设置最小值
          max: maxX           // 设置最大值
        });

        series.push({
          type: 'line',
          showSymbol: false,
          data: waveform.data,
          xAxisIndex: index,
          yAxisIndex: index,
          smooth: true, // 修复拼写错误：Soomth -> smooth
          sampling: 'lttb'  // 添加采样
        });
      });

      this.chartInstance.setOption({
        grid: grids,
        xAxis: xAxis,
        yAxis: yAxis,
        dataZoom: dataZoom,  // 注意这里是 dataZoom 而不是 datazoom
        series: series,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationThreshold: 2000,
        progressiveThreshold: 3000,
        progressive: 200,
        animationDelay: function(idx) {
          return idx * 100;
        }
      }, true);
    }
  }
}
</script>

<style scoped>
.mtcontainer{
  width: 100%;
  height: 100%;
  /* border: 1px solid red; */
}
.trend-toolbar {
  background: #f5f5f5;
  height: 30px;
}
.chart {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  /* border: 1px solid red; */
  padding: 5px;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.chart.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  color: red;
  text-align: center;
  margin-top: 10px;
}
</style>
