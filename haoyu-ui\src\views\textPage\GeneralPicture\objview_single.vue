<template>
    <div>
      <div ref="objViewer" class="obj-viewer">
        <!-- 修改进度条显示 -->
        <transition name="fade">
          <div v-show="showProgress" class="download-progress-container">
            <div class="download-progress">
              <div class="progress-text">
                <i class="el-icon-loading"></i>
                正在加载模型... {{ totalProgress.toFixed(0) }}%
              </div>
              <el-progress
                :percentage="totalProgress"
                :stroke-width="8"
                :format="() => ''"
                status="primary">
              </el-progress>
            </div>
          </div>
        </transition>

        <!-- 添加操作说明按钮 -->
        <div class="operation-guide">
          <el-popover
            placement="right"
            width="300"
            trigger="hover">
            <div class="operation-tips">
              <h4>操作说明：</h4>
              <div class="tips-section">
                <h5>视角控制：</h5>
                <ul>
                  <li>鼠标左键：旋转视角</li>
                  <li>鼠标右键：平移视角</li>
                  <li>鼠标滚轮：缩放视角</li>
                </ul>
              </div>
              <div class="tips-section">
                <h5>浮框操作：</h5>
                <ul>
                  <li>拖拽：移动浮框位置</li>
                  <li>悬停：高亮对应模型</li>
                  <li>点击测点：查看测点数据</li>
                </ul>
              </div>
              <div class="tips-section">
                <h5>信号绑定：</h5>
                <ul>
                  <li>点击浮框内的信号：查看详细数据</li>
                  <li>点击信号名称：跳转到趋势图</li>
                  <li>点击频谱范围：查看频谱分析</li>
                </ul>
              </div>
              <div class="tips-section">
                <h5>快捷操作：</h5>
                <ul>
                  <li>Ctrl + 鼠标滚轮：快速缩放</li>
                </ul>
              </div>
            </div>
            <el-button slot="reference" type="text" icon="el-icon-question" class="guide-button">
              操作说明
            </el-button>
          </el-popover>
        </div>

        <!-- 修改浮框容器，基于绑定关系渲染 -->
        <div v-for="(match, index) in modelPointMatches" :key="match.id"
             class="model-tooltip"
             :style="getTooltipStyle(match)"
             v-show="match.isShow"
             @mousedown="handleBoxMouseDown($event, match)"
             @mouseenter="highlightModel(getModelByName(match.modelName))"
             @mouseleave="unhighlightModel(getModelByName(match.modelName))"
             :data-match-id="match.id">
          <div class="tooltip-content">
            <div class="tooltip-header">{{ match.pointName }}</div>
            <div v-if="getMatchedPoint(match)" class="measure-definitions">
              <div v-for="def in getPointDefinitions(match)"
                   :key="def.id"
                   class="definition-item"
                   @click="handleDefinitionSingleClick(def, match)"
                   @dblclick="handleDefinitionDoubleClick(def, match)">
                {{ def.name }}{{ def.lowerLimitFrequency && def.upperLimitFrequency ?
                  `(${def.lowerLimitFrequency}-${def.upperLimitFrequency}HZ)` : '' }}
                {{ def.value }} {{ getUnitText(def.timeSignalUnitId) }}
              </div>
            </div>
          </div>
        </div>
        <!-- 添加点和连线容器，基于绑定关系渲染 -->
        <div v-for="(match, index) in modelPointMatches" :key="'point-'+match.id"
             :data-match-id="match.id"
             class="model-point"
             :style="getPointStyle(getModelByName(match.modelName))"
             v-show="match.isShow"></div>
        <svg class="connection-lines" :style="getSvgStyle()">
          <path v-for="(match, index) in modelPointMatches"
                :key="'line-'+match.id"
                :data-match-id="match.id"
                :d="getConnectionPath(getModelByName(match.modelName), match)"
                class="connection-path"
                v-show="match.isShow" />
        </svg>
      </div>
      <!-- 添加匹配弹窗组件 -->
      <model-point-match
        :visible.sync="matchDialogVisible"
        :device-id="data.id"
        @confirm="handleMatchConfirm"
      />
    </div>
  </template>

  <script>
  import * as THREE from 'three';
  import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
  import { GetOrNewDateDeviceRelation, DownloadImgObjList,getModelPosition, GetBackgroundPhoto,getAllModelPosition } from '@/api/haoyu-system/web3d_api/web_3dapi.js';
  import ModelPointMatch from './components/ModelPointMatch.vue';
  import { getDeviceModelIdAndName, getDeviceMeasurePoint ,getDeviceWithPoint, CreateDeviceWithPoint, UpdateDeviceWithPoint, deleteDeviceWithPoint } from './api/Dalog.js';

  export default {
    name: 'ObjViewer',
    components: {
      ModelPointMatch
    },
    props: {
      data: {
        type: Object,
        default: null
      },
      pointList: {
        type: Array,
        default: () => []
      }
    },

    data() {
      return {
        scene: null,
        renderer: null,
        camera: null,
        controls: null,
        models: [],
        tooltipPositions: {},
        containerWidth: 0,
        containerHeight: 0,
        matchDialogVisible: false,
        modelPointMatches: [],
        pointStatuses: {},
        draggingBox: null,
        dragOffset: { x: 0, y: 0 },
        highlightedModel: null,
        originalScale: new THREE.Vector3(1, 1, 1),
        isAnimating: false,
        backgroundColor: '#ffffff',
        groundColor: '#ffffff',
        backgroundTexture: null,
        groundTexture: null,
        backgroundMesh: null,
        deviceId: null,
        showProgress: false,
        totalProgress: 0,
        modelLoadingCount: 0,
        totalModelsCount: 0,
        isInitialized: false,
        is2DView: false,
        previousRoute: null
      };
    },

    created() {
      this.previousRoute = this.$route.path;
    },

    mounted() {
      console.log('objView mounted, data:', this.data);
      this.deviceId = this.data.id;

      const fromPJ = this.previousRoute && this.previousRoute.includes('objview_pj');
      if (fromPJ) {
        console.log('从3D编辑页面返回，切换到2D视图');
        this.is2DView = true;
      }

      window.addEventListener('mousemove', this.handleMouseMove);
      window.addEventListener('mouseup', this.handleMouseUp);

      if (!this.is2DView) {
        this.$nextTick(() => {
          this.initObjViewer();
          this.loadDeviceModels();
        });
      } else {
        this.loadingModelPointMatches();
      }
    },

    beforeDestroy() {
      window.removeEventListener('resize', this.onWindowResize);
      window.removeEventListener('mousemove', this.handleMouseMove);
      window.removeEventListener('mouseup', this.handleMouseUp);

      // 清理Three.js资源
      if (this.scene) {
        this.resetScene();
      }

      if (this.renderer) {
        this.renderer.dispose();
        this.renderer.forceContextLoss();
        this.renderer.context = null;
        this.renderer.domElement = null;
        this.renderer = null;
      }

      // 清理其他资源
      this.scene = null;
      this.camera = null;
      this.controls = null;
      this.models = [];
      this.backgroundMesh = null;
    },

    methods: {
      async loadDeviceModels() {
        if (!this.data || !this.data.id) {
          console.warn('No valid data provided:', this.data);
          return;
        }

        console.log('Starting model loading...');

        // 重置进度状态
        this.showProgress = true;
        this.totalProgress = 0;
        this.modelLoadingCount = 0;

        // 确保进度条显示
        await this.$nextTick();

        try {
          const response = await GetOrNewDateDeviceRelation(this.data.id);
          if (!response || !response.data) {
            console.warn('No model data received');
            this.showProgress = false;
            return;
          }

          const modelList = response.data;
          if (!modelList || Object.keys(modelList).length === 0) {
            console.warn('No models to load');
            this.showProgress = false;
            return;
          }

          // 计算总模型数量
          this.totalModelsCount = Object.keys(modelList).filter(key => key !== 'error').length;
          console.log('Total models to load:', this.totalModelsCount);

          // const loadPromises = [];
          // for (const key in modelList) {
          //   if(key === 'error') continue;
          //   loadPromises.push(this.loadModelById(key));
          // }

          const loadPromises = [];
          let ids = ''
          for (const key in modelList) {
            if(key === 'error') continue;
            ids += key + ','
          }

          loadPromises.push(this.loadAllModelById(ids));

          await Promise.all(loadPromises);
          console.log('All models loaded');

          // 确保进度条显示100%
          this.totalProgress = 100;

          // 延迟隐藏进度条
          setTimeout(() => {
            this.showProgress = false;
            this.isInitialized = true;
          }, 500);
        } catch (error) {
          console.error('Error loading device models:', error);
          this.showProgress = false;
          this.$message.error('加载模型失败');
        }
      },
      
      async loadAllModelById(ids) {
        getAllModelPosition(ids).then(response => {
          console.log('response:', response)
        })
        return new Promise((resolve, reject) => {
          DownloadImgObjList(id).then(response => {
            if (!response) {
              reject(new Error('No response from download API'));
              return;
            }

            const blob = new Blob([response], { type: 'obj/binary' });
            const url = URL.createObjectURL(blob);

            getModelPosition(id).then((posResponse) => {
              const loader = new OBJLoader();

              loader.load(
                url,
                (object) => {
                  if (posResponse && posResponse.rows && posResponse.rows[0] && posResponse.rows[0].fileModelInfo) {
                    const modelInfo = posResponse.rows[0].fileModelInfo[0];

                    // 设置材质和颜色
                    const color = modelInfo.color || 'rgba(255,255,255,1)';
                    const rgbaMatch = color.match(/rgba\((\d+),(\d+),(\d+),([\d.]+)\)/);
                    let materialColor, opacity;

                    if (rgbaMatch) {
                      const [_, r, g, b, a] = rgbaMatch;
                      materialColor = new THREE.Color(
                        parseInt(r) / 255,
                        parseInt(g) / 255,
                        parseInt(b) / 255
                      );
                      opacity = parseFloat(a);
                    } else {
                      materialColor = new THREE.Color(0xffffff);
                      opacity = 1;
                    }

                    const material = new THREE.MeshStandardMaterial({
                      color: materialColor,
                      roughness: 0.3,
                      metalness: 0.8,
                      transparent: opacity < 1,
                      opacity: opacity
                    });

                    object.traverse((child) => {
                      if (child.isMesh) {
                        child.material = material.clone();
                        child.castShadow = true;
                        child.receiveShadow = true;
                      }
                    });

                    object.name = modelInfo.name || `模型${this.models.length + 1}`;
                  } else {
                    // 使用默认材质
                    const material = new THREE.MeshStandardMaterial({
                      color: 0xcccccc,
                      roughness: 0.3,
                      metalness: 0.8,
                      transparent: false,
                      opacity: 1
                    });

                    object.traverse((child) => {
                      if (child.isMesh) {
                        child.material = material.clone();
                        child.castShadow = true;
                        child.receiveShadow = true;
                      }
                    });

                    object.name = `模型${this.models.length + 1}`;
                  }

                  this.scene.add(object);
                  this.models.push(object);
                  // 更新进度
                  this.modelLoadingCount++;
                  this.totalProgress = (this.modelLoadingCount / this.totalModelsCount) * 100;
                  resolve();
                },
                (xhr) => {
                  // 更新单个模型的加载进度
                  if (xhr.lengthComputable) {
                    const individualProgress = (xhr.loaded / xhr.total) * (100 / this.totalModelsCount);
                    const baseProgress = (this.modelLoadingCount / this.totalModelsCount) * 100;
                    this.totalProgress = Math.min(baseProgress + individualProgress, 100);
                  }
                },
                (error) => {
                  console.error('Failed to load model:', error);
                  reject(error);
                }
              );
            }).catch(error => {
              console.error('获取模型信息失败:', error);
              reject(error);
            });
          }).catch(error => {
            console.error('下载模型失败:', error);
            reject(error);
          });
        });
      },

      loadModelById(id) {
        return new Promise((resolve, reject) => {
          DownloadImgObjList(id).then(response => {
            if (!response) {
              reject(new Error('No response from download API'));
              return;
            }

            const blob = new Blob([response], { type: 'obj/binary' });
            const url = URL.createObjectURL(blob);

            getModelPosition(id).then((posResponse) => {
              const loader = new OBJLoader();

              loader.load(
                url,
                (object) => {
                  if (posResponse && posResponse.rows && posResponse.rows[0] && posResponse.rows[0].fileModelInfo) {
                    const modelInfo = posResponse.rows[0].fileModelInfo[0];

                    // 设置材质和颜色
                    const color = modelInfo.color || 'rgba(255,255,255,1)';
                    const rgbaMatch = color.match(/rgba\((\d+),(\d+),(\d+),([\d.]+)\)/);
                    let materialColor, opacity;

                    if (rgbaMatch) {
                      const [_, r, g, b, a] = rgbaMatch;
                      materialColor = new THREE.Color(
                        parseInt(r) / 255,
                        parseInt(g) / 255,
                        parseInt(b) / 255
                      );
                      opacity = parseFloat(a);
                    } else {
                      materialColor = new THREE.Color(0xffffff);
                      opacity = 1;
                    }

                    const material = new THREE.MeshStandardMaterial({
                      color: materialColor,
                      roughness: 0.3,
                      metalness: 0.8,
                      transparent: opacity < 1,
                      opacity: opacity
                    });

                    object.traverse((child) => {
                      if (child.isMesh) {
                        child.material = material.clone();
                        child.castShadow = true;
                        child.receiveShadow = true;
                      }
                    });

                    object.name = modelInfo.name || `模型${this.models.length + 1}`;
                  } else {
                    // 使用默认材质
                    const material = new THREE.MeshStandardMaterial({
                      color: 0xcccccc,
                      roughness: 0.3,
                      metalness: 0.8,
                      transparent: false,
                      opacity: 1
                    });

                    object.traverse((child) => {
                      if (child.isMesh) {
                        child.material = material.clone();
                        child.castShadow = true;
                        child.receiveShadow = true;
                      }
                    });

                    object.name = `模型${this.models.length + 1}`;
                  }

                  this.scene.add(object);
                  this.models.push(object);
                  // 更新进度
                  this.modelLoadingCount++;
                  this.totalProgress = (this.modelLoadingCount / this.totalModelsCount) * 100;
                  resolve();
                },
                (xhr) => {
                  // 更新单个模型的加载进度
                  if (xhr.lengthComputable) {
                    const individualProgress = (xhr.loaded / xhr.total) * (100 / this.totalModelsCount);
                    const baseProgress = (this.modelLoadingCount / this.totalModelsCount) * 100;
                    this.totalProgress = Math.min(baseProgress + individualProgress, 100);
                  }
                },
                (error) => {
                  console.error('Failed to load model:', error);
                  reject(error);
                }
              );
            }).catch(error => {
              console.error('获取模型信息失败:', error);
              reject(error);
            });
          }).catch(error => {
            console.error('下载模型失败:', error);
            reject(error);
          });
        });
      },

      async loadingModelPointMatches() {
        try {
          const response = await getDeviceWithPoint(this.deviceId);
          if (response && response.code === 200 && response.rows) {
            this.tooltipPositions = {};

            this.modelPointMatches = response.rows.map(row => ({
              modelName: row.modelName,
              pointName: row.measurementName,
              isShow: row.isShow === "1",
              id: row.id,
              modelId: row.modelId,
              measurementId: row.measurementId
            }));

            let hasSetPosition = false;
            for (const row of response.rows) {
              if (row.id && row.location) {
                try {
                  const location = JSON.parse(row.location);
                  if (Array.isArray(location) && location.length >= 2) {
                    this.$set(this.tooltipPositions, row.id, {
                      x: location[0],
                      y: location[1]
                    });
                    hasSetPosition = true;
                  }
                } catch (error) {
                  console.error('解析location失败:', error);
                }
              }
            }

            if (!hasSetPosition) {
              this.modelPointMatches.forEach(match => {
                this.setDefaultPosition(match);
              });
            }

            await this.$nextTick();
            this.$forceUpdate();
          }
        } catch (error) {
          console.error('获取设备绑定情况失败:', error);
        }
      },

      setDefaultPosition(match) {
        if (!match || !match.id) return;

        const index = this.modelPointMatches.findIndex(m => m.id === match.id);
        if (index !== -1) {
          const xSpacing = 100 / (this.modelPointMatches.length + 1);
          this.$set(this.tooltipPositions, match.id, {
            x: xSpacing * (index + 1),
            y: 10 + (index * 5)
          });
        }
      },

      async initObjViewer() {
        const container = this.$refs.objViewer;
        if (!container) {
          console.error('Container element not found');
          return;
        }

        // 保存容器尺寸
        this.containerWidth = container.offsetWidth;
        this.containerHeight = container.offsetHeight;

        console.log('Container dimensions:', {
          width: this.containerWidth,
          height: this.containerHeight
        });

        // 场景设置
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(this.backgroundColor);

        // 相机设置
        this.camera = new THREE.PerspectiveCamera(
          75,
          this.containerWidth / this.containerHeight,
          0.1,
          1000
        );
        this.camera.position.set(0, 2, 5);
        this.camera.lookAt(0, 0, 0);

        // 渲染设置
        this.renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true
        });
        this.renderer.setSize(this.containerWidth, this.containerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        container.appendChild(this.renderer.domElement);

        this.initLightingAndGround();

        // 控制器设置
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.minDistance = 3;
        this.controls.maxDistance = 20;
        this.controls.maxPolarAngle = Math.PI / 2;
        this.controls.target.set(0, 0, 0);

        // 监听容器尺寸变化
        const resizeObserver = new ResizeObserver(() => {
          this.containerWidth = container.offsetWidth;
          this.containerHeight = container.offsetHeight;
          this.onWindowResize();
          this.updateAllTooltipPositions();
        });
        resizeObserver.observe(container);

        // 加载背景设置
        await this.loadBackgroundSettings();

        // 开始动画循环
        this.animate();
      },

      updateAllTooltipPositions() {
        if (!this.modelPointMatches) return;

        this.modelPointMatches.forEach(match => {
          if (match.id && this.tooltipPositions[match.id]) {
            const position = this.tooltipPositions[match.id];
            // 确保位置在容器范围内
            this.$set(this.tooltipPositions, match.id, {
              x: Math.max(50, Math.min(position.x, this.containerWidth - 50)),
              y: Math.max(50, Math.min(position.y, this.containerHeight - 50))
            });
          }
        });
      },

      initLightingAndGround() {
        // 增加环境光强度
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        this.scene.add(ambientLight);

        // 调整平行光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(5, 10, 7.5);
        directionalLight.castShadow = true;
        // 调整阴影参数
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        this.scene.add(directionalLight);

        // 添加一个辅助光源，使地面更亮
        const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0xffffff, 0.4);
        this.scene.add(hemisphereLight);

        // 解析地面颜色的透明度
        const groundColorStr = this.groundColor;
        let groundOpacity = 1;
        if (groundColorStr.startsWith('rgba')) {
          const matches = groundColorStr.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
          if (matches) {
            groundOpacity = parseFloat(matches[4]);
          }
        }

        // 只在透明度不为0时创建地面
        if (groundOpacity !== 0) {
          const planeGeometry = new THREE.PlaneGeometry(100, 100);
          const planeMaterial = new THREE.MeshBasicMaterial({
            color: new THREE.Color(this.groundColor),
            side: THREE.DoubleSide,  // 双面可见
            transparent: groundOpacity < 1,
            opacity: groundOpacity
          });

          // 如果有地面纹理，添加到材质
          if (this.groundTexture) {
            planeMaterial.map = this.groundTexture;
          }

          const plane = new THREE.Mesh(planeGeometry, planeMaterial);
          plane.rotation.x = -Math.PI / 2;
          plane.position.y = -1;
          plane.receiveShadow = true;
          plane.isGround = true;
          this.scene.add(plane);
        }
      },

      resetScene() {
        if (!this.scene) return;

        while (this.scene.children.length > 0) {
          const object = this.scene.children[0];
          if (!object) continue;

          this.scene.remove(object);
          if (object.geometry) {
            object.geometry.dispose();
          }
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach((material) => {
                if (material) material.dispose();
              });
            } else {
              object.material.dispose();
            }
          }
        }
      },

      onWindowResize() {
        const container = this.$refs.objViewer;
        if (!container) return;

        this.containerWidth = container.offsetWidth;
        this.containerHeight = container.offsetHeight;

        if (this.camera) {
          this.camera.aspect = this.containerWidth / this.containerHeight;
          this.camera.updateProjectionMatrix();
        }

        if (this.renderer) {
          this.renderer.setSize(this.containerWidth, this.containerHeight);
        }

        // 更新所有浮框位置
        this.updateAllTooltipPositions();
      },

      getTooltipStyle(match) {
        if (!match) return { display: 'none' };

        const position = this.tooltipPositions[match.id] || {
          x: 50,
          y: 10
        };

        if (this.is2DView) {
          return {
            position: 'absolute',
            left: `${position.x}%`,
            top: `${position.y}%`,
            transform: 'translate(-50%, 0)',
            cursor: 'move'
          };
        }

        return {
          position: 'absolute',
          left: `${position.x}%`,
          top: `${position.y}%`,
          transform: 'translate(-50%, 0)',
          cursor: 'move'
        };
      },

      getPointStyle(model) {
        if (this.is2DView) {
          return {
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: '16px',
            height: '16px',
            transform: 'translate(-50%, -50%)',
            pointerEvents: 'none'
          };
        }

        if (!model || !this.camera) return {};

        const vector = new THREE.Vector3();
        const box = new THREE.Box3().setFromObject(model);
        box.getCenter(vector);
        vector.project(this.camera);

        const x = (vector.x * 0.5 + 0.5) * this.renderer.domElement.clientWidth;
        const y = (-vector.y * 0.5 + 0.5) * this.renderer.domElement.clientHeight;

        return {
          position: 'absolute',
          left: `${x}px`,
          top: `${y}px`,
          width: '16px',
          height: '16px',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
          display: vector.z > 1 ? 'none' : 'block'
        };
      },

      getSvgStyle() {
        return {
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none'
        };
      },

      getConnectionPath(model, match) {
        if (this.is2DView) {
          const boxPosition = this.tooltipPositions[match.id];
          if (!boxPosition) return '';

          const container = this.$refs.objViewer;
          if (!container) return '';

          const rect = container.getBoundingClientRect();
          const startX = (boxPosition.x * rect.width / 100);
          const startY = (boxPosition.y * rect.height / 100) + 80;
          const endX = rect.width / 2;
          const endY = rect.height / 2;

          const middleY = (startY + endY) / 2;
          return `M ${startX} ${startY} L ${startX} ${middleY} L ${endX} ${middleY} L ${endX} ${endY}`;
        }

        if (!model || !this.camera || !match) return '';

        const boxPosition = this.tooltipPositions[match.id];
        if (!boxPosition) return '';

        const container = this.$refs.objViewer;
        if (!container) return '';

        const rect = container.getBoundingClientRect();

        const startX = (boxPosition.x * rect.width / 100);
        const startY = (boxPosition.y * rect.height / 100) + 80;

        const vector = new THREE.Vector3();
        const box = new THREE.Box3().setFromObject(model);
        box.getCenter(vector);
        vector.project(this.camera);

        const endX = (vector.x * 0.5 + 0.5) * rect.width;
        const endY = (-vector.y * 0.5 + 0.5) * rect.height;

        if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
          return '';
        }

        const middleY = (startY + endY) / 2;
        return `M ${startX} ${startY} L ${startX} ${middleY} L ${endX} ${middleY} L ${endX} ${endY}`;
      },

      animate() {
        if (this.renderer && this.scene) {
          requestAnimationFrame(this.animate);
          this.controls.update();

          // 执行脉冲动画
          if (this.highlightedModel && this.isAnimating) {
            const time = Date.now() * 0.001; // 将毫秒转换为秒
            const scale = 1.05 + Math.sin(time * 3) * 0.02; // 使用正弦函数创建脉冲效果
            if (this.highlightedModel.scale && this.originalScale) {
              this.highlightedModel.scale.copy(this.originalScale).multiplyScalar(scale);
            }
          }

          // 只在没有拖拽时更新连线和点的位置
          if (!this.draggingBox) {
            // 清除所有现有的点和线
            const points = document.querySelectorAll('.model-point');
            const paths = document.querySelectorAll('.connection-path');

            // 隐藏所有点和线
            points.forEach(point => {
              point.style.display = 'none';
            });
            paths.forEach(path => {
              path.style.display = 'none';
            });

            // 更新需要显示的点和线
            this.modelPointMatches.forEach((match) => {
              if (match.isShow) {
                // 更新点
                const point = document.querySelector(`.model-point[data-match-id="${match.id}"]`);
                if (point) {
                  const style = this.getPointStyle(this.getModelByName(match.modelName));
                  Object.assign(point.style, style);
                  point.style.display = style.display;
                }

                // 更新连线
                const path = document.querySelector(`.connection-path[data-match-id="${match.id}"]`);
                if (path) {
                  const pathData = this.getConnectionPath(this.getModelByName(match.modelName), match);
                  path.setAttribute('d', pathData);
                  path.style.display = pathData ? 'block' : 'none';
                }
              }
            });
          }

          this.renderer.render(this.scene, this.camera);
        }
      },

      moveModel(direction, axis) {
        if (this.selectedModelIndex !== null) {
          const model = this.models[this.selectedModelIndex];
          const step = this.inputStep || 0.01;

          switch(direction) {
            case 'left':
              model.position.x -= step;
              break;
            case 'right':
              model.position.x += step;
              break;
            case 'up':
              model.position.y += step;
              break;
            case 'down':
              model.position.y -= step;
              break;
            case 'forward':
              model.position.z -= step;
              break;
            case 'backward':
              model.position.z += step;
              break;
          }

          // 更新浮框位置
          this.$forceUpdate();
        }
      },

      isModelVisible(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        return match ? match.isShow : true;
      },

      getModelPointName(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        return match ? match.pointName : model.name;
      },

      getPointStatus(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        if (!match) return '正常';
        const point = this.pointList.find(p => p.title === match.pointName);
        return point ? point.status || '正常' : '正常';
      },

      getPointHealth(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        if (!match) return 100;
        const point = this.pointList.find(p => p.title === match.pointName);
        return point ? point.healthValue || 100 : 100;
      },

      handleMatchConfirm(matches) {
        this.modelPointMatches = matches;

        // 更新或初始化每个匹配的位置信息
        matches.forEach(match => {
          // 如果没有位置信息，设置默认位置
          if (!this.tooltipPositions[match.id]) {
            this.setDefaultPosition(match);
          }
        });

        // 重新加载位置信息
        this.loadingModelPointMatches();
      },

      hasMatchedPoint(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        return match && match.pointName;
      },

      getMatchedPoint(match) {
        return this.pointList.find(p => p.title === match.pointName);
      },

      getPointDefinitions(match) {
        const point = this.getMatchedPoint(match);
        return point ? point.measureDefinitions || [] : [];
      },

      handleBoxMouseDown(event, match) {
        event.preventDefault();
        this.draggingBox = match;
        const container = this.$refs.objViewer;
        const rect = container.getBoundingClientRect();
        const position = this.tooltipPositions[match.id] || {
          x: 50,
          y: 10
        };

        this.dragOffset = {
          x: event.clientX - rect.left - (position.x * rect.width / 100),
          y: event.clientY - rect.top - (position.y * rect.height / 100)
        };
      },

      handleMouseMove(event) {
        if (this.draggingBox) {
          const container = this.$refs.objViewer;
          const rect = container.getBoundingClientRect();

          const x = ((event.clientX - rect.left - this.dragOffset.x) / rect.width) * 100;
          const y = ((event.clientY - rect.top - this.dragOffset.y) / rect.height) * 100;

          const boundedX = Math.max(0, Math.min(100, x));
          const boundedY = Math.max(0, Math.min(100, y));

          this.$set(this.tooltipPositions, this.draggingBox.id, { x: boundedX, y: boundedY });

          const path = document.querySelector(`.connection-path[data-match-id="${this.draggingBox.id}"]`);
          if (path) {
            path.setAttribute('d', this.getConnectionPath(this.getModelByName(this.draggingBox.modelName), this.draggingBox));
          }
        }
      },

      async handleMouseUp() {
        if (this.draggingBox) {
          const match = this.draggingBox;
          const position = this.tooltipPositions[match.id];

          try {
            if (match.id) {
              const updatedData = {
                id: match.id,
                deviceId: Number(this.deviceId),
                modelId: Number(match.modelId),
                measurementId: Number(match.measurementId),
                isShow: match.isShow ? "1" : "0",
                location: JSON.stringify([position.x, position.y, 0])
              };

              await UpdateDeviceWithPoint(updatedData);
              console.log('浮框位置（百分比）已保存到后端');
            }
          } catch (error) {
            console.error('保存位置信息失败:', error);
          }
        }
        this.draggingBox = null;
      },

      handleDefinitionSingleClick(def, match) {
        const point = this.getMatchedPoint(match);
        if (!point) return;

        // 发出事件通知父组件更新趋势图和健康度
        this.$emit('update-trend', {
          id: def.id,
          pointId: point.id,
          pointTitle: point.title,
          definitionName: def.name
        });
      },

      handleDefinitionDoubleClick(def, match) {
        const point = this.getMatchedPoint(match);
        if (!point) return;

        this.$emit('definition-click', {
          id: def.id,
          pointId: point.id,
          title: def.name,
          pointTitle: point.title
        });
      },

      getUnitText(unitName) {
        switch(unitName) {
          case "2":
            return 'm/s²';
          case "1":
            return 'mm/s';
          case "4":
            return 'm/s²';
          case "3":
            return 'μm';
          default:
            return '';
        }
      },

      highlightModel(model) {
        if (!model) return;

        // 如果是同一个模型，不重复处理
        if (this.highlightedModel === model) return;

        // 如果之前有高亮的模型，先恢复它的状态
        if (this.highlightedModel) {
          this.unhighlightModel(this.highlightedModel);
        }

        this.highlightedModel = model;
        this.isAnimating = true;

        // 保存原始缩放
        this.originalScale.copy(model.scale);

        // 遍历所有模型，设置透明度
        this.models.forEach(otherModel => {
          if (otherModel !== model) {
            otherModel.traverse(child => {
              if (child.isMesh) {
                // 保存原始材质属性（如果还没保存）
                if (!child.material.originalOpacity) {
                  child.material.originalOpacity = child.material.opacity;
                }
                // 设置其他模型为半透明
                child.material.transparent = true;
                child.material.opacity = 0.1;
                child.material.needsUpdate = true;
              }
            });
          }
        });

        // 设置选中模型的高亮状态
        model.traverse(child => {
          if (child.isMesh) {
            // 保存原始材质属性
            child.material.originalColor = child.material.color.clone();
            child.material.originalEmissive = child.material.emissive ? child.material.emissive.clone() : new THREE.Color(0x000000);
            child.material.originalOpacity = child.material.opacity;

            // 设置高亮状态
            child.material.emissive = new THREE.Color(0x3399ff);
            child.material.emissiveIntensity = 0.3;
            child.material.transparent = false;
            child.material.opacity = 1;
            child.material.needsUpdate = true;
          }
        });
      },

      unhighlightModel(model) {
        if (!model) return;

        // 停止动画
        this.isAnimating = false;

        // 恢复所有模型的原始状态
        this.models.forEach(otherModel => {
          otherModel.traverse(child => {
            if (child.isMesh && child.material.originalOpacity !== undefined) {
              child.material.transparent = child.material.originalOpacity < 1;
              child.material.opacity = child.material.originalOpacity;
              // 如果是之前的高亮模型，还原其他属性
              if (otherModel === model) {
                child.material.emissive.copy(child.material.originalEmissive);
                child.material.emissiveIntensity = 0;
              }
              child.material.needsUpdate = true;
            }
          });
        });

        // 恢复原始大小
        if (this.originalScale && model.scale) {
          model.scale.copy(this.originalScale);
        }

        // 清理状态
        if (this.highlightedModel === model) {
          this.highlightedModel = null;
        }
      },

      easeInOutQuad(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      },

      async loadBackgroundSettings() {
        try {
          // 确保scene已经初始化
          if (!this.scene) {
            console.warn('Scene not initialized yet');
            return;
          }

          const response = await GetBackgroundPhoto(this.deviceId);
          if (response.code === 200) {
            const data = response.data;

            // 设置背景颜色
            if (data['color']) {
              const colorArray = JSON.parse(data.color);
              const color = new THREE.Color(
                colorArray[0] / 255,
                colorArray[1] / 255,
                colorArray[2] / 255
              );
              this.scene.background = color;
              this.backgroundColor = `#${color.getHexString()}`;
            }

            // 设置地面颜色
            if (data['ground-color']) {
              const groundColorArray = JSON.parse(data['ground-color']);
              const groundColor = groundColorArray.length === 4 ?
                `rgba(${groundColorArray[0]},${groundColorArray[1]},${groundColorArray[2]},${groundColorArray[3]})` :
                `rgb(${groundColorArray[0]},${groundColorArray[1]},${groundColorArray[2]})`;
              this.groundColor = groundColor;

              // 查找现有的地面
              let groundMesh = null;
              this.scene.traverse((object) => {
                if (object.isGround) {
                  groundMesh = object;
                }
              });

              // 如果透明度为0，移除地面
              if (groundColorArray.length === 4 && groundColorArray[3] <= 0.2) {
                if (groundMesh) {
                  this.scene.remove(groundMesh);
                  if (groundMesh.geometry) groundMesh.geometry.dispose();
                  if (groundMesh.material) groundMesh.material.dispose();
                }
              } else {
                // 如果已存在地面，更新它
                if (groundMesh) {
                  const opacity = groundColorArray.length === 4 ? groundColorArray[3] : 1;
                  groundMesh.material.color = new THREE.Color(
                    groundColorArray[0] / 255,
                    groundColorArray[1] / 255,
                    groundColorArray[2] / 255
                  );
                  groundMesh.material.transparent = opacity < 1;
                  groundMesh.material.opacity = opacity;
                  groundMesh.material.needsUpdate = true;
                } else {
                  // 如果不存在地面，创建新的
                  this.initLightingAndGround();
                }
              }
            }

            // 加载地面纹理
            if (data['ground-image']) {
              await new Promise((resolve, reject) => {
                const textureLoader = new THREE.TextureLoader();
                textureLoader.setCrossOrigin('Anonymous');
                textureLoader.load(
                  data['ground-image'],
                  (texture) => {
                    texture.wrapS = THREE.RepeatWrapping;
                    texture.wrapT = THREE.RepeatWrapping;
                    texture.repeat.set(10, 10);
                    this.groundTexture = texture;

                    let groundMesh = null;
                    this.scene.traverse((object) => {
                      if (object.isGround) {
                        groundMesh = object;
                      }
                    });

                    if (groundMesh) {
                      groundMesh.material.map = texture;
                      groundMesh.material.needsUpdate = true;
                    }
                    resolve();
                  },
                  undefined,
                  reject
                );
              });
            }

            // 加载背景图片
            if (data['background-image']) {
              await new Promise((resolve, reject) => {
                const textureLoader = new THREE.TextureLoader();
                textureLoader.setCrossOrigin('Anonymous');
                textureLoader.load(
                  data['background-image'],
                  (texture) => {
                    if (this.backgroundMesh) {
                      this.scene.remove(this.backgroundMesh);
                      this.backgroundMesh.geometry.dispose();
                      this.backgroundMesh.material.dispose();
                    }

                    const geometry = new THREE.SphereGeometry(500, 60, 40);
                    geometry.scale(-1, 1, 1);
                    const material = new THREE.MeshBasicMaterial({
                      map: texture
                    });
                    this.backgroundMesh = new THREE.Mesh(geometry, material);
                    this.scene.add(this.backgroundMesh);
                    this.backgroundTexture = texture;
                    this.scene.background = null;
                    resolve();
                  },
                  undefined,
                  reject
                );
              });
            }
          }
        } catch (error) {
          console.error('加载背景设置失败:', error);
        }
      },

      getModelByName(modelName) {
        return this.models.find(model => model.name === modelName);
      }
    },

    watch: {
      data: {
        handler(newVal) {
          console.log('objView data changed:', newVal)
        },
        deep: true
      }
    }
  };
  </script>

  <style scoped>
  .obj-viewer {
    width: 100%;
    height: 100%;
    min-height: 500px;
    background: #ffffff;
    position: relative; /* 添加相对定位 */
    overflow: hidden;
  }

  .model-tooltip {
    position: absolute;
    z-index: 10;
    pointer-events: auto;
    cursor: move;
    user-select: none;
    transition: transform 0.3s ease;
  }

  .tooltip-content {
    background: linear-gradient(135deg, rgba(18, 30, 52, 0.98), rgba(5, 15, 35, 0.9));
    border-radius: 8px;
    padding: 12px;
    color: white;
    font-size: 12px;
    min-width: 150px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 20px rgba(0, 140, 255, 0.15),
                inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 140, 255, 0.2);
  }

  .tooltip-header {
    font-weight: bold;
    margin-bottom: 6px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(0, 140, 255, 0.3);
    color: #1890ff;
    text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
  }

  .tooltip-body {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .tooltip-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-value {
    color: #67C23A;
  }

  .health-value {
    color: #409EFF;
  }

  .model-point {
    position: absolute;
    width: 16px !important;
    height: 16px !important;
    background: radial-gradient(circle at center, #1890ff 30%, rgba(24, 144, 255, 0.8) 70%);
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.5),
                0 0 5px rgba(24, 144, 255, 0.8);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.8);
    pointer-events: none;
    transition: none;
  }

  .connection-lines {
    pointer-events: none;
    z-index: 1;
  }

  .connection-path {
    fill: none;
    stroke: rgba(24, 144, 255, 0.7);
    stroke-width: 3;  /* 增加线条粗细 */
    stroke-dasharray: 6 6;  /* 调整虚线间距 */
    filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.3));
    transition: stroke-width 0.3s ease;
  }

  .connection-path:hover {
    stroke-width: 5;
    stroke: rgba(24, 144, 255, 0.9);
  }

  .measure-definitions {
    margin-top: 10px;
    background: rgba(0, 140, 255, 0.1);
    border-radius: 4px;
    padding: 4px;
  }

  .definition-item {
    padding: 6px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    pointer-events: auto;
    border-radius: 4px;
    margin: 2px 0;
  }

  .definition-item:hover {
    background: rgba(24, 144, 255, 0.2);
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.1);
    transform: translateX(2px);
  }

  .model-tooltip {
    cursor: move;
    user-select: none;
  }

  .model-tooltip:hover {
    transform: translate(-50%, 0) scale(1.05);
  }

  .operation-guide {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
  }

  .guide-button {
    background: rgba(24, 144, 255, 0.1);
    border-radius: 4px;
    padding: 8px 16px;
    color: #1890ff;
    transition: all 0.3s;
  }

  .guide-button:hover {
    background: rgba(24, 144, 255, 0.2);
  }

  .operation-tips {
    font-size: 14px;
    color: #333;
  }

  .operation-tips h4 {
    margin: 0 0 16px 0;
    color: #1890ff;
    font-size: 16px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
  }

  .tips-section {
    margin-bottom: 16px;
  }

  .tips-section h5 {
    margin: 0 0 8px 0;
    color: #1890ff;
    font-size: 14px;
  }

  .tips-section ul {
    margin: 0;
    padding-left: 20px;
  }

  .tips-section li {
    margin: 4px 0;
    color: #666;
    line-height: 1.5;
  }

  .tips-section li:hover {
    color: #1890ff;
  }

  .download-progress-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .download-progress {
    width: 300px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .progress-text {
    text-align: center;
    margin-bottom: 15px;
    color: #606266;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .progress-text i {
    color: #409EFF;
    font-size: 16px;
  }

  /* 添加过渡动画 */
  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
  }

  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

  /* 自定义进度条样式 */
  :deep(.el-progress-bar__outer) {
    background-color: #e9ecef !important;
  }

  :deep(.el-progress-bar__inner) {
    background-color: #409EFF !important;
    transition: width 0.3s ease !important;
  }
  </style>
