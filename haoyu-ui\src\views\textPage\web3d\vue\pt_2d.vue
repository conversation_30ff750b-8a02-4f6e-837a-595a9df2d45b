<template>
    <div>
      <div>设备现场图片</div>
      <el-upload
        ref="upload"
        action="#"
        list-type="picture-card"
        :auto-upload="true"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :file-list="fileList">
        <i slot="default" class="el-icon-plus"></i>
        <div slot="file" slot-scope="{ file }">
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
              <i class="el-icon-download"></i>
            </span>
            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </el-upload>

      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
      <!-- 省略分页控件 -->
    </div>
  </template>

  <script>
  import { UploadFile, GetImgObjList, DownloadImgObjList ,DeleteItem } from '@/api/haoyu-system/web3d_api/web_3dapi.js';
  import { chunkAndMergeFile } from '../js/ChunkAndMergeFile.js';

  export default {
    data() {
      return {
        dialogImageUrl: '',
        dialogVisible: false,
        disabled: false,
        fileList: [],
        download_list: [],
        uploadData: [],
        pageSize: 5, // 每页显示的图片数量
        pageNum: 1 // 当前页码
      };
    },
    created() {
      // 在组件创建时获取图片列表
      this.fetchImageList();
    },
    methods: {
      fetchImageList() {
        // 调用 get_img_obj_list 获取图片列表
        GetImgObjList('png', this.pageSize, this.pageNum).then((response) => {
          this.download_list = response.rows;
          // 清空 fileList，然后重新加载
          this.fileList = [];
          this.download_list.forEach((item) => {
            if (item.fileName.endsWith('.png') || item.fileName.endsWith('.jpg') || item.fileName.endsWith('.jpeg')) {
              this.DownloadAndLoading(item.id);
            }
          });
        });
      },

      DownloadAndLoading(id) {
        // 调用下载 API，获取二进制数据流
        DownloadImgObjList(id).then((response) => {
          // 创建 Blob 对象
          const blob = new Blob([response], { type: 'image/png' }); // 根据实际类型调整 MIME 类型
          const url = URL.createObjectURL(blob); // 创建 Blob URL

          // 检查图片是否已存在于 fileList 中，避免重复添加
          if (!this.fileList.some(file => file.uid === id)) {
            // 将生成的图片添加到 fileList 中
            this.fileList.push({
              name: `image_${id}.png`, // 给图片一个名称
              url: url, // 使用 Blob URL 显示图片
              status: 'success',
              uid: id // 使用唯一的 ID 作为 uid
            });

            // 如果 fileList 超过了 pageSize，则删除最前面的图片
            if (this.fileList.length > this.pageSize) {
              const removedFile = this.fileList.shift(); // 移除最前面的图片
              // 释放 Blob URL
              if (removedFile.url.startsWith('blob:')) {
                URL.revokeObjectURL(removedFile.url);
              }
            }
          }
        }).catch((error) => {
          console.error('下载图片时出错:', error);
          this.$message.error('图片下载失败');
        });
      },

      handleRemove(file) {
        // 调用删除 API
        DeleteItem(file.uid).then(() => {
          this.$message.success('图片删除成功');
          // 从 fileList 中移除
          this.fileList = this.fileList.filter((f) => f.uid !== file.uid);
          // 重新获取图片列表，确保页面上的图片数量为 pageSize
          this.fetchImageList();
        }).catch((error) => {
          console.error('删除图片时出错:', error);
          this.$message.error('图片删除失败');
        });
      },

      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },

      handleDownload(file) {
        console.log(file);
        // 实现下载功能（如果需要）
      },

      beforeUpload(file) {
        // 检查文件格式是否为 PNG/JPG/JPEG/BMP
        const isValidType = ['image/png', 'image/jpeg', 'image/jpg', 'image/bmp'].includes(file.type);
        if (!isValidType) {
          this.$message.error('只能上传 PNG/JPG/JPEG/BMP 格式的图片');
          return false;
        }

        // 上传文件
        chunkAndMergeFile(file).then(() => {
          this.$message.success('图片上传成功');
          // 上传成功后，重新获取图片列表
          this.fetchImageList();
        }).catch((error) => {
          console.error('上传图片时出错:', error);
          this.$message.error('图片上传失败');
        });

        return false; // 阻止默认的上传行为
      },

      beforeDestroy() {
        // 在组件销毁时释放所有的 Blob URL
        this.fileList.forEach(file => {
          if (file.url.startsWith('blob:')) {
            URL.revokeObjectURL(file.url);
          }
        });
      },

      handleSuccess(response, file) {
        this.$message.success('图片上传成功');
      }
    }
  };
  </script>
