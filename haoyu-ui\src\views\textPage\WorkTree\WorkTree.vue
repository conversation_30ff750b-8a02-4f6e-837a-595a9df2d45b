<template>
  <div ref="treeContainer" class="tree-container" @contextmenu.prevent="handleRightClick">
    <!-- 搜索框 -->
    <div class="content-wrapper" style="max-height: 40vh;">
      <!-- 搜索框和搜索按钮 -->
      <div class="search-container">
        <a-input-search
          v-model="searchQuery"
          class="search-input"
          placeholder="搜索节点..."
          @search="performSearch"
        />
        <a-button
          type="default"
          shape="circle"
          icon="reload"
          @click="Reset"
        />
      </div>
      <!-- 树 -->
      <a-tree
        v-if="treeData.length > 0"
        ref="tree"
        show-icon
        show-line
        show-leaf-icon="false"
        :selected-keys="[selectedKey]"
        :icon="getIcon"
        :tree-data="treeData"
        :expanded-keys="expandedKeys"
        class="custom-tree"
        @dblclick="handleDoubleClick"
        @rightClick="handleRightClickOnNode"
        @select="handleSelect"
        @expand="handleExpand"
      >
        <!-- 定义树节点的标题模板 -->
        <template #title="{ title }">
          {{ title }}
        </template>
      </a-tree>
    </div>
    <!-- 修改节点名称 -->
    <a-modal
      v-model="dialogVisible"
      title="修改节点名称"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <a-input v-model="dialogTitle" />
    </a-modal>
    <!-- 新建下一级区域名称 -->
    <a-modal
      v-model="newNodeDialogVisible"
      title="新建下一级区域"
      @ok="handleNewNodeOk"
      @cancel="handleNewNodeCancel"
    >
      <a-input v-model="newNodeTitle" placeholder="请输入区域名称" />
    </a-modal>

    <!-- 新建设备 -->
    <a-modal
      v-model="newDeviceDialogVisible"
      title="设备信息"
      :width="800"
      class="centered-modal"
    >
      <a-form>
        <a-form-item
          v-for="(item, index) in formItems"
          :key="index"
          class="form-item"
          :label="item.label"
        >
          <a-input v-model="item.model" :placeholder="item.placeholder" />
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="modal-footer">
          <a-button @click="handleNewDeviceTermSettings">词条设置</a-button>
          <a-button @click="handleNewDeviceCancel">取消</a-button>
          <a-button type="primary" @click="handleNewDeviceOk">确定</a-button>
        </div>
      </template>
    </a-modal>
    <!-- 右击事件组件 -->
    <!-- <RightClickMenu ref="rightClickMenu" :container-rect="containerRect" @menu-click="handleMenuClick" /> -->
  </div>
</template>

<script>
import { Tree, Input, Modal, message } from 'ant-design-vue'
// import RightClickMenu from '../RightClickMenu/RightClickMenu.vue'
import hotkeys from 'hotkeys-js'
import rightClickHandlers from './rightClickHandlers'
import { listDeviceManagement } from '@/api/haoyu-system/deviceManagement/deviceManagement.js'

import { mapState, mapActions } from 'vuex'

export default {
  components: {
    'a-tree': Tree,
    'a-input': Input,
    'a-modal': Modal //,
    /* RightClickMenu */
  },
  data() {
    return {
      // 定义树形数据
      treeData: [],
      // 输入框的值
      formItems: [
        { label: '序列号：', model: 'newNodeSerialNumber', placeholder: '请输入序列号' },
        { label: '名称：', model: 'newNodeName', placeholder: '请输入名称' },
        { label: '马达序列号：', model: 'newNodeMotorSerial', placeholder: '请输入马达序列号' },
        { label: '转动方式：', model: 'newNodeRotationMethod', placeholder: '请输入转动方式' },
        { label: '轴承型号：', model: 'newNodeBearingModel', placeholder: '请输入轴承型号' },
        { label: '设备使用方式：', model: 'newNodeUsageMethod', placeholder: '请输入设备使用方式' }
      ],
      // 控制模态框的显示状态
      dialogVisible: false,
      // 设备信息弹窗
      newDeviceDialogVisible: false,
      // 存储当前编辑节点的标题
      dialogTitle: '',
      // 新建下一区域弹窗
      newNodeDialogVisible: false,
      // 新建下一区域标题
      newNodeTitle: '',
      // 存储当前编辑的节点对象
      nodeToEdit: null,
      // 剪切板数据
      clipboard: null,
      // 当前右击的节点
      currentNode: null,
      searchQuery: '', // 搜索框的值
      // tree的真实数据
      RealTreeData: [],
      // 搜索筛选节点数据
      filteredData: [],
      // 高亮中的点
      heightLight: '',
      // 右键菜单的位置
      containerRect: { width: 0, height: 0 },
      localExpandedKeys: [] // 添加本地数据来存储展开的keys
    }
  },

  computed: {
    ...mapState('tree', ['treeDataSend', 'expandedKeys', 'selectedKey', 'treePathText']),
    filteredTreeData() {
      return this.filteredData.length ? this.filteredData : this.treeData
    },
    expandedKeys: {
      get() {
        return this.localExpandedKeys
      },
      set(value) {
        this.localExpandedKeys = value
        // 如果需要，这里可以触发其他操作
        this.setExpandedKeys(value) // 更新 Vuex store
      }
    }
  },
  /* created() {
    // 获取树控组件数据列表
    this.getTreelist()
  }, */
  mounted() {
    this.getTreelist()
    // 从 store 中恢复展开状态
    this.expandedKeys = this.$store.state.tree.expandedKeys || []
    // 检查路由参数
    const { deviceId, fromGeneralPicture , fromAlarmDetail , measurementId} = this.$route.query
    console.log(this.$route.query);

    // 如果是从GeneralPicture页面跳转来的
    if (fromGeneralPicture) {
      // 从 Vuex 获取选中的节点数据
      const selectedRow = this.$store.getters['selectedRowModule/selectedRow']
      if (selectedRow) {
        // 等待树数据加载完成后再选中节点
        const checkTreeData = setInterval(() => {
          if (this.treeData && this.treeData.length > 0) {
            clearInterval(checkTreeData)

            // 根据测量定义ID或测点ID选中对应节点
            this.selectNodeById(selectedRow.id)

            // 展开所有父节点
            const node = this.findNodeById(this.treeData, selectedRow.id)
            if (node) {
              const parentKeys = this.getParentKeys(node.key)
              if (parentKeys.length > 0) {
                this.expandedKeys = parentKeys
                this.$store.dispatch('tree/setExpandedKeys', parentKeys)
              }
            }
          }
        }, 100)
        // 设置超时以防止无限等待
        setTimeout(() => clearInterval(checkTreeData), 5000)
      }
    }
    // 如果是从报警详情跳转来的
    if (fromAlarmDetail) {
      console.log('检测到fromAlarmDetail参数:', fromAlarmDetail)
      const alarmData = this.$store.getters['dataStore/getAlarmDetailData']
      console.log('从Vuex获取的alarmData:', alarmData)
      if (alarmData) {
        // 等待树数据加载完成后再选中节点
        const checkTreeData = setInterval(() => {
          if (this.treeData && this.treeData.length > 0) {
            clearInterval(checkTreeData)
            console.log(alarmData.measurementId);
            // 根据测点ID选中对应节点
            this.selectNodeById(alarmData.measurementId)
            // 展开所有父节点
            const node = this.findNodeById(this.treeData, alarmData.measurementId)
            if (node) {
              const parentKeys = this.getParentKeys(node.key)
              if (parentKeys.length > 0) {
                this.expandedKeys = parentKeys
                this.$store.dispatch('tree/setExpandedKeys', parentKeys)
              }
            }
          }
        }, 100)

        setTimeout(() => clearInterval(checkTreeData), 5000)
      }
    }
    if (measurementId) {
      console.log('Processing measurementId from URL:', measurementId, typeof measurementId);
      const checkTreeData = setInterval(() => {
        if (this.treeData && this.treeData.length > 0) {
          clearInterval(checkTreeData)
          console.log('Tree data structure:', JSON.stringify(this.treeData, null, 2));
          console.log('Tree data loaded, searching for measurementId:', measurementId);
          // 根据测点ID选中对应节点
          this.selectNodeById(measurementId)
          // 展开所有父节点
          const node = this.findNodeById(this.treeData, measurementId)
          console.log('Found node:', node);
          if (node) {
            const parentKeys = this.getParentKeys(node.key)
            console.log('Parent keys:', parentKeys);
            if (parentKeys.length > 0) {
              this.expandedKeys = parentKeys
              this.$store.dispatch('tree/setExpandedKeys', parentKeys)
              console.log('Expanded keys set:', parentKeys);
            }
          } else {
            console.warn('Node not found with measurementId:', measurementId);
          }
        }
      }, 100)

      setTimeout(() => {
        clearInterval(checkTreeData)
        console.log('Timeout cleared after 5 seconds');
      }, 5000)
    }
    // 绑定快捷键
    hotkeys('ctrl+c', (event) => {
      event.preventDefault()
      this.copyNodeToClipboard(this.currentNode)
    })
    hotkeys('ctrl+v', (event) => {
      event.preventDefault()
      this.pasteNodeAndOverwrite()
    })
    hotkeys('ctrl+x', (event) => {
      event.preventDefault()
      this.cutNode()
    })
  },
  beforeDestroy() {
    // 解绑快捷键
    hotkeys.unbind('ctrl+c')
    hotkeys.unbind('ctrl+v')
    hotkeys.unbind('ctrl+x')
    // 在组件销毁前保存当前展开状态
    this.$store.dispatch('tree/setExpandedKeys', this.expandedKeys)
  },
  methods: {
    ...mapActions('tree', ['setTreeData', 'setExpandedKeys', 'setSelectedKey', 'moveNode', 'setSelectedParentKey', 'setTreePathText']),
    expandTreeById(id) {
      console.log("读取到的节点id为",id)
      // 找到要展开的节点及其所有父节点
      const keysToExpand = this.findParentKeys(id, this.treeData)

      // 通过 Vuex action 设置展开的 keys
      this.setExpandedKeys(keysToExpand)

      // 通过 Vuex action 更新选中的 key(设置高亮的地方)
      // this.setSelectedKey([id])
    },

    findParentKeys(targetId, nodes, parentKeys = []) {
      for (const node of nodes) {
        // console.log(node.id)
        if (node.id === targetId) { // 这里使用 id 进行比对
          return [...parentKeys, node.key] // 用 node.key 作为返回的 key
        }
        if (node.children) {
          const result = this.findParentKeys(targetId, node.children, [...parentKeys, node.key])
          if (result) {
            return result
          }
        }
      }
      return null
    },
    // 搜索功能
    performSearch() {
      if (!this.searchQuery) {
        this.filteredData = []
        return
      }
      const searchNodes = (nodes, query) => {
        return nodes
          .map(node => {
            const children = node.children ? searchNodes(node.children, query) : []
            // 如果当前节点匹配查询，返回该节点及其所有子节点
            if (node.title.includes(query)) {
              return {
                ...node,
                children: node.children || []
              }
            }
            // 如果子节点中有匹配的节点，返回该节点及其匹配的子节点
            if (children.length) {
              return {
                ...node,
                children
              }
            }
            return null
          })
          .filter(node => node)
      }

      this.filteredData = searchNodes(this.treeData, this.searchQuery)
      this.treeData = this.filteredData
    },
    // 获取树的列表
    getTreelist() {
      console.log('Fetching tree data...')
      listDeviceManagement().then(res => {
        console.log('Raw tree data:', res.data)
        this.RealTreeData = res.data
        // 重置
        this.Reset()
        // 再次检查measurementId
        if (this.$route.query.measurementId) {
          console.log('Re-checking measurementId after tree load:', this.$route.query.measurementId)
          this.selectNodeById(this.$route.query.measurementId)
        }
      }).catch(err => {
        console.error('Error fetching tree data:', err)
      })
    },
    // 重置
    Reset() {
      this.treeData = this.RealTreeData
      // this.expandTreeById(this.$store.getters['selectedRowModule/selectedRow'].id)
      // 从 Vuex store 中获取 selectedRow
      const selectedRow = this.$store.getters['selectedRowModule/selectedRow']
      // 检查 selectedRow 是否为 null 或 undefined
      if (selectedRow && selectedRow.id) {
        this.expandTreeById(selectedRow.id)
        console.log(selectedRow.id)
      } else {
        console.log('No selected row or selectedRow.id is missing')
      }
      this.setTreeData(this.RealTreeData)
    },
    // 获取Icon
    getIcon(props) {
      const { treeIcon, status } = props.dataRef
      let iconClass = treeIcon
      // 判断icon状态
      switch (status) {
        // 预警状态
        case 'warning':
          iconClass = `${treeIcon}-warningValue`
          break
        // 危险状态
        case 'danger':
          iconClass = `${treeIcon}-dangerValue`
          break
        // 连接状态
        case 'connected':
          iconClass = `${treeIcon}-connectedData`
          break
        // 未连接状态
        case 'disconnected':
          iconClass = `${treeIcon}-disconnectedData`
          break
        default:
          iconClass = treeIcon
      }
      // 返回icon的DOM元素
      return <svg-icon icon-class={iconClass} style='width: 1.5em; height: 1.5em;'/>
    },
    // 用于更新Icon状态
    updateTreeIcon(key, newStatus) {
      const updateStatus = (data) => {
        return data.map(item => {
          if (item.key === key) {
            return { ...item, status: newStatus }
          }
          if (item.children) {
            return { ...item, children: updateStatus(item.children) }
          }
          return item
        })
      }

      this.treeData = updateStatus(this.treeData)
    },
    // 当状态改变时调用这个函数更新图标
    someConditionFunction() {
      this.updateTreeIcon('0-0', 'warning')
    },
    // 获取当前选中节点信息
    handleSelect(keys, event) {
      // console.log(event.node.dataRef.id)
      // console.log(this.$store)
      // 点击之后存储点击的节点的id去监听并且切换图表
      const selectData = {
        id: event.node.dataRef.id,
        treeicon: event.node.dataRef.treeIcon,
        status: event.node.dataRef.status
      }
      this.$store.dispatch('tree/setSelectedTreeNode', selectData)
      console.log(this.$store.state.tree.selectedTreeNode)
      const newSelectedKey = keys[0]
      // 保持高亮态
      if (keys.length === 0) {
        console.log(this.heightLight)
        this.setSelectedKey(this.heightLight)
      } else {
        this.heightLight = keys[0]
        this.setSelectedKey(newSelectedKey)
      }
      // 设置当前节点为选中节点
      if (event.selectedNodes.length > 0) {
        this.currentNode = this.findNodeByKey(this.treeData, newSelectedKey)
        this.updateTreePathText(this.currentNode)
        //将当前节点全部信息存储到vuex
        this.$store.dispatch('SettingConnect/setConfigNodeInfo', this.currentNode)
        console.log(this.currentNode);
      } else {
        this.currentNode = null
        // 清除组态设置节点信息
        this.$store.dispatch('SettingConnect/clearConfigNodeInfo')
      }
    },
    updateTreePathText(node) {
      let path = []
      const findPath = (nodes, currentPath) => {
        nodes.forEach(n => {
          const newPath = [...currentPath, n.title]
          if (n.key === node.key) {
            path = newPath
          } else if (n.children) {
            findPath(n.children, newPath)
          }
        })
      }
      findPath(this.treeData, [])
      const newPathText = path.join('/')
      this.setTreePathText(newPathText)
    },
    // 击节点时打开弹窗，并设置当前编辑节点的信息
    handleDoubleClick(event, node) {
      this.nodeToEdit = node
      this.dialogTitle = this.findNodeByKey(this.treeData, node.eventKey).title
      this.dialogVisible = true
    },
    // 获取节点所有信息
    findNodeByKey(data, key) {
      let foundNode = null
      const findNode = (data) => {
        data.forEach((item) => {
          if (item.key === key) {
            foundNode = item
          } else if (item.children) {
            findNode(item.children)
          }
        })
      }
      findNode(data)
      return foundNode
    },
    // 点击模态框的"确定"按钮时，更新节点标题并关闭弹窗
    handleDialogOk() {
      if (this.nodeToEdit) {
        this.updateNodeTitle(this.nodeToEdit.eventKey, this.dialogTitle)
        this.nodeToEdit = null
        this.dialogVisible = false
      }
    },
    // 点击模态框的"取消"按钮时，关闭弹窗
    handleDialogCancel() {
      this.nodeToEdit = null
      this.dialogVisible = false
    },
    // 新建下一区域的确定
    handleNewNodeOk() {
      if (this.currentNode && this.newNodeTitle) {
        const newNode = {
          title: this.newNodeTitle,
          key: `${this.currentNode.key}-${new Date().getTime()}`,
          children: []
        }
        // 查找并更新树形数据结构中的相应节点
        const updateTreeData = (nodes) => {
          nodes.forEach((node) => {
            if (node.key === this.currentNode.key) {
              if (!node.children) {
                this.$set(node, 'children', [])
              }
              node.children.push(newNode)
            } else if (node.children) {
              updateTreeData(node.children)
            }
          })
        }
        updateTreeData(this.treeData)
        this.newNodeTitle = ''
        this.newNodeDialogVisible = false
        message.success('新节点已添加')
      }
    },
    // 新建下一区域的取消
    handleNewNodeCancel() {
      this.newNodeTitle = ''
      this.newNodeDialogVisible = false
    },
    // 新建设备信息确定
    handleNewDeviceOk() {
      this.newDeviceDialogVisible = false
    },
    // 新建设备信息取消
    handleNewDeviceCancel() {
      this.newDeviceDialogVisible = false
    },
    // 词条设置
    handleNewDeviceTermSettings() {
      console.log('新建设备信息')
    },
    // 更新节点标题的方法
    updateNodeTitle(key, title) {
      const updateTitle = (data) => {
        data.forEach((item) => {
          if (item.key === key) {
            item.title = title
          } else if (item.children) {
            updateTitle(item.children)
          }
        })
      }
      updateTitle(this.treeData)
    },
    // 右击树节点时触发的事件，显示右键菜单
    handleRightClick(event) {
      if (!this.$refs.rightClickMenu.$el.contains(event.target)) {
        this.$refs.rightClickMenu.showMenu(event, this.getContextMenuOptions())
      }
    },
    // 右击事件获取节点信息
    handleRightClickOnNode({ event, node }) {
      if (node) {
        console.log('handleRightClickOnNode:', node)
        this.currentNode = this.findNodeByKey(this.treeData, node.eventKey)
        const options = this.getContextMenuOptions()
        this.$refs.rightClickMenu.showMenu(event, options)
      }
    },
    // handleMenuClick方法,this指向组件中的方法
    handleMenuClick(action) {
      rightClickHandlers.handleMenuClick(action, this)
    },
    // 右击事件
    getContextMenuOptions() {
      if (!this.currentNode) return []
      // 通用菜单
      const commonOptions = [
        { label: '编辑', action: 'edit', shortcut: 'ENTER', disabled: true },
        { label: '剪切', action: 'cut', shortcut: 'Ctrl+X' },
        { label: '复制', action: 'copy', shortcut: 'Ctrl+C' },
        { label: '粘贴', action: 'paste', shortcut: 'Ctrl+V' },
        { label: '删除', action: 'delete', shortcut: 'Del' },
        { label: '查找', action: 'search', shortcut: 'Ctrl+F' },
        { label: '设备特征频率', action: 'characteristicFrequency', shortcut: '' },
        { label: '频谱显示范围设置', action: 'spectrumRangeSettings', shortcut: '' },
        { label: '导出文件', action: 'exportFile' },
        { label: '导入Excel', action: 'importExcel' },
        { label: '预留', action: 'reserve' }
      ]
      const level = this.getNodeLevel(this.currentNode.key)
      switch (level) {
        case 1: // 公司
          return [
            { label: '新建', subMenu: [
              { label: '细化厂区结构', action: 'newArea', icon: 'el-icon-folder-opened' },
              { label: '设备', action: 'newDevice', icon: 'el-icon-setting' },
              { label: '预留', action: 'reserve', icon: 'el-icon-document' },
              { label: '复制', action: 'copy', icon: 'el-icon-copy-document' }
            ] },
            ...commonOptions
          ]
        case 2: // 厂区
          return [
            { label: '新建', subMenu: [
              { label: '细化厂区结构', action: 'newArea', icon: 'el-icon-folder-opened' },
              { label: '设备', action: 'newDevice', icon: 'el-icon-setting' },
              { label: '预留', action: 'reserve', icon: 'el-icon-document' },
              { label: '复制', action: 'copy', icon: 'el-icon-copy-document' }
            ] },
            ...commonOptions
          ]
        case 3: // 设备
          return [
            { label: '新建', subMenu: [
              { label: '测量点', action: 'newNode', icon: 'el-icon-view' },
              { label: '预留', action: 'reserve', icon: 'el-icon-document' },
              { label: '复制', action: 'copy', icon: 'el-icon-copy-document' }
            ] },
            ...commonOptions
          ]
        case 4: // 测点
          return [
            { label: '新建', subMenu: [
              { label: '测量定义', action: 'newDefinition', icon: 'el-icon-edit' },
              { label: '预留', action: 'reserve', icon: 'el-icon-document' },
              { label: '复制', action: 'copy', icon: 'el-icon-copy-document' }
            ] },
            ...commonOptions
          ]
        default:
          return commonOptions
      }
    },
    // 计算树的等级
    getNodeLevel(nodeKey) {
      return nodeKey.split('-').length - 1
    },
    // 深度克隆节点数据，排除循环引用
    deepClone(data) {
      return JSON.parse(JSON.stringify(data, (key, value) => {
        if (key === '_isVue') return undefined
        return value
      }))
    },

    // 复制节点到剪贴板
    copyNodeToClipboard() {
      if (this.currentNode) {
        const nodeData = this.deepClone(this.currentNode)
        this.clipboard = nodeData
        message.success('节点已复制到剪贴板')
      }
    },
    // 剪切节点
    cutNode() {
      if (this.currentNode) {
        this.clipboard = this.deepClone(this.currentNode)
        this.deleteNode(this.treeData, this.currentNode.key)
        message.success('节点已剪切到剪贴板')
      }
    },
    // 粘贴
    pasteNodeAndOverwrite() {
      if (this.clipboard && this.currentNode) {
        const newNodeData = this.deepClone(this.clipboard)
        if (!this.currentNode.children) {
          this.currentNode.children = []
        }
        this.currentNode.children.push(newNodeData)
        message.success('节点数据已粘贴')
      } else {
        message.error('没有可粘贴的数据或未选中目标节点')
      }
    },
    // 删除节点
    deleteNode(data, key) {
      const findAndDeleteNode = (data) => {
        data.forEach((item, index) => {
          if (item.key === key) {
            data.splice(index, 1)
          } else if (item.children) {
            findAndDeleteNode(item.children)
          }
        })
      }
      findAndDeleteNode(data)
    },
    // 全部展开，获取所有的节点key并将其设置为 expandedKeys，从而展开所有节点
    expandAll() {
      const allKeys = this.getAllKeys(this.treeData)
      this.expandedKeys = allKeys // 现在可以安全地赋值
    },
    // 全部收缩， 将 expandedKeys设置为空数组，收缩所有节点。
    collapseAll() {
      this.expandedKeys = [] // 现在可以安全地赋值
    },
    // 递归遍历树结构，获取所有节点的键。
    getAllKeys(data) {
      const keys = []
      const traverse = (nodes) => {
        nodes.forEach(node => {
          if (node.key) {
            keys.push(node.key)
          }
          if (node.children && node.children.length) {
            traverse(node.children)
          }
        })
      }
      traverse(data)
      return keys
    },
    // 更新当前展开的节点键，以确保用户手动展开或收缩某个节点时状态能够保持。
    handleExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    },
    // 根据 ID 查找节点（支持多个id字段）
    findNodeById(nodes, id) {
      console.log('Searching for id:', id, 'in nodes:', nodes)
      for (const node of nodes) {
        console.log('Checking node:', node)
        // 检查可能的id字段
        const matched = node.id == id ||
                       node.key == id ||
                       node.measurementId == id ||
                       (node.dataRef && node.dataRef.id == id)
        if (matched) {
          console.log('Found matching node:', node)
          return node
        }
        if (node.children) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      console.log('No matching node found for id:', id)
      return null
    },
    // 获取节点的所有父节点的 key
    getParentKeys(key) {
      const keys = []
      const parts = key.split('-')
      let currentKey = ''

      for (let i = 0; i < parts.length; i++) {
        currentKey = currentKey ? `${currentKey}-${parts[i]}` : parts[i]
        keys.push(currentKey)
      }
      return keys
    },
    // 根据 ID 选中节点
    selectNodeById(id) {
      console.log('Selecting node by id:', id, typeof id);
      // 确保id是字符串类型，因为URL参数可能是字符串而节点id可能是数字
      const searchId = typeof id === 'string' && !isNaN(id) ? parseInt(id) : id;
      console.log('Searching with id:', searchId);

      const findAndSelectNode = (nodes) => {
        for (const node of nodes) {
          if (node.children) {
            // 先检查子节点中是否有匹配的测量定义
            const matchingDef = node.children.find(child =>
              child.id == id ||
              child.key == id ||
              child.measurementId == id
            )
            if (matchingDef) {
              console.log('Found matching definition:', matchingDef);

              // 设置选中状态
              const selectData = {
                id: matchingDef.id,
                treeicon: matchingDef.treeIcon,
                status: matchingDef.status
              }
              this.$store.dispatch('tree/setSelectedTreeNode', selectData)

              // 强制设置选中状态
              this.setSelectedKey(matchingDef.key)
              this.heightLight = matchingDef.key
              this.currentNode = matchingDef
              this.$nextTick(() => {
                // 确保DOM更新后高亮
                this.setSelectedKey(matchingDef.key)
              })

              // 更新路径文本
              this.updateTreePathText(matchingDef)

              // 展开父节点
              const parentKeys = this.getParentKeys(matchingDef.key)
              if (parentKeys.length > 0) {
                this.setExpandedKeys(parentKeys)
                this.$nextTick(() => {
                  // 确保展开后再次设置选中
                  this.setSelectedKey(matchingDef.key)
                })
              }

              return true
            }

            // 如果子节点中没找到,继续递归查找
            if (findAndSelectNode(node.children)) {
              return true
            }
          }

          // 如果当前节点就是要找的节点
          if (node.id == id || node.key == id || node.measurementId == id) {
            console.log('Found matching node:', node);

            const selectData = {
              id: node.id,
              treeicon: node.treeIcon,
              status: node.status
            }
            this.$store.dispatch('tree/setSelectedTreeNode', selectData)

            // 强制设置选中状态
            this.setSelectedKey(node.key)
            this.heightLight = node.key
            this.currentNode = node
            this.$nextTick(() => {
              this.setSelectedKey(node.key)
            })

            this.updateTreePathText(node)

            const parentKeys = this.getParentKeys(node.key)
            if (parentKeys.length > 0) {
              this.setExpandedKeys(parentKeys)
              this.$nextTick(() => {
                this.setSelectedKey(node.key)
              })
            }

            return true
          }
        }
        return false
      }

      if (this.treeData && this.treeData.length > 0) {
        findAndSelectNode(this.treeData)
      } else {
        console.warn('Tree data not loaded yet')
      }
    }
  }
}
</script>

<style scoped>
@import './WorkTree.css';
</style>
