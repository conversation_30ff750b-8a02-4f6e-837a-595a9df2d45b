<!-- 轴承选择对话框 -->
<template>
    <div v-if="dialogVisible" class="bearing-dialog" ref="dialogRef">
      <div class="dialog-header" @mousedown="startDrag">
        <span>齿轮啮合频率分析</span>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      <div class="bearing-content">
        <!-- 齿轮箱结构表格 -->
        <div class="gear-table-container">
          <!-- 齿轮箱信息卡片 -->
          <div v-for="gearbox in groupedTableData" :key="gearbox.id" class="gearbox-card">
            <!-- 齿轮箱标题栏 -->
            <div class="gearbox-header" @click="toggleExpand(gearbox)">
              <div class="header-left">
                <i :class="['el-icon-arrow-right', { 'expanded': isExpanded(gearbox) }]"></i>
                <svg-icon icon-class="gearbox" class="gearbox-icon" />
                <span class="gearbox-title">{{ gearbox.deviceName }}
                  <el-tag size="mini" type="success" style="margin-left: 16px;">{{ getGearboxType(gearbox) }}</el-tag>
                </span>
              </div>
            </div>

            <!-- 齿轮箱详细信息 -->
             <!-- 平行轴系 -->
            <ParallelAxisSystem
              v-if="gearbox.children[0].GearBoxType === '平行轴系'"
              v-show="isExpanded(gearbox)"
              :gearbox="gearbox"
              @update="handleGearboxUpdate"
            />

            <!-- 行星轴系 -->
            <PlanetaryAxisSystem
              v-if="gearbox.children[0].GearBoxType === '行星轴系'"
              v-show="isExpanded(gearbox)"
              :gearbox="gearbox"
              @update="handleGearboxUpdate"
              @frequency-change="handleFrequencyChange"
            />
          </div>
        </div>
      </div>
      <div class="dialog-footer">

        <el-tooltip
          content="如果不保存，则视为临时修改，点击 保存数据 后才会更新到系统"
          placement="top"
          effect="light"
        >
          <i class="el-icon-question" style="color:#409EFF;cursor:pointer;margin-right:20px;"></i>
        </el-tooltip>

        <!-- <el-button type="success" @click="handleSave">保 存 数 据</el-button> -->
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </div>
  </template>


  <script>
  import request from '@/utils/request'
  import { mapState } from 'vuex'
  import ParallelAxisSystem from './CalculateAxisSystem/ParallelAxisSystem.vue'
  import PlanetaryAxisSystem from './CalculateAxisSystem/PlanetaryAxisSystem.vue'
  import { planetGearRPMFromRing, absoluteGearRPMFromRing, sunGearRPMFromSun, planetGearRPMFromSun, planetCarrierRPMFromRing, ringFailureFrequency, planetGearRPM3, planetCarrierRPMFromSun, ringGearFailureFrequency,callingFrequency } from './CalculateAxisSystem/calculate.js';

  export default {
    name: 'GearAnalysisDialog',
    components: {
      ParallelAxisSystem,
      PlanetaryAxisSystem
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      containerRef: {
        type: HTMLElement,
        default: null
      }
    },
    data() {
      return {
        dialogVisible: false,
        isDragging: false,
        initialX: 0,
        initialY: 0,
        currentX: -50,
        currentY: -50,
        deviceId: null,
        tableData: [], // 表格数据
        originalData: null,
        expandedRows: []
      }
    },
    computed: {
      ...mapState('tree', ['selectedTreeNode']),
      groupedTableData() {
        // 按齿轮箱id分组
        const groups = {};
        this.tableData.forEach(item => {
          if (!groups[item.id]) {
            groups[item.id] = {
              id: item.id,
              deviceName: item.deviceName,
              children: []
            };
          }
          // 复制一份数据，避免直接修改原数据
          groups[item.id].children.push({...item});
        });

        return Object.values(groups);
      },
      getGearOptions() {
        if (!this.speedCalcForm.selectedGearbox) {
          return [];
        }
        const gearbox = this.groupedTableData.find(gearbox => gearbox.id === this.speedCalcForm.selectedGearbox);
        return gearbox ? gearbox.children : [];
      }
    },
    watch: {
      visible(val) {
        this.dialogVisible = val;
      },
      dialogVisible(val) {
        if (!val) {
          this.$emit('update:visible', false);
          this.$emit('close');
        }
      }
    },
    mounted() {
      this.dialogVisible = this.visible;
      if (this.selectedTreeNode && this.selectedTreeNode.id) {
        this.deviceId = this.selectedTreeNode.id;
        this.getDeviceId(this.deviceId);
      }

      // 将对话框挂载到body以避免被其他元素遮挡
      this.$nextTick(() => {
        document.body.appendChild(this.$el);
      });
    },
    methods: {
      startDrag(event) {
        if (event.target.classList.contains('close-btn')) return;
        this.isDragging = true;

        // 获取当前transform的值
        const transform = window.getComputedStyle(this.$refs.dialogRef).transform;
        const matrix = new DOMMatrix(transform);

        this.initialX = event.clientX - matrix.m41;
        this.initialY = event.clientY - matrix.m42;

        document.addEventListener('mousemove', this.drag);
        document.addEventListener('mouseup', this.stopDrag);
      },

      drag(event) {
        if (this.isDragging) {
          event.preventDefault();
          const newX = event.clientX - this.initialX;
          const newY = event.clientY - this.initialY;

          const dialog = this.$refs.dialogRef;
          if (dialog) {
            dialog.style.transform = `translate(${newX}px, ${newY}px)`;
          }
        }
      },

      stopDrag() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.drag);
        document.removeEventListener('mouseup', this.stopDrag);
      },

      handleClose() {
        this.dialogVisible = false;
        this.$emit('update:visible', false);
        this.$emit('close');
      },

      async handleConfirm() {
        // 收集所有选中的齿轮啮合频率
        const result = this.groupedTableData
          .filter(gearbox => {
            if (gearbox.children[0].GearBoxType === '行星轴系') {
              return gearbox.children.some(gear =>
                gear.selectedMultiples && gear.selectedMultiples.length > 0
              );
            } else if (gearbox.children[0].GearBoxType === '平行轴系') {
              return gearbox.children.some(gear =>
                (gear.selectedFollowerMultiples && gear.selectedFollowerMultiples.length > 0) ||
                (gear.selectedDriverMultiples && gear.selectedDriverMultiples.length > 0)
              );
            }
            return false;
          })
          .map(gearbox => {
            const frequencies = [];
            if (gearbox.children[0].GearBoxType === '行星轴系') {
              gearbox.children.forEach(gear => {
                if (gear.selectedMultiples && gear.selectedMultiples.length > 0) {
                  console.log("行星轴系基频",gear.baseFrequency)
                  gear.selectedMultiples.forEach(multiple => {
                    frequencies.push({
                      frequency: gear.baseFrequency * multiple,
                      multiple,
                      type: 'GMF',
                      description: `${gearbox.deviceName}-${gear.type}`
                    });
                  });
                }
              });
            } else if (gearbox.children[0].GearBoxType === '平行轴系') {
              gearbox.children.forEach(gear => {
                // 处理从动轮频率
                if (gear.selectedFollowerMultiples && gear.selectedFollowerMultiples.length > 0 && gear.description) {
                  console.log('从动轮基频:', gear.baseFrequency);
                  gear.selectedFollowerMultiples.forEach(multiple => {
                    frequencies.push({
                      frequency: gear.baseFrequency * multiple,
                      multiple,
                      type: 'GMF',
                      description: `${gear.description}-从动轮啮合`
                    });
                  });
                }
                // 处理驱动轮频率
                if (gear.selectedDriverMultiples && gear.selectedDriverMultiples.length > 0 && gear.mainDescription) {
                  console.log('驱动轮基频:', gear.baseFrequency);
                  gear.selectedDriverMultiples.forEach(multiple => {
                    frequencies.push({
                      frequency: gear.baseFrequency * multiple,
                      multiple,
                      type: 'GMF',
                      description: `${gear.mainDescription}-驱动轮啮合`
                    });
                  });
                }
              });
            }
            return {
              id: gearbox.id,
              deviceName: gearbox.deviceName,
              frequencies
            };
          })
          .filter(item => item.frequencies.length > 0);

        const flattenedFrequencies = result.flatMap(gearbox =>
          gearbox.frequencies.map(freq => ({
            id: `${gearbox.id}_${freq.multiple}`,
            bearingName: freq.description,
            frequencies: [{
              frequency: Number(freq.frequency),
              multiple: freq.multiple,
              type: freq.type
            }]
          }))
        );

        this.$emit('confirm', flattenedFrequencies);
        this.handleClose();
      },

      getGearboxType(gearbox) {
        if (gearbox.children[0].GearBoxType === '平行轴系') {
          return '平行轴系';
        } else if (gearbox.children[0].GearBoxType === '行星轴系') {
          return '行星轴系';
        } else {
          return '未知类型';
        }
      },

      toggleExpand(row) {
        const index = this.expandedRows.indexOf(row.id);
        if (index > -1) {
          this.expandedRows.splice(index, 1);
        } else {
          this.expandedRows.push(row.id);
        }
      },

      isExpanded(row) {
        return this.expandedRows.includes(row.id);
      },

      // 计算啮合频率
      calculateMeshFrequency(row) {
        if (!row) return '0.00';

        // 判断是否为行星轴系
        if (row.type === '行星轮' || row.type === '内齿圈' || row.type === '太阳轮') {
          return this.calculatePlanetaryMeshFrequency(row,row.selectedFrequencyType);
        }

        // 平行轴系的啮合频率计算
        if (type === 'follower' && row.teethCount && row.inputSpeed) {
          // 从动轮啮合频率 = 从动轮齿数 × (转速/60)
          const frequency = (Number(row.teethCount) * Number(row.inputSpeed)) / 60;
          return frequency.toFixed(2);
        } else if (type === 'driver' && row.mainTeethCount && row.inputSpeed) {
          // 驱动轮啮合频率 = 驱动轮齿数 × (转速/60)
          const frequency = (Number(row.mainTeethCount) * Number(row.inputSpeed)) / 60;
          return frequency.toFixed(2);
        }
        return '0.00';
      },

      // 计算行星轴系的啮合频率
      calculatePlanetaryMeshFrequency(row,type) {
        if (!row.teethCount || !row.inputSpeed) return '0.00';

        // 获取当前齿轮所属的齿轮箱
        const gearbox = this.groupedTableData.find(g => g.children.includes(row));
        if (!gearbox) return '0.00';

        // 获取行星轴系的所有齿轮
        const planetaryGears = gearbox.children;
        const sunGear = planetaryGears.find(g => g.type === '太阳轮');
        const ringGear = planetaryGears.find(g => g.type === '内齿圈');
        const planetGear = planetaryGears.find(g => g.type === '行星轮');
        const planetCount = Number(planetGear.teethCount);

        if (!sunGear || !ringGear || !planetGear) return '0.00';

        const inputSpeed = Number(row.inputSpeed);
        let frequency = 0;

        switch (type) {
          case 'fp_s': // 太阳轮-行星轮啮合频率
            frequency = (Number(sunGear.teethCount) * inputSpeed) / 60;
            break;
          case 'fp_r': // 内齿圈-行星轮啮合频率
            frequency = (Number(ringGear.teethCount) * inputSpeed) / 60;
            break;
          case 'fp_s_single': // 单个行星轮与太阳轮的啮合频率
            frequency = (Number(sunGear.teethCount) * inputSpeed) / (60 * planetCount);
            break;
          case 'fp_r_single': // 单个行星轮与内齿圈的啮合频率
            frequency = inputSpeed / 60;
            break;
          case 'f_s_pass': // 太阳轮上某一点通过的行星轮的频率
            frequency = (inputSpeed * planetCount) / (60 * (1 + Number(sunGear.teethCount) / Number(ringGear.teethCount)));
            break;
          case 'f_r_pass': // 内齿圈某一点通过的行星轮的频率
            frequency = (inputSpeed * planetCount) / 60;
            break;
        }

        return frequency.toFixed(2);
      },

      async getDeviceId(id) {
        try {
          const res = await request({
            url: `/measureDefinition/measureDefinition/${id}`,
            method: 'get'
          })
          this.deviceId = res.data.machineId
          this.getBearingData(this.deviceId)
        } catch (error) {
          console.error('获取设备信息失败:', error)
        }
      },

      async getBearingData(id) {
        try {
          const res = await request({
            url: `/deviceConfiguration/configuration/list/${id}`,
            method: 'get'
          })

          // 检查响应数据结构
          if (res.rows) {
            this.processApiData(res.rows)
          } else {
            console.error('API返回数据格式不正确:', res)
            this.$message.error('获取轴承数据格式错误')
          }
        } catch (error) {
          console.error('获取轴承数据失败:', error)
          this.$message.error('获取轴承数据失败')
        }
      },

      processApiData(rows) {
        if (!Array.isArray(rows)) {
          console.error('数据不是数组格式:', rows);
          return;
        }

        // 保存原始数据
        this.originalData = JSON.parse(JSON.stringify(rows));

        // 处理齿轮箱数据
        this.tableData = rows
          .filter(item => item.treeIcon === 'gearbox')
          .flatMap(gearbox => {
            console.log('处理齿轮箱数据:', gearbox);

            // 处理平行轴系
            if (gearbox.table?.gearboxType === 'noDeleteParallelShafting') {
              const processedData = [];
              const tableData = gearbox.table.tableData;

              // 第一根轴 - 只有驱动轮
              processedData.push({
                id: gearbox.id,
                GearBoxType: '平行轴系',
                deviceName: gearbox.deviceName,
                level: '一级',
                description: '', // 第一根轴没有从动轮
                axis: '',
                teethCount: '',
                mainDescription: tableData[0].mainDescription,
                mainAxis: tableData[0].mainAxis,
                mainTeethCount: tableData[0].mainTeethCount,
                inputSpeed: tableData[0].speed,
                editableTeethCount: false,
                editableMainTeethCount: false,
                editableInputSpeed: false,
                selectedFollowerMultiples: [],
                selectedDriverMultiples: []
              });

              // 处理中间的轴和最后一根轴
              for (let i = 1; i < tableData.length; i++) {
                const gear = tableData[i];
                processedData.push({
                  id: gearbox.id,
                  deviceName: gearbox.deviceName,
                  level: `${i + 1}级`,
                  description: gear.description,
                  axis: gear.axis,
                  teethCount: gear.teethCount,
                  mainDescription: gear.mainDescription,
                  mainAxis: gear.mainAxis,
                  mainTeethCount: gear.mainTeethCount,
                  inputSpeed: gear.speed,
                  editableTeethCount: false,
                  editableMainTeethCount: false,
                  editableInputSpeed: false,
                  selectedFollowerMultiples: [],
                  selectedDriverMultiples: []
                });
              }

              return processedData;
            }

            // 处理行星轴系
            if (gearbox.table?.gearboxType === 'noDeletePlanetaryShaftSys') {
              const data = gearbox.table.tableData;
              // 使用 inputShaftSpeed 作为输入轴转速的统一字段
              const inputSpeedVal = Number(data.inputShaftSpeed) || Number(data.speed) || 0;
              // 类型描述优先使用输入轴类型
              const typeDescription = data.inputShaftType === 'carrier' ? '行星架输入' : '太阳轮输入';

              return [
                {
                  GearBoxType: '行星轴系',
                  id: gearbox.id,
                  deviceName: `${gearbox.deviceName} - ${typeDescription}`,
                  level: '一级',
                  type: '行星轮',
                  teethCount: data.planetaryGearTeeth,
                  // 行星架输入模式或太阳轮输入模式都使用统一的输入轴转速
                  inputSpeed: inputSpeedVal,
                  planetaryCount: data.planetaryGearCount,
                  editableTeethCount: false,
                  editableInputSpeed: false,
                  selectedMultiples: []
                },
                {
                  id: gearbox.id,
                  deviceName: `${gearbox.deviceName} - ${typeDescription}`,
                  level: '一级',
                  type: '内齿圈',
                  teethCount: data.ringGearTeeth,
                  // 内齿圈固定为0
                  inputSpeed: data.ringSpeed,
                  editableTeethCount: false,
                  editableInputSpeed: false,
                  selectedMultiples: []
                },
                {
                  id: gearbox.id,
                  deviceName: `${gearbox.deviceName} - ${typeDescription}`,
                  level: '一级',
                  type: '太阳轮',
                  teethCount: data.sunGearTeeth,
                  // 行星架输入模式或太阳轮输入模式都使用统一的输入轴转速
                  inputSpeed: inputSpeedVal,
                  editableTeethCount: false,
                  editableInputSpeed: false,
                  selectedMultiples: []
                }
              ];
            }

            return [];
          });

        console.log('处理后的表格数据:', this.tableData);

        // 为每个齿轮箱添加输入轴转速属性
        this.groupedTableData.forEach(gearbox => {
          const firstGear = gearbox.children[0];
          if (firstGear) {
            this.$set(gearbox, 'inputSpeed', firstGear.inputSpeed || 0);
          }
        });
      },

      // 处理齿轮箱数据更新
      handleGearboxUpdate(updatedGearbox) {
        // 打印行星轴系的关键参数
        if (updatedGearbox.children && updatedGearbox.children[0].GearBoxType === '行星轴系') {
          const planetGear = updatedGearbox.children.find(g => g.type === '行星轮');
          const sunGear = updatedGearbox.children.find(g => g.type === '太阳轮');
          const ringGear = updatedGearbox.children.find(g => g.type === '内齿圈');

          console.log('行星轴系更新数据:', {
            完整数据: updatedGearbox,
            行星轮数量: planetGear?.planetaryCount,
            输入转速: updatedGearbox.inputSpeed,
            齿轮参数: {
              太阳轮齿数: sunGear?.teethCount,
              内齿圈齿数: ringGear?.teethCount,
              行星轮齿数: planetGear?.teethCount,
              行星轮转速: planetGear?.inputSpeed,
              太阳轮转速: sunGear?.inputSpeed,
              内齿圈转速: ringGear?.inputSpeed
            }
          });
        }

        // 更新齿轮箱数据
        const index = this.groupedTableData.findIndex(g => g.id === updatedGearbox.id);
        if (index !== -1) {
          this.$set(this.groupedTableData, index, updatedGearbox);
        }
      },

      // 添加处理频率变化的方法
      handleFrequencyChange(updatedGear) {
        // 找到对应的齿轮箱
        const gearbox = this.groupedTableData.find(g =>
          g.children.some(gear => gear.type === updatedGear.type)
        );

        if (!gearbox) return;

        // 更新齿轮的基频和倍频信息
        const gearIndex = gearbox.children.findIndex(g => g.type === updatedGear.type);
        if (gearIndex !== -1) {
          this.$set(gearbox.children, gearIndex, {
            ...gearbox.children[gearIndex],
            baseFrequency: updatedGear.baseFrequency,
            selectedFrequencyType: updatedGear.frequencyType,
            selectedMultiples: updatedGear.selectedMultiples
          });
        }
      }
    },
    beforeDestroy() {
      document.removeEventListener('mousemove', this.drag);
      document.removeEventListener('mouseup', this.stopDrag);

      // 在组件销毁前，从body中移除对话框元素
      if (this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el);
      }
    }
  }
  </script>

  <style scoped lang="scss">
    @import './GearAnalysisDialog.css';
  </style>
