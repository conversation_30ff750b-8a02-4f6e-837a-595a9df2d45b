<template>
  <div ref="imageContainer">
    <el-upload
      ref="upload"
      action="#"
      list-type="picture-card"
      :auto-upload="false"
      :on-remove="handleRemove"
      :on-change="handleUploadChange"
      :file-list="fileList"
      :limit="pageSize"
      :on-exceed="handleExceed"
      class="image-upload">
      <i slot="default" class="el-icon-plus"></i>
    </el-upload>

    <!-- 图片展示区，显示下载的图片 -->
    <div class="image-gallery">
      <img v-for="(url, index) in dialogImageUrls" :key="index" :src="url" alt="下载的图片" class="gallery-image">
    </div>
  </div>
</template>

<script>
import { Upload } from 'element-ui';
import { UploadFile, GetImgObjList, DownloadImgObjList ,DeleteItem } from '@/api/haoyu-system/web3d_api/web_3dapi.js';

export default {
  name: 'UploadComponent',
  components: {
    'el-upload': Upload,
  },
  data() {
    return {
      dialogImageUrls: [], // 存储下载的图片 URL
      uploadData: [], // 存储上传的文件信息
      fileList: [], // 存储上传的文件列表
      pageSize: 0, // 每页显示的图片数量，初始化为0
      pageNum: 1, // 当前页码
      imageWidth: 120, // 假设每张图片的宽度，包括margin（可根据实际调整）
    };
  },
  mounted() {
    this.calculatePageSize(); // 计算每页显示的图片数量
    this.get_imglist(); // 初始化时获取图片列表
    window.addEventListener('resize', this.calculatePageSize); // 监听窗口大小变化，重新计算
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculatePageSize);
  },

  methods: {
    // 计算每页显示的图片数量
    calculatePageSize() {
      this.$nextTick(() => {
        const container = this.$refs.imageContainer;
        if (container) {
          const containerWidth = container.clientWidth;
          this.pageSize = Math.floor(containerWidth / this.imageWidth);
          if (this.pageSize <= 0) {
            this.pageSize = 1; // 至少显示一张图片
          }
          this.dialogImageUrls = []; // 重置图片列表
          this.pageNum = 1; // 重置页码
          this.get_imglist(); // 重新获取图片列表
        }
      });
    },

    // 获取图片列表
    get_imglist() {
      // 清空当前的上传数据
      this.uploadData = [];
      // 调用获取图片列表的函数
      this.fetchImages(this.pageNum);
    },

    // 递归获取图片，直到满足当前页需要显示的数量，或没有更多数据
    fetchImages(pageNum) {
      get_img_obj_list({ pageSize: this.pageSize, pageNum: pageNum }).then(response => {
        const data = response.rows;
        // 过滤符合条件的图片
        const validImages = data.filter(item => {
          const fileName = item.fileName.toLowerCase();
          return fileName.endsWith('.png') || fileName.endsWith('.jpg') || fileName.endsWith('.jpeg');
        });

        // 将过滤后的图片添加到 uploadData
        this.uploadData = this.uploadData.concat(validImages);

        // 如果已获取的图片数量小于需要显示的数量，且还有更多数据，则继续请求下一页
        if (this.uploadData.length < this.pageSize && data.length === response.pageSize) {
          this.pageNum += 1;
          this.fetchImages(this.pageNum);
        } else {
          // 下载并显示图片
          this.downloadAndShowImages();
        }
      }).catch(error => {
        console.error('获取图片列表失败', error);
      });
    },

    // 上传
    handleUploadChange(file, fileList) {
      const formData = new FormData();
      formData.append('file', file.raw);
      upload_file(formData).then(response => {
        console.log('上传成功', response);
        file.url = response.data.url; // 假设返回的数据中包含文件 URL
        this.fileList.push(file);
        this.calculatePageSize(); // 上传成功后重新计算并获取图片列表
      }).catch(error => {
        console.error('上传失败', error);
      });
    },

    // 处理删除文件
    handleRemove(file) {
      console.log('删除文件:', file);
      // 根据业务需求，你可以在此处理删除请求
      // 删除后重新获取图片列表
      this.calculatePageSize();
    },

    // 下载并显示图片
    downloadAndShowImages() {
      this.dialogImageUrls = []; // 清空当前显示的图片
      const promises = this.uploadData.slice(0, this.pageSize).map(item => {
        return download_img_obj_list(item.id, { responseType: 'blob' }).then(response => {
          const blob = new Blob([response], { type: response.type || 'image/png' });
          const imageUrl = URL.createObjectURL(blob);
          this.dialogImageUrls.push(imageUrl);
          console.log('生成的图片 URL:', imageUrl);
        }).catch(error => {
          console.error('图片下载失败:', error);
        });
      });

      // 等待所有图片下载完成
      Promise.all(promises).then(() => {
        console.log('所有图片下载完成');
      });
    },

    // 处理文件数量超出限制
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.pageSize} 个文件`);
    },
  }
};
</script>

<style scoped>
.image-upload {
  display: inline-block;
  white-space: nowrap;
  overflow-x: auto;
}
.image-gallery {
  display: flex;
  flex-wrap: nowrap; /* 不换行 */
  overflow-x: auto; /* 超出时可滚动 */
}
.gallery-image {
  width: 100px; /* 根据实际调整 */
  height: 100px;
  margin-right: 10px;
}
</style>
