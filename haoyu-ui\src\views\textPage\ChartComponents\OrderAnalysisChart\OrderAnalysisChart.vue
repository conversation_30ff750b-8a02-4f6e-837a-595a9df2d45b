<!-- 频谱图 -->
<template>
  <div class="sycontainer">
    <div class="top-container">{{ treePathText }} 阶次分析图 {{ time_point }}  {{ currentXValue }} order, {{ currentYValue }} {{ amplitudeUnit }}</div>
    <div ref="chart" class="chart">
      <div v-if="error" class="error-message">
        <div class="error-icon"></div>
        <div>{{ error }}</div>
      </div>
      <div v-if="loading" class="loading-container">
        <div class="loading-text">数据加载中...</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import axios from 'axios'
import { getWaveform } from './data'
import { getParams } from '@/utils/graph_Interface'
import { chartZoomMixin } from '../mixins/chartZoomMixin'

export default {
  name: 'OrderAnalysisChart',
  mixins: [chartZoomMixin],
  data() {
    return {
      chartInstance: null,
      currentXValue: null,
      currentYValue: null,
      data: [], //当前显示数据
      origindata:[], //原始数据
      error: null,
      loading: false, // 添加loading状态
      currentUnit: 'order',
      resizeObserver: null,
      // 添加数据优化相关属性
      sampleThreshold: 10000, // 数据抽样阈值
      isOptimizing: false,    // 是否正在优化
      optimizedData: [],      // 优化后的数据
      renderDebounceTimer: null, // 防抖定时器
      abortController: null // 请求取消控制器
    }
  },
  computed: {
    ...mapState('tree', ['treePathText']),
    ...mapState('dataStore', ['device_id', 'point_id', 'time_point']),
    ...mapState('SettingConnect', ['configNodeInfo']),
    amplitudeUnit() {
      const measureName = this.configNodeInfo?.measureDefinitions?.measureDefineName;
      if (measureName?.includes('加速度') || measureName?.includes('包络')) {
        return 'm/s²';
      }
      if (measureName === '速度') {
        return 'mm/s';
      }
      return 'm/s²';
    }
  },
  watch:{
    '$store.state.tree.selectedTreeNode.id':{
      async handler(newNodeId) {
        if (!newNodeId) return

        // 检查是否有数据
        const dataStore = this.$store.state.dataStore;
        if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
          // 没有数据，清空图表
          this.clearChart();
          return;
        }

        if (this.$store.state.dataStore && this.$store.state.dataStore.length > 0) {
          this.loading = true
          if (this.chartInstance) {
            await this.getSpectrumData();
            this.updateChart();
          } else {
            this.initChart();
            await this.getSpectrumData();
          }
        }
      }
    },
    'time_point':{
      async handler(newTimePoint) {
        if (!newTimePoint) return
        const { device_id, point_id } = this.$store.state.dataStore;
        if (!device_id || !point_id) return;
        this.loading = true
        await this.getSpectrumData()
        this.initChart()
        this.updateChart()
      }
    },
    // 监听整个dataStore的变化
    '$store.state.dataStore': {
      handler(newDataStore) {
        console.log('dataStore变化:', newDataStore);
        // 如果没有数据，清空图表
        if (!newDataStore || !newDataStore.device_id || !newDataStore.point_id || !newDataStore.time_point) {
          console.log('检测到没有数据，清空图表');
          this.clearChart();
        }
      },
      deep: true,
      immediate: true
    },
    // 监听测量定义的变化
    'configNodeInfo': {
      handler() {
        // 检查是否有数据
        const dataStore = this.$store.state.dataStore;
        if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
          // 没有数据，清空图表
          this.clearChart();
        }
      },
      deep: true
    }
  },
  async mounted() {
    this.loading = true

    // 检查是否有数据
    const dataStore = this.$store.state.dataStore;
    if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
      // 没有数据，清空图表并显示提示
      this.clearChart();
      return;
    }

    await this.checkDataStore()
    await this.getSpectrumData()
    this.initChart()

    // 初始化缩放功能
    this.initZoomEvents(this.data.length)
    this.loading = false

    const resizeObserver = new ResizeObserver(() => {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    });

    if (this.$refs.chart) {
      resizeObserver.observe(this.$refs.chart);
    }
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // 销毁缩放事件监听
    this.disposeZoomEvents();

    // 取消未完成的请求
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }

    // 清除定时器
    if (this.renderDebounceTimer) {
      clearTimeout(this.renderDebounceTimer);
      this.renderDebounceTimer = null;
    }
  },
  methods: {
    ...mapState('tree', ['setTreePathText']),
    // 添加数据抽样方法
    sampleData(data, maxPoints) {
      if (!data || data.length <= maxPoints) return data;

      this.isOptimizing = true;

      // 计算抽样间隔
      const step = Math.floor(data.length / maxPoints);

      // 使用 Largest-Triangle-Three-Buckets 算法进行抽样
      // 这种算法能保留数据的视觉特征
      const sampled = [];

      // 保留第一个点
      sampled.push(data[0]);

      // 对中间的点进行抽样
      for (let i = 1; i < data.length - 1; i += step) {
        // 计算当前桶的范围
        const bucketStart = i;
        const bucketEnd = Math.min(i + step, data.length - 1);

        // 在当前桶中找到最大值点
        let maxVal = -Infinity;
        let maxIdx = bucketStart;

        for (let j = bucketStart; j <= bucketEnd; j++) {
          if (data[j][1] > maxVal) {
            maxVal = data[j][1];
            maxIdx = j;
          }
        }

        // 添加最大值点
        sampled.push(data[maxIdx]);
      }

      // 保留最后一个点
      if (data.length > 1) {
        sampled.push(data[data.length - 1]);
      }

      this.isOptimizing = false;
      return sampled;
    },

    // 优化更新图表方法，添加防抖
    debouncedUpdateChart() {
      if (this.renderDebounceTimer) {
        clearTimeout(this.renderDebounceTimer);
      }

      this.renderDebounceTimer = setTimeout(() => {
        this.updateChart();
      }, 100);
    },

    initChart() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }

      // 创建图表实例时添加渲染器类型选项，提高性能
      this.chartInstance = echarts.init(this.$refs.chart, null, {
        renderer: 'canvas', // 使用canvas渲染器，大数据量下性能更好
        useDirtyRect: true  // 启用脏矩形渲染优化
      })

      this.chart = this.chartInstance // 为缩放混入提供chart引用
      this.updateChart()

      this.chartInstance.on('mousemove', (params) => {
        if (params.componentType === 'series') {
          const xValue = params.value[0];
          const yValue = params.value[1];
          this.currentXValue = xValue;
          this.currentYValue = yValue;
        }
      });

      this.chartInstance.on('globalout', () => {
        this.currentXValue = null;
        this.currentYValue = null;
      });
    },

    updateChart() {
      // 对大数据进行优化处理
      let displayData = this.data;

      // 当数据量超过阈值时进行抽样
      if (this.data.length > this.sampleThreshold) {
        // 根据当前视图宽度动态计算合适的点数
        const chartWidth = this.$refs.chart ? this.$refs.chart.clientWidth : 800;
        const optimalPoints = Math.min(chartWidth * 2, this.sampleThreshold);

        displayData = this.sampleData(this.data, optimalPoints);
        this.optimizedData = displayData;
      }

      const option = {
        animation: false,
        grid: {
          left: '0%',
          right: '3.5%',
          top: '5%',
          bottom: '3%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            },
            lineStyle: {
              color: '#6a7985',
              width: 1,
              type: 'dashed'
            }
          },
          backgroundColor: 'rgba(50,50,50,0.7)',
          borderColor: '#333',
          borderWidth: 0,
          padding: [5,10],
          textStyle: {
            color: '#fff'
          },
          formatter: (params) => {
            const data = params[0]
            if (!data) return ''
            this.currentXValue = data.value[0]
            this.currentYValue = data.value[1]
            return `频率: ${this.currentXValue} order<br/>幅值: ${this.currentYValue} ${this.amplitudeUnit}`
          }
        },
        dataZoom: [{
          type: 'inside',
          zoomOnMouseWheel: true,
          moveOnMouseMove: false,
          preventDefaultMouseMove: true,
          zoomLock: false,
          throttle: 100
        }],
        xAxis: {
          type: 'value',
          name: this.currentUnit,
          // 优化X轴配置
          axisLabel: {
            showMaxLabel: true,
            showMinLabel: true
          },
          // 启用分片渲染
          splitNumber: Math.min(10, Math.ceil(displayData.length / 1000))
        },
        yAxis: {
          type: 'value',
          name: this.amplitudeUnit,
          nameLocation: 'end',
          axisLabel: {
            formatter: (value) => {
              return value.toFixed(4)
            }
          }
        },
        series: [
          {
            type: 'line',
            smooth: false, // 关闭平滑，提高性能
            animation: false,
            sampling: 'lttb', // 使用 Largest-Triangle-Three-Buckets 采样算法
            symbolSize: 0,    // 不显示点，提高性能
            showSymbol: false,
            data: displayData,
            itemStyle: {
              color: '#4985DF'
            },
            lineStyle: {
              color: '#4985DF',
              width: 1.5
            },
            emphasis: {
              scale: false, // 关闭缩放效果，提高性能
              focus: 'none'  // 关闭聚焦效果，提高性能
            },
            // 大数据量优化选项
            progressive: 500,        // 渐进式渲染，每次渲染500个点
            progressiveThreshold: 3000, // 超过3000个点时启用渐进式渲染
            large: true,             // 启用大数据量优化
            largeThreshold: 2000     // 超过2000个点时启用大数据量优化
          }
        ]
      }

      this.chartInstance.setOption(option, {
        replaceMerge: ['series']
      });
    },

    async getSpectrumData() {
      try {
        // 显示加载状态
        this.loading = true
        this.error = null

        // 取消之前的请求（如果存在）
        if (this.abortController) {
          console.log('取消之前的请求')
          this.abortController.abort()
          this.abortController = null
        }

        // 创建新的取消控制器
        this.abortController = new AbortController()
        const signal = this.abortController.signal

        const dataStore = this.$store.state.dataStore
        if (!dataStore.device_id && !dataStore.point_id && !dataStore.time_point) {
          return
        }
        const params = await getParams(9)
        if(this.configNodeInfo.measureDefinitions.measureDefineName.includes('包络')){
          params.band = this.$store.state.SettingConnect.configNodeInfo.measureDefinitions.envelopeFrequencyDetail.split('-').map(Number)
          params.ftype = 9
        }

        // 使用signal进行请求，允许取消
        const result = await getWaveform(params, signal)

        const measureName = this.configNodeInfo?.measureDefinitions?.measureDefineName;
        if (measureName === '速度') {
          this.originalData = result.data.x.map((xValue, index) => [
            xValue,
            result.data.y[index]
          ]);
        } else {
          this.originalData = result.data.x.map((xValue, index) => [
            xValue,
            result.data.y[index]
          ]);
        }

        this.data = [...this.originalData]

        // 更新缩放事件处理
        this.initZoomEvents(this.data.length)

        // 检查数据量，决定是否需要优化
        if (this.data.length > this.sampleThreshold) {
          console.log(`数据量(${this.data.length})超过阈值(${this.sampleThreshold})，启用性能优化`);
        }

        this.updateChart();
        // 隐藏加载状态
        this.loading = false
        // 请求完成，清除控制器
        this.abortController = null
      } catch (error) {
        // 如果是请求被取消，不显示错误
        if (error.name === 'AbortError' || (axios && axios.isCancel && axios.isCancel(error))) {
          console.log('请求被取消')
          this.loading = false
          return
        }

        this.error = '数据加载失败'
        console.error('数据加载失败:', error)
        this.loading = false
      }
    },
    // 清空图表方法
    clearChart() {
      // 清空数据
      this.data = [];
      this.originalData = [];
      this.currentXValue = null;
      this.currentYValue = null;

      // 首先设置错误信息，确保它能够显示
      this.error = '没有可用的数据';

      // 取消正在进行的请求
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }

      // 如果图表实例存在，更新图表显示空数据
      if (this.chartInstance) {
        // 清除图表内容
        this.chartInstance.clear();

        // 设置空数据
        const option = {
          series: [{
            type: 'line',
            data: []
          }]
        };
        this.chartInstance.setOption(option, { replaceMerge: ['series'] });
      } else {
        // 如果图表实例不存在，初始化一个空图表
        this.initChart();
      }

      // 隐藏加载状态
      this.loading = false;
    },

    async checkDataStore() {
      const dataStore = this.$store.state.dataStore
      if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
        this.loading = true

        // 取消之前的请求（如果存在）
        if (this.abortController) {
          console.log('取消之前的请求')
          this.abortController.abort()
          this.abortController = null
        }

        return new Promise(resolve => {
          const interval = setInterval(() => {
            if (this.$store.state.dataStore && this.$store.state.dataStore.device_id) {
              clearInterval(interval)
              resolve()
            }
          }, 100)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.sycontainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chart {
  flex: 1;
  width: 100%;
  height: calc(100% - 30px);
  box-sizing: border-box;
  padding: 5px;
  background-color: #f5f5f5;
  position: relative;
}

.top-container {
  width: 100%;
  height: 30px;
  background-color: #f5f5f5;
  font-size: 14px;
  text-align: left;
  display: flex;
  padding: 3px;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #f56c6c;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 10;
}

.error-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f56c6c;
  position: relative;
}

.error-icon:before {
  content: '!';
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  font-size: 14px;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.7);
  z-index: 10;
}

.loading-text {
  color: #595959;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-text:before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #1890ff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
