<template>
  <div class="Online-container">
    <div class="left-Online-container">
      <BaseStationTree
        style="height: 100%"
        @node-click="handleNodeClick"
        @company-changed="companyChanged"
      />
    </div>
    <div class="right-Online-container">
      <component :is="currentComponent" class="content-Online-box" />
    </div>
  </div>
</template>

<script>
import BaseStationTree from './BaseStationTree/BaseStationTree.vue'
import Relevancy from './ConnectRelevancy/Relevancy/relevancy.vue'
import RelevancyDetailed from './ConnectRelevancy/RelevancyDetailed/RelevancyDetailed.vue'
// import { mapActions } from 'vuex'
export default {
  name: 'OnlineManagement',
  components: {
    BaseStationTree,
    Relevancy,
    RelevancyDetailed
  },
  data() {
    return {
      currentComponent: 'Relevancy'
    }
  },
  methods: {
    // ...mapActions('SettingConnect', ['addNode']),
    handleNodeClick(node) {
      if (node && node.id) { // 使用 id 而不是 key 来检查节点有效性
        try {
          this.$store.dispatch('SettingConnect/addNode', node)
          console.log('节点添加成功')
          this.currentComponent = 'Relevancy'
        } catch (error) {
          console.error('添加节点时出错:', error)
        }
      } else {
        console.error('无效的节点数据:', node)
        this.currentComponent = 'RelevancyDetailed'
      }
    },
    // 切换公司的同时将Relevancy 组件重置
    companyChanged(id) {
      this.currentComponent = 'Relevancy'
      // console.log('公司已经更改获取到的值是', id)
    }

  }
}
</script>

<style scoped>
.Online-container {
  display: flex;
  width: 100%;
  height: 91vh; /* 高度为视口的91% */
  background-color: #f5f7fa; /* 整体背景色 */
  padding: 10px;
  box-sizing: border-box; /* 包含内边距的计算 */
}

.left-Online-container {
  flex: 1;
  max-width: 25%; /* 左侧最大占25% */
  background-color: #ffffff; /* 左侧背景颜色 */
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影 */
  padding: 20px;
  margin-right: 10px; /* 与右侧容器的间距 */
  box-sizing: border-box;
}

.right-Online-container {
  flex: 4;
  background-color: #ffffff; /* 右侧背景颜色 */
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影 */
  padding: 20px;
  box-sizing: border-box;
}

.content-Online-box {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  padding: 20px;
  background-color: #f9f9f9; /* 内容背景颜色 */
  border-radius: 8px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1); /* 内部阴影 */
  box-sizing: border-box;
}
</style>
