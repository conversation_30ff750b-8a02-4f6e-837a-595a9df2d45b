<!-- 轴承选择对话框 -->
<template>
  <div v-if="dialogVisible" class="bearing-dialog" ref="dialogRef">
    <div class="dialog-header" @mousedown="startDrag">
      <span>特征频率标注</span>
      <button class="close-btn" @click="handleClose">×</button>
    </div>
    <div class="bearing-content">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        @selection-change="handleSelectionChange">
        <el-table-column type="expand">
          <template #default="props">
            <el-table
              :data="props.row.bearings"
              style="width: 100%">
              <el-table-column prop="description" label="轴承名称" >
                <template #default="scope">
                  {{ scope.row.description }} ({{ scope.row.manufacturer }}-{{ scope.row.model }})
                </template>
              </el-table-column>
              <el-table-column label="BPFO">
                <template #header>
                  <span>BPFO</span>
                  <el-checkbox v-model="props.row.isBPFOSelected"
                  @change="handleHeaderCheckboxChange('bpfo', props.index)" />
                </template>
                <template #default="scope">
                  <div class="frequency-input">
                    <el-input
                      v-model="scope.row.bpfoInput"
                      min="0"
                      @input="handleInputChange(scope.row, 'bpfo')"
                    />x
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="BPFI">
                <template #header>
                  <span>BPFI</span>
                  <el-checkbox v-model="props.row.isBPFISelected"
                  @change="handleHeaderCheckboxChange('bpfi', props.index)" />
                </template>
                <template #default="scope">
                  <div class="frequency-input">
                    <el-input
                      v-model="scope.row.bpfiInput"
                      min="0"
                      @input="handleInputChange(scope.row, 'bpfi')"
                    />x
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="BSF">
                <template #header>
                  <span>BSF</span>
                  <el-checkbox v-model="props.row.isBSFSelected"
                  @change="handleHeaderCheckboxChange('bsf', props.index)" />
                </template>
                <template #default="scope">
                  <div class="frequency-input">
                    <el-input
                      v-model="scope.row.bsfInput"
                      min="0"
                      @input="handleInputChange(scope.row, 'bsf')"
                    />x
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="FTF">
                <template #header>
                  <span>FTF</span>
                  <el-checkbox v-model="props.row.isFTFSelected"
                  @change="handleHeaderCheckboxChange('ftf', props.index)" />
                </template>
                <template #default="scope">
                  <div class="frequency-input">
                    <el-input
                      v-model="scope.row.ftfInput"
                      min="0"
                      @input="handleInputChange(scope.row, 'ftf')"
                    />x
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="轴名称" show-overflow-tooltip/>
        <el-table-column label="转速" width="180">
          <template #default="scope">
            <div class="speed-input-container">
              <button
                class="speed-btn decrease-btn"
                @click="adjustSpeed(scope.row, -1)"
                title="减少转速"
              >
                -
              </button>
              <el-input
                v-model="scope.row.inputSpeed"
                type="number"
                min="0"
                step="1"
                class="speed-input"
                @input="handleSpeedChange(scope.row)"
                @blur="validateSpeed(scope.row)"
                placeholder="输入转速"
              />
              <button
                class="speed-btn increase-btn"
                @click="adjustSpeed(scope.row, 1)"
                title="增加转速"
              >
                +
              </button>
              <span class="speed-unit">RPM</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import { mapState } from 'vuex'

export default {
  name: 'BearingDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    containerRef: {
      type: HTMLElement,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      isDragging: false,
      initialX: 0,
      initialY: 0,
      currentX: 0,
      currentY: 0,
      deviceId: null,
      tableData: [], // 表格数据
      originalData: null
    }
  },
  computed: {
    ...mapState('tree', ['selectedTreeNode'])
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.$nextTick(() => {
          this.resetToCenter();
        });
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
        this.$emit('close');
      }
    }
  },
  mounted() {
    this.dialogVisible = this.visible;
    if (this.selectedTreeNode && this.selectedTreeNode.id) {
      this.deviceId = this.selectedTreeNode.id
      this.getDeviceId(this.deviceId)
    }
    if (this.visible) {
      this.$nextTick(() => {
        this.resetToCenter();
      });
    }

    // 将对话框挂载到body以避免被其他元素遮挡
    this.$nextTick(() => {
      document.body.appendChild(this.$el);
    });
  },
  methods: {
    // 重置对话框到屏幕中央
    resetToCenter() {
      const dialog = this.$refs.dialogRef;
      if (dialog) {
        // 重置为CSS居中定位
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.top = '50%';
        dialog.style.left = '50%';

        // 重置拖动状态
        this.isDragging = false;
        this.initialX = 0;
        this.initialY = 0;
        this.currentX = 0;
        this.currentY = 0;
      }
    },

    handleClose() {
      this.dialogVisible = false;
      this.$emit('update:visible', false);
      this.$emit('close');

      // 确保下次打开时对话框重新居中
      this.$nextTick(() => {
        this.resetToCenter();
      });
    },

    handleConfirm() {
      // 计算实际频率值
      const calculateFrequency = (baseFreq, rpm) => {
        const speedHz = rpm / 60;
        return baseFreq * speedHz;
      };

      const result = this.tableData.filter(row => {
        // 检查是否有选中的频率类型
        return row.bearings.some(bearing => {
          const hasSelectedFrequencies = ['bpfo', 'bpfi', 'bsf', 'ftf'].some(type => {
            return row[`is${type.toUpperCase()}Selected`] &&
                   bearing[`${type}Input`] &&
                   bearing.selectedFrequencies[type].length > 0;
          });
          return hasSelectedFrequencies;
        });
      }).map(row => {
        const rpm = Number(row.inputSpeed);

        const bearingsWithFreq = row.bearings
          .map(bearing => {
            const frequencies = [];
            ['bpfo', 'bpfi', 'bsf', 'ftf'].forEach(type => {
              if (row[`is${type.toUpperCase()}Selected`] && bearing[`${type}Input`]) {
                const baseFreq = calculateFrequency(bearing.frequencies[type], rpm);
                bearing.selectedFrequencies[type].forEach(multiple => {
                  frequencies.push({
                    frequency: baseFreq * multiple,
                    multiple,
                    type: type.toUpperCase()
                  });
                });
              }
            });

            return {
              id: bearing.id,
              bearingName: bearing.description,
              frequencies: frequencies
            };
          })
          .filter(bearing => bearing.frequencies.length > 0);

        return {
          bearings: bearingsWithFreq
        };
      });

      if (result.length === 0 || !result.some(row => row.bearings.length > 0)) {
        this.$message.warning('请选择至少一个轴承的频率');
        return;
      }

      const bearingFrequencies = result.flatMap(row => row.bearings);
      this.$emit('confirm', bearingFrequencies);
      this.handleClose();
    },

    async getDeviceId(id) {
      try {
        const res = await request({
          url: `/measureDefinition/measureDefinition/${id}`,
          method: 'get'
        })
        this.deviceId = res.data.machineId
        this.getBearingData(this.deviceId)
      } catch (error) {
        console.error('获取设备信息失败:', error)
      }
    },

    async getBearingData(id) {
      try {
        const res = await request({
          url: `/deviceConfiguration/configuration/list/${id}`,
          method: 'get'
        })

        // 检查响应数据结构
        if (res.rows) {
          this.processApiData(res.rows)
        } else {
          console.error('API返回数据格式不正确:', res)
          this.$message.error('获取轴承数据格式错误')
        }
      } catch (error) {
        console.error('获取轴承数据失败:', error)
        this.$message.error('获取轴承数据失败')
      }
    },

    processApiData(rows) {
      console.log("BearRows",rows)
      if (!Array.isArray(rows)) {
        console.error('轴承数据不是数组格式:', rows)
        return
      }

      const processedShafts = [];

      rows.forEach(item => {
        const childBearingsData = item.children
          ?.filter(child => child.treeIcon === 'bearing')
          ?.map(bearingInfo => ({
            id: bearingInfo.id,
            description: bearingInfo.deviceName,
            ...bearingInfo.table,
            selectedFrequencies: { // Default to empty arrays
              bpfo: [],
              bpfi: [],
              bsf: [],
              ftf: []
            },
            frequencies: bearingInfo.table ? {
              bpfo: Number(bearingInfo.table.bpfo) || 0,
              bpfi: Number(bearingInfo.table.bpfi) || 0,
              bsf: Number(bearingInfo.table.bsf) || 0,
              ftf: Number(bearingInfo.table.ftf) || 0
            } : null,
            // Default input for multiples to empty string
            bpfoInput: '',
            bpfiInput: '',
            bsfInput: '',
            ftfInput: '',
          })) || [];

        if (childBearingsData.length > 0) {
          processedShafts.push({
            id: item.id,
            deviceName: item.deviceName,
            inputSpeed: Number(item.inputSpeed) || 0,
            bearings: childBearingsData,
            // Default header checkboxes to false (unchecked)
            isBPFOSelected: false,
            isBPFISelected: false,
            isBSFSelected: false,
            isFTFSelected: false
          });
        }
      });

      this.tableData = processedShafts;

      if (this.tableData.length === 0) {
        this.$message.warning('未找到包含轴承的模块数据')
      }
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    handleHeaderCheckboxChange(type, bearing) {
      // 更新当前轴承的选中状态和输入值
      if (bearing[`is${type.toUpperCase()}Selected`]) {
        // 如果选中，设置默认值为1
        bearing.bearings.forEach(b => {
          b[`${type}Input`] = '1';
          b.selectedFrequencies[type] = [1];
        });
      } else {
        // 如果取消选中，清空值
        bearing.bearings.forEach(b => {
          b[`${type}Input`] = '';
          b.selectedFrequencies[type] = [];
        });
      }
    },

    handleInputChange(bearing, type) {
      const input = bearing[`${type}Input`];
      const typeUpperCase = type.toUpperCase(); // e.g., BPFO

      if (!input || isNaN(input) || Number(input) <= 0) {
        bearing.selectedFrequencies[type] = [];

        // Logic to potentially uncheck parent shaft's header checkbox
        const parentShaft = this.tableData.find(shaft =>
          shaft.bearings && shaft.bearings.some(b => b.id === bearing.id)
        );

        if (parentShaft) {
          const allInputsForTypeEmpty = parentShaft.bearings.every(
            b => !b[`${type}Input`] || isNaN(b[`${type}Input`]) || Number(b[`${type}Input`]) <= 0
          );
          if (allInputsForTypeEmpty) {
            parentShaft[`is${typeUpperCase}Selected`] = false;
          }
        }
      } else {
        const maxMultiple = parseInt(input);
        bearing.selectedFrequencies[type] = Array.from(
          { length: maxMultiple },
          (_, i) => i + 1
        );

        if (bearing.selectedFrequencies[type].length > 0) {
          const parentShaft = this.tableData.find(shaft =>
            shaft.bearings && shaft.bearings.some(b => b.id === bearing.id)
          );
          if (parentShaft) {
            parentShaft[`is${typeUpperCase}Selected`] = true;
          }
        }
      }
    },

    startDrag(event) {
      if (event.target.classList.contains('close-btn')) return;

      this.isDragging = true;

      // 获取当前对话框的位置和鼠标位置
      const dialog = this.$refs.dialogRef;

      // 记录鼠标的初始位置
      this.initialX = event.clientX;
      this.initialY = event.clientY;

      // 记录对话框的初始样式
      const style = window.getComputedStyle(dialog);
      const transform = style.transform;

      // 如果是初始居中状态
      if (transform.includes('matrix') && style.top === '50%' && style.left === '50%') {
        // 设置为绝对定位，移除transform
        dialog.style.transform = 'none';
        dialog.style.top = `${(window.innerHeight - dialog.offsetHeight) / 2}px`;
        dialog.style.left = `${(window.innerWidth - dialog.offsetWidth) / 2}px`;

        // 由于我们刚刚改变了定位方式，需要重新获取鼠标相对于对话框的位置
        const rect = dialog.getBoundingClientRect();
        this.currentX = rect.left;
        this.currentY = rect.top;
      } else {
        // 对话框已经被移动过，直接获取当前位置
        this.currentX = parseInt(style.left) || 0;
        this.currentY = parseInt(style.top) || 0;
      }

      document.addEventListener('mousemove', this.drag);
      document.addEventListener('mouseup', this.stopDrag);
    },

    drag(event) {
      if (!this.isDragging) return;

      event.preventDefault();

      // 计算鼠标移动的距离
      const dx = event.clientX - this.initialX;
      const dy = event.clientY - this.initialY;

      // 更新对话框位置
      const dialog = this.$refs.dialogRef;
      const newLeft = this.currentX + dx;
      const newTop = this.currentY + dy;

      dialog.style.left = `${newLeft}px`;
      dialog.style.top = `${newTop}px`;

      // 更新鼠标初始位置，以便连续拖动
      this.initialX = event.clientX;
      this.initialY = event.clientY;
      this.currentX = newLeft;
      this.currentY = newTop;
    },

    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.drag);
      document.removeEventListener('mouseup', this.stopDrag);
    },

    adjustSpeed(row, direction) {
      const currentSpeed = Number(row.inputSpeed) || 0;
      const newSpeed = Math.max(0, currentSpeed + direction);
      row.inputSpeed = newSpeed;
      this.handleSpeedChange(row);
    },

    handleSpeedChange(row) {
      // 确保转速为数字且不小于0
      const speed = Number(row.inputSpeed);
      if (isNaN(speed) || speed < 0) {
        row.inputSpeed = 0;
        return;
      }

      // 转速改变时，重新计算所有轴承的实际频率值
      // 这里可以添加实时频率计算的逻辑
      console.log(`轴承 ${row.deviceName} 转速已更改为: ${speed} RPM`);

      // 如果有选中的频率类型，更新对应的频率计算
      this.updateFrequencyCalculations(row);
    },

    validateSpeed(row) {
      // 验证并修正转速输入
      let speed = Number(row.inputSpeed);

      if (isNaN(speed) || speed < 0) {
        speed = 0;
      }

      // 限制最大转速（可根据实际需求调整）
      if (speed > 100000) {
        speed = 100000;
        this.$message.warning('转速不能超过100000 RPM');
      }

      row.inputSpeed = speed;
      this.handleSpeedChange(row);
    },

    updateFrequencyCalculations(row) {
      // 根据新的转速重新计算所有轴承的频率
      if (row.bearings && row.bearings.length > 0) {
        row.bearings.forEach(bearing => {
          // 重新计算各类型的频率倍数
          ['bpfo', 'bpfi', 'bsf', 'ftf'].forEach(type => {
            if (row[`is${type.toUpperCase()}Selected`] && bearing[`${type}Input`]) {
              this.handleInputChange(bearing, type);
            }
          });
        });
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.drag);
    document.removeEventListener('mouseup', this.stopDrag);

    // 在组件销毁前，从body中移除对话框元素
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  }
}
</script>

<style scoped lang="scss">
.bearing-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  width: 800px;
  max-height: 80vh;
  height: auto;
  min-height: 600px;
  z-index: 2000 !important;
  display: flex;
  flex-direction: column;
  margin: 0;

  .dialog-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    user-select: none;
    flex-shrink: 0;
    background: #f5f7fa;

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .close-btn {
      border: none;
      background: none;
      font-size: 20px;
      color: #909399;
      cursor: pointer;
      padding: 0;

      &:hover {
        color: #409EFF;
      }
    }
  }

  .bearing-content {
    padding: 24px;
    flex: 1;
    overflow: auto;

    ::v-deep .el-table {
      .el-table__header-wrapper {
        th {
          background: #f5f7fa;
          color: #606266;
          font-weight: 500;
          padding: 12px 0;
          font-size: 14px;
        }
      }

      .el-table__body-wrapper {
        td {
          padding: 12px 0;
        }
      }

      .el-table__expand-icon {
        color: #409EFF;
      }
    }

    .frequency-input {
      display: flex;
      align-items: center;
      gap: 0px;

      ::v-deep .el-input {
        width: 60px;

        .el-input__inner {
          height: 28px;
          line-height: 28px;
          padding: 0 8px;
          text-align: center;
          font-size: 13px;
        }
      }
    }

    ::v-deep .el-table__expanded-cell {
      padding: 20px !important;
      background: #f5f7fa;

      .el-table {
        background: transparent;

        th {
          background: #e6e8eb !important;
        }

        td {
          background: transparent;
        }
      }
    }

    ::v-deep .el-checkbox {
      margin-left: 8px;
      .el-checkbox__label {
        font-size: 13px;
      }
    }

    .speed-input-container {
      display: flex;
      align-items: center;
      gap: 0;
      max-width: 300px;
      min-width: 150px;

      .speed-btn {
        border: 1px solid #dcdfe6;
        background: #ffffff;
        color: #606266;
        cursor: pointer;
        padding: 0 12px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 0;
        min-width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;

        &:hover {
          color: #409EFF;
          border-color: #409EFF;
          background: #f0f9ff;
        }

        &:active {
          background: #e1f4ff;
        }

        &.decrease-btn {
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
          border-right: none;
        }

        &.increase-btn {
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
          border-left: none;
        }
      }

      // 只针对转速输入框
      ::v-deep .speed-input {
        width: 200px;

        .el-input__inner {
          height: 32px;
          line-height: 32px;
          padding: 0 8px;
          text-align: center;
          font-size: 16px;
          border-radius: 0;
          border-left: none;
          border-right: none;
          box-shadow: none;
          /* 隐藏 Chrome, Safari, Edge, Opera 的上下箭头 */
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
          /* 隐藏 Firefox 的上下箭头 */
          -moz-appearance: textfield;
        }
      }

      .speed-unit {
        font-size: 14px;
        color: #909399;
        margin-left: 8px;
        white-space: nowrap;
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
    flex-shrink: 0;
    background: #f5f7fa;

    .el-button {
      padding: 10px 20px;
      font-size: 14px;

      & + .el-button {
        margin-left: 12px;
      }
    }
  }

  .frequency-checkboxes {
    display: flex;
    gap: 8px;
    align-items: center;

    .el-checkbox-group {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .el-checkbox {
      margin-right: 0;
      .el-checkbox__label {
        padding-left: 6px;
        font-size: 13px;
      }
    }
  }
}
</style>
