<template>
  <div v-show="visible" :style="menuStyle" class="right-click-menu" @contextmenu.prevent>
    <div class="menu-container" ref="menuContainer">
      <div
        v-for="(option, index) in options"
        :key="index"
        class="menu-item"
        :class="{ disabled: option.disabled }"
        @mouseenter="handleMouseEnter(option,index)"
        @mouseleave="handleMouseLeave(option,index)"
        @click.stop="handleClick(option)"
        :ref="`menuItem-${index}`"
      >
        <span class="menu-label">{{ option.label }}</span>

        <!-- 快捷键 -->
        <span v-if="option.shortcut" class="shortcut">{{ option.shortcut }}</span>

        <!-- 子菜单箭头 -->
        <span v-if="option.subMenu" class="submenu-arrow">›</span>

        <!-- 子菜单 -->
        <div
          v-show="option.subMenu"
          class="submenu"
          :class="{ active: activeSubmenu === index }"
          :style="getSubmenuPosition(index)"
        >
          <div
            v-for="(subOption, subIndex) in option.subMenu"
            :key="subIndex"
            class="menu-item"
            :class="{ disabled: subOption.disabled }"
            @click.stop="handleClick(subOption)"
          >
            <span class="menu-label">
              <i v-if="subOption.icon" :class="subOption.icon" class="menu-icon"></i>
              {{ subOption.label }}
            </span>
            <span v-if="subOption.shortcut" class="shortcut">{{ subOption.shortcut }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RightClickMenu',

  data() {
    return {
      visible: false,
      menuStyle: {
        position: 'fixed',
        top: '0px',
        left: '0px'
      },
      options: [],
      activeSubmenu: null,
      subMenuTimeoutId: null
    }
  },

  methods: {
    show(event, options) {
      // 先隐藏任何已存在的菜单
      if (this.visible) {
        this.hide()
      }

      this.options = options
      this.visible = true
      this.activeSubmenu = null

      // 等待DOM更新后再计算位置
      this.$nextTick(() => {
        const { clientX, clientY } = event
        const menu = this.$refs.menuContainer
        const { width, height } = menu.getBoundingClientRect()

        // 计算可用空间
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight

        // 计算最终位置
        let left = clientX
        let top = clientY

        // 处理右边界
        if (left + width > viewportWidth) {
          left = viewportWidth - width
        }

        // 处理下边界
        if (top + height > viewportHeight) {
          top = viewportHeight - height
        }

        // 确保不会超出左边和上边界
        left = Math.max(0, left)
        top = Math.max(0, top)

        // 应用位置
        this.menuStyle = {
          position: 'fixed',
          left: `${left}px`,
          top: `${top}px`,
          zIndex: 9999
        }

        // 添加全局点击监听
        window.addEventListener('click', this.handleOutsideClick)
        window.addEventListener('scroll', this.handleScroll)
        window.addEventListener('resize', this.handleResize)
      })
    },

    hide() {
      this.visible = false
      this.activeSubmenu = null
      this.removeGlobalListeners()
    },

    handleMouseEnter(option,index) {
      if (this.subMenuTimeoutId) {
        clearTimeout(this.subMenuTimeoutId)
      }
      this.activeSubmenu = option.subMenu ? index : null  // 使用 index 来控制
    },

    handleMouseLeave(option) {
      if (option.disabled) return

      this.subMenuTimeoutId = setTimeout(() => {
        this.activeSubmenu = null
      }, 100)
    },

    handleClick(option, index) {
      if (option.disabled) return

      if (option.subMenu) {
        // 如果有子菜单，切换显示状态
        this.activeSubmenu = this.activeSubmenu === index ? null : index
        return
      }

      if (option.action) {
        this.$emit('menu-click', option.action)
        this.hide()
      }
    },
    getSubmenuPosition(index) {
      const menuItem = this.$refs[`menuItem-${index}`]?.[0]
      if (!menuItem) {
        return {
          position: 'absolute',
          top: '0',
          left: '100%'
        }
      }

      const rect = menuItem.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      let position = {
        position: 'absolute',
        top: '0',
        left: '100%'
      }

      // 检查右侧空间
      if (rect.right + this.subMenuWidth > viewportWidth) {
        position.left = 'auto'
        position.right = '100%'
      }

      // 检查底部空间
      const estimatedHeight = this.menuHeight || 200 // 预估子菜单高度
      if (rect.top + estimatedHeight > viewportHeight) {
        position.top = 'auto'
        position.bottom = '0'
      }

      return position
    },

    handleOutsideClick(event) {
      if (!this.$el.contains(event.target)) {
        this.hide()
      }
    },

    handleScroll() {
      this.hide()
    },

    handleResize() {
      this.hide()
    },

    removeGlobalListeners() {
      window.removeEventListener('click', this.handleOutsideClick)
      window.removeEventListener('scroll', this.handleScroll)
      window.removeEventListener('resize', this.handleResize)
    }
  },

  beforeDestroy() {
    this.removeGlobalListeners()
    if (this.subMenuTimeoutId) {
      clearTimeout(this.subMenuTimeoutId)
    }
  }
}
</script>

<style scoped>
.right-click-menu {
  font-size: 14px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: visible;
  user-select: none;
}

.menu-container {
  min-width: 160px;
  padding: 4px 0;
}

.menu-item {
  position: relative;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  color: #333;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item.disabled {
  cursor: not-allowed;
  color: #c0c4cc;
}

.menu-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.shortcut {
  margin-left: 16px;
  color: #909399;
  font-size: 12px;
}

.submenu-arrow {
  margin-left: 8px;
  color: #909399;
}

.submenu {
  position: absolute;
  top: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 160px;
}

.menu-icon {
  margin-right: 8px;
  font-size: 16px;
}
.submenu {
  position: absolute;
  top: 0;
  left: 100%;
  display: none;  /* 默认隐藏 */
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 160px;
}

.submenu.active {
  display: block;  /* 显示激活的子菜单 */
}
</style>
