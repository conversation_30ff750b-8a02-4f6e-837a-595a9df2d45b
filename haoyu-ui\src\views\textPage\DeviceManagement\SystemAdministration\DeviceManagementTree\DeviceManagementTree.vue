<template>
  <div style="height: 100%">
    <div class="container">
      <div ref="topContainer" class="half-height">
        <WorkTree ref="workTree" @switch-component="switchComponent" />
      </div>
    </div>
  </div>
</template>

<script>
// 引入 Tree 组件
import WorkTree from './tree.vue'
export default {
  components: {
    WorkTree
  },
  methods: {
    switchComponent(componentName, node) {
      this.$emit('switch-component', componentName, node)
    }
  }
}
</script>

<style scoped>
@import './DeviceManagementTree.css'
</style>
