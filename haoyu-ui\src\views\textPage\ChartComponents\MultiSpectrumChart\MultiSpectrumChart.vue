<!-- 多频谱图 -->
<template>
  <div @contextmenu.prevent>
    <div ref="chart" class="chart" :class="{ loading: loading }" />
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import { getTimeFreq } from './data'
import { getParams } from '@/utils/graph_Interface'
import ToolBar from '../ChartsToolsBar/ToolBar'

export default {
  components: {
    ToolBar
  },
  data() {
    return {
      chartInstance: null,
      spectrumDataList: [], // 存储多条频谱数据
      loading: false,
      error: null
    }
  },
  computed: {
    ...mapState('dataStore', ['selectedRowsData'])
  },
  watch: {
    selectedRowsData: {
      immediate: true,
      handler: 'handleSelectedRowsDataChange'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      window.addEventListener('resize', this.resizeChart)
      // 初始化时如果没有选中数据，确保图表为空
      if (!this.selectedRowsData || this.selectedRowsData.length === 0) {
        this.spectrumDataList = [];
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null;
    }
    this.spectrumDataList = [];
  },
  methods: {
    initChart() {
      if (!this.$refs.chart) {
        return;
      }

      if (this.chartInstance) {
        this.chartInstance.dispose();
      }

      this.chartInstance = echarts.init(this.$refs.chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          }
        },
        dataZoom: [{
          type: 'inside',
          xAxisIndex: Array.from({ length: this.spectrumDataList.length }, (_, i) => i)
        }]
      }

      this.chartInstance.setOption(option)
    },
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    },
    async handleSelectedRowsDataChange(newData) {
      if (!this.chartInstance) {
        this.initChart();
      }

      if (!newData || newData.length === 0) {
        this.spectrumDataList = [];
        this.chartInstance.clear();
        return;
      }

      try {
        this.loading = true;

        // 记录已请求的数据
        const requestedIds = new Set(this.spectrumDataList.map(spectrum => spectrum.id));

        const spectrumPromises = newData.map(async rowData => {
          const id = `${rowData.device_id}-${rowData.point_id}-${rowData.time_point}`;

          // 如果该点的数据已经请求过，则直接返回缓存的数据
          if (requestedIds.has(id)) {
            return null; // 返回 null 以便后续过滤
          }

          const params = await getParams(2); // ftype 为 2
          params.device_id = String(rowData.device_id);
          params.point_id = String(rowData.point_id);
          params.time_point = rowData.time_point;

          const result = await getTimeFreq(params);
          return {
            id: id,
            data: result.data.x.map((x, index) => [x, result.data.y[index]])
          };
        });

        const results = await Promise.all(spectrumPromises);
        // 过滤掉 null 值并合并新数据
        this.spectrumDataList = [
          ...this.spectrumDataList,
          ...results.filter(Boolean) // 只保留有效的结果
        ];

        this.updateChart();
      } catch (error) {
        console.error('获取频谱数据失败:', error);
        this.error = '数据加载失败';
      } finally {
        this.loading = false;
      }
    },

    updateChart() {
      if (!this.chartInstance || !this.spectrumDataList.length) return;

      const seriesCount = this.spectrumDataList.length;
      const grids = [];
      const yAxis = [];
      const xAxis = [];
      const series = [];

      // Define layout properties for better spacing and borders
      const containerPadding = { top: 5, bottom: 5, left: 6, right: 4 }; // in percent
      const chartGap = 3; // gap between charts in percent
      const totalUsableHeight = 100 - containerPadding.top - containerPadding.bottom;
      const gridHeight = (totalUsableHeight - (seriesCount > 1 ? (seriesCount - 1) * chartGap : 0)) / seriesCount;

      // 创建所有子图表的索引数组
      const xAxisIndexes = Array.from({ length: seriesCount }, (_, i) => i);

      // 设置缩放控件
      const dataZoom = [
        {
          type: 'inside',
          xAxisIndex: xAxisIndexes
        }
      ];

      this.spectrumDataList.forEach((spectrum, index) => {
        // 计算数据的最小值和最大值
        const xValues = spectrum.data.map(point => point[0]);
        const minX = Math.min(...xValues);
        const maxX = Math.max(...xValues);
        grids.push({
          height: `${gridHeight}%`,
          top: `${containerPadding.top + index * (gridHeight + chartGap)}%`,
          left: `${containerPadding.left}%`,
          right: `${containerPadding.right}%`,
          show: true,
          borderColor: '#ccc',
          borderWidth: 1
        });

        yAxis.push({
          gridIndex: index,
          type: 'value',
          scale: true
        });

        xAxis.push({
          gridIndex: index,
          type: 'value',
          scale: true,
          boundaryGap: false, // 设置为 false
          min: minX,          // 设置最小值
          max: maxX           // 设置最大值
        });

        series.push({
          type: 'line',
          showSymbol: false,
          data: spectrum.data,
          xAxisIndex: index,
          yAxisIndex: index,
          smooth: true,
          sampling: 'lttb'  // 添加采样
        });
      });

      this.chartInstance.setOption({
        grid: grids,
        xAxis: xAxis,
        yAxis: yAxis,
        dataZoom: dataZoom,  // 注意这里是 dataZoom 而不是 datazoom
        series: series,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationThreshold: 2000,
        progressiveThreshold: 3000,
        progressive: 200,
        animationDelay: function(idx) {
          return idx * 100;
        }
      }, true);
    }
  }
}
</script>

<style scoped>
.trend-toolbar {
  background: #f5f5f5;
  height: 30px;
}
.chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  color: red;
  text-align: center;
  margin-top: 10px;
}
</style>
