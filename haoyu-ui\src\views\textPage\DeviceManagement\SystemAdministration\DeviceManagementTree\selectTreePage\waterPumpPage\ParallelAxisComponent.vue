<template>
  <div class="gearbox-parallel-axis">
    <!-- 第一行输入框 -->
    <div class="input-group">
      <label for="gearbox">齿轮箱名称</label>
      <el-input
        id="gearbox"
        v-model="gearbox"
        placeholder="请输入齿轮箱名称"
        size="mini"
        style="width: 300px;"
      />
      <el-button size="mini" type="primary" style="margin-left: 10px;" @click="submitData">
        {{ editModel ? '保存' : '添加' }}
      </el-button>
    </div>
    
    <!-- 添加总传动比显示区域 -->
    <div class="transmission-ratio">
      <div class="ratio-item">
        <span class="ratio-label">总传动比:</span>
        <span class="ratio-value">{{ totalRatio }}</span>
      </div>
      <div class="ratio-item">
        <span class="ratio-label">输入转速:</span>
        <span class="ratio-value">{{ tableData.length > 0 ? tableData[0].speed : 0 }} rpm</span>
      </div>
      <div class="ratio-item">
        <span class="ratio-label">输出转速:</span>
        <span class="ratio-value">{{ tableData.length > 0 ? tableData[tableData.length - 1].speed : 0 }} rpm</span>
      </div>
    </div>

    <!-- 表格部分 -->
    <el-table :data="tableData" border style="width: 100%">
      <!-- 转速 -->
      <el-table-column label="转速" prop="speed">
        <template slot-scope="scope">
          <div v-if="scope.row.editableSpeed">
            <el-input
              :ref="'inputSpeed_' + scope.row.index"
              v-model="scope.row.speed"
              size="mini"
              @blur="validateAndBlur(scope.row, 'speed')"
              @keyup.enter.native="validateAndBlur(scope.row, 'speed')"
            />
          </div>
          <div v-else @dblclick="editCell(scope.row, 'speed')">
            {{ scope.row.speed || '--' }}
          </div>
        </template>
      </el-table-column>
      <!-- 从动轮 -->
      <el-table-column label="从动轮" align="center">

        <el-table-column label="描述" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.editableDescription">
              <el-input
                :ref="'inputDescription_' + scope.row.index"
                v-model="scope.row.description"
                size="mini"
                @blur="validateAndBlur(scope.row, 'description')"
              />
            </div>
            <div v-else @click="editCell(scope.row, 'description')">
              {{ scope.row.description }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="所在轴" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.editableAxis">
              <el-input
                :ref="'inputAxis_' + scope.row.index"
                v-model="scope.row.axis"
                size="mini"
                @blur="validateAndBlur(scope.row, 'axis')"
              />
            </div>
            <div v-else @click="editCell(scope.row, 'axis')">
              {{ scope.row.axis }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="齿数" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.editableTeethCount">
              <el-input
                v-model="scope.row.teethCount"
                :ref="'inputTeethCount_' + scope.row.index"
                size="mini"
                @blur="validateAndBlur(scope.row, 'teethCount')"
                @keyup.enter.native="validateAndBlur(scope.row, 'teethCount')"
              />
            </div>
            <div v-else @dblclick="editCell(scope.row, 'teethCount')">
              {{ scope.row.teethCount || '--' }}
            </div>
          </template>
        </el-table-column>

      </el-table-column>

      <!-- 主（驱）动轮 -->
      <el-table-column label="主（驱）动轮" align="center">
        <el-table-column label="描述" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.editableMainDescription">
              <el-input
                :ref="'inputMainDescription_' + scope.row.index"
                v-model="scope.row.mainDescription"
                size="mini"
                @blur="validateAndBlur(scope.row, 'mainDescription')"
              />
            </div>
            <div v-else @click="editCell(scope.row, 'mainDescription')">
              {{ scope.row.mainDescription }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="轴" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.editableMainAxis">
              <el-input
                :ref="'inputMainAxis_' + scope.row.index"
                v-model="scope.row.mainAxis"
                size="mini"
                @blur="validateAndBlur(scope.row, 'mainAxis')"
              />
            </div>
            <div v-else @click="editCell(scope.row, 'mainAxis')">
              {{ scope.row.mainAxis }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="齿数" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.editableMainTeethCount">
              <el-input
                :ref="'inputMainTeethCount_' + scope.row.index"
                v-model="scope.row.mainTeethCount"
                size="mini"
                @blur="validateAndBlur(scope.row, 'mainTeethCount')"
                @keyup.enter.native="validateAndBlur(scope.row, 'mainTeethCount')"
              />
            </div>
            <div v-else @dblclick="editCell(scope.row, 'mainTeethCount')">
              {{ scope.row.mainTeethCount || '--' }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  batchAddGroupComponent,
  batchUpdateGroupComponent
} from '@/api/haoyu-system/SystemAdministration/Configuration.js'
export default {
  props: {
    editModel: {
      typeof: 'boolean',
      default: false
    },
    formData: {
      type: [Object, Array],
      default: null
    },
    deviceId: {
      type: Number,
      default: null
    },
    tableDataTree: {
      type: [Object, Array], // 接受 Object 或 Array 类型
      required: true
    },
    selectedNode: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      gearbox: '',
      tableData: [],
      totalRatio: '0.00' // 添加总传动比字段
    }
  },
  watch: {
    formData: {
      immediate: true,
      handler(newData) {
        if (newData) {
          if (!this.editModel) {
            // 新建模式
            const levelMatch = newData.label?.match(/(\d+)级/)
            const level = levelMatch ? parseInt(levelMatch[1]) : 1

            // 修改生成表格数据的逻辑
            this.tableData = Array(level).fill(null).map((_, index) => ({
              speed: index === 0 ? '1000' : '', // 只设置第一行转速，后续会计算
              // 从动轮描述和所在轴使用相同的罗马数字(index + 1)
              description: `${this.toRoman(index + 2)}转轴从动轮`,
              axis: `${this.toRoman(index + 2)}转轴`,
              teethCount: '26', // 设置默认齿数
              // 主动轮描述和轴使用相同的罗马数字(index + 1)
              // 除了最后一行外，其他行都设置主动轮信息
              mainDescription: index < level-1 ? `${this.toRoman(index + 1)}转轴驱动轮` : '',
              mainAxis: index < level-1 ? `${this.toRoman(index + 1)}转轴` : '',
              mainTeethCount: index < level-1 ? '13' : '', // 只有非最后一行才设置主动轮齿数
              editableDescription: false,
              editableAxis: false,
              editableTeethCount: false,
              editableMainDescription: false,
              editableMainAxis: false,
              editableMainTeethCount: false,
              editableSpeed: false,
              index
            }))
            this.gearbox = newData.gearbox || ''
            
            // 计算转速和传动比
            this.recalculateAllSpeeds()
            this.totalRatio = this.calculateTotalRatio()
          } else {
            // 编辑模式
            this.tableData = newData.table.tableData
            this.gearbox = newData.table.gearbox
            
            // 确保编辑模式下也能正确计算和显示转速和传动比
            this.recalculateAllSpeeds()
            this.totalRatio = this.calculateTotalRatio()
          }
        }
      }
    },
    // 监听表格数据变化，自动更新传动比
    tableData: {
      deep: true,
      handler() {
        this.totalRatio = this.calculateTotalRatio()
      }
    }
  },
  created() {
    if (this.editModel) {
      this.tableData = this.formData.table.tableData
      this.gearbox = this.formData.table.gearbox
    } else {
      this.tableData = this.formData.tableData
      this.gearbox = this.formData.gearbox
    }
    
    // 确保所有齿数都填写了默认值
    this.tableData.forEach((row, index) => {
      if (!row.teethCount && index > 0) row.teethCount = '26'; // 默认从动轮齿数
      if (!row.mainTeethCount && index < this.tableData.length - 1) row.mainTeethCount = '13'; // 默认驱动轮齿数，但最后一根轴除外
    });
    
    // 计算初始转速和传动比
    this.recalculateAllSpeeds();
    this.totalRatio = this.calculateTotalRatio();
  },
  methods: {
    validateAndBlur(row, key) {
      // 检查相应字段是否为空
      if (!row[key]) {
        this.$message.error(`${key} 不能为空！`);
        return // 如果为空，不允许失去焦点
      }

      // 对齿数和转速进行数值验证
      if (key === 'teethCount' || key === 'mainTeethCount' || key === 'speed') {
        const value = Number(row[key]);
        if (isNaN(value) || value <= 0) {
          this.$message.error(`${key === 'speed' ? '转速' : '齿数'}必须为大于0的数字！`);
          return;
        }
        // 转换为整数
        row[key] = Math.floor(value);
        
        // 当齿数发生变化时，重新计算转速
        if (key === 'teethCount' || key === 'mainTeethCount') {
          this.recalculateSpeed(row, key);
        }
        
        // 当转速发生变化，并且是首个元素时，更新后续所有转速
        if (key === 'speed' && this.tableData.indexOf(row) === 0) {
          this.recalculateAllSpeeds();
        }
      }

      // 设置为不可编辑状态
      row[`editable${this.capitalizeFirstLetter(key)}`] = false;
    },
    
    // 重新计算转速的方法
    recalculateSpeed(currentRow, changedField) {
      const currentIndex = this.tableData.indexOf(currentRow);
      
      // 如果是齿数变更
      if (changedField === 'teethCount' || changedField === 'mainTeethCount') {
        // 在tableData中找到该行的索引
        if (currentIndex >= 0) {
          // 如果是第一个元素的mainTeethCount变更
          if (currentIndex === 0 && changedField === 'mainTeethCount') {
            // 如果有下一个元素，计算下一个元素的speed
            if (currentIndex + 1 < this.tableData.length) {
              const nextRow = this.tableData[currentIndex + 1];
              if (nextRow.teethCount && currentRow.mainTeethCount) {
                const ratio = Number(nextRow.teethCount) / Number(currentRow.mainTeethCount);
                nextRow.speed = (Number(currentRow.speed) / ratio).toFixed(2);
              }
            }
          } 
          // 如果不是第一个元素的teethCount变更
          else if (currentIndex > 0 && changedField === 'teethCount') {
            const prevRow = this.tableData[currentIndex - 1];
            if (prevRow.mainTeethCount && currentRow.teethCount) {
              const ratio = Number(currentRow.teethCount) / Number(prevRow.mainTeethCount);
              currentRow.speed = (Number(prevRow.speed) / ratio).toFixed(2);
            }
          }
          
          // 若修改的是mainTeethCount且不是最后一个元素，向下传递影响
          if (changedField === 'mainTeethCount' && currentIndex < this.tableData.length - 1) {
            const nextRow = this.tableData[currentIndex + 1];
            if (nextRow.teethCount && currentRow.mainTeethCount) {
              const ratio = Number(nextRow.teethCount) / Number(currentRow.mainTeethCount);
              nextRow.speed = (Number(currentRow.speed) / ratio).toFixed(2);
            }
          }
          
          // 更新后面所有行的转速
          this.updateFollowingSpeeds(currentIndex + 1);
        }
      }
    },
    
    // 更新第startIndex之后所有行的转速
    updateFollowingSpeeds(startIndex) {
      for (let i = startIndex; i < this.tableData.length - 1; i++) {
        const currentRow = this.tableData[i];
        const nextRow = this.tableData[i + 1];
        
        if (currentRow.mainTeethCount && nextRow.teethCount) {
          const ratio = Number(nextRow.teethCount) / Number(currentRow.mainTeethCount);
          nextRow.speed = (Number(currentRow.speed) / ratio).toFixed(2);
        }
      }
    },
    
    // 重新计算所有行的转速（基于第一行的转速）
    recalculateAllSpeeds() {
      if (this.tableData.length <= 1) return;
      
      const firstRowSpeed = Number(this.tableData[0].speed);
      let currentSpeed = firstRowSpeed;
      
      for (let i = 0; i < this.tableData.length - 1; i++) {
        const currentRow = this.tableData[i];
        const nextRow = this.tableData[i + 1];
        
        if (currentRow.mainTeethCount && nextRow.teethCount) {
          const ratio = Number(nextRow.teethCount) / Number(currentRow.mainTeethCount);
          currentSpeed = currentSpeed / ratio;
          nextRow.speed = currentSpeed.toFixed(2);
        }
      }
    },
    
    // 计算总传动比
    calculateTotalRatio() {
      let ratio = 1;
      
      for (let i = 0; i < this.tableData.length - 1; i++) {
        const currentRow = this.tableData[i];
        const nextRow = this.tableData[i + 1];
        
        if (currentRow.mainTeethCount && nextRow.teethCount) {
          ratio *= Number(nextRow.teethCount) / Number(currentRow.mainTeethCount);
        }
      }
      
      return ratio.toFixed(2);
    },

    // 用于将索引转换为罗马数字的函数
    toRoman(num) {
      const romanNumerals = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']
      return romanNumerals[num] || '' // 支持前十个罗马数字
    },

    // 提交表单0
    submitData() {
      // 修改这里：使用当前组件的tableData，而不是formData中的tableData
      const tableDataToUse = this.tableData

      // 构建子节点树结构
      const children = []
      let currentAxis = null

      tableDataToUse.forEach((item, index) => {
        // 为每个转轴创建节点
        currentAxis = {
          id: this.editModel ? item.noDeleteAxisId : '',
          parentId: this.deviceId,
          deviceName: `${this.toRoman(index)}转轴`,
          treeIcon: 'noDeleteAxis',
          mongodbId: '',
          treeRank: this.editModel ? item.noDeleteAxisTreeRank : index,
          children: [],
          table: item.table || {}
        }

        // 添加驱动轮节点
        if (item.mainDescription && item.mainAxis && item.mainTeethCount) {
          currentAxis.children.push({
            id: this.editModel ? item.mainId : '',
            parentId: this.deviceId,
            deviceName: `${item.mainDescription}${item.mainTeethCount}齿`,
            treeIcon: 'noDeleteParallelShafting',
            treeRank: this.editModel ? item.mainTreeRank : 1,
            mongodbId: '',
            children: [],
            table: {}
          })
        }

        // 添加从动轮节点
        if (item.description && item.axis && item.teethCount) {
          currentAxis.children.push({
            id: this.editModel ? item.followId : '',
            parentId: this.deviceId,
            deviceName: `${item.description}${item.teethCount}齿`,
            treeIcon: 'noDeleteParallelShafting',
            treeRank: this.editModel ? item.followTreeRank : 0,
            mongodbId: '',
            children: [],
            table: {}
          })
        }

        children.push(currentAxis)
      })

      // 构建根节点数据
      const data = {
        id: this.editModel ? this.formData.id : (this.selectedNode ? this.selectedNode.id : ''),
        parentId: this.deviceId,
        deviceName: this.gearbox,
        treeIcon: 'gearbox',
        mongodbId: '',
        treeRank: this.editModel ? this.formData.treeRank : this.getTreeRank(),
        children,
        table: {
          parentTreeRank: this.getTreeRank(),
          gearboxType: 'noDeleteParallelShafting',
          gearbox: this.gearbox,
          tableData: this.tableData
        }
      }

      // 提交数据
      if (this.editModel) {
        this.syncMongodbIds(this.formData, data)
        batchUpdateGroupComponent(data).then(() => {
          this.$emit('submit')
        })
      } else {
        batchAddGroupComponent(data).then(() => {
          this.$emit('submit')
        })
      }
    },
    // 把原有的树节点直接赋值给提交的树结构
    syncMongodbIds(selectedNode, dataNode) {
      // console.log(dataNode)
      // 确保两个节点都存在
      if (!selectedNode || !dataNode) return
      // 同步 mongodbId
      // selectedNode.mongodbId给dataNode.mongodbId
      dataNode.mongodbId = selectedNode.mongodbId
      dataNode.treeRank = selectedNode.treeRank
      // 如果两个节点都有子节点，递归处理每个子节点
      if (selectedNode.children && dataNode.children) {
        // 确保两个子节点数组长度相同
        const length = Math.min(selectedNode.children.length, dataNode.children.length);

        for (let i = 0; i < length; i++) {
          this.syncMongodbIds(selectedNode.children[i], dataNode.children[i]) // 递归处理子节点
        }
      }
    },
    editCell(row, key) {
      console.log(this.$refs)
      // console.log(`input${this.capitalizeFirstLetter(key)}_${row.index}`)
      row[`editable${this.capitalizeFirstLetter(key)}`] = true // 设置可编辑状态

      // 聚焦输入框
      this.$nextTick(() => {
        const inputRef = this.$refs[`input${this.capitalizeFirstLetter(key)}_${row.index}`]
        console.log(`input${this.capitalizeFirstLetter(key)}_${row.index}`)
        if (inputRef) {
          inputRef.focus() // 聚焦到输入框
        } else {
          console.error(`输入框未找到: input${this.capitalizeFirstLetter(key)}_${row.index}`);
        }
      })
    },
    editCellSpeed(row, key) {
      row[`editable${key.charAt(0).toUpperCase() + key.slice(1)}`] = true
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1)
    },
    initData(data) {
      let globalIndex = 0;
      this.tableData = (data.tableData || []).map((item) => {
        return {
          ...item,
          index: globalIndex++ // 使用全局自增index
        };
      });
    },
    getTreeRank() {
      if (this.selectedNode) {
        return this.selectedNode.children ? this.selectedNode.children.length : 0
      }
      return this.tableDataTree.length
    }
  }
}
</script>

<style scoped>
.gearbox-parallel-axis {
  padding: 20px;
}

.input-group {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

label {
  margin-right: 10px;
}

.el-table th,
.el-table td {
  text-align: center;
  cursor: pointer;
}

.transmission-ratio {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.ratio-item {
  margin-right: 30px;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.ratio-label {
  font-weight: bold;
  color: #606266;
  font-size: 14px;
}

.ratio-value {
  margin-left: 10px;
  font-size: 14px;
  color: #409EFF;
  font-weight: 500;
}
</style>
