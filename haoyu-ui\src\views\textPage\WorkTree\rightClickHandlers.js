import { message } from 'ant-design-vue'
export default {
  // 处理右击菜单的点击事件
  handleMenuClick(action, context) {
    console.log('handleMenuClick:', action)
    switch (action) {
      case 'copy':
        this.copyNodeToClipboard(context) // 调用复制节点方法
        break
      case 'cut':
        this.cutNode(context) // 调用剪切节点方法
        break
      case 'paste':
        this.pasteNodeAndOverwrite(context) // 调用粘贴并覆盖节点方法
        break
      case 'delete':
        this.deleteNode(context) // 调用删除方法
        break
      case 'newDevice':
        this.addNewDevice(context) // 调用添加新设备方法
        break
      case 'newNode':
        this.addNewNode(context) // 调用添加新节点方法
        break
      case 'newDefinition':
        this.addNewFactory(context) // 调用添加新工厂方法
        break
      case 'newArea':
        context.newNodeDialogVisible = true // 显示新建节点对话框
        break
      case 'characteristicFrequency':
        this.setCharacteristicFrequency(context) // 调用设置设备特征频率方法
        break
      case 'spectrumRangeSettings':
        this.setSpectrumRangeSettings(context) // 调用设置频谱显示范围方法
        break
      case 'exportFile':
        this.exportFile(context) // 调用导出文件方法
        break
      case 'importExcel':
        this.importExcel(context) // 调用导入Excel方法
        break
      case 'reserve':
        this.reserve(context) // 调用预留方法
        break
      default:
        console.log('Unknown action:', action) // 输出未知操作类型
        message.error(`未知操作: ${action}`) // 显示提示框
    }
  },
  // 复制节点到剪贴板
  copyNodeToClipboard(context) {
    if (context.currentNode) {
      const nodeData = context.deepClone(context.currentNode)
      context.clipboard = nodeData
      message.success('节点已复制到剪贴板')
    }
  },
  // 剪切节点
  cutNode(context) {
    if (context.currentNode) {
      context.clipboard = context.deepClone(context.currentNode)
      context.deleteNode(context.treeData, context.currentNode.key)
      message.success('节点已剪切到剪贴板')
    }
  },
  // 粘贴
  pasteNodeAndOverwrite(context) {
    if (context.clipboard && context.currentNode) {
      const newNodeData = context.deepClone(context.clipboard)
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
      context.currentNode.children.push(newNodeData)
      message.success('节点数据已粘贴')
    } else {
      message.error('没有可粘贴的数据或未选中目标节点')
    }
  },
  // 新建设备
  addNewDevice(context) {
    context.newDeviceDialogVisible = true
    if (context.currentNode) {
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
      context.currentNode.children.push({ title: '新建设备', key: `${context.currentNode.key}-newDevice` })
    }
  },
  // 新建测点
  addNewNode(context) {
    if (context.currentNode) {
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
      context.currentNode.children.push({ title: '新建节点', key: `${context.currentNode.key}-newNode` })
    }
  },

  // 新建设备
  addNewFactory(context) {
    if (context.currentNode) {
      if (!context.currentNode.children) {
        context.currentNode.children = []
      }
      context.currentNode.children.push({ title: '新建工厂', key: `${context.currentNode.key}-newFactory` })
    }
  },
  //  设置设备特征频率
  setCharacteristicFrequency(context) {
    console.log('设置设备特征频率')
    // 实现设置设备特征频率逻辑
  },
  // 设置频谱显示范围
  setSpectrumRangeSettings(context) {
    console.log('设置频谱显示范围')
    // 实现设置频谱显示范围逻辑
  },
  // 导出文件
  exportFile(context) {
    console.log('导出文件')
    // 实现导出文件逻辑
  },
  // 实现导入Excel逻辑
  importExcel(context) {
    console.log('导入Excel')
    // 实现导入Excel逻辑
  },
  // 实现预留功能逻辑
  reserve(context) {
    console.log('预留功能')
    // 实现预留功能逻辑
  }
}
