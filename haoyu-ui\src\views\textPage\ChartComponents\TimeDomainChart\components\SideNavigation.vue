<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="800px"
    title="圆周波形图"
    :close-on-click-modal="false"
    :modal="false"
    :append-to-body="true"
    custom-class="polar-wave-dialog"
    @mousedown.native="handleMouseDown"
  >
    <div class="polar-container">
      <div class="control-panel">
        <div class="panel-section">
          <div class="control-group">
            <div class="control-item">
              <span class="label">转速(RPM):</span>
              <el-input-number
                v-model="rpm"
                :min="1"
                :max="10000"
                :step="rpmStep"
                :precision="rpmPrecision"
                size="small"
                controls-position="right"
                @change="handleRpmChange"
              ></el-input-number>
            </div>
            <div class="control-item">
              <span class="label">周期(s):</span>
              <el-input-number
                v-model="period"
                :min="0.001"
                :max="10"
                :step="periodStep"
                :precision="currentPrecision"
                size="small"
                controls-position="right"
                @change="handlePeriodChange"
              ></el-input-number>
            </div>
            <div class="control-item step-control">
              <span class="label-small">步长:</span>
              <el-input-number
                v-model="step"
                :min="initstep"
                :max="1"
                :step="stepStep"
                :precision="stepPrecision"
                size="small"
                controls-position="right"
                @change="handleStepChange"
              ></el-input-number>
            </div>
          </div>
        </div>

        <div class="panel-section time-section">
          <div class="time-control-header">
            <div class="time-range">
              <span class="label">时间范围(s):</span>
              <div class="range-inputs">
                <el-input-number
                  v-model="startTime"
                  :min="0"
                  :max="finnalData"
                  :step="step"
                  :precision="currentPrecision"
                  size="small"
                  controls-position="right"
                  @change="handleTimeChange"
                ></el-input-number>
                <span class="separator">~</span>
                <el-input-number
                  v-model="endTime"
                  :min="startTime"
                  :max="finnalData"
                  :step="step"
                  :precision="endTimePrecision"
                  size="small"
                  controls-position="right"
                  @change="handleTimeChange"
                ></el-input-number>
              </div>
            </div>
            <div class="button-group">
              <el-button
                type="primary"
                class="play-button"
                :class="{ 'is-playing': isPlaying }"
                @click.stop="toggleAutoPlay"
              >
                <i :class="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
                {{ isPlaying ? '暂停' : '播放' }}
              </el-button>
              <el-button
                type="primary"
                class="sync-button"
                title="同步A2-A1差值作为周期"
                @click="syncPeriod"
              >
                <i class="el-icon-refresh"></i>
                同步周期
              </el-button>
            </div>
          </div>
          <div class="slider-wrapper">
            <el-slider
              v-model="timeRange"
              range
              :min="0"
              :max="finnalData"
              :step="initstep"
              @input="handleSliderChange"
            >
              <template #default="{ value }">
                <div class="slider-tooltip">{{ value.toFixed(5) }}s</div>
              </template>
            </el-slider>
          </div>
        </div>
      </div>
      <div ref="polarChart" class="polar-chart"></div>
    </div>
  </el-dialog>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'PolarWaveDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    waveData: {
      type: Array,
      default: () => []
    },
    freqDiff: {
      type: Number,
      default: 0.020
    }
  },

  data() {
    return {
      dialogVisible: false,
      rpm: 3000,
      displayTime: 0.02,
      chartInstance: null,
      period: this.freqDiff || 0.020,
      periodStep: 0.001,
      currentPrecision: 4,
      endTimePrecision: 4,
      rpmPrecision: 3,
      stepPrecision: 4,
      frequency: 0,
      step: 0,
      stepStep: 0.001,
      initstep: 0,
      initialPeriodPrecision: null,
      initialRpmPrecision: null,
      initialStepPrecision: null,
      pointsPerCircle: 0,
      finnalData: [],
      isDragging: false,
      startX: 0,
      startY: 0,
      offsetX: 0,
      offsetY: 0,
      rpmStep: 0.1,
      isPlaying: false,
      startTime: 0,
      endTime: 0.02,
      timeRange: [0, 0.02],
      updateTimer: null,
    };
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val;
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
      if (val) {
        this.$nextTick(() => {
          this.initChart();
          this.updateChart();
          if (this.freqDiff && this.freqDiff > 0) {
            this.handlePeriodChange(this.freqDiff);
          }
        });
      } else {
        this.stopAutoPlay();
        if (this.chartInstance) {
          this.chartInstance.dispose();
          this.chartInstance = null;
        }
        this.initialPeriodPrecision = null;
        this.initialRpmPrecision = null;
        this.initialStepPrecision = null;
      }
    },
    waveData: {
      handler() {
        if (this.dialogVisible && this.chartInstance) {
          this.updateChart();
        }
      },
      deep: true
    }
  },

  methods: {
    handlePeriodChange(value) {
      this.period = value;

      if (this.initialPeriodPrecision === null) {
        this.initialPeriodPrecision = value.toString().includes('.') ?
          value.toString().split('.')[1].length : 0;
        this.periodStep = Math.pow(10, -this.initialPeriodPrecision);
        this.currentPrecision = this.initialPeriodPrecision;
      }

      this.frequency = 1 / value;
      this.rpm = this.frequency * 60;
      this.updateChart();
    },

    handleRpmChange(value) {
      this.rpm = value;

      if (this.initialRpmPrecision === null) {
        this.initialRpmPrecision = value.toString().includes('.') ?
          value.toString().split('.')[1].length : 0;
        this.rpmStep = Math.pow(10, -this.initialRpmPrecision);
        this.rpmPrecision = this.initialRpmPrecision;
      }

      this.frequency = this.rpm / 60;
      this.period = 1 / this.frequency;
      this.updateChart();
    },

    handleTimeChange() {
      this.timeRange = [this.startTime, this.endTime];
      this.displayTime = this.endTime - this.startTime;
      this.updateChart();
    },

    handleSliderChange(value) {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
      }
      this.updateTimer = setTimeout(() => {
        this.startTime = value[0];
        this.endTime = value[1];
        this.displayTime = this.endTime - this.startTime;
        this.updateChart();
      }, 16);
    },

    handleStepChange(value) {
      this.step = value;

      if (this.initialStepPrecision === null) {
        this.initialStepPrecision = value.toString().includes('.') ?
          value.toString().split('.')[1].length : 0;
        this.stepStep = Math.pow(10, -this.initialStepPrecision);
        this.stepPrecision = this.initialStepPrecision;
      }
    },

    calculateFrequency() {
      this.frequency = this.rpm / 60;
      this.period = 1 / this.frequency;
    },

    initChart() {
      this.finnalData = this.waveData[this.waveData.length-1][0];
      let timeStep = 0;
      
      for (let i = 1; i < this.waveData.length; i++) {
        timeStep = this.waveData[i][0] - this.waveData[i-1][0];
        if (timeStep > 0) {
          break;
        }
      }

      this.initstep = timeStep;
      this.step = timeStep;
      this.endTimePrecision = timeStep.toString().split('.')[1]?.length || 0;
      
      this.endTime = Math.min(this.finnalData, 0.02);
      this.timeRange = [this.startTime, this.endTime];

      this.$nextTick(() => {
        const slider = this.$el.querySelector('.el-slider');
        if (slider) {
          const sliderComponent = slider.__vue__;
          if (sliderComponent) {
            sliderComponent.step = timeStep;
            sliderComponent.setValues();
          }
        }
      });

      if (!this.chartInstance) {
        this.chartInstance = echarts.init(this.$refs.polarChart);
      }
      this.calculateFrequency();
    },

    updateChart() {
      if (!this.chartInstance || !this.waveData || this.waveData.length < 2) return;

      const data = this.processData();

      const option = {
        title: {
          text: `转速: ${this.rpm} RPM  周期${this.period}s  频率${this.frequency}Hz`,
          left: 'center',
          top: 10
        },
        polar: {
          radius: '70%'
        },
        angleAxis: {
          type: 'value',
          startAngle: 90,
          min: 0,
          max: 360,
          interval: 60,
          axisLabel: {
            formatter: '{value}°'
          }
        },
        radiusAxis: {
          type: 'value',
          min: Math.min(...this.waveData.map(point => point[1])) * 1.1,
          max: Math.max(...this.waveData.map(point => point[1])) * 1.1
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: (params) => {
            const data = params[0];
            return `角度: ${data.value[1].toFixed(1)}°<br/>幅值: ${data.value[0].toFixed(3)} m/s²`;
          }
        },
        series: [{
          coordinateSystem: 'polar',
          name: '波形',
          type: 'line',
          smooth: true,
          symbol: 'none',
          data: data,
          lineStyle: {
            color: '#4985DF',
            width: 1
          },
          emphasis: {
            focus: 'series'
          },
          animation: false
        }]
      };

      this.chartInstance.setOption(option, true);
    },

    processData() {
      if (!this.waveData || this.waveData.length < 2) return [];

      const processedData = [];

      const startIndex = this.waveData.findIndex(point => point[0] >= this.startTime);
      const endIndex = this.waveData.findIndex(point => point[0] > this.endTime);

      const pointsToShow = endIndex === -1 ?
        this.waveData.length - startIndex :
        endIndex - startIndex;

      for (let i = 0; i < pointsToShow; i++) {
        const point = this.waveData[startIndex + i];
        const time = point[0];
        const amplitude = point[1];

        const angle = ((time / this.period) % 1) * 360;
        processedData.push([amplitude, angle]);
      }

      return processedData;
    },

    toggleAutoPlay() {
      if (this.isPlaying) {
        this.stopAutoPlay();
      } else {
        this.startAutoPlay();
      }
    },

    startAutoPlay() {
      if (this.isPlaying) return;
      this.isPlaying = true;
      this.playInterval = setInterval(() => {
        if (this.endTime + this.step <= this.finnalData) {
          this.endTime += this.step;
          console.log('this.endTime', this.endTime,this.step)
          this.timeRange = [this.startTime, this.endTime];
          this.displayTime = this.endTime - this.startTime;
          this.updateChart();
        } else {
          this.stopAutoPlay();
        }
      }, 100);
    },

    stopAutoPlay() {
      this.isPlaying = false;
      if (this.playInterval) {
        clearInterval(this.playInterval);
        this.playInterval = null;
      }
    },

    handleMouseDown(event) {
      if (event.target.classList.contains('el-dialog__header')) {
        this.isDragging = true;
        this.startX = event.clientX;
        this.startY = event.clientY;
        const dialog = this.$el.querySelector('.el-dialog');

        this.offsetX = parseFloat(dialog.style.left) || 0;
        this.offsetY = parseFloat(dialog.style.top) || 0;

        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);
      }
    },
    handleMouseMove(event) {
      if (this.isDragging) {
        const dialog = this.$el.querySelector('.el-dialog');
        if (!dialog) return;

        const dx = event.clientX - this.startX;
        const dy = event.clientY - this.startY;

        dialog.style.left = `${this.offsetX + dx}px`;
        dialog.style.top = `${this.offsetY + dy}px`;
      }
    },
    handleMouseUp(event) {
      if (this.isDragging) {
        const dialog = this.$el.querySelector('.el-dialog');
      }
      this.isDragging = false;
      document.removeEventListener('mousemove', this.handleMouseMove);
      document.removeEventListener('mouseup', this.handleMouseUp);
    },

    syncPeriod() {
      if (this.freqDiff && this.freqDiff > 0) {
        this.handlePeriodChange(this.freqDiff);
      } else {
        this.$message.warning('当前没有有效的A2-A1差值');
      }
    },

    handleClose() {
      this.initialPeriodPrecision = null;
      this.initialRpmPrecision = null;
      this.initialStepPrecision = null;
      this.dialogVisible = false;
      this.$emit('update:visible', false);
    }

  },

  mounted() {
    if (this.visible) {
      this.initChart();
      this.updateChart();
    }
  },

  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }
  }
};
</script>

<style scoped>
/* 优化对话框整体样式 */
.polar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background-color: #f9f9f9; /* 更柔和的背景颜色 */
  border-radius: 8px; /* 添加圆角 */
}

.control-panel {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
}

.panel-section {
  padding: 16px 20px;
}

.panel-section:first-child {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.left-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-control {
  margin-left: 8px;
}

.label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  min-width: 85px;
}

.label-small {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  min-width: 45px;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.separator {
  color: #909399;
  margin: 0 4px;
  font-weight: 500;
}

.slider-wrapper {
  padding: 0 8px;
}

/* 输入框样式 */
:deep(.el-input-number--small) {
  width: 120px;
}

:deep(.el-input-number--small .el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  border-color: #dcdfe6;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  border-color: #dcdfe6;
}

/* 滑块样式 */
:deep(.el-slider__runway) {
  height: 4px;
  margin: 16px 0;
}

:deep(.el-slider__bar) {
  height: 4px;
  background-color: #409eff;
}

:deep(.el-slider__button) {
  width: 12px;
  height: 12px;
  border: 2px solid #409eff;
  background-color: #fff;
}

:deep(.el-slider__button-wrapper) {
  top: -15px;
}

/* 播放按钮样式 */
.play-button {
  min-width: 88px;
  height: 32px;
  margin-left: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.play-button.is-playing {
  background-color: #67c23a;
  border-color: #67c23a;
}

.play-button i {
  font-size: 14px;
}

.slider-tooltip {
  font-size: 12px;
  padding: 3px 8px;
  background-color: #303133;
  color: #fff;
  border-radius: 4px;
  white-space: nowrap;
}

/* 图表容器样式 */
.polar-chart {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 20px;
  height: 500px;
}

/* 优化对话框样式 */
.polar-wave-dialog {
  position: relative;
  z-index: 3001 !important;
  border-radius: 12px;
  overflow: hidden;
}

.polar-wave-dialog .el-dialog {
  position: absolute;
  margin: 0;
  left: 0;
  top: 0;
}

.polar-wave-dialog .el-dialog__wrapper {
  position: relative;
  z-index: 3001 !important;
}

.polar-wave-dialog .el-dialog__body {
  padding: 0;
}

/* 遮罩层在对话框下方 */
.v-modal {
  z-index: 2000 !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-button:hover {
  background-color: #409eff;
  color: white;
}

/* 图表区域悬停提示优化 */
.polar-chart .echarts-tooltip {
  font-size: 14px;
  color: #fff;
  background-color: rgba(50, 50, 50, 0.85);
  border-radius: 4px;
  padding: 10px;
}

.polar-wave-dialog .el-dialog__header {
  cursor: move;
  user-select: none;
}

.time-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 24px;
}

.panel-section:first-child {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.sync-button {
  min-width: 88px;
  height: 32px;
  margin-left: 8px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.sync-button i {
  font-size: 14px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.play-button,
.sync-button {
  min-width: 88px;
  height: 32px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.play-button i,
.sync-button i {
  font-size: 14px;
}

</style>
