export const getChartButtons = (switchChart) => [
    { title: '趋势波形频谱', iconClass: 'trendWaveformSpectrum-chart', placement: 'bottom', chartType: 'TrendWaveformSpectrumChart', onClick: () => switchChart('TrendWaveformSpectrumChart') },
    // { title: '趋势图', iconClass: 'trend-chart', placement: 'bottom', chartType: 'TrendChart', onClick: () => switchChart('TrendChart') },
    // { title: '时域波形图', iconClass: 'timeDomain-chart', placement: 'bottom', chartType: 'TimeDomainChart', onClick: () => switchChart('TimeDomainChart') },
    // { title: '频谱', iconClass: 'spectrum-chart', placement: 'bottom', chartType: 'SpectrumChart', onClick: () => switchChart('SpectrumChart') },
    { title: '多时域波形图', iconClass: 'multiTimeDomain-chart', placement: 'bottom', chartType: 'MultiTimeDomainChart', onClick: () => switchChart('MultiTimeDomainChart') },
    { title: '多频谱', iconClass: 'multiSpectrum-chart', placement: 'bottom', chartType: 'MultiSpectrumChart', onClick: () => switchChart('MultiSpectrumChart') },
    { title: '倒谱', iconClass: 'cepstrum-chart', placement: 'bottom', chartType: 'CepstrumChart', onClick: () => switchChart('CepstrumChart') },
    { title: '通道温度', iconClass: 'channelTemperature-chart', placement: 'bottom', chartType: 'ChannelTemperatureChart', onClick: () => switchChart('ChannelTemperatureChart') },
    // { title: '通道日志', iconClass: 'channelLog-chart', placement: 'bottom', chartType: 'ChannelLogChart', onClick: () => switchChart('ChannelLogChart') },
    // { title: '包络谱', iconClass: 'envelopeSpectrum-chart', placement: 'bottom', chartType: 'EnvelopeSpectrumChart', onClick: () => switchChart('EnvelopeSpectrumChart') },
    // { title: '长波形', iconClass: 'longWaveform-chart', placement: 'bottom', chartType: 'LongWaveformChart', onClick: () => switchChart('LongWaveformChart') },
    // { title: '交叉相位', iconClass: 'crossPhase-chart', placement: 'bottom', chartType: 'CrossPhaseChart', onClick: () => switchChart('CrossPhaseChart') },
    { title: '瀑布图', iconClass: 'waterfall-chart', placement: 'bottom', chartType: 'WaterfallChart', onClick: () => switchChart('WaterfallChart') },
    // { title: '时域指标多趋势', iconClass: 'multiTrendTimeDomain-chart', placement: 'bottom', chartType: 'MultiTrendTimeDomainChart', onClick: () => switchChart('MultiTrendTimeDomainChart') },
    // { title: '设备温度多趋势', iconClass: 'multiTrendDeviceTemperature-chart', placement: 'bottom', chartType: 'MultiTrendDeviceTemperatureChart', onClick: () => switchChart('MultiTrendDeviceTemperatureChart') },
    { title: '多参量', iconClass: 'multiParameter-chart', placement: 'bottom', chartType: 'MultiParameterChart', onClick: () => switchChart('MultiParameterChart') },
    // { title: '频谱-包络趋势', iconClass: 'spectrumEnvelopeTrend-chart', placement: 'bottom', chartType: 'SpectrumEnvelopeTrendChart', onClick: () => switchChart('SpectrumEnvelopeTrendChart') },
    { title: '阶次分析', iconClass: 'orderAnalysis-chart', placement: 'bottom', chartType: 'OrderAnalysisChart', onClick: () => switchChart('OrderAnalysisChart') },
    // { title: '工艺趋势', iconClass: 'processTrend-chart', placement: 'bottom', chartType: 'ProcessTrendChart', onClick: () => switchChart('ProcessTrendChart') },
    // { title: '总貌图', iconClass: 'generalPicture-view', placement: 'bottom', chartType: 'GeneralPictureView', onClick: () => switchChart('GeneralPictureView') },
    { title: '报警', iconClass: 'alarm-view', placement: 'bottom', chartType: 'AlarmView', onClick: () => switchChart('AlarmView') },
    // { title: '故障案例', iconClass: 'faultCase-view', placement: 'bottom', chartType: 'FaultCaseView', onClick: () => switchChart('FaultCaseView') },
    { title: '诊断报告', iconClass: 'diagnostic-report', placement: 'bottom', chartType: 'DiagnosticReport', onClick: () => switchChart('DiagnosticReport') },
    // { title: '体检报告', iconClass: 'health-report', placement: 'bottom', chartType: 'HealthReport', onClick: () => switchChart('HealthReport') },
    // { title: '检测模式', iconClass: 'detection-mode', placement: 'bottom', chartType: 'DetectionMode', onClick: () => switchChart('DetectionMode') },
    // { title: '设置', iconClass: 'settings-view', placement: 'bottom', chartType: 'SettingsView', onClick: () => switchChart('SettingsView') },
    { title: '健康度', iconClass: 'healthyIcon', placement: 'bottom', chartType: 'HealthDegree', onClick: () => switchChart('HealthDegree') },
    { title: '数据导出', iconClass: 'download', placement: 'bottom', chartType: 'DataExport', onClick: () => switchChart('DataExport') }
    // { title: '设置', iconClass: 'value-disconnectedData', placement: 'bottom', chartType: 'SettingsView', onClick: () => switchChart('SettingsView') }
]
