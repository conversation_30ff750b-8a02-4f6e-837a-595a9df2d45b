/**
 * 行星轴系计算公式
 * 
 * 符号定义:
 * N1：行星架转速（Hz）
 * N2：行星轮自转转速（相对行星架）（Hz）
 * N2,abs：行星轮绝对转速（相对地面）（Hz）
 * N3：太阳轮转速（Hz）
 * Zs：太阳轮齿数
 * Zp：行星轮齿数
 * Zr：齿圈齿数（内齿圈齿数）
 * p：行星轮个数
 */

/**
 * 工况一：行星架输入（风电工况）- 齿圈固定不动，行星架为输入，太阳轮为输出
 */

/**
 * 计算太阳轮转频（从行星架输入）
 * @param {Number} carrierRPM - 行星架转速 N1（rpm）或太阳轮转速（太阳轮输入时）
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @param {Boolean} isCarrierInput - 是否行星架输入
 * @returns {Number} 太阳轮转频（Hz）
 */
export function sunGearRPMFromSun(carrierRPM, sunTeeth, ringTeeth, isCarrierInput) {
    // 确保输入参数为数字
    const numCarrierRPM = Number(carrierRPM) || 0;
    const numSunTeeth = Number(sunTeeth) || 1;
    const numRingTeeth = Number(ringTeeth) || 1;

    const carrierFreq = numCarrierRPM / 60; // 转换为Hz

    if (isCarrierInput) {
        // 行星架输入: N3 = (1 + Zr/Zs) * N1
        const ratio = 1 + numRingTeeth / numSunTeeth;
        const sunFreq = ratio * carrierFreq;
        console.log("太阳轮转频计算(行星架输入):", {
            行星架转速RPM: numCarrierRPM,
            行星架转频Hz: carrierFreq,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            传动比系数: ratio,
            计算公式: "N3 = (1 + Zr/Zs) * N1",
            计算过程: `${carrierFreq} * (1 + ${numRingTeeth}/${numSunTeeth}) = ${sunFreq}`,
            结果Hz: sunFreq,
            结果RPM: sunFreq * 60
        });
        return sunFreq;
    } else {
        // 太阳轮输入: 返回输入频率
        console.log("太阳轮转频(太阳轮输入):", {
            输入转速RPM: numCarrierRPM,
            输入转频Hz: carrierFreq,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            计算公式: "N3 = N3 (输入频率)",
            结果Hz: carrierFreq,
            结果RPM: numCarrierRPM
        });
        return carrierFreq;
    }
}

/**
 * 计算行星轮转频（相对行星架）
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @param {Number} planetTeeth - 行星轮齿数 Zp
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} carrierRPM - 行星架转速 N1（rpm）
 * @param {Boolean} isCarrierInput - 是否行星架输入
 * @returns {Number} 行星轮转频（Hz）
 */
export function planetGearRPMFromRing(ringTeeth, planetTeeth, sunTeeth, carrierRPM, isCarrierInput) {
    // 确保输入参数为数字
    const numRingTeeth = Number(ringTeeth) || 1;
    const numPlanetTeeth = Number(planetTeeth) || 1;
    const numSunTeeth = Number(sunTeeth) || 1;
    const numCarrierRPM = Number(carrierRPM) || 0;

    const carrierFreq = numCarrierRPM / 60; // 转换为Hz

    if (isCarrierInput) {
        // 行星架输入: N2 = (Zr/Zp) * N1
        const planetFreq = (numRingTeeth / numPlanetTeeth) * carrierFreq;
        console.log("行星轮转频计算(行星架输入):", {
            行星架转速RPM: numCarrierRPM,
            行星架转频Hz: carrierFreq,
            内齿圈齿数: numRingTeeth,
            行星轮齿数: numPlanetTeeth,
            计算公式: "N2 = (Zr/Zp) * N1",
            计算过程: `${carrierFreq} * (${numRingTeeth}/${numPlanetTeeth}) = ${planetFreq}`,
            结果Hz: planetFreq,
            结果RPM: planetFreq * 60
        });
        return planetFreq;
    } else {
        // 太阳轮输入: N2 = (Zr/Zp) * N1，N1 = Zs/(Zs+Zr) * N3
        const sunFreq = numCarrierRPM / 60; // 传入的是太阳轮转速
        const carrierFreqSun = (numSunTeeth / (numRingTeeth + numSunTeeth)) * sunFreq;
        const planetFreq = (numRingTeeth / numPlanetTeeth) * carrierFreqSun;
        console.log("行星轮转频计算(太阳轮输入):", {
            太阳轮转速RPM: numCarrierRPM,
            太阳轮转频Hz: sunFreq,
            行星架转频Hz: carrierFreqSun,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            行星轮齿数: numPlanetTeeth,
            计算公式: "N2 = (Zr/Zp) * [Zs/(Zs+Zr)] * N3",
            计算过程: `(${numRingTeeth}/${numPlanetTeeth}) * (${numSunTeeth}/(${numSunTeeth}+${numRingTeeth})) * ${sunFreq} = ${planetFreq}`,
            结果Hz: planetFreq,
            结果RPM: planetFreq * 60
        });
        return planetFreq;
    }
}

/**
 * 计算行星轮绝对转频（相对地面）
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @param {Number} planetTeeth - 行星轮齿数 Zp
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} inputRPM - 输入转速（rpm）
 * @param {Boolean} isCarrierInput - 是否行星架输入
 * @returns {Number} 行星轮绝对转频（Hz）
 */
export function absoluteGearRPMFromRing(ringTeeth, planetTeeth, sunTeeth, inputRPM, isCarrierInput) {
    // 确保输入参数为数字
    const numRingTeeth = Number(ringTeeth) || 1;
    const numPlanetTeeth = Number(planetTeeth) || 1;
    const numSunTeeth = Number(sunTeeth) || 1;
    const numInputRPM = Number(inputRPM) || 0;

    const inputFreq = numInputRPM / 60; // 转换为Hz

    if (isCarrierInput) {
        // 行星架输入: N2,abs = (1 + Zr/Zp) * N1
        const absoluteFreq = (1 + numRingTeeth / numPlanetTeeth) * inputFreq;
        console.log("行星轮绝对转频计算(行星架输入):", {
            行星架转速RPM: numInputRPM,
            行星架转频Hz: inputFreq,
            内齿圈齿数: numRingTeeth,
            行星轮齿数: numPlanetTeeth,
            计算公式: "N2,abs = (1 + Zr/Zp) * N1",
            计算过程: `${inputFreq} * (1 + ${numRingTeeth}/${numPlanetTeeth}) = ${absoluteFreq}`,
            结果Hz: absoluteFreq,
            结果RPM: absoluteFreq * 60
        });
        return absoluteFreq;
    } else {
        // 太阳轮输入: N2,abs = (1 + Zr/Zp) * N1，N1 = Zs/(Zs+Zr) * N3
        const sunFreq = inputFreq;
        const carrierFreq = (numSunTeeth / (numRingTeeth + numSunTeeth)) * sunFreq;
        const absoluteFreq = (1 + numRingTeeth / numPlanetTeeth) * carrierFreq;
        console.log("行星轮绝对转频计算(太阳轮输入):", {
            太阳轮转速RPM: numInputRPM,
            太阳轮转频Hz: sunFreq,
            行星架转频Hz: carrierFreq,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            行星轮齿数: numPlanetTeeth,
            计算公式: "N2,abs = (1 + Zr/Zp) * [Zs/(Zs+Zr)] * N3",
            计算过程: `(1 + ${numRingTeeth}/${numPlanetTeeth}) * (${numSunTeeth}/(${numSunTeeth}+${numRingTeeth})) * ${sunFreq} = ${absoluteFreq}`,
            结果Hz: absoluteFreq,
            结果RPM: absoluteFreq * 60
        });
        return absoluteFreq;
    }
}

/**
 * 计算行星架转频（从太阳轮输入）
 * @param {Number} sunRPM - 太阳轮转速（rpm）
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @param {Boolean} isCarrierInput - 是否行星架输入
 * @returns {Number} 行星架转频（Hz）
 */
export function planetCarrierRPMFromSun(sunRPM, sunTeeth, ringTeeth, isCarrierInput) {
    // 确保输入参数为数字
    const numSunRPM = Number(sunRPM) || 0;
    const numSunTeeth = Number(sunTeeth) || 1;
    const numRingTeeth = Number(ringTeeth) || 1;

    const sunFreq = numSunRPM / 60; // 转换为Hz

    if (isCarrierInput) {
        // 行星架输入: 返回输入频率
        console.log("行星架转频(行星架输入):", {
            输入转速RPM: numSunRPM,
            输入转频Hz: sunFreq,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            计算公式: "N1 = N1 (输入频率)",
            结果Hz: sunFreq,
            结果RPM: numSunRPM
        });
        return sunFreq;
    } else {
        // 太阳轮输入: N1 = Zs/(Zs+Zr) * N3
        const ratio = numSunTeeth / (numSunTeeth + numRingTeeth);
        const carrierFreq = ratio * sunFreq;
        console.log("行星架转频(太阳轮输入):", {
            太阳轮转速RPM: numSunRPM,
            太阳轮转频Hz: sunFreq,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            传动比系数: ratio,
            计算公式: "N1 = Zs/(Zs+Zr) * N3",
            计算过程: `${sunFreq} * (${numSunTeeth}/(${numSunTeeth}+${numRingTeeth})) = ${carrierFreq}`,
            结果Hz: carrierFreq,
            结果RPM: carrierFreq * 60
        });
        return carrierFreq;
    }
}

/**
 * 计算齿圈故障通过频率
 * @param {Number} carrierRPM - 行星架转速（rpm）
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} planetTeeth - 行星轮齿数 Zp
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @param {Boolean} isCarrierInput - 是否行星架输入
 * @param {Number} planetCount - 行星轮个数 p
 * @returns {Number} 齿圈故障通过频率（Hz）
 */
export function ringFailureFrequency(carrierRPM, sunTeeth, planetTeeth, ringTeeth, isCarrierInput, planetCount = 4) {
    // 确保输入参数为数字
    const numCarrierRPM = Number(carrierRPM) || 0;
    const numSunTeeth = Number(sunTeeth) || 1;
    const numPlanetTeeth = Number(planetTeeth) || 1;
    const numRingTeeth = Number(ringTeeth) || 1;
    const numPlanetCount = Number(planetCount) || 4;

    let carrierFreq;

    if (isCarrierInput) {
        // 行星架输入: ffault = p * N1
        carrierFreq = numCarrierRPM / 60; // 转换为Hz
        const faultFreq = numPlanetCount * carrierFreq;
        console.log("齿圈故障通过频率计算(行星架输入):", {
            行星架转速RPM: numCarrierRPM,
            行星架转频Hz: carrierFreq,
            行星轮数量: numPlanetCount,
            计算公式: "f_fault = p * N1",
            计算过程: `${numPlanetCount} * ${carrierFreq} = ${faultFreq}`,
            结果Hz: faultFreq
        });
        return faultFreq;
    } else {
        // 太阳轮输入: ffault = p * N1，N1 = Zs/(Zs+Zr) * N3
        const sunFreq = numCarrierRPM / 60; // 传入的是太阳轮转速
        carrierFreq = (numSunTeeth / (numRingTeeth + numSunTeeth)) * sunFreq;
        const faultFreq = numPlanetCount * carrierFreq;
        console.log("齿圈故障通过频率计算(太阳轮输入):", {
            太阳轮转速RPM: numCarrierRPM,
            太阳轮转频Hz: sunFreq,
            行星架转频Hz: carrierFreq,
            行星轮数量: numPlanetCount,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            计算公式: "f_fault = p * [Zs/(Zs+Zr)] * N3",
            计算过程: `${numPlanetCount} * (${numSunTeeth}/(${numSunTeeth}+${numRingTeeth})) * ${sunFreq} = ${faultFreq}`,
            结果Hz: faultFreq
        });
        return faultFreq;
    }
}

/**
 * 计算啮合频率
 * @param {Number} inputRPM - 输入转速（rpm）
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @param {Number} planetCount - 行星轮个数 p
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Boolean} isCarrierInput - 是否行星架输入
 * @returns {Number} 啮合频率（Hz）
 */
export function callingFrequency(inputRPM, ringTeeth, planetCount, sunTeeth, isCarrierInput) {
    // 确保输入参数为数字
    const numInputRPM = Number(inputRPM) || 0;
    const numRingTeeth = Number(ringTeeth) || 1;
    const numPlanetCount = Number(planetCount) || 4;
    const numSunTeeth = Number(sunTeeth) || 1;

    const inputFreq = numInputRPM / 60; // 转换为Hz

    if (isCarrierInput) {
        // 行星架输入: fmesh = Zr * N1
        const meshFreq = numRingTeeth * inputFreq;
        console.log("啮合频率计算(行星架输入):", {
            行星架转速RPM: numInputRPM,
            行星架转频Hz: inputFreq,
            内齿圈齿数: numRingTeeth,
            计算公式: "fmesh = Zr * N1",
            计算过程: `${numRingTeeth} * ${inputFreq} = ${meshFreq}`,
            结果Hz: meshFreq
        });
        return meshFreq;
    } else {
        // 太阳轮输入: fmesh = Zr * N1，N1 = Zs/(Zs+Zr) * N3
        const sunFreq = inputFreq;
        const carrierFreq = (numSunTeeth / (numRingTeeth + numSunTeeth)) * sunFreq;
        const meshFreq = numRingTeeth * carrierFreq;
        console.log("啮合频率计算(太阳轮输入):", {
            太阳轮转速RPM: numInputRPM,
            太阳轮转频Hz: sunFreq,
            太阳轮齿数: numSunTeeth,
            内齿圈齿数: numRingTeeth,
            行星架转频Hz: carrierFreq,
            计算公式: "fmesh = Zr * [Zs/(Zs+Zr)] * N3",
            计算过程: `${numRingTeeth} * (${numSunTeeth}/(${numSunTeeth}+${numRingTeeth})) * ${sunFreq} = ${meshFreq}`,
            结果Hz: meshFreq
        });
        return meshFreq;
    }
}

/**
 * 计算行星轮相对行星架的转频
 * @param {Number} planetRPM - 行星轮绝对转速（rpm）
 * @param {Number} carrierRPM - 行星架转速（rpm）
 * @returns {Number} 行星轮相对行星架的转频（Hz）
 */
export function planetGearRPM3(planetRPM, carrierRPM) {
    const planetFreq = planetRPM / 60; // 转换为Hz
    const carrierFreq = carrierRPM / 60; // 转换为Hz

    // 行星轮相对行星架的转频 = |N2 - N1|
    return Math.abs(planetFreq - carrierFreq);
}

/**
 * 计算行星轮转频（从太阳轮输入）
 * @param {Number} inputRPM - 太阳轮转速（rpm）
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} planetTeeth - 行星轮齿数 Zp
 * @returns {Number} 行星轮转频（Hz）
 */
export function planetGearRPMFromSun(inputRPM, sunTeeth, planetTeeth) {
    // 行星轮转频 = n_s * Z_s / (Z_p * 60)
    return (inputRPM * sunTeeth) / (planetTeeth * 60);
}

/**
 * 计算齿圈故障频率（从太阳轮输入）
 * @param {Number} inputRPM - 太阳轮转速（rpm）
 * @param {Number} sunTeeth - 太阳轮齿数 Zs
 * @param {Number} planetCount - 行星轮个数 p
 * @param {Number} ringTeeth - 齿圈齿数 Zr
 * @returns {Number} 齿圈故障频率（Hz）
 */
export function ringGearFailureFrequency(inputRPM, sunTeeth, planetCount, ringTeeth) {
    // 确保输入参数为数字
    const numInputRPM = Number(inputRPM) || 0;
    const numSunTeeth = Number(sunTeeth) || 1;
    const numPlanetCount = Number(planetCount) || 4;
    const numRingTeeth = Number(ringTeeth) || 1;

    // 齿圈故障频率 = n_s * Z_r / (Z_s * N * 60)
    const failureFreq = (numInputRPM * numRingTeeth) / (numSunTeeth * numPlanetCount * 60);
    console.log("齿圈故障频率计算(太阳轮输入-旧方法):", {
        太阳轮转速RPM: numInputRPM,
        太阳轮齿数: numSunTeeth,
        内齿圈齿数: numRingTeeth,
        行星轮数量: numPlanetCount,
        计算公式: "f_fault = n_s * Z_r / (Z_s * N * 60)",
        计算过程: `${numInputRPM} * ${numRingTeeth} / (${numSunTeeth} * ${numPlanetCount} * 60) = ${failureFreq}`,
        结果Hz: failureFreq
    });
    return failureFreq;
}