accelerate==0.34.2
aiofiles==23.2.1
altair==5.4.1
amqp==5.2.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.4.0
appdirs==1.4.4
attrs==24.2.0
axios==0.4.0
billiard==4.2.0
blinker==1.8.2
cachetools==5.5.0
celery==5.4.0
certifi==2024.8.30
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.3.0
cycler==0.12.1
einops==0.7.0
exceptiongroup==1.2.2
fastapi==0.115.0
ffmpy==0.4.0
filelock==3.13.1
Flask==3.0.3
flatbuffers==24.3.25
fonttools==4.53.1
fsspec==2024.2.0
gitdb==4.0.11
GitPython==3.1.43
glcontext==2.5.0
gradio==4.44.0
gradio_client==1.3.0
h11==0.14.0
httpcore==1.0.5
httpx==0.27.2
huggingface-hub==0.25.0
humanfriendly==10.0
idna==3.8
imageio==2.35.1
imageio-ffmpeg==0.5.1
importlib_resources==6.4.5
itsdangerous==2.2.0
Jinja2==3.1.3
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
kiwisolver==1.4.7
kombu==5.4.1
lazy_loader==0.4
llvmlite==0.43.0
lxml==5.3.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.9.2
mdurl==0.1.2
moderngl==5.10.0
mpmath==1.3.0
narwhals==1.7.0
networkx==3.2.1
nptyping==2.5.0
numba==0.60.0
numpy==1.26.3
omegaconf==2.3.0
onnxruntime==1.19.2
opencv-python-headless==*********
orjson==3.10.7
packaging==24.1
pandas==2.2.2
peft==0.12.0
Pillow==10.1.0
platformdirs==4.3.2
pooch==1.8.2
progress==1.6
prompt_toolkit==3.0.47
protobuf==5.28.1
psutil==6.0.0
pyarrow==17.0.0
pybind11==2.13.5
pydantic==2.9.1
pydantic_core==2.23.3
pydeck==0.9.1
pydub==0.25.1
Pygments==2.18.0
PyMatting==1.1.12
PyMySQL==1.1.1
pyparsing==3.1.4
pyreadline3==3.4.3
pyshark==0.6
python-dateutil==2.9.0.post0
python-multipart==0.0.9
pytz==2024.2
PyYAML==6.0.2
referencing==0.35.1
regex==2024.9.11
rembg==2.0.59
requests==2.32.3
rich==13.8.1
rpds-py==0.20.0
ruff==0.6.5
safetensors==0.4.5
scapy==2.6.1
scikit-image==0.24.0
scikit-learn==1.5.2
scipy==1.14.1
semantic-version==2.10.0
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
starlette==0.38.5
streamlit==1.38.0
sympy==1.12
tenacity==8.5.0
termcolor==2.5.0
threadpoolctl==3.5.0
tifffile==2024.8.30
tokenizers==0.19.1
toml==0.10.2
tomlkit==0.12.0
torch==2.4.0+cu124
torchaudio==2.4.0+cu124
torchmcubes @ git+https://github.com/tatsy/torchmcubes.git@cb81cddece46a8a126b08f7fbb9742f8605eefab
torchvision==0.19.0+cu124
tornado==6.4.1
tqdm==4.66.5
transformers==4.44.2
trimesh==4.0.5
typer==0.12.5
typing_extensions==4.12.2
tzdata==2024.1
urllib3==2.2.2
uuid==1.30
uvicorn==0.30.6
vine==5.1.0
watchdog==4.0.2
wcwidth==0.2.13
websockets==11.0.3
Werkzeug==3.0.4
xatlas==0.0.9
