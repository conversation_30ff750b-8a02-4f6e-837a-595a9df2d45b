<template>
    <div>
      <div ref="objViewer" class="obj-viewer">
        <!-- 修改loading显示，移除进度条 -->

        <!-- 添加操作说明按钮 -->
        <div class="operation-guide">
          <el-popover
            placement="right"
            width="300"
            trigger="hover">
            <div class="operation-tips">
              <h4>操作说明：</h4>
              <div class="tips-section">
                <h5>视角控制：</h5>
                <ul>
                  <li>鼠标左键：旋转视角</li>
                  <li>鼠标右键：平移视角</li>
                  <li>鼠标滚轮：缩放视角</li>
                </ul>
              </div>
              <div class="tips-section">
                <h5>浮框操作：</h5>
                <ul>
                  <li>拖拽：移动浮框位置</li>
                  <li>悬停：高亮对应模型</li>
                  <li>点击测点：查看测点数据</li>
                </ul>
              </div>
              <div class="tips-section">
                <h5>信号绑定：</h5>
                <ul>
                  <li>点击浮框内的信号：查看详细数据</li>
                  <li>点击信号名称：跳转到趋势图</li>
                  <li>点击频谱范围：查看频谱分析</li>
                </ul>
              </div>
              <div class="tips-section">
                <h5>快捷操作：</h5>
                <ul>
                  <li>双击空白处：重置视角</li>
                  <li>Ctrl + 鼠标滚轮：快速缩放</li>
                  <li>Shift + 拖拽：精确移动</li>
                </ul>
              </div>
            </div>
            <el-button slot="reference" type="text" icon="el-icon-question" class="guide-button">
              操作说明
            </el-button>
          </el-popover>
        </div>

        <!-- 修改浮框容器，基于绑定关系渲染 -->
        <div v-for="(match, index) in modelPointMatches" :key="match.id"
             class="model-tooltip"
             :style="getTooltipStyle(match)"
             v-show="match.isShow"
             @mousedown="handleBoxMouseDown($event, match)"
             @mouseenter="highlightModel(getModelByName(match.modelName))"
             @mouseleave="unhighlightModel(getModelByName(match.modelName))"
             :data-match-id="match.id">
          <div class="tooltip-content">
            <div class="tooltip-header">{{ match.pointName }}</div>
            <div v-if="getMatchedPoint(match)" class="measure-definitions">
              <div v-for="def in getPointDefinitions(match)"
                   :key="def.id"
                   class="definition-item"
                   @click="handleDefinitionSingleClick(def, match)"
                   @dblclick="handleDefinitionDoubleClick(def, match)">
                {{ def.name }}{{ def.lowerLimitFrequency && def.upperLimitFrequency ?
                  `(${def.lowerLimitFrequency}-${def.upperLimitFrequency}HZ)` : '' }}
                {{ def.value }} {{ getUnitText(def.timeSignalUnitId) }}
              </div>
            </div>
          </div>
        </div>
        <!-- 添加点和连线容器，基于绑定关系渲染 -->
        <div v-for="(match, index) in modelPointMatches" :key="'point-'+match.id"
             :data-match-id="match.id"
             class="model-point"
             :style="getPointStyle(getModelByName(match.modelName))"
             v-show="match.isShow"></div>
        <svg class="connection-lines" :style="getSvgStyle()">
          <path v-for="(match, index) in modelPointMatches"
                :key="'line-'+match.id"
                :data-match-id="match.id"
                :d="getConnectionPath(getModelByName(match.modelName), match)"
                class="connection-path"
                v-show="match.isShow" />
        </svg>
      </div>
      <!-- 添加匹配弹窗组件 -->
      <model-point-match
        :visible.sync="matchDialogVisible"
        :device-id="data.id"
        @confirm="handleMatchConfirm"
      />
    </div>
  </template>

  <script>
  import * as THREE from 'three';
  import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
  import { GetOrNewDateDeviceRelation, DownloadImgObjList,getModelPosition, GetBackgroundPhoto,getAllModelPosition } from '@/api/haoyu-system/web3d_api/web_3dapi.js';
  import ModelPointMatch from './components/ModelPointMatch.vue';
  import { getDeviceModelIdAndName, getDeviceMeasurePoint ,getDeviceWithPoint, CreateDeviceWithPoint, UpdateDeviceWithPoint, deleteDeviceWithPoint } from './api/Dalog.js';
  import axios from 'axios';

  export default {
    name: 'ObjViewer',
    components: {
      ModelPointMatch
    },
    props: {
      data: {
        type: Object,
        default: null
      },
      pointList: {
        type: Array,
        default: () => []
      }
    },

    data() {
      return {
        scene: null,
        renderer: null,
        camera: null,
        controls: null,
        models: [],
        tooltipPositions: {},
        containerWidth: 0,
        containerHeight: 0,
        matchDialogVisible: false,
        modelPointMatches: [],
        pointStatuses: {},
        draggingBox: null,
        dragOffset: { x: 0, y: 0 },
        highlightedModel: null,
        originalScale: new THREE.Vector3(1, 1, 1),
        isAnimating: false,
        backgroundColor: '#ffffff',
        groundColor: '#ffffff',
        backgroundTexture: null,
        groundTexture: null,
        backgroundMesh: null,
        deviceId: null,
        showProgress: false,
        totalProgress: 0,
        modelLoadingCount: 0,
        totalModelsCount: 0,
        isInitialized: false,
        is2DView: false,
        previousRoute: null,
        animationRunning: false,
        animationFrameId: null,
        abortControllers: [],
        isDestroyed: false
      };
    },

    created() {
      this.previousRoute = this.$route.path;
      
      // 使用$router.beforeEach可能会导致全局路由钩子，改用组件内的导航守卫
      this._routeGuard = this.$watch('$route', (to, from) => {
        // 当路由变化时取消所有下载
        console.log('路由变化，取消所有下载请求');
        this.cancelAllDownloads();
      });
    },

    mounted() {
      console.log('objView mounted, data:', this.data);
      this.deviceId = this.data.id;

      // 检查是否从3D编辑页面返回
      const fromPJ = this.previousRoute && this.previousRoute.includes('objview_pj');
      if (fromPJ) {
        console.log('从3D编辑页面返回，切换到2D视图');
        this.is2DView = true;
      }

      window.addEventListener('mousemove', this.handleMouseMove);
      window.addEventListener('mouseup', this.handleMouseUp);

      // 不管是否是2D视图，都先加载模型点位置数据
      this.loadingModelPointMatches();
      
      // 如果不是2D视图，则初始化3D场景和加载模型
      if (!this.is2DView) {
        this.$nextTick(() => {
          this.initObjViewer();
          this.loadDeviceModels();
        });
      }
    },
    
    watch: {
      data: {
        handler(newVal) {
          console.log('objView data changed:', newVal)
        },
        deep: true
      },
      '$parent.show3D': {
        immediate: true,
        handler(newVal) {
          console.log('Parent show3D changed:', newVal);
          if (newVal === true && this.is2DView === true) {
            this.is2DView = false;
            this.$nextTick(async () => {
              if (!this.scene) {
                await this.initObjViewer();
              }
              if (this.models.length === 0) {
                await this.loadDeviceModels();
              }
            });
          }
        }
      }
    },

    beforeDestroy() {
      // 标记组件为已销毁状态
      this.isDestroyed = true;
      
      // 取消所有正在进行的下载
      this.cancelAllDownloads();
      
      // 移除事件监听器和路由监听
      window.removeEventListener('mousemove', this.handleMouseMove);
      window.removeEventListener('mouseup', this.handleMouseUp);
      
      // 移除路由监听
      if (this._routeGuard) {
        this._routeGuard();
        this._routeGuard = null;
      }
      
      // 组件销毁前清理资源
      this.cleanupResources();
    },

    methods: {
      cancelAllDownloads() {
        if (this.abortControllers && this.abortControllers.length > 0) {
          console.log(`正在取消 ${this.abortControllers.length} 个下载请求`);
          this.abortControllers.forEach(controller => {
            if (controller) {
              try {
                controller.abort();
                console.log('已取消一个下载请求');
              } catch (error) {
                console.error('取消请求失败:', error);
              }
            }
          });
          this.abortControllers = [];
        }
      },
      
      async loadDeviceModels() {
        if (!this.data || !this.data.id) {
          console.warn('No valid data provided:', this.data);
          return;
        }

        if (this.models.length > 0) {
          console.log('Models already loaded');
          return;
        }

        if (!this.scene) {
          console.warn('Scene not initialized, initializing...');
          await this.initObjViewer();
        }

        console.log('Starting model loading...');

        await this.$nextTick();

        try {
          if (this.isDestroyed) {
            console.log('组件已销毁，停止加载模型');
            return;
          }
          
          const response = await GetOrNewDateDeviceRelation(this.data.id);
          if (!response || !response.data) {
            console.warn('No model data received');
            this.$message.warning('未获取到模型数据，可能未新建3D模型，请前往指定页面新建');
            return;
          }

          const modelList = response.data;
          if (!modelList || Object.keys(modelList).length === 0 || Object.keys(modelList).length === 1 && modelList.error) {
            console.warn('No models to load');
            this.$message.warning('未获取到模型数据，可能未新建3D模型，请前往指定页面新建');
            return;
          }

          const validKeys = Object.keys(modelList).filter(key => key !== 'error');
          if (validKeys.length === 0) {
            this.$message.warning('未获取到模型数据，可能未新建3D模型，请前往指定页面新建');
            return;
          }

          this.totalModelsCount = validKeys.length;
          console.log('Total models to load:', this.totalModelsCount);

          const loadPromises = [];
          let ids = '';
          for (const key in modelList) {
            if(key === 'error') continue;
            ids += key + ',';
          }

          if (!ids) {
            this.$message.warning('未获取到模型数据，可能未新建3D模型，请前往指定页面新建');
            return;
          }

          loadPromises.push(this.loadAllModelById(ids));

          await Promise.all(loadPromises);
          console.log('All models loaded');

          setTimeout(() => {
            this.isInitialized = true;
          }, 500);
        } catch (error) {
          console.error('Error loading device models:', error);
          this.$message.error('加载模型失败');
        }
      },
      
      async loadAllModelById(ids) {
        return new Promise((resolve, reject) => {
          if (this.isDestroyed) {
            // console.log('组件已销毁，终止模型加载');
            resolve();
            return;
          }
          
          getAllModelPosition(ids).then(response => {
            if (this.isDestroyed) {
              console.log('组件已销毁，终止模型位置加载');
              resolve();
              return;
            }
            
            const loadPromises = [];
            
            for(const item of response.rows) {
              const controller = new AbortController();
              this.abortControllers.push(controller);
              
              const loadPromise = this.loadModelWithAbortController(item, controller.signal)
                .then(result => {
                  const index = this.abortControllers.indexOf(controller);
                  if (index !== -1) {
                    this.abortControllers.splice(index, 1);
                  }
                  return result;
                })
                .catch(error => {
                  const index = this.abortControllers.indexOf(controller);
                  if (index !== -1) {
                    this.abortControllers.splice(index, 1);
                  }
                  
                  if (error.name !== 'AbortError') {
                    console.error('下载模型失败:', error);
                  } else {
                    console.log('模型下载已取消');
                  }
                  return null;
                });
              
              loadPromises.push(loadPromise);
            }
            
            Promise.all(loadPromises)
              .then(() => resolve())
              .catch(reject);
          }).catch(error => {
            console.error('获取模型位置信息失败:', error);
            reject(error);
          });
        });
      },
      
      loadModelWithAbortController(item, signal) {
        return new Promise((resolve, reject) => {
          if (this.isDestroyed) {
            reject(new Error('组件已销毁'));
            return;
          }
          
          DownloadImgObjList(item.id, signal)
            .then(response => {
              if (this.isDestroyed) {
                reject(new Error('组件已销毁'));
                return;
              }
              
              const blob = new Blob([response], { type: 'obj/binary' });
              const url = URL.createObjectURL(blob);
              const loader = new OBJLoader();
              
              loader.load(
                url,
                (object) => {
                  if (this.isDestroyed) {
                    URL.revokeObjectURL(url);
                    reject(new Error('组件已销毁'));
                    return;
                  }
                  
                  if (item && item.fileModelInfo) {
                    const modelInfo = item.fileModelInfo[0];

                    const color = modelInfo.color || 'rgba(255,255,255,1)';
                    const rgbaMatch = color.match(/rgba\((\d+),(\d+),(\d+),([\d.]+)\)/);
                    let materialColor, opacity;

                    if (rgbaMatch) {
                      const [_, r, g, b, a] = rgbaMatch;
                      materialColor = new THREE.Color(
                        parseInt(r) / 255,
                        parseInt(g) / 255,
                        parseInt(b) / 255
                      );
                      opacity = parseFloat(a);
                    } else {
                      materialColor = new THREE.Color(0xffffff);
                      opacity = 1;
                    }

                    const material = new THREE.MeshStandardMaterial({
                      color: materialColor,
                      roughness: 0.3,
                      metalness: 0.8,
                      transparent: opacity < 1,
                      opacity: opacity
                    });

                    object.traverse((child) => {
                      if (child.isMesh) {
                        child.material = material.clone();
                        child.castShadow = true;
                        child.receiveShadow = true;
                      }
                    });

                    object.name = modelInfo.name || `模型${this.models.length + 1}`;
                  } else {
                    const material = new THREE.MeshStandardMaterial({
                      color: 0xcccccc,
                      roughness: 0.3,
                      metalness: 0.8,
                      transparent: false,
                      opacity: 1
                    });

                    object.traverse((child) => {
                      if (child.isMesh) {
                        child.material = material.clone();
                        child.castShadow = true;
                        child.receiveShadow = true;
                      }
                    });

                    object.name = `模型${this.models.length + 1}`;
                  }

                  if (!this.isDestroyed && this.scene) {
                    this.scene.add(object);
                    this.models.push(object);
                    this.modelLoadingCount++;
                  }
                  
                  URL.revokeObjectURL(url);
                  resolve();
                },
                (xhr) => {
                  if (this.isDestroyed && signal && !signal.aborted) {
                    signal.abort();
                  }
                },
                (error) => {
                  console.error('Failed to load model:', error);
                  URL.revokeObjectURL(url);
                  reject(error);
                }
              );
            }).catch(error => {
              console.error('下载模型失败:', error);
              reject(error);
            });
        });
      },

      loadModelById(id) {
        return new Promise((resolve, reject) => {
          if (this.isDestroyed) {
            reject(new Error('组件已销毁'));
            return;
          }
          
          const controller = new AbortController();
          this.abortControllers.push(controller);
          
          DownloadImgObjList(id, controller.signal)
            .then(response => {
              const index = this.abortControllers.indexOf(controller);
              if (index !== -1) {
                this.abortControllers.splice(index, 1);
              }
              
              if (this.isDestroyed) {
                reject(new Error('组件已销毁'));
                return;
              }
              
              const blob = new Blob([response], { type: 'obj/binary' });
              const url = URL.createObjectURL(blob);

              getModelPosition(id).then((posResponse) => {
                const loader = new OBJLoader();

                loader.load(
                  url,
                  (object) => {
                    if (this.isDestroyed) {
                      URL.revokeObjectURL(url);
                      reject(new Error('组件已销毁'));
                      return;
                    }
                    
                    if (posResponse && posResponse.rows && posResponse.rows[0] && posResponse.rows[0].fileModelInfo) {
                      const modelInfo = posResponse.rows[0].fileModelInfo[0];

                      const color = modelInfo.color || 'rgba(255,255,255,1)';
                      const rgbaMatch = color.match(/rgba\((\d+),(\d+),(\d+),([\d.]+)\)/);
                      let materialColor, opacity;

                      if (rgbaMatch) {
                        const [_, r, g, b, a] = rgbaMatch;
                        materialColor = new THREE.Color(
                          parseInt(r) / 255,
                          parseInt(g) / 255,
                          parseInt(b) / 255
                        );
                        opacity = parseFloat(a);
                      } else {
                        materialColor = new THREE.Color(0xffffff);
                        opacity = 1;
                      }

                      const material = new THREE.MeshStandardMaterial({
                        color: materialColor,
                        roughness: 0.3,
                        metalness: 0.8,
                        transparent: opacity < 1,
                        opacity: opacity
                      });

                      object.traverse((child) => {
                        if (child.isMesh) {
                          child.material = material.clone();
                          child.castShadow = true;
                          child.receiveShadow = true;
                        }
                      });

                      object.name = modelInfo.name || `模型${this.models.length + 1}`;
                    } else {
                      const material = new THREE.MeshStandardMaterial({
                        color: 0xcccccc,
                        roughness: 0.3,
                        metalness: 0.8,
                        transparent: false,
                        opacity: 1
                      });

                      object.traverse((child) => {
                        if (child.isMesh) {
                          child.material = material.clone();
                          child.castShadow = true;
                          child.receiveShadow = true;
                        }
                      });

                      object.name = `模型${this.models.length + 1}`;
                    }

                    if (!this.isDestroyed && this.scene) {
                      this.scene.add(object);
                      this.models.push(object);
                      this.modelLoadingCount++;
                    }
                    
                    URL.revokeObjectURL(url);
                    resolve();
                  },
                  (xhr) => {
                    if (this.isDestroyed && controller && !controller.signal.aborted) {
                      controller.abort();
                    }
                  },
                  (error) => {
                    console.error('Failed to load model:', error);
                    URL.revokeObjectURL(url);
                    reject(error);
                  }
                );
              }).catch(error => {
                const index = this.abortControllers.indexOf(controller);
                if (index !== -1) {
                  this.abortControllers.splice(index, 1);
                }
                console.error('获取模型信息失败:', error);
                reject(error);
              });
            }).catch(error => {
              const index = this.abortControllers.indexOf(controller);
              if (index !== -1) {
                this.abortControllers.splice(index, 1);
              }
              
              if (error.name !== 'AbortError') {
                console.error('下载模型失败:', error);
              } else {
                console.log('模型下载已取消');
              }
              reject(error);
            });
        });
      },

      async loadingModelPointMatches() {
        try {
          const response = await getDeviceWithPoint(this.deviceId);
          if (response && response.code === 200 && response.rows) {
            this.tooltipPositions = {};

            this.modelPointMatches = response.rows.map(row => ({
              modelName: row.modelName,
              pointName: row.measurementName,
              isShow: row.isShow === "1",
              id: row.id,
              modelId: row.modelId,
              measurementId: row.measurementId
            }));

            let hasSetPosition = false;
            for (const row of response.rows) {
              if (row.id && row.location) {
                try {
                  const location = JSON.parse(row.location);
                  if (Array.isArray(location) && location.length >= 2) {
                    this.$set(this.tooltipPositions, row.id, {
                      x: location[0],
                      y: location[1]
                    });
                    hasSetPosition = true;
                  }
                } catch (error) {
                  console.error('解析location失败:', error);
                }
              }
            }

            if (!hasSetPosition) {
              this.modelPointMatches.forEach(match => {
                this.setDefaultPosition(match);
              });
            }

            await this.$nextTick();
            this.$forceUpdate();
          }
        } catch (error) {
          console.error('获取设备绑定情况失败:', error);
        }
      },

      setDefaultPosition(match) {
        if (!match || !match.id) return;

        const index = this.modelPointMatches.findIndex(m => m.id === match.id);
        if (index !== -1) {
          const xSpacing = 100 / (this.modelPointMatches.length + 1);
          this.$set(this.tooltipPositions, match.id, {
            x: xSpacing * (index + 1),
            y: 10 + (index * 5)
          });
        }
      },

      async initObjViewer() {
        const container = this.$refs.objViewer;
        if (!container) {
          console.error('Container element not found');
          return;
        }

        if (this.scene && this.renderer && this.camera) {
          console.log('3D场景已初始化，重新调整尺寸');
          this.containerWidth = container.offsetWidth;
          this.containerHeight = container.offsetHeight;
          this.onWindowResize();
          return;
        }

        this.containerWidth = container.offsetWidth;
        this.containerHeight = container.offsetHeight;

        console.log('Container dimensions:', {
          width: this.containerWidth,
          height: this.containerHeight
        });

        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(this.backgroundColor);

        this.camera = new THREE.PerspectiveCamera(
          75,
          this.containerWidth / this.containerHeight,
          0.1,
          1000
        );
        this.camera.position.set(0, 2, 5);
        this.camera.lookAt(0, 0, 0);

        this.renderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true
        });
        this.renderer.setSize(this.containerWidth, this.containerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        container.appendChild(this.renderer.domElement);

        this.initLightingAndGround();

        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.minDistance = 0;
        this.controls.maxDistance = 80;
        this.controls.maxPolarAngle = Math.PI / 2;
        this.controls.target.set(0, 0, 0);

        const resizeObserver = new ResizeObserver(() => {
          this.containerWidth = container.offsetWidth;
          this.containerHeight = container.offsetHeight;
          this.onWindowResize();
          this.updateAllTooltipPositions();
        });
        resizeObserver.observe(container);

        await this.loadBackgroundSettings();

        if (!this.isInitialized) {
          this.animate();
          this.isInitialized = true;
        }
      },

      updateAllTooltipPositions() {
        if (!this.modelPointMatches) return;

        this.modelPointMatches.forEach(match => {
          if (match.id && this.tooltipPositions[match.id]) {
            const position = this.tooltipPositions[match.id];
            this.$set(this.tooltipPositions, match.id, {
              x: Math.max(50, Math.min(position.x, this.containerWidth - 50)),
              y: Math.max(50, Math.min(position.y, this.containerHeight - 50))
            });
          }
        });
      },

      initLightingAndGround() {
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(5, 10, 7.5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        this.scene.add(directionalLight);

        const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0xffffff, 0.4);
        this.scene.add(hemisphereLight);

        const groundColorStr = this.groundColor;
        let groundOpacity = 1;
        if (groundColorStr.startsWith('rgba')) {
          const matches = groundColorStr.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
          if (matches) {
            groundOpacity = parseFloat(matches[4]);
          }
        }

        if (groundOpacity !== 0) {
          const planeGeometry = new THREE.PlaneGeometry(100, 100);
          const planeMaterial = new THREE.MeshBasicMaterial({
            color: new THREE.Color(this.groundColor),
            side: THREE.DoubleSide,
            transparent: groundOpacity < 1,
            opacity: groundOpacity
          });

          if (this.groundTexture) {
            planeMaterial.map = this.groundTexture;
          }

          const plane = new THREE.Mesh(planeGeometry, planeMaterial);
          plane.rotation.x = -Math.PI / 2;
          plane.position.y = -1;
          plane.receiveShadow = true;
          plane.isGround = true;
          this.scene.add(plane);
        }
      },

      resetScene() {
        if (!this.scene) return;

        while (this.scene.children.length > 0) {
          const object = this.scene.children[0];
          if (!object) continue;

          this.scene.remove(object);
          if (object.geometry) {
            object.geometry.dispose();
          }
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach((material) => {
                if (material) material.dispose();
              });
            } else {
              object.material.dispose();
            }
          }
        }
      },

      onWindowResize() {
        const container = this.$refs.objViewer;
        if (!container) return;

        this.containerWidth = container.offsetWidth;
        this.containerHeight = container.offsetHeight;

        if (this.camera) {
          this.camera.aspect = this.containerWidth / this.containerHeight;
          this.camera.updateProjectionMatrix();
        }

        if (this.renderer) {
          this.renderer.setSize(this.containerWidth, this.containerHeight);
        }

        this.updateAllTooltipPositions();
      },

      getTooltipStyle(match) {
        if (!match) return { display: 'none' };

        const position = this.tooltipPositions[match.id] || {
          x: 50,
          y: 10
        };

        if (this.is2DView) {
          return {
            position: 'absolute',
            left: `${position.x}%`,
            top: `${position.y}%`,
            transform: 'translate(-50%, 0)',
            cursor: 'move'
          };
        }

        return {
          position: 'absolute',
          left: `${position.x}%`,
          top: `${position.y}%`,
          transform: 'translate(-50%, 0)',
          cursor: 'move'
        };
      },

      getPointStyle(model) {
        if (this.is2DView) {
          return {
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: '16px',
            height: '16px',
            transform: 'translate(-50%, -50%)',
            pointerEvents: 'none'
          };
        }

        if (!model || !this.camera) return {};

        const vector = new THREE.Vector3();
        const box = new THREE.Box3().setFromObject(model);
        box.getCenter(vector);
        vector.project(this.camera);

        const x = (vector.x * 0.5 + 0.5) * this.renderer.domElement.clientWidth;
        const y = (-vector.y * 0.5 + 0.5) * this.renderer.domElement.clientHeight;

        return {
          position: 'absolute',
          left: `${x}px`,
          top: `${y}px`,
          width: '16px',
          height: '16px',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
          display: vector.z > 1 ? 'none' : 'block'
        };
      },

      getSvgStyle() {
        return {
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none'
        };
      },

      getConnectionPath(model, match) {
        if (this.is2DView) {
          const boxPosition = this.tooltipPositions[match.id];
          if (!boxPosition) return '';

          const container = this.$refs.objViewer;
          if (!container) return '';

          const rect = container.getBoundingClientRect();
          const startX = (boxPosition.x * rect.width / 100);
          const startY = (boxPosition.y * rect.height / 100) + 80;
          const endX = rect.width / 2;
          const endY = rect.height / 2;

          const middleY = (startY + endY) / 2;
          return `M ${startX} ${startY} L ${startX} ${middleY} L ${endX} ${middleY} L ${endX} ${endY}`;
        }

        if (!model || !this.camera || !match) return '';

        const boxPosition = this.tooltipPositions[match.id];
        if (!boxPosition) return '';

        const container = this.$refs.objViewer;
        if (!container) return '';

        const rect = container.getBoundingClientRect();

        const startX = (boxPosition.x * rect.width / 100);
        const startY = (boxPosition.y * rect.height / 100) + 80;

        const vector = new THREE.Vector3();
        const box = new THREE.Box3().setFromObject(model);
        box.getCenter(vector);
        vector.project(this.camera);

        const endX = (vector.x * 0.5 + 0.5) * rect.width;
        const endY = (-vector.y * 0.5 + 0.5) * rect.height;

        if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
          return '';
        }

        const middleY = (startY + endY) / 2;
        return `M ${startX} ${startY} L ${startX} ${middleY} L ${endX} ${middleY} L ${endX} ${endY}`;
      },

      animate() {
        if (!this.renderer || !this.scene || !this.camera) {
          console.warn('渲染器、场景或相机未初始化，无法启动动画循环');
          return;
        }

        if (!this.animationRunning) {
          this.animationRunning = true;
        } else {
          return;
        }
        
        const animateFrame = () => {
          this.animationFrameId = requestAnimationFrame(animateFrame);
          if (this.controls) {
            this.controls.update();
          }

          if (this.highlightedModel && this.isAnimating) {
            const time = Date.now() * 0.001;
            const scale = 1.05 + Math.sin(time * 3) * 0.02;
            if (this.highlightedModel.scale && this.originalScale) {
              this.highlightedModel.scale.copy(this.originalScale).multiplyScalar(scale);
            }
          }

          if (!this.draggingBox) {
            const points = document.querySelectorAll('.model-point');
            const paths = document.querySelectorAll('.connection-path');

            points.forEach(point => {
              point.style.display = 'none';
            });
            paths.forEach(path => {
              path.style.display = 'none';
            });

            this.modelPointMatches.forEach((match) => {
              if (match.isShow) {
                const point = document.querySelector(`.model-point[data-match-id="${match.id}"]`);
                if (point) {
                  const style = this.getPointStyle(this.getModelByName(match.modelName));
                  Object.assign(point.style, style);
                  point.style.display = style.display;
                }

                const path = document.querySelector(`.connection-path[data-match-id="${match.id}"]`);
                if (path) {
                  const pathData = this.getConnectionPath(this.getModelByName(match.modelName), match);
                  path.setAttribute('d', pathData);
                  path.style.display = pathData ? 'block' : 'none';
                }
              }
            });
          }

          this.renderer.render(this.scene, this.camera);
        };
        
        animateFrame();
      },

      moveModel(direction, axis) {
        if (this.selectedModelIndex !== null) {
          const model = this.models[this.selectedModelIndex];
          const step = this.inputStep || 0.01;

          switch(direction) {
            case 'left':
              model.position.x -= step;
              break;
            case 'right':
              model.position.x += step;
              break;
            case 'up':
              model.position.y += step;
              break;
            case 'down':
              model.position.y -= step;
              break;
            case 'forward':
              model.position.z -= step;
              break;
            case 'backward':
              model.position.z += step;
              break;
          }

          this.$forceUpdate();
        }
      },

      isModelVisible(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        return match ? match.isShow : true;
      },

      getModelPointName(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        return match ? match.pointName : model.name;
      },

      getPointStatus(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        if (!match) return '正常';
        const point = this.pointList.find(p => p.title === match.pointName);
        return point ? point.status || '正常' : '正常';
      },

      getPointHealth(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        if (!match) return 100;
        const point = this.pointList.find(p => p.title === match.pointName);
        return point ? point.healthValue || 100 : 100;
      },

      handleMatchConfirm(matches) {
        this.modelPointMatches = matches;

        matches.forEach(match => {
          if (!this.tooltipPositions[match.id]) {
            this.setDefaultPosition(match);
          }
        });

        this.loadingModelPointMatches();
      },

      hasMatchedPoint(model) {
        const match = this.modelPointMatches.find(m => m.modelName === model.name);
        return match && match.pointName;
      },

      getMatchedPoint(match) {
        return this.pointList.find(p => p.title === match.pointName);
      },

      getPointDefinitions(match) {
        const point = this.getMatchedPoint(match);
        return point ? point.measureDefinitions || [] : [];
      },

      handleBoxMouseDown(event, match) {
        event.preventDefault();
        this.draggingBox = match;
        const container = this.$refs.objViewer;
        const rect = container.getBoundingClientRect();
        const position = this.tooltipPositions[match.id] || {
          x: 50,
          y: 10
        };

        this.dragOffset = {
          x: event.clientX - rect.left - (position.x * rect.width / 100),
          y: event.clientY - rect.top - (position.y * rect.height / 100)
        };
      },

      handleMouseMove(event) {
        if (this.draggingBox) {
          const container = this.$refs.objViewer;
          const rect = container.getBoundingClientRect();

          const x = ((event.clientX - rect.left - this.dragOffset.x) / rect.width) * 100;
          const y = ((event.clientY - rect.top - this.dragOffset.y) / rect.height) * 100;

          const boundedX = Math.max(0, Math.min(100, x));
          const boundedY = Math.max(0, Math.min(100, y));

          this.$set(this.tooltipPositions, this.draggingBox.id, { x: boundedX, y: boundedY });

          const path = document.querySelector(`.connection-path[data-match-id="${this.draggingBox.id}"]`);
          if (path) {
            path.setAttribute('d', this.getConnectionPath(this.getModelByName(this.draggingBox.modelName), this.draggingBox));
          }
        }
      },

      async handleMouseUp() {
        if (this.draggingBox) {
          const match = this.draggingBox;
          const position = this.tooltipPositions[match.id];

          try {
            if (match.id) {
              const updatedData = {
                id: match.id,
                deviceId: Number(this.deviceId),
                modelId: Number(match.modelId),
                measurementId: Number(match.measurementId),
                isShow: match.isShow ? "1" : "0",
                location: JSON.stringify([position.x, position.y, 0])
              };

              await UpdateDeviceWithPoint(updatedData);
              console.log('浮框位置（百分比）已保存到后端');
            }
          } catch (error) {
            console.error('保存位置信息失败:', error);
          }
        }
        this.draggingBox = null;
      },

      handleDefinitionSingleClick(def, match) {
        const point = this.getMatchedPoint(match);
        if (!point) return;

        this.$emit('update-trend', {
          id: def.id,
          pointId: point.id,
          pointTitle: point.title,
          definitionName: def.name
        });
      },

      handleDefinitionDoubleClick(def, match) {
        const point = this.getMatchedPoint(match);
        if (!point) return;

        this.$emit('definition-click', {
          id: def.id,
          pointId: point.id,
          title: def.name,
          pointTitle: point.title
        });
      },

      getUnitText(unitName) {
        switch(unitName) {
          case "2":
            return 'm/s²';
          case "1":
            return 'mm/s';
          case "4":
            return 'm/s²';
          case "3":
            return 'μm';
          default:
            return '';
        }
      },

      highlightModel(model) {
        if (!model) return;

        if (this.highlightedModel === model) return;

        if (this.highlightedModel) {
          this.unhighlightModel(this.highlightedModel);
        }

        this.highlightedModel = model;
        this.isAnimating = true;

        this.originalScale.copy(model.scale);

        this.models.forEach(otherModel => {
          if (otherModel !== model) {
            otherModel.traverse(child => {
              if (child.isMesh) {
                if (!child.material.originalOpacity) {
                  child.material.originalOpacity = child.material.opacity;
                }
                child.material.transparent = true;
                child.material.opacity = 0.1;
                child.material.needsUpdate = true;
              }
            });
          }
        });

        model.traverse(child => {
          if (child.isMesh) {
            child.material.originalColor = child.material.color.clone();
            child.material.originalEmissive = child.material.emissive ? child.material.emissive.clone() : new THREE.Color(0x000000);
            child.material.originalOpacity = child.material.opacity;

            child.material.emissive = new THREE.Color(0x3399ff);
            child.material.emissiveIntensity = 0.3;
            child.material.transparent = false;
            child.material.opacity = 1;
            child.material.needsUpdate = true;
          }
        });
      },

      unhighlightModel(model) {
        if (!model) return;

        this.isAnimating = false;

        this.models.forEach(otherModel => {
          otherModel.traverse(child => {
            if (child.isMesh && child.material.originalOpacity !== undefined) {
              child.material.transparent = child.material.originalOpacity < 1;
              child.material.opacity = child.material.originalOpacity;
              if (otherModel === model) {
                child.material.emissive.copy(child.material.originalEmissive);
                child.material.emissiveIntensity = 0;
              }
              child.material.needsUpdate = true;
            }
          });
        });

        if (this.originalScale && model.scale) {
          model.scale.copy(this.originalScale);
        }

        if (this.highlightedModel === model) {
          this.highlightedModel = null;
        }
      },

      easeInOutQuad(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      },

      async loadBackgroundSettings() {
        try {
          if (!this.scene) {
            console.warn('Scene not initialized yet');
            return;
          }

          const response = await GetBackgroundPhoto(this.deviceId);
          if (response.code === 200) {
            const data = response.data;

            if (data['color']) {
              const colorArray = JSON.parse(data.color);
              const color = new THREE.Color(
                colorArray[0] / 255,
                colorArray[1] / 255,
                colorArray[2] / 255
              );
              this.scene.background = color;
              this.backgroundColor = `#${color.getHexString()}`;
            }

            if (data['ground-color']) {
              const groundColorArray = JSON.parse(data['ground-color']);
              const groundColor = groundColorArray.length === 4 ?
                `rgba(${groundColorArray[0]},${groundColorArray[1]},${groundColorArray[2]},${groundColorArray[3]})` :
                `rgb(${groundColorArray[0]},${groundColorArray[1]},${groundColorArray[2]})`;
              this.groundColor = groundColor;

              let groundMesh = null;
              this.scene.traverse((object) => {
                if (object.isGround) {
                  groundMesh = object;
                }
              });

              if (groundColorArray.length === 4 && groundColorArray[3] <= 0.2) {
                if (groundMesh) {
                  this.scene.remove(groundMesh);
                  if (groundMesh.geometry) groundMesh.geometry.dispose();
                  if (groundMesh.material) groundMesh.material.dispose();
                }
              } else {
                if (groundMesh) {
                  const opacity = groundColorArray.length === 4 ? groundColorArray[3] : 1;
                  groundMesh.material.color = new THREE.Color(
                    groundColorArray[0] / 255,
                    groundColorArray[1] / 255,
                    groundColorArray[2] / 255
                  );
                  groundMesh.material.transparent = opacity < 1;
                  groundMesh.material.opacity = opacity;
                  groundMesh.material.needsUpdate = true;
                } else {
                  this.initLightingAndGround();
                }
              }
            }

            if (data['ground-image']) {
              await new Promise((resolve, reject) => {
                const textureLoader = new THREE.TextureLoader();
                textureLoader.setCrossOrigin('Anonymous');
                textureLoader.load(
                  data['ground-image'],
                  (texture) => {
                    texture.wrapS = THREE.RepeatWrapping;
                    texture.wrapT = THREE.RepeatWrapping;
                    texture.repeat.set(10, 10);
                    this.groundTexture = texture;

                    let groundMesh = null;
                    this.scene.traverse((object) => {
                      if (object.isGround) {
                        groundMesh = object;
                      }
                    });

                    if (groundMesh) {
                      groundMesh.material.map = texture;
                      groundMesh.material.needsUpdate = true;
                    }
                    resolve();
                  },
                  undefined,
                  reject
                );
              });
            }

            if (data['background-image']) {
              await new Promise((resolve, reject) => {
                const textureLoader = new THREE.TextureLoader();
                textureLoader.setCrossOrigin('Anonymous');
                textureLoader.load(
                  data['background-image'],
                  (texture) => {
                    if (this.backgroundMesh) {
                      this.scene.remove(this.backgroundMesh);
                      this.backgroundMesh.geometry.dispose();
                      this.backgroundMesh.material.dispose();
                    }

                    const geometry = new THREE.SphereGeometry(500, 60, 40);
                    geometry.scale(-1, 1, 1);
                    const material = new THREE.MeshBasicMaterial({
                      map: texture
                    });
                    this.backgroundMesh = new THREE.Mesh(geometry, material);
                    this.scene.add(this.backgroundMesh);
                    this.backgroundTexture = texture;
                    this.scene.background = null;
                    resolve();
                  },
                  undefined,
                  reject
                );
              });
            }
          }
        } catch (error) {
          console.error('加载背景设置失败:', error);
        }
      },

      getModelByName(modelName) {
        return this.models.find(model => model.name === modelName);
      },

      cleanupResources() {
        if (this.animationFrameId) {
          cancelAnimationFrame(this.animationFrameId);
          this.animationFrameId = null;
        }

        this.animationRunning = false;

        window.removeEventListener('resize', this.onWindowResize);
        window.removeEventListener('mousemove', this.handleMouseMove);
        window.removeEventListener('mouseup', this.handleMouseUp);

        if (this.model) {
          if (this.scene) {
            this.scene.remove(this.model);
          }
          this.model = null;
        }
        
        if (this.models && this.models.length > 0) {
          if (this.scene) {
            this.models.forEach(model => {
              this.scene.remove(model);
            });
          }
          this.models = [];
        }
        
        if (this.scene) {
          this.resetScene();
        }

        if (this.renderer) {
          this.renderer.dispose();
          this.renderer.forceContextLoss();
          this.renderer.context = null;
          this.renderer.domElement = null;
          this.renderer = null;
        }

        if (this.scene) {
          this.scene.traverse((obj) => {
            if (obj.isMesh) {
              if (obj.geometry) obj.geometry.dispose();
              if (obj.material) {
                if (Array.isArray(obj.material)) {
                  obj.material.forEach(m => m.dispose());
                } else {
                  obj.material.dispose();
                }
              }
            }
          });
          this.scene = null;
        }

        this.camera = null;
        if (this.controls) {
          this.controls.dispose();
          this.controls = null;
        }
        
        this.backgroundMesh = null;

        console.log('THREE.js资源已完全清理');
      },

      clearModels() {
        const objectsToRemove = [];
  
        if (this.scene) {
          this.scene.traverse((child) => {
            if (child.isMesh && !child.isGround) {
              objectsToRemove.push(child);
            }
          });
  
          objectsToRemove.forEach((object) => {
            this.scene.remove(object);
            if (object.geometry) object.geometry.dispose();
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((material) => material.dispose());
              } else {
                object.material.dispose();
              }
            }
          });
        }
  
        this.model = null;
      }
    }
  };
  </script>

  <style scoped>
  @import "./css/objview.css";
  </style>
