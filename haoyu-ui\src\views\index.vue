<template>
  <div :class="backgroundClass">
    <!-- 使用动态组件根据 currentComponent 的值渲染相应的组件 -->
    <component :is="currentComponent" :data="selectedRowData" @switchToGeneralPicture="switchToGeneralPicture" @Goback="Goback" @3D="GoTo3D" @switchToBigScreen="switchToBigScreen" />
  </div>
</template>

<script>
import BigScreen from '@/views/textPage/BigScreen/indexs/index.vue'
import GeneralPicture from '@/views/textPage/GeneralPicture/GeneralPicture.vue'
import Web3D from '@/views/textPage/web3d/index.vue'

export default {
  components: {
    BigScreen,
    GeneralPicture,
    Web3D,
  },
  data() {
    return {
      currentComponent: 'BigScreen', // 初始组件为 IndexBigscreen
      selectedRowData: null // 存储传递的数据
    }
  },
  computed: {
    backgroundClass() {
      // 根据 currentComponent 返回不同的类名
      return this.currentComponent === 'BigScreen' || this.currentComponent === 'BigScreen' ? 'background-container black-bg' : 'background-container white-bg'
    }
  },
  methods: {
    GoTo3D() {
      this.currentComponent = 'Web3D'

    },
    // 切换到 GeneralPicture 组件的方法
    switchToGeneralPicture(rowData) {
      this.selectedRowData = rowData // 存储传递过来的数据
      console.log(this.selectedRowData)
      this.currentComponent = 'GeneralPicture' // 切换组件
    },
    Goback() {
      this.currentComponent = 'BigScreen' // 切换组件
    },
    switchToBigScreen() {
      this.currentComponent = 'BigScreen' // 切换到 BigScreen 组件
    }
  }
}
</script>

<style scoped>

/* 大屏模式下的黑色背景 */
.black-bg {
  background-color: #03050C;
  width: 100%;
  height: 100%;
}

/* GeneralPicture 模式下的白色背景 */
.white-bg {
  background-color: #FFFFFF;
  width: 100%;
  height: 100%;
}
</style>
