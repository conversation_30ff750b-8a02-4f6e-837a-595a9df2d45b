// ChartMarker.js
export default class ChartMarker {
  constructor(chartInstance, container) {
    if (!chartInstance || !container) {
      throw new Error('ChartMarker requires both chartInstance and container');
    }

    this.chart = chartInstance;
    this.container = container;
    this.currentXValue = null;
    this.currentYValue = null;
    this.isDragging = false;
    this.dragStartX = 0;

    // 初始化标记样式
    this.markerStyle = {
      lineColor: '#FF0000',
      lineWidth: 1,
      lineDash: [5, 5],
      textColor: '#000000',
      textFont: '14px Arial'
    };

    // 初始化事件监听
    this._initEventListeners();
  }

  // 初始化事件监听器
  _initEventListeners = () => {
    try {
      this.chart.on('click', this._handleClick);
      this.chart.on('dataZoom', this._handleDataZoom);
      this.container.addEventListener('mousedown', this._handleMouseDown);
      document.addEventListener('mousemove', this._handleMouseMove);
      document.addEventListener('mouseup', this._handleMouseUp);
    } catch (error) {
      console.error('Failed to initialize event listeners:', error);
    }
  }

  // 处理点击事件
  _handleClick = (params) => {
    if (!params || params.componentType !== 'series') return;

    try {
      const xValue = params.name || params.data[0];
      const yValue = params.value instanceof Array ? params.value[1] : params.value;

      this.currentXValue = xValue;
      this.currentYValue = yValue;

      this.drawMarker(xValue, yValue);
      this.highlightPoint(xValue, yValue);
    } catch (error) {
      console.error('Error in click handler:', error);
    }
  }

  // 处理缩放事件
  _handleDataZoom = () => {
    if (this.currentXValue !== null && this.currentYValue !== null) {
      this.updateMarkerVisibility();
    }
  }

  // 处理鼠标按下事件
  _handleMouseDown = (e) => {
    if (!this.currentXValue || !this.chart) return;

    try {
      const xPixel = this.chart.convertToPixel({ xAxisIndex: 0 }, this.currentXValue);
      const rect = this.container.getBoundingClientRect();

      // 检查点击是否在参考线附近（5像素范围内）
      if (Math.abs(e.clientX - (rect.left + xPixel)) <= 5) {
        this.isDragging = true;
        this.dragStartX = e.clientX;
        this.container.style.cursor = 'ew-resize';
        e.stopPropagation();
        e.preventDefault();
      }
    } catch (error) {
      console.error('Error in mousedown handler:', error);
    }
  }

  // 处理鼠标移动事件
  _handleMouseMove = (e) => {
    if (!this.isDragging || !this.chart) return;

    try {
      e.stopPropagation();
      e.preventDefault();

      const rect = this.container.getBoundingClientRect();
      const chartX = e.clientX - rect.left;
      const newXValue = this.chart.convertFromPixel({ xAxisIndex: 0 }, chartX);

      if (this._isValidXValue(newXValue)) {
        this.currentXValue = newXValue;
        const closestPoint = this._findClosestPoint(this.chart.getOption().series[0].data, newXValue);
        if (closestPoint) {
          this.currentYValue = closestPoint.y;
          this.drawMarker(this.currentXValue, this.currentYValue);
          this.highlightPoint(this.currentXValue, this.currentYValue);
          this._emitValueChange();
        }
      }
    } catch (error) {
      console.error('Error in mousemove handler:', error);
    }
  }

  // 处理鼠标松开事件
  _handleMouseUp = () => {
    if (this.isDragging) {
      this.isDragging = false;
      if (this.container) {
        this.container.style.cursor = 'default';
      }
    }
  }

  // 绘制标记
  drawMarker(xValue, yValue) {
    if (!this.chart || !this.container) return;

    try {
      const xPixel = this.chart.convertToPixel({ xAxisIndex: 0 }, xValue);
      const yPixel = this.chart.convertToPixel({ yAxisIndex: 0 }, yValue);
      const chartHeight = this.container.clientHeight;

      this.chart.setOption({
        graphic: [
          {
            // 垂直参考线
            type: 'line',
            shape: {
              x1: xPixel,
              y1: chartHeight * 0.05,
              x2: xPixel,
              y2: chartHeight * 0.98
            },
            style: {
              stroke: this.markerStyle.lineColor,
              lineWidth: this.markerStyle.lineWidth,
              lineDash: this.markerStyle.lineDash,
              cursor: 'ew-resize'
            },
            z: 1000,
            silent: false,
            draggable: true
          },
          {
            // 坐标值文本
            type: 'text',
            left: xPixel - 50,
            top: yPixel - 30,
            style: {
              text: `X: ${xValue.toFixed(2)}, Y: ${yValue.toFixed(2)}`,
              fill: this.markerStyle.textColor,
              font: this.markerStyle.textFont,
              padding: [2, 5]
            },
            z: 1000
          }
        ]
      });
    } catch (error) {
      console.error('Error drawing marker:', error);
    }
  }

  // 更新标记可见性
  updateMarkerVisibility() {
    if (!this.chart || !this.container) return;

    try {
      const xPixel = this.chart.convertToPixel({ xAxisIndex: 0 }, this.currentXValue);
      const yPixel = this.chart.convertToPixel({ yAxisIndex: 0 }, this.currentYValue);
      const chartWidth = this.container.clientWidth;
      const chartHeight = this.container.clientHeight;

      const isInView = xPixel > 0 && xPixel < chartWidth && yPixel > 0 && yPixel < chartHeight;

      if (isInView) {
        this.drawMarker(this.currentXValue, this.currentYValue);
      } else {
        this.clearMarker();
      }
    } catch (error) {
      console.error('Error updating marker visibility:', error);
    }
  }

  // 高亮选中点
  highlightPoint(xValue, yValue) {
    if (!this.chart) return;

    try {
      const series = this.chart.getOption().series;
      if (!series || !series[0] || !series[0].data) return;

      const data = series[0].data;
      const index = data.findIndex(item => {
        const x = item instanceof Array ? item[0] : item.value instanceof Array ? item.value[0] : null;
        return Math.abs(x - xValue) < 0.001; // 使用小的阈值进行比较
      });

      if (index !== -1) {
        const highlightData = data.map((item, i) => {
          const value = item instanceof Array ? item : item.value;
          return {
            value: value,
            itemStyle: {
              color: i === index ? '#ff0000' : undefined
            }
          };
        });

        this.chart.setOption({
          series: [{
            data: highlightData
          }]
        });
      }
    } catch (error) {
      console.error('Error highlighting point:', error);
    }
  }

  // 检查X值是否在有效范围内
  _isValidXValue(xValue) {
    if (!this.chart) return false;

    try {
      const option = this.chart.getOption();
      const xAxis = option.xAxis[0];

      if (xAxis.min !== undefined && xValue < xAxis.min) return false;
      if (xAxis.max !== undefined && xValue > xAxis.max) return false;

      return true;
    } catch (error) {
      console.error('Error checking valid X value:', error);
      return false;
    }
  }

  // 找到最接近的数据点
  _findClosestPoint(data, xValue) {
    if (!data || data.length === 0) return null;

    try {
      let closestPoint = null;
      let minDistance = Infinity;

      data.forEach(item => {
        const x = item instanceof Array ? item[0] : item.value instanceof Array ? item.value[0] : null;
        const y = item instanceof Array ? item[1] : item.value instanceof Array ? item.value[1] : null;

        if (x !== null && y !== null) {
          const distance = Math.abs(x - xValue);
          if (distance < minDistance) {
            minDistance = distance;
            closestPoint = { x, y };
          }
        }
      });

      return closestPoint;
    } catch (error) {
      console.error('Error finding closest point:', error);
      return null;
    }
  }

  // 触发值变化事件
  _emitValueChange() {
    try {
      const event = new CustomEvent('markerchange', {
        detail: {
          x: this.currentXValue,
          y: this.currentYValue
        }
      });
      this.container.dispatchEvent(event);
    } catch (error) {
      console.error('Error emitting value change:', error);
    }
  }

  // 清除标记
  clearMarker() {
    if (this.chart) {
      this.chart.setOption({
        graphic: []
      });
    }
  }

  // 设置标记样式
  setMarkerStyle(options = {}) {
    this.markerStyle = {
      ...this.markerStyle,
      ...options
    };
  }

  // 销毁实例，清理事件监听
  destroy() {
    try {
      if (this.chart) {
        this.chart.off('click', this._handleClick);
        this.chart.off('dataZoom', this._handleDataZoom);
      }

      if (this.container) {
        this.container.removeEventListener('mousedown', this._handleMouseDown);
      }

      document.removeEventListener('mousemove', this._handleMouseMove);
      document.removeEventListener('mouseup', this._handleMouseUp);

      this.clearMarker();
      this.currentXValue = null;
      this.currentYValue = null;
    } catch (error) {
      console.error('Error destroying ChartMarker:', error);
    }
  }
}
