.basic-config {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

/* 影响了其他页面先注释掉 */
/* .left-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-right: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  height: 100%;
} */

.right-container {
  flex: 4;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  height: 100%;
}

.left-container h3, .right-container h3 {
  margin-top: 0;
}

.station-info {
  flex: 1;
  padding: 5px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #ffffff;
}

.additional-info {
  flex: 1;
  padding: 5px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #ffffff;
}

.station-info {
  margin-bottom: 20px;
}

.additional-info h4 {
  margin-top: 0;
}

.el-tree {
  margin-top: 20px;
}

.compact-form .el-form-item {
  margin-bottom: 5px;
  margin-left: 10px;
  padding: 0;
}

.compact-form-item .el-form-item__label {
  margin-bottom: 0;
  padding-bottom: 0;
  font-size: 12px; /* 小号字体 */
}

.compact-form-item .el-form-item__content {
  margin-top: 0;
  padding-top: 0;
}

.small-input .el-input__inner {
  height: 24px; /* 最小高度 */
  font-size: 12px; /* 小号字体 */
}


