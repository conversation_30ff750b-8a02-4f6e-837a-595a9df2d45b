.header {
    background-color: #f5f5f5;
    padding: 5px;
    border-bottom: 2px solid #dcdcdc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
    text-align: center;
    box-sizing: border-box;
    /* border:1px solid red; */
}

.layout-body {
    display: flex;
    flex: 1;
    height: 80%;
    box-sizing: border-box;
    /* border:1px solid red; */
}

.main-layout {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
    /* border:1px solid red; */
    /* overflow: auto; */
}

.sidebar {
    width: 50px;
    background-color: #e0e0e0;
    overflow: auto;
    /*  border:1px solid red; */
}

.maincontainer {
    display: flex;
    /* flex-grow: 1; */
    width: 100%;
    height: 100%;
    /* border:1px solid red; */
    flex-direction: row;
    /* overflow:auto; */
    position: relative;
    /* 添加这行 */
}


/* .split-container {
  display: flex;
  width: 100%;
  height: 100%;

} */

.left-container {
    width: 20%;
    /* overflow: auto; */
    height: 100%;
    /* border:1px solid red; */
    position: relative;
    z-index: 1;
    /* 设置较低的z-index */
    min-width: 0;
    /* 添加这行 */
    max-width: 35%;
}

.right-container {
    flex: 1;
    /* overflow: auto; */
    background-color: #f9f9f9;
    height: 100%;
    /*   border-left: 2px solid blue; */
    border-radius: 0 8px 8px 0;
    /* border:1px solid red; */
    padding: 0px;
    z-index: 1;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    min-width: 0;
    /* 添加这行 */
    max-width: 100%;
}

.resizer {
    width: 4px;
    background-color: gray;
    cursor: ew-resize;
    height: 100%;
    z-index: 1 !important;
    /* border: 2px solid red; */
}

.tooltip {
    position: absolute;
    background-color: white;
    border: 1px solid black;
    padding: 5px;
    display: none;
}

.bottom-container {
    width: 100%;
    /* 确保宽度为100% */
    background-color: #f1f1f1;
    text-align: left;
    padding-left: 10px;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
}