const state = {
  treeDataSend: [],
  expandedKeys: [],
  selectedKey: null,
  treePathText: '', // 初始化显示的路径和数据
  selectedTreeNode: {
    id: 0,
    treeicon: '',
    status: null
  }
}

const mutations = {
  SET_TREE_DATA(state, data) {
    state.treeDataSend = data
  },
  SET_EXPANDED_KEYS(state, keys) {
    state.expandedKeys = keys
    // console.log(keys)
  },
  SET_SELECTED_KEY(state, key) {
    state.selectedKey = key
  },
  // 选择的节点对象
  SET_SELECTED_TREE_NODE(state, node) {
    state.selectedTreeNode = {
      id: node.id,
      treeicon: node.treeicon,
      status: node.status
    }
  },
  MOVE_NODE(state, direction) {
    const move = (nodes) => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].key === state.selectedKey) {
          const targetIndex = i + direction
          if (targetIndex >= 0 && targetIndex < nodes.length) {
            const [node] = nodes.splice(i, 1)
            nodes.splice(targetIndex, 0, node)
          }
          return true
        }
        if (nodes[i].children && move(nodes[i].children)) return true
      }
      return false
    }
    move(state.treeDataSend)
  },
  SET_SELECTED_PARENT_KEY(state, key) {
    const findParentKey = (nodes, targetKey, parentKey = null) => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return parentKey
        }
        if (node.children) {
          const result = findParentKey(node.children, targetKey, node.key)
          if (result) return result
        }
      }
      return null
    }
    state.selectedKey = findParentKey(state.treeDataSend, key)
  },
  SET_TREEPATH_TEXT(state, text) {
    state.treePathText = text
    state.ceshi = 'ascascascasc'
  }
}

const actions = {
  setTreeData({ commit }, data) {
    commit('SET_TREE_DATA', data)
  },
  setExpandedKeys({ commit }, keys) {
    commit('SET_EXPANDED_KEYS', keys)
  },
  setSelectedKey({ commit }, key) {
    commit('SET_SELECTED_KEY', key)
  },
  // 选择的节点对象
  setSelectedTreeNode({ commit }, node) {
    commit('SET_SELECTED_TREE_NODE', node)
  },
  setSelectedParentKey({ commit }, key) {
    commit('SET_SELECTED_PARENT_KEY', key)
  },
  moveNode({ commit }, direction) {
    commit('MOVE_NODE', direction)
  },
  setTreePathText({ commit }, text) {
    commit('SET_TREEPATH_TEXT', text)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
