<!-- 频谱图 -->
<template>
  <div class="sycontainer">
    <div class="top-container">{{ treePathText }} 频谱图 {{ time_point }}  {{ currentXValue }} {{ currentUnit === 'RPM' ? 'RPM (CPM)' : currentUnit }}, {{ currentYValue }} {{ currentAmplitudeUnit }}
      <button class="btn" style="margin-left: auto; box-sizing: border-box; border-radius: 8px;
        width: 30px;height: 30px;" @click="handleToggle">
        <svg-icon icon-class='unfold' />
      </button>
    </div>
    <!-- 工具栏 -->
    <div class="spectrum-toolbar">
      <div class="fuction">
        <button
          class="cursor-btn"
          :class="{ 'active': showCursor1 }"
          @click="toggleCursor(1)"
        >
          A
        </button>
        <button
          class="cursor-btn"
          :class="{ 'active': showHarmonics }"
          @click="toggleHarmonics"
          :disabled="!showCursor1"
          :title="!showCursor1 ? '请先启用游标A' : ''"
        >
          谐波
        </button>
        <button
          class="cursor-btn"
          :class="{ 'active': showSidebands }"
          @click="toggleSidebands"
          :disabled="!showCursor1"
          :title="!showCursor1 ? '请先启用游标A' : ''"
        >
          边频
        </button>
        <button
          class="cursor-btn"
          @click="downloadChart"
          title="下载图表"
        >
          下载
        </button>
        <button
          class="cursor-btn"
          @click="handleBearingClick"
          title="轴承分析"
        >
          轴承
        </button>

        <button
          class="cursor-btn"
          @click="handleGearAnalysisClick"
          title="齿轮分析"
        >
          齿轮
        </button>

        <button
          class="cursor-btn"
          :class="{ 'active': showBearingHealthPanel }"
          v-if="bearingHealth"
          @click="toggleBearingHealthPanel"
          title="健康度显示/隐藏"
        >
          健康度
        </button>

        <button
          class="cursor-btn"
          @click="clearBearingMarkers"
          title="清除轴承标记"
        >
          清除标记
        </button>
        <button
          class="cursor-btn"
          @click="openTempModifyDialog"
          title="临时修改"
        >
          临时修改
        </button>
        <button
          class="cursor-btn"
          @click="showAxisRangeDialog = true"
          title="设置坐标轴范围"
        >
          范围
        </button>
        <!-- 添加频率单位切换按钮 -->
        <el-dropdown trigger="click" @command="changeFrequencyUnit" class="unit-dropdown">
          <button class="cursor-btn unit-dropdown-btn" title="点击切换频率单位">
            {{ currentUnit }} <i class="el-icon-arrow-down el-icon--right"></i>
          </button>
          <el-dropdown-menu slot="dropdown" class="unit-dropdown-menu">
            <el-dropdown-item command="Hz" :disabled="currentUnit === 'Hz'">
              <span class="unit-name">Hz</span>
              <span class="unit-desc">赫兹，频率基本单位</span>
            </el-dropdown-item>
            <el-dropdown-item command="RPM" :disabled="currentUnit === 'RPM'">
              <span class="unit-name">RPM (CPM)</span>
              <span class="unit-desc">每分钟转数/周期数</span>
            </el-dropdown-item>
            <el-dropdown-item command="Orders" :disabled="currentUnit === 'Orders'">
              <span class="unit-name">Orders</span>
              <span class="unit-desc">阶次，相对于参考转速的倍数</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="display-data">
        <div>A: {{ cursor1 ? cursor1.toFixed(4) : '-' }} {{ currentUnit }}</div>
      </div>
      <div v-if="showHarmonics" class="harmonic-settings">
        <span>基频:</span>
        <input
          type="number"
          v-model.number="baseFrequency"
          @keyup.enter="handleBaseFrequencyEnter"
          placeholder="请输入基频"
          class="frequency-input"
        />
        <span>{{ currentUnit }}</span>
        <span v-if="baseAmplitude !== null">
          {{ baseAmplitude.toFixed(3) }} {{ currentAmplitudeUnit }}
        </span>
        <span style="margin-left: 12px;">谐波数:</span>
        <input
          type="number"
          v-model.number="harmonicCount"
          min="1"
          max="20"
          class="frequency-input"
          @blur="handleHarmonicCountChange"
          @keyup.enter="handleHarmonicCountChange"
        />
      </div>
      <div v-if="showSidebands" class="sideband-settings">
        <span>边频数:</span>
        <input
          type="number"
          v-model.number="sidebandCount"
          min="1"
          max="10"
          class="frequency-input"
          @blur="handleSidebandCountChange"
          @keyup.enter="handleSidebandCountChange"
        />
        <span>间隔:</span>
        <input
          type="number"
          :value="sidebandSpacing ? Number(sidebandSpacing.toFixed(2)) : sidebandSpacing"
          @input="e => sidebandSpacing = Number(e.target.value)"
          :step="0.1"
          class="frequency-input"
          @blur="handleSidebandSpacingChange"
          @keyup.enter="handleSidebandSpacingChange"
        />
        <span>{{ currentUnit }}</span>
      </div>
      <!-- 加载中状态显示 -->
      <div class="loading-info" v-if="isCalculating">
        <span class="loading-text">轴承健康度计算中...</span>
      </div>


      <vue-draggable-resizable
        v-if="bearingHealth && showBearingHealthPanel"
        :w="150"
        :h="120"
        :x="bearingHealthPosition.x"
        :y="bearingHealthPosition.y"
        :z="1000"
        :parent="false"
        :handles="[]"
        class-name="draggable-health-box"
        @dragging="onDragBearingHealth"
        @resizing="onResizeBearingHealth"
      >
        <div class="damage-info" :class="bearingHealth.level">
          <div class="health-score">
            <span class="score-value">{{ bearingHealth.score }}</span>
            <span class="score-level">{{ getHealthLevelText(bearingHealth.level) }}</span>
          </div>
          <div class="damage-type" v-if="bearingHealth.damageAnalysis && bearingHealth.damageAnalysis.mostLikelyDamage" :title="bearingHealth.damageAnalysis.mostLikelyDamage.type + '：' + bearingHealth.damageAnalysis.mostLikelyDamage.probability + '%'">
            <span class="damage-label">轴承：</span>
            <span class="damage-value">{{ bearingHealth.damageAnalysis.mostLikelyDamage.type }}</span>
          </div>
          <div class="damage-probability" v-if="bearingHealth.damageAnalysis && bearingHealth.damageAnalysis.mostLikelyDamage">
            <span class="damage-label">概率：</span>
            <span class="damage-value">{{ bearingHealth.damageAnalysis.mostLikelyDamage.probability }}%</span>
          </div>
          <div class="damage-percentage">
            <span class="damage-label">损伤率：</span>
            <span class="damage-value">{{ bearingHealth.damagePercentage }}%</span>
          </div>
          <div class="damage-points" v-if="bearingHealth.matchedPoints && bearingHealth.matchedPoints.length">
            <span class="damage-label">损伤点位：</span>
            <span class="damage-value">{{ bearingHealth.matchedPoints.join(', ') }}</span>
          </div>
          <!-- <div class="measure-type" v-if="bearingHealth.details && bearingHealth.details.measureType">{{ bearingHealth.details.measureType }}</div> -->
        </div>
      </vue-draggable-resizable>


    </div>
    <div ref="chart" class="chart"
         tabindex="0"
         v-on:keydown.left="handleLeftArrow"
         v-on:keydown.right="handleRightArrow"
         @mouseenter="handleChartMouseEnter"
         @mouseleave="handleChartMouseLeave">
      <div v-if="error" class="error-message">{{ error }}</div>
    </div>
    <!-- 添加轴承对话框组件 -->
    <bearing-dialog
      v-if="showBearingDialog"
      :visible.sync="showBearingDialog"
      @close="showBearingDialog = false"
      @confirm="handleBearingFrequencies"
    />

    <gear-analysis-dialog
      v-if="showGearAnalysisDialog"
      :visible.sync="showGearAnalysisDialog"
      @close="showGearAnalysisDialog = false"
      @confirm="handleGearAnalysis"
    />

    <!-- 添加临时修改对话框 -->
    <div v-if="showTempModifyDialog" class="temp-modify-dialog" ref="tempModifyDialogRef">
      <div class="dialog-header" @mousedown="startDragTempDialog">
        <span>临时修改</span>
        <button class="close-btn" @click="showTempModifyDialog = false">×</button>
      </div>
      <div class="dialog-content">
        <div class="form-item">
          <span>线数：</span>
          <el-input v-model="tempModifyForm.rangeMin" size="small" />
        </div>
        <div class="form-item">
          <span>范围上限：</span>
          <el-input v-model="tempModifyForm.rangeMax" size="small" />
        </div>
      </div>
      <div class="dialog-footer">
        <el-button @click="showTempModifyDialog = false">取消</el-button>
        <el-button type="primary" @click="handleTempModify">应用修改</el-button>
      </div>
    </div>
    <!-- 添加坐标轴范围对话框 -->
    <el-dialog
      title="设置坐标轴范围"
      :visible.sync="showAxisRangeDialog"
      width="400px"
      :close-on-click-modal="false"
      :modal="false"
      custom-class="axis-range-dialog"
    >
      <div class="axis-range-form">
        <div class="axis-group">
          <div class="axis-label">X轴范围:</div>
          <el-input
            v-model="axisRange.xMin"
            placeholder="最小值"
            type="text"
            size="small"
            style="width: 45%; margin-right: 10px;"
          >
            <template slot="prepend">Min</template>
          </el-input>
          <el-input
            v-model="axisRange.xMax"
            placeholder="最大值"
            type="text"
            size="small"
            style="width: 45%;"
          >
            <template slot="prepend">Max</template>
          </el-input>
        </div>
        <div class="axis-group">
          <div class="axis-label">Y轴范围:</div>
          <el-input
            v-model="axisRange.yMin"
            placeholder="最小值"
            type="text"
            size="small"
            style="width: 45%; margin-right: 10px;"
          >
            <template slot="prepend">Min</template>
          </el-input>
          <el-input
            v-model="axisRange.yMax"
            placeholder="最大值"
            type="text"
            size="small"
            style="width: 45%;"
          >
            <template slot="prepend">Max</template>
          </el-input>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetAxisRange">重置</el-button>
        <el-button type="primary" @click="applyAxisRange">确定</el-button>
      </div>
    </el-dialog>
    <!-- 添加参考转速设置对话框 -->
    <el-dialog
      title="设置参考转速"
      :visible.sync="showReferenceSpeedDialog"
      width="450px"
      :close-on-click-modal="false"
      custom-class="reference-speed-dialog"
      :modal="false"
    >
      <div class="reference-speed-form">
        <p class="reference-speed-description">
          Orders单位需要设置参考转速。Orders = 频率 / (参考转速/60)，表示频率相对于参考转速的倍数。
        </p>
        <div class="reference-speed-input">
          <span class="reference-speed-label">参考转速 (RPM):</span>
          <el-input-number
            v-model="referenceSpeed"
            :min="1"
            :max="100000"
            :step="10"
            size="small"
            style="width: 150px;"
          ></el-input-number>
        </div>
        <div class="reference-speed-options">
          <el-checkbox v-model="saveReferenceSpeed">保存此设置作为默认值</el-checkbox>
          <el-tooltip content="保存后，下次切换到Orders单位时将自动使用此参考转速" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showReferenceSpeedDialog = false">取消</el-button>
        <el-button type="primary" @click="setReferenceSpeed(referenceSpeed)">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { mapState, mapActions } from 'vuex'
import { getWaveform } from './data'
import { getParams } from '@/utils/graph_Interface'
import ChartZoom from '../ChartZoom'
import chartKeyboardNavigation from '../TimeDomainChart/chartKeyboardNavigation'
import html2canvas from 'html2canvas'
import { chartDownloadMixin } from '../mixins/chartDownload'
import BearingDialog from '@/views/textPage/BearingDialog/BearingDialog.vue'
import request from '@/utils/request'
import GearAnalysisDialog from './Dialog/GearAnalysisDialog.vue'
import VueDraggableResizable from 'vue-draggable-resizable'
import 'vue-draggable-resizable/dist/VueDraggableResizable.css'
import dataManager from '@/utils/dataManager' // 导入数据管理器

export default {
  name: 'SpectrumChart',
  components: {
    BearingDialog,
    GearAnalysisDialog,
    VueDraggableResizable
  },
  mixins: [chartDownloadMixin],
  props: {
    isExpanded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      _isUpdatingFromSelf: false, // 新增：用于防止更新循环的标志位
      // 添加防抖和请求控制
      dataRequestDebounceTimer: null,
      isDataLoading: false,
      lastRequestParams: null,

      chartInstance: null,
      currentXValue: null,
      currentYValue: null,
      data: [], //当前显示数据
      origindata:[], //原始数据
      originalData: [], // 保存原始数据
      error: null,
      currentUnit: 'Hz',
      currentAmplitudeUnit: 'm/s²', // 当前幅值单位
      speedUnitOptions: ['mm/s', 'in/s'], // 速度单位选项
      speedUnitIndex: 0, // 速度单位索引
      conversionFactor: 1,
      referenceSpeed: 1000, // 添加参考转速，默认1000 RPM用于Orders计算
      isHoverMode: false, // 控制显示模式：true为悬浮显示，false为点击显示
      currentSelectedPoint: null,// 当前选中的点
      resizeObserver: null, // 添加 resizeObserver
      keyboardNavigation: null, // 添加键盘导航
      showCursor1: false,
      // showCursor2: false, // 注释掉游标B状态
      cursor1: null,
      // cursor2: null, // 注释掉游标B位置
      // freqDiff: 0, // 注释掉差值
      // ampDiff: 0,
      isDragging: false,
      activeCursor: null,
      cursor1Y: null,
      // cursor2Y: null, // 注释掉游标B的Y值
      showHarmonics: false, // 是否显示谐波标记
      baseFrequency: null, // 基频
      harmonicMarkers: [], // 谐波标记数据
      showSidebands: false, // 是否显示边频
      sidebandSpacing: null, // 边频间隔
      sidebandCount: 3, // 边频数量
      isDraggingSideband: false, // 是否正在拖拽边频
      activeSidebandIndex: null, // 当前激活的边频索引
      harmonicCount: 5, // 默认显示5次谐波
      baseAmplitude: null, // 添加基频幅值
      isChartFocused: false, // 添加新的状态来跟踪图表是否被聚焦
      showBearingDialog: false,
      showGearAnalysisDialog: false,
      showTempModifyDialog: false,
      tempModifyForm: {
        rangeMin: null,
        rangeMax: null,
        windowFunction: ''
      },
      tempDialogPos: {
        x: 0,
        y: 0
      },
      showAxisRangeDialog: false,
      axisRange: {
        xMin: null,
        xMax: null,
        yMin: null,
        yMax: null
      },
      defaultAxisRange: {
        xMin: null,
        xMax: null,
        yMin: null,
        yMax: null
      },
      temporaryData: null, // 存储我们构建好的请求参数作为临时数据
      bearingHealth: null, // 新增的轴承健康度属性
      isCalculating: false,
      bearingHealthPosition: { x: 10, y: 10 },
      bearingHealthSize: { width: 300, height: 180 },
      showBearingHealthPanel: false, // 添加控制轴承健康度显示的状态变量
      showReferenceSpeedDialog: false, // 控制参考转速设置对话框显示
      saveReferenceSpeed: true, // 是否保存参考转速设置
      fixedCursorStep: 0.01, // 固定游标步进值
      isDraggingHarmonic: false, // 新增：标记是否正在拖动谐波线
      activeHarmonicOrder: null, // 新增：记录当前拖动的谐波阶数
    }
  },
  computed: {
    ...mapState('tree', ['treePathText']),
    ...mapState('dataStore', ['device_id', 'point_id', 'time_point']), // 映射 Vuex 状态
    ...mapState('SettingConnect', ['configNodeInfo']), // 添加 configNodeInfo 映射
    amplitudeUnit() {
      const measureName = this.configNodeInfo?.measureDefinitions?.measureDefineName;
      // 如果是加速度或包络，返回 m/s²
      if (measureName?.includes('加速度') || measureName?.includes('包络')) {
        return 'm/s²';
      }
      // 如果是速度，返回 mm/s
      if (measureName === '速度') {
        return 'mm/s';
      }
      // 默认返回 m/s²
      return 'm/s²';
    },
    // 添加计算属性判断是否是速度测量
    isSpeedMeasurement() {
      const measureName = this.configNodeInfo?.measureDefinitions?.measureDefineName;
      return measureName === '速度';
    },
    machineId() {
    // 安全地解构获取machineId，避免undefined错误
      return this.configNodeInfo && this.configNodeInfo.measureDefinitions && this.configNodeInfo.measureDefinitions.machineId || null;
    },
    ...mapState('chartLink', {
      // 使用一个别名来从Vuex获取状态，避免与data中的baseFrequency冲突
      linkedBaseFrequency: 'baseFrequency'
    })
  },
  watch:{
    // 统一的数据更新处理，避免重复请求
    '$store.state.tree.selectedTreeNode.id': {
      handler(newNodeId) {
        this.debouncedDataUpdate('nodeId', newNodeId);
      }
    },
    'time_point': {
      handler(newTimePoint) {
        this.debouncedDataUpdate('timePoint', newTimePoint);
      }
    },
    isExpanded: {
      handler(newVal) {
        // 先让 DOM 完成更新
        this.$nextTick(() => {
          // 给足够的时间让过渡动画完成
          setTimeout(() => {
            if (this.chartInstance) {
              this.chartInstance.resize();
              this.updateChart();
            }
          }, 300);
        });
      },
      immediate: true
    },
    data: {
      handler(newData) {
        if (newData && newData.length > 0) {
          this.initDefaultAxisRange();
        }
      },
      immediate: true
    },
    machineId: {
      handler(newVal) {
        if (newVal) {
          this.smartcalculate(); // 当 machineId 变化时重新调用方法
        }
      },
      immediate: true // 组件创建时立即执行一次
    },
    configNodeInfo: {
      handler(newVal, oldVal) {
        this.debouncedDataUpdate('configNodeInfo', newVal);
      },
      deep: true
    },
    linkedBaseFrequency(newFreqInHz) {
      // 关键：如果这次更新是本组件自己发起的，则忽略
      if (this._isUpdatingFromSelf) {
        this._isUpdatingFromSelf = false; // 重置标志位并返回
        return;
      }

      if (newFreqInHz === null || newFreqInHz <= 0) return;

      console.log(`频谱图已接收频率: ${newFreqInHz.toFixed(4)} Hz`);

      // 将接收到的Hz单位频率，转换为图表当前显示的单位 (Hz, RPM, Orders)
      let freqInCurrentUnit;
      if (this.currentUnit === 'RPM') {
        freqInCurrentUnit = newFreqInHz * 60;
      } else if (this.currentUnit === 'Orders') {
        freqInCurrentUnit = (newFreqInHz * 60) / this.referenceSpeed;
      } else { // 'Hz'
        freqInCurrentUnit = newFreqInHz;
      }

      // 更新游标A和基频输入框
      this.cursor1 = freqInCurrentUnit;
      this.baseFrequency = freqInCurrentUnit;

      // 如果游标A没有显示，则自动打开它
      if (!this.showCursor1) {
        this.showCursor1 = true;
      }

      // 更新图表上的谐波等标记
      this.calculateDiff(); // 更新游标Y值
      if (this.showHarmonics) {
        this.calculateHarmonics();
      }
      this.updateChart();
    },
  },
  async mounted() {
    // 从本地存储读取保存的参考转速设置（如果有）
    const savedReferenceSpeed = localStorage.getItem('spectrumChartReferenceSpeed');
    if (savedReferenceSpeed) {
      this.referenceSpeed = Number(savedReferenceSpeed);
    }

    // 初始化幅值单位
    if (this.isSpeedMeasurement) {
      this.currentAmplitudeUnit = this.speedUnitOptions[this.speedUnitIndex];
    } else {
      this.currentAmplitudeUnit = this.amplitudeUnit;
    }

    // 等待DOM渲染完成
    await this.$nextTick();
    
    // 先初始化图表实例
    this.initChart();

    // 创建 ResizeObserver 实例
    const resizeObserver = new ResizeObserver(() => {
      if (this.chartInstance) {  // 已有判断，很好
        this.chartInstance.resize();
      }
    });

    // 监听图表容器
    if (this.$refs.chart) {  // 添加判断
      resizeObserver.observe(this.$refs.chart);
    }

    // ResizeObserver实例保存到组件属性中，便于清理
    this.resizeObserver = resizeObserver;

          // 然后加载数据
      try {
        await this.checkDataStore();
        const dataLoaded = await this.getSpectrumData();
        if (dataLoaded) {
          await this.smartcalculate();
          // 确保图表更新
          this.$nextTick(() => {
            this.updateChart();
          });
        }
      } catch (error) {
        console.error('数据加载过程中出错:', error);
      }
  },
  beforeDestroy() {
    if (this.chartInstance) {
      const zr = this.chartInstance.getZr();
      zr.off('mousedown', this.handleMouseDown);
      zr.off('mousemove', this.handleMouseMove);
      zr.off('mouseup', this.handleMouseUp);
      zr.off('globalout', this.handleMouseUp);
      this.chartInstance.dispose();
    }
    if (this.keyboardNavigation) {
        this.keyboardNavigation.destroy();
    }

    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.dataRequestDebounceTimer) {
      clearTimeout(this.dataRequestDebounceTimer);
    }
  },
  created() {
    // 从本地存储恢复轴承健康框的位置（如果有）
    const savedPosition = localStorage.getItem('bearingHealthPosition');
    if (savedPosition) {
      try {
        this.bearingHealthPosition = JSON.parse(savedPosition);
      } catch (e) {
        console.error('恢复轴承健康框位置失败', e);
      }
    }

    // 从本地存储恢复显示状态
    const savedShowStatus = localStorage.getItem('showBearingHealthPanel');
    if (savedShowStatus !== null) {
      this.showBearingHealthPanel = savedShowStatus === 'true';
    }
  },
  methods: {
    ...mapActions('tree', ['setTreePathText']),
    /* 智能计算 */
    smartcalculate() {
      try {
        if (!this.data || this.data.length === 0) {
          console.log('数据为空，无法计算');
          return;
        }
        console.log('计算轴承健康度...');

        // 清空之前的计算结果
        this.spectrumEnhancedData = [];

        // 获取设备信息
        this.machineId = this.row?.id || this.$route.query.machineId || 0;
        this.deviceId = this.row?.deviceId || this.$route.query.deviceId || 0;

        if (!this.machineId || this.machineId === 0) {
          console.warn('未找到有效的设备ID，跳过轴承健康度计算');
          return;
        }

        // 初始化计算标志
        this.isCalculating = true;

        // 开始计算流程
        this.findAndCalculateBearingHealth();
      } catch (error) {
        console.error('轴承健康度计算发生错误:', error);
        // 重置计算标志
        this.isCalculating = false;
      }
    },
    // 防抖数据更新方法
    debouncedDataUpdate(type, value) {
      if (this.dataRequestDebounceTimer) {
        clearTimeout(this.dataRequestDebounceTimer);
      }
      this.dataRequestDebounceTimer = setTimeout(async () => {
        await this.handleDataUpdate(type, value);
      }, 300);
    },
    // 统一的数据更新处理方法
  async handleDataUpdate(type, value) {
    // 如果正在加载数据，则跳过
    if (this.isDataLoading) {
      return;
    }

    try {
      this.isDataLoading = true;

      // 构建当前请求参数
      const currentParams = {
        nodeId: this.$store.state.tree.selectedTreeNode?.id,
        deviceId: this.$store.state.dataStore?.device_id,
        pointId: this.$store.state.dataStore?.point_id,
        timePoint: this.$store.state.dataStore?.time_point,
        configNodeInfo: this.configNodeInfo
      };

      // 检查参数是否与上次请求相同，避免重复请求
      const paramsKey = JSON.stringify(currentParams);
      if (this.lastRequestParams === paramsKey) {
        return;
      }
      this.lastRequestParams = paramsKey;

      // 检查必要参数
      if (!currentParams.nodeId) {
        this.error = '暂无数据';
        this.data = [];
        this.originalData = [];
        this.updateChart();
        return;
      }

      // 检查趋势数据
      let hasTrendData = false;
      try {
        hasTrendData = await dataManager.hasTrendData(currentParams.nodeId);
      } catch (err) {
        console.error('检查趋势图数据失败:', err);
        hasTrendData = false;
      }

      // 检查表格数据
      const hasTableData = currentParams.deviceId && currentParams.pointId && currentParams.timePoint;

      if (hasTrendData && hasTableData) {
        // 初始化图表（如果需要）
        if (!this.chartInstance) {
          this.initChart();
        }

        // 获取数据并更新图表
        await this.getSpectrumData();
        this.updateChart();
      } else {
        // 没有数据时的处理
        this.error = '暂无数据';
        this.data = [];
        this.originalData = [];

        if (!this.chartInstance) {
          this.initChart();
        } else {
          // 确保图表状态完全重置，清除之前的无数据状态
          this.chartInstance.clear();
          this.updateChart();
        }
      }
    } catch (error) {
      console.error('数据更新失败:', error);
      this.error = '数据加载失败';
      this.updateChart();
    } finally {
      this.isDataLoading = false;
    }
  },
    // 添加新方法，用于查找轴承信息并计算健康度
    async findAndCalculateBearingHealth() {
      try {
        // 使用从configNodeInfo解构获取的machineId
        const machineId = this.machineId;
        console.log('开始智能计算，测量类型:', this.configNodeInfo?.measureDefinitions?.measureDefineName);
        console.log('使用设备ID:', machineId);

        if (!machineId) {
          console.warn('无法获取有效的machineId，跳过轴承健康计算');
          this.isCalculating = false;
          return null;
        }

        const res = await request({
          url: '/measurePoint/measurePointManagement/withConfiguration/list',
          method: 'Get',
          params: {
            device_id: machineId,
          }
        });
        console.log('测量点列表:', res);

        const matchResult = res.rows.find(item => item.deviceId === machineId);
        if (!matchResult) {
          console.warn('未找到匹配的设备配置，跳过轴承健康计算');
          this.isCalculating = false;
          return null;
        }

        console.log('匹配的设备信息:', matchResult);
        console.log('拿到了设备ID，接下来需要通过设备id拿到组态信息');

        const configNodeInfo = await request({
          url: `/deviceConfiguration/configuration/list/${matchResult.deviceId}`,
          method: 'Get',
        });
        console.log('组态信息:', configNodeInfo);

        if (!configNodeInfo || !configNodeInfo.rows || configNodeInfo.rows.length === 0) {
          console.warn('未获取到有效的组态信息，跳过轴承健康计算');
          this.isCalculating = false;
          return null;
        }

        console.log('拿到了组态信息，接下来需要匹配到绑定的轴承');
        const bearingInfo = configNodeInfo.rows.find(item => item.deviceName === matchResult.deviceName);
        console.log('轴承信息:', bearingInfo);

        if (!bearingInfo || !bearingInfo.children || bearingInfo.children.length === 0) {
          console.warn('未找到绑定的轴承信息，跳过轴承健康计算');
          this.isCalculating = false;
          return null;
        }

        console.log('拿到了轴承信息，接下来需要提取绑定的轴承频率');
        let frequencyData = null;
        if (bearingInfo && bearingInfo.children) {
          const bearingTable = bearingInfo.children[0].table;
          console.log('轴承频率数据:', bearingTable);

          if (!bearingTable || !bearingTable.bpfo) {
            console.warn('未找到有效的轴承频率数据，跳过轴承健康计算');
            this.isCalculating = false;
            return null;
          }

          // 创建一个包含所需频率的对象
          frequencyData = {
            bpfo: bearingTable.bpfo,
            bpfi: bearingTable.bpfi,
            bsf: bearingTable.bsf,
            ftf: bearingTable.ftf
          };
        }
        console.log('提取的频率数据:', frequencyData);

        if (!bearingInfo.inputSpeed) {
          console.warn('未找到有效的轴承转速，跳过轴承健康计算');
          this.isCalculating = false;
          return null;
        }

        /* 将数据转换为频率 */
        const speedHz = bearingInfo.inputSpeed / 60;
        const Data = {
          bpfo: (frequencyData.bpfo * speedHz).toFixed(4),
          bpfi: (frequencyData.bpfi * speedHz).toFixed(4),
          bsf: (frequencyData.bsf * speedHz).toFixed(4),
          ftf: (frequencyData.ftf * speedHz).toFixed(4)
        };
        console.log('转换后的频率数据:', Data);

        // 确保已加载频谱数据
        if (!this.data || this.data.length === 0) {
          console.warn('频谱数据未加载，跳过轴承健康计算');
          this.isCalculating = false;
          return null;
        }

        // 计算轴承健康度
        const bearingHealth = this.calculateBearingHealth(Data);
        console.log('轴承健康度分析结果:');
        console.log('-------------------');
        console.log('总体健康分数:', bearingHealth.score);
        console.log('健康等级:', bearingHealth.level);
        console.log('发现的问题:', bearingHealth.issues);

        // 使用可选链和条件检查防止空引用错误
        if (bearingHealth.damageAnalysis && bearingHealth.damageAnalysis.mostLikelyDamage) {
          console.log('最可能的损伤类型:', bearingHealth.damageAnalysis.mostLikelyDamage.type);
          console.log('损伤可能性:', bearingHealth.damageAnalysis.mostLikelyDamage.probability + '%');
        } else {
          console.log('无法确定损伤类型，可能是振动水平极低');
        }

        console.log('所有损伤类型得分:', bearingHealth.damageAnalysis.allDamageTypes);
        console.log('-------------------');

        this.bearingHealth = bearingHealth;
        this.isCalculating = false;
        return bearingHealth;
      } catch (error) {
        console.error('轴承健康度计算失败:', error);
        this.isCalculating = false;
        return null;
      }
    },

    // 添加计算轴承健康度的方法
    calculateBearingHealth(bearingFreqs) {
      // 获取当前测量类型
      const measureName = this.configNodeInfo?.measureDefinitions?.measureDefineName || '';

      // 定义不同测量类型的阈值标准 (参考ISO 10816和ISO 20816标准)
      const measurementThresholds = {
        '加速度': {
          critical: 8, // m/s²
          warning: 5,
          normal: 2,
          // 添加绝对幅值门限
          minSeverity: 0.5 // 小于此值的振动不视为严重故障
        },
        '速度': {
          critical: 10, // mm/s
          warning: 6,
          normal: 3,
          // 添加绝对幅值门限，基于ISO 10816标准
          minSeverity: 0.7 // 小于此值的振动不视为严重故障
        },
        '包络': {
          critical: 2.5, // m/s²
          warning: 1.2,
          normal: 0.5,
          // 添加绝对幅值门限
          minSeverity: 0.3 // 小于此值的振动不视为严重故障
        }
      };

      // 获取对应的阈值标准
      let thresholds;
      if (measureName.includes('加速度')) {
        thresholds = measurementThresholds['加速度'];
      } else if (measureName === '速度') {
        thresholds = measurementThresholds['速度'];
      } else if (measureName.includes('包络')) {
        thresholds = measurementThresholds['包络'];
      } else {
        thresholds = measurementThresholds['加速度']; // 默认
      }

      console.log('当前测量类型:', measureName, '使用阈值标准:', thresholds);

      // 轴承损伤特征定义
      const freqTypes = {
        'bpfo': { name: '外圈损伤', harmonics: 4, sideband: true, modulation: 'ftf' },
        'bpfi': { name: '内圈损伤', harmonics: 3, sideband: true, modulation: 'ftf' },
        'bsf': { name: '滚动体损伤', harmonics: 4, sideband: true, modulation: 'ftf' },
        'ftf': { name: '保持架损伤', harmonics: 2, sideband: false }
      };

      // 噪声基线评估 - 用于对比信号强度
      const noiseBaseline = this.calculateNoiseBaseline();
      console.log('频谱噪声基线:', noiseBaseline);

      // 轴承损伤类型评估结果
      let damageTypes = {
        outerRace: { score: 0, description: '外圈损伤', features: [], harmonicMatches: [] },
        innerRace: { score: 0, description: '内圈损伤', features: [], harmonicMatches: [] },
        rollingElement: { score: 0, description: '滚动体损伤', features: [], harmonicMatches: [] },
        cage: { score: 0, description: '保持架损伤', features: [], harmonicMatches: [] }
      };

      let issues = [];
      let harmonicMatchCount = 0; // 倍频匹配计数
      let totalHarmonics = 0;     // 总计应检查的倍频数

      // 基频匹配阈值 - 基频匹配的最小幅值与噪声基线的比值
      const minAmplitudeRatio = 1.5;

      // 最大振动幅值，用于判断整体振动水平
      const maxAmplitude = Math.max(...this.data.map(point => point[1]));
      console.log('最大振动幅值:', maxAmplitude, this.amplitudeUnit);

      // 振动水平判断 - 如果整体振动水平低于minSeverity，则不可能有严重损伤
      const vibrationLevel = maxAmplitude < thresholds.minSeverity ? 'low' :
                            (maxAmplitude < thresholds.normal ? 'normal' :
                            (maxAmplitude < thresholds.warning ? 'warning' : 'high'));
      console.log('振动水平判断:', vibrationLevel);

      // 检查振动水平是否太低而不可能有严重损伤
      const lowVibrationLevel = maxAmplitude < thresholds.minSeverity;

      // 遍历每个特征频率类型(BPFO, BPFI, BSF, FTF)
      Object.entries(bearingFreqs).forEach(([type, baseFreq]) => {
        const freqInfo = freqTypes[type];
        const maxHarmonics = freqInfo.harmonics;
        totalHarmonics += maxHarmonics;

        let damageScore = 0;
        let harmonicMatches = [];
        let sidebandsFound = false;
        let featureDescription = '';
        let maxFeatureAmplitude = 0; // 特征频率及其倍频中的最大幅值

        // 计算基频信噪比
        const baseMatch = this.findNearestPeak(baseFreq);

        // 1. 检查基频匹配
        if (baseMatch && baseMatch.amplitude > noiseBaseline * minAmplitudeRatio) {
          // 基频匹配成功，继续检查倍频
          const baseNFreq = baseMatch.frequency;
          maxFeatureAmplitude = Math.max(maxFeatureAmplitude, baseMatch.amplitude);
          console.log(`${type} 基频匹配: ${baseNFreq} Hz, 幅值: ${baseMatch.amplitude.toFixed(6)} ${this.amplitudeUnit}`);

          // 计算频率匹配精度
          const baseFreqRatio = Math.abs(baseNFreq - baseFreq) / baseFreq;
          let matchScore = 0;

          if (baseFreqRatio <= 0.03) {  // 频率匹配度极高
            matchScore = 100;
            featureDescription = '基频匹配精准，';
          } else if (baseFreqRatio <= 0.05) {  // 频率匹配度高
            matchScore = 80;
            featureDescription = '基频匹配良好，';
          } else if (baseFreqRatio <= 0.08) {  // 频率匹配度中等
            matchScore = 60;
            featureDescription = '基频匹配度中等，';
          } else if (baseFreqRatio <= 0.1) {   // 频率匹配度尚可
            matchScore = 40;
            featureDescription = '基频匹配度一般，';
          } else {                           // 频率匹配度低
            matchScore = 20;
            featureDescription = '基频匹配度较低，';
            issues.push(`${freqInfo.name}的基频匹配度低`);
          }

          // 2. 检查倍频匹配 (从2倍频开始检查)
          let lastHarmonicAmplitude = baseMatch.amplitude;
          let harmonicDecay = false;  // 判断倍频幅值是否递减
          let highHarmonicFound = false;  // 是否找到高次倍频

          for (let n = 2; n <= maxHarmonics; n++) {
            const harmonicFreq = baseFreq * n;
            const harmonic = this.findNearestPeak(harmonicFreq);

            if (harmonic) {
              // 评估频率匹配精度
              const harmonicRatio = Math.abs(harmonic.frequency - harmonicFreq) / harmonicFreq;

              if (harmonicRatio <= 0.1) {  // 倍频匹配成功 (允许10%的频率偏差)
                console.log(`${type} ${n}倍频匹配: ${harmonic.frequency} Hz, 幅值: ${harmonic.amplitude.toFixed(6)} ${this.amplitudeUnit}`);
                maxFeatureAmplitude = Math.max(maxFeatureAmplitude, harmonic.amplitude);

                // 确认倍频是否高于噪声基线
                if (harmonic.amplitude > noiseBaseline * minAmplitudeRatio) {
                  harmonicMatches.push({
                    order: n,
                    frequency: harmonic.frequency,
                    amplitude: harmonic.amplitude,
                    ratio: harmonicRatio
                  });

                  harmonicMatchCount++;

                  // 判断倍频衰减模式 (正常情况下倍频幅值应逐渐衰减)
                  if (harmonic.amplitude > lastHarmonicAmplitude * 0.7) {
                    // 倍频幅值未明显衰减，可能表示严重损伤
                    if (n >= 3) {
                      highHarmonicFound = true;
                    }
                  } else {
                    harmonicDecay = true;
                  }

                  lastHarmonicAmplitude = harmonic.amplitude;
                }
              }
            }
          }

          // 3. 检查边频带
          let sidebandScore = 0;
          if (freqInfo.sideband && freqInfo.modulation) {
            const modulationFreq = Number(bearingFreqs[freqInfo.modulation]);
            if (!isNaN(modulationFreq)) {
              const sidebands = this.checkSidebands(baseNFreq, modulationFreq);
              if (sidebands.length > 0) {
                sidebandsFound = true;
                sidebandScore = Math.min(60, sidebands.length * 20); // 每个边频20分，最高60分
                featureDescription += `存在${sidebands.length}个边频带，`;

                // 记录边频带的最大幅值
                sidebands.forEach(sideband => {
                  maxFeatureAmplitude = Math.max(maxFeatureAmplitude, sideband.amplitude);
                });
              }
            }
          }

          // 4. 计算总体故障评分
          // 基础分: 基于基频匹配度
          damageScore = matchScore;

          // 加分: 基于倍频匹配数量和质量
          if (harmonicMatches.length > 0) {
            // 倍频匹配加分 (根据匹配的倍频数量，最多加50分)
            const harmonicBonus = Math.min(50, harmonicMatches.length * 15);
            damageScore += harmonicBonus;

            featureDescription += `${harmonicMatches.length}个倍频匹配，`;

            // 高次倍频有显著幅值 (损伤较严重)
            if (highHarmonicFound) {
              damageScore += 20;
              featureDescription += '高次倍频幅值显著，';
            }

            // 倍频衰减模式异常 (可能暗示损伤发展)
            if (!harmonicDecay) {
              damageScore += 20;
              featureDescription += '倍频衰减不明显，';
            }
          }

          // 边频带加分
          damageScore += sidebandScore;

          // 振动幅值评估 - 修正得分，考虑绝对振动水平
          if (lowVibrationLevel) {
            // 如果振动水平很低，无论频率匹配度如何，都不应判定为严重故障
            const amplitudeRatio = maxFeatureAmplitude / thresholds.minSeverity;
            // 按照振动幅值与门限的比例缩放得分
            damageScore = Math.min(damageScore, damageScore * amplitudeRatio);
            console.log(`${type} 幅值过低修正: 最大特征幅值 ${maxFeatureAmplitude.toFixed(6)} < 门限 ${thresholds.minSeverity}，得分调整为 ${damageScore.toFixed(1)}`);

            // 如果幅值极低，得分上限为30 (轻微损伤以下)
            if (maxFeatureAmplitude < thresholds.minSeverity * 0.1) {
              damageScore = Math.min(damageScore, 30);
              featureDescription += '幅值极低，';
            } else {
              featureDescription += '幅值较低，';
            }
          }

          // 限制最终分数在0-100之间
          damageScore = Math.min(100, Math.round(damageScore));

          // 5. 根据损伤得分确定严重程度描述
          if (damageScore >= 80) {
            featureDescription += '严重损伤';
            issues.push(`${freqInfo.name}特征显著，可能存在严重损伤`);
          } else if (damageScore >= 60) {
            featureDescription += '中度损伤';
            issues.push(`${freqInfo.name}特征明显，存在中度损伤`);
          } else if (damageScore >= 40) {
            featureDescription += '轻度损伤';
            issues.push(`${freqInfo.name}特征可见，可能存在轻度损伤`);
          } else if (damageScore >= 20) {
            featureDescription += '早期损伤';
            issues.push(`${freqInfo.name}有早期损伤迹象`);
          } else {
            featureDescription += '无明显损伤';
          }
        } else {
          console.log(`${type} 基频未匹配或幅值低于阈值`);
          featureDescription = '未检测到明显特征频率';
          issues.push(`未检测到明显的${freqInfo.name}特征频率`);
        }

        // 6. 将评估结果添加到对应损伤类型
        switch(type) {
          case 'bpfo':
            damageTypes.outerRace.score = damageScore;
            damageTypes.outerRace.features.push(featureDescription);
            damageTypes.outerRace.harmonicMatches = harmonicMatches;
            break;
          case 'bpfi':
            damageTypes.innerRace.score = damageScore;
            damageTypes.innerRace.features.push(featureDescription);
            damageTypes.innerRace.harmonicMatches = harmonicMatches;
            break;
          case 'bsf':
            damageTypes.rollingElement.score = damageScore;
            damageTypes.rollingElement.features.push(featureDescription);
            damageTypes.rollingElement.harmonicMatches = harmonicMatches;
            break;
          case 'ftf':
            damageTypes.cage.score = damageScore;
            damageTypes.cage.features.push(featureDescription);
            damageTypes.cage.harmonicMatches = harmonicMatches;
            break;
        }
      });

      // 7. 计算最可能的损伤类型
      let maxDamageType = null;
      let maxScore = 0;
      Object.entries(damageTypes).forEach(([type, data]) => {
        if (data.score > maxScore) {
          maxScore = data.score;
          maxDamageType = {
            type: data.description,
            probability: data.score,
            features: data.features,
            harmonicMatches: data.harmonicMatches
          };
        }
      });

      // 如果所有得分都很低，设置一个默认的损伤类型
      if (maxScore === 0 || maxDamageType === null) {
        // 使用外圈作为默认类型，但标记为"无明显损伤"
        maxDamageType = {
          type: damageTypes.outerRace.description,
          probability: 0,
          features: ['振动幅值极低，无明显损伤'],
          harmonicMatches: []
        };
      }

      // 8. 计算总体损伤百分比
      // 基于倍频匹配率和各损伤类型最高分，但考虑振动水平修正
      const harmonicMatchRate = totalHarmonics > 0 ? harmonicMatchCount / totalHarmonics : 0;
      let damagePercentage = Math.min(100, Math.round((maxScore * 0.7 + harmonicMatchRate * 100 * 0.3)));

      // 总体振动水平修正：如果振动水平低于警戒线，则总体损伤率不应过高
      if (lowVibrationLevel) {
        const vibrationFactor = Math.min(1, maxAmplitude / thresholds.minSeverity);
        damagePercentage = Math.round(damagePercentage * vibrationFactor);
        issues.unshift(`总体振动水平低 (${maxAmplitude.toFixed(4)} ${this.amplitudeUnit})，损伤率已修正`);
      }

      // 9. 计算健康分数 (100 - 损伤百分比)
      const healthScore = Math.max(0, 100 - damagePercentage);

      // 10. 返回健康度评估结果
      return {
        score: healthScore,
        level: this.getHealthLevel(healthScore),
        issues: issues,
        damagePercentage: damagePercentage,
        details: {
          frequencies: bearingFreqs,
          maxAmplitude: maxAmplitude,
          measureType: measureName,
          noiseBaseline: noiseBaseline,
          harmonicMatchRate: harmonicMatchRate,
          vibrationLevel: vibrationLevel
        },
        damageAnalysis: {
          mostLikelyDamage: maxDamageType || {
            type: '未知',
            probability: 0,
            features: ['数据不足，无法判断'],
            harmonicMatches: []
          },
          allDamageTypes: damageTypes
        }
      };
    },

    // 计算频谱噪声基线
    calculateNoiseBaseline() {
      if (!this.data || this.data.length < 10) return 0;

      // 对幅值进行排序
      const sortedAmplitudes = [...this.data].map(point => point[1]).sort((a, b) => a - b);

      // 取最低30%的幅值的平均值作为噪声基线
      const baselineCount = Math.floor(sortedAmplitudes.length * 0.3);
      const baselineAmplitudes = sortedAmplitudes.slice(0, baselineCount);

      return baselineAmplitudes.reduce((sum, amp) => sum + amp, 0) / baselineCount;
    },

    // 检查边频带
    checkSidebands(centerFreq, spacingFreq) {
      if (!centerFreq || !spacingFreq) return [];

      const sidebands = [];
      const maxFreq = Math.max(...this.data.map(point => point[0]));
      const noiseBaseline = this.calculateNoiseBaseline();
      const minAmplitudeRatio = 1.3; // 边频带最小幅值与噪声基线比值

      // 检查上下边频带 (最多检查3对)
      for (let i = 1; i <= 3; i++) {
        // 上边频
        const upperFreq = centerFreq + (spacingFreq * i);
        if (upperFreq < maxFreq) {
          const upperSideband = this.findNearestPeak(upperFreq);
          if (upperSideband) {
            const ratio = Math.abs(upperSideband.frequency - upperFreq) / upperFreq;
            if (ratio <= 0.1 && upperSideband.amplitude > noiseBaseline * minAmplitudeRatio) {
              sidebands.push({
                type: 'upper',
                order: i,
                frequency: upperSideband.frequency,
                amplitude: upperSideband.amplitude
              });
            }
          }
        }

        // 下边频
        const lowerFreq = centerFreq - (spacingFreq * i);
        if (lowerFreq > 0) {
          const lowerSideband = this.findNearestPeak(lowerFreq);
          if (lowerSideband) {
            const ratio = Math.abs(lowerSideband.frequency - lowerFreq) / lowerFreq;
            if (ratio <= 0.1 && lowerSideband.amplitude > noiseBaseline * minAmplitudeRatio) {
              sidebands.push({
                type: 'lower',
                order: i,
                frequency: lowerSideband.frequency,
                amplitude: lowerSideband.amplitude
              });
            }
          }
        }
      }

      return sidebands;
    },

    // 查找最接近的峰值
    findNearestPeak(targetFreq) {
      let nearestPeak = null;
      let minDiff = Infinity;
      let maxSearchRange = 0.1 * targetFreq; // 搜索范围为目标频率的±10%

      // 遍历数据点寻找峰值
      for (let i = 1; i < this.data.length - 1; i++) {
        const prev = this.data[i - 1][1];
        const curr = this.data[i][1];
        const next = this.data[i + 1][1];
        const freq = this.data[i][0];

        // 判断是否为峰值，且在搜索范围内
        if (curr > prev && curr > next && Math.abs(freq - targetFreq) <= maxSearchRange) {
          const diff = Math.abs(freq - targetFreq);

          if (diff < minDiff) {
            minDiff = diff;
            nearestPeak = {
              frequency: freq,
              amplitude: curr
            };
          }
        }
      }

      return nearestPeak;
    },

    // 添加查找谐波的方法
    findHarmonics(baseFreq) {
      const harmonics = [];
      const maxFreq = Math.max(...this.data.map(point => point[0]));

      // 查找前3次谐波
      for (let i = 2; i <= 4; i++) {
        const harmonicFreq = baseFreq * i;
        if (harmonicFreq > maxFreq) break;

        const nearestPeak = this.findNearestPeak(harmonicFreq);
        if (nearestPeak) {
          const ratio = Math.abs(nearestPeak.frequency - harmonicFreq) / harmonicFreq;
          if (ratio <= 0.1) {  // 谐波频率匹配度在10%以内
            harmonics.push({
              order: i,
              frequency: nearestPeak.frequency,
              amplitude: nearestPeak.amplitude
            });
          }
        }
      }

      return harmonics;
    },

    // 获取健康等级
    getHealthLevel(score) {
      if (score >= 90) return 'excellent';
      if (score >= 75) return 'good';
      if (score >= 60) return 'fair';
      if (score >= 40) return 'poor';
      return 'critical';
    },
    initKeyboardNavigation() {
      if (this.keyboardNavigation) {
        this.keyboardNavigation.destroy();
      }
      this.keyboardNavigation = new chartKeyboardNavigation(this.chartInstance, {
        data: this.data,
        xAxisData: this.data.map(item => item[0]),
        chartElement: this.$refs.chart,
        onPointChange: ({ xValue, yValue }) => {
          this.currentXValue = xValue;
          this.currentYValue = yValue;

          this.updateMark();
        }
      });
      this.keyboardNavigation.init();
    },
    handleToggle() {
      this.$emit('toggleExpand')
    },
    initChart() {
      if (!this.$refs.chart) {
        console.log('DOM元素不存在，无法初始化图表');
        return; // 如果DOM元素不存在，直接返回
      }
      
      console.log('开始初始化图表实例');
      
      if (this.chartInstance) { // 检查实例是否已存在
        console.log('销毁现有图表实例');
        this.chartInstance.dispose() // 销毁现有实例
      }
      
      try {
        this.chartInstance = echarts.init(this.$refs.chart)
        console.log('图表实例创建成功');
        this.updateChart()
      } catch (error) {
        console.error('图表实例创建失败:', error);
      }
      // 初始化缩放控制器
      this.zoomController = new ChartZoom(this.chartInstance)
      this.chartInstance.on('click', this.handleChartClick)
      this.chartInstance.on('dataZoom', this.handleDataZoom)

      // 添加鼠标移动事件监听
      this.chartInstance.on('mousemove', (params) => {
        if (this.isHoverMode && params.componentType === 'series') {
          const xValue = params.value[0];  // 注意这里要用 value[0]
          const yValue = params.value[1];  // 注意这里要用 value[1]
          this.currentXValue = xValue;
          this.currentYValue = yValue;
        }
      });

      // 添加鼠标离开事件监听
      this.chartInstance.on('globalout', () => {
        if (this.isHoverMode) {
          // 清除参考线和当前值
          this.chartInstance.setOption({
            graphic: []
          });
          this.currentXValue = null;
          this.currentYValue = null;
        }
      });

      // 添加事件监听
      this.chartInstance.getZr().on('mousedown', this.handleMouseDown);
      this.chartInstance.getZr().on('mousemove', this.handleMouseMove);
      this.chartInstance.getZr().on('mouseup', this.handleMouseUp);
      this.chartInstance.getZr().on('globalout', this.handleMouseUp);
    },
    handleUnitConversion({ fromUnit, toUnit }) {
      // 使用新的统一转换函数
      this.applyUnit(fromUnit, toUnit);
    },

    // 创建一个统一的 applyUnit 方法，处理所有单位转换
    applyUnit(fromUnit, toUnit) {
      // 验证输入
      if (fromUnit === toUnit) {
        return; // 无需转换
      }

      // 确保 originalData 存在且不为空
      if (!this.originalData || this.originalData.length === 0) {
        console.warn('没有可用的原始数据进行单位转换');
        return;
      }

      // 保存当前的轴范围，以便转换后保持相对位置
      const oldAxisRange = { ...this.axisRange };

      // 单位转换函数，将值从一个单位转换到另一个单位
      const convertValue = (value, from, to) => {
        // 先转成Hz
        let valueInHz;
        if (from === 'Hz') {
          valueInHz = value;
        } else if (from === 'RPM') {
          valueInHz = value / 60;
        } else if (from === 'Orders') {
          valueInHz = (value * this.referenceSpeed) / 60;
        } else if (from === 'kHz') {
          valueInHz = value * 1000;
        } else if (from === 'MHz') {
          valueInHz = value * 1000000;
        } else {
          valueInHz = value; // 默认情况
        }

        // 再从Hz转到目标单位
        let result;
        if (to === 'Hz') {
          result = valueInHz;
        } else if (to === 'RPM') {
          result = valueInHz * 60;
        } else if (to === 'Orders') {
          result = valueInHz * 60 / this.referenceSpeed;
        } else if (to === 'kHz') {
          result = valueInHz / 1000;
        } else if (to === 'MHz') {
          result = valueInHz / 1000000;
        } else {
          result = valueInHz; // 默认情况
        }

        // 根据单位选择适当的精度
        let precision;
        switch(to) {
          case 'MHz':
            precision = 6;
            break;
          case 'kHz':
            precision = 4;
            break;
          case 'RPM':
            precision = 1;
            break;
          case 'Orders':
            precision = 3;
            break;
          default: // Hz
            precision = 2;
        }

        return Number(result.toFixed(precision));
      };

      // 根据目标单位转换数据
      if (toUnit === 'Hz') {
        // 任何单位转到Hz，直接使用原始Hz数据
        this.data = [...this.originalData];
      } else if (toUnit === 'RPM') {
        // 从原始Hz数据转到RPM: RPM = Hz * 60
        this.data = this.originalData.map(point => [
          point[0] * 60, // Hz -> RPM
          point[1] // 幅值不变
        ]);
      } else if (toUnit === 'Orders') {
        // 从原始Hz数据转到Orders: Orders = Hz * 60 / 参考转速
        this.data = this.originalData.map(point => [
          point[0] * 60 / this.referenceSpeed, // Hz -> Orders
          point[1] // 幅值不变
        ]);
      } else {
        // 其他单位转换(kHz, MHz等)，从原始Hz数据转换
        const conversionFactors = {
          'Hz': 1,
          'kHz': 1000,
          'MHz': 1000000,
        };

        this.data = this.originalData.map(point => {
          const originalValue = point[0]; // 原始值(Hz)
          const convertedValue = originalValue / conversionFactors[toUnit];

          // 根据单位选择适当的精度
          let precision;
          switch(toUnit) {
            case 'MHz':
              precision = 6; // 保持更多小数位
              break;
            case 'kHz':
              precision = 4;
              break;
            case 'RPM':
              precision = 1; // RPM整数精度较合适
              break;
            case 'Orders':
              precision = 3; // Orders通常需要比较高的精度
              break;
            default: // Hz
              precision = 2;
          }

          return [
            Number(convertedValue.toFixed(precision)),
            point[1] // y轴值保持不变
          ];
        });
      }

      // 转换坐标轴范围，保持相对视图
      if (oldAxisRange && oldAxisRange.xMin !== undefined && oldAxisRange.xMax !== undefined) {
        // 转换X轴范围
        this.axisRange.xMin = convertValue(oldAxisRange.xMin, fromUnit, toUnit);
        this.axisRange.xMax = convertValue(oldAxisRange.xMax, fromUnit, toUnit);

        // Y轴范围不需要转换，因为幅值单位不变
      }

      // 更新当前单位
      this.currentUnit = toUnit;

      // 更新当前显示的 X 值
      if (this.currentXValue !== null) {
        this.currentXValue = convertValue(this.currentXValue, fromUnit, toUnit);
      }

      // 更新游标1位置
      if (this.showCursor1 && this.cursor1 !== null) {
        this.cursor1 = convertValue(this.cursor1, fromUnit, toUnit);
      }

      // 更新游标2位置（如果存在）
      if (this.showCursor2 && this.cursor2 !== null) {
        this.cursor2 = convertValue(this.cursor2, fromUnit, toUnit);
      }

      // 如果有谐波标记也需要更新
      if (this.showHarmonics && this.harmonicMarkers.length > 0) {
        this.harmonicMarkers = this.harmonicMarkers.map(marker => ({
          ...marker,
          freq: convertValue(marker.freq, fromUnit, toUnit)
        }));
      }

      // 更新图表配置和显示
      this.updateChart();
    },

    // 辅助函数：在原始数据中找到最接近指定值的点的索引（使用二分搜索提高效率）
    findClosestPointIndex(value, fromUnit) {
      if (!this.originalData || this.originalData.length === 0) {
        return -1;
      }

      // 先将值转换为Hz单位以便在原始数据中查找
      let valueInHz;
      if (fromUnit === 'Hz') {
        valueInHz = value;
      } else if (fromUnit === 'RPM') {
        valueInHz = value / 60;
      } else if (fromUnit === 'Orders') {
        valueInHz = (value * this.referenceSpeed) / 60;
      } else if (fromUnit === 'kHz') {
        valueInHz = value * 1000;
      } else if (fromUnit === 'MHz') {
        valueInHz = value * 1000000;
      } else {
        valueInHz = value; // 默认情况
      }

      // 使用二分搜索找到最接近的点
      let left = 0;
      let right = this.originalData.length - 1;

      // 如果要查找的值超出了数据范围，直接返回边界值的索引
      if (valueInHz <= this.originalData[0][0]) {
        return 0;
      }
      if (valueInHz >= this.originalData[right][0]) {
        return right;
      }

      // 二分搜索查找最接近的点
      while (left <= right) {
        const mid = Math.floor((left + right) / 2);

        if (this.originalData[mid][0] === valueInHz) {
          return mid; // 精确匹配
        }

        if (this.originalData[mid][0] < valueInHz) {
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }

      // 此时 left > right，需要比较这两个位置的值哪个更接近
      // 确保索引不越界
      const leftIndex = Math.min(left, this.originalData.length - 1);
      const rightIndex = Math.max(right, 0);

      // 计算差值
      const leftDiff = Math.abs(this.originalData[leftIndex][0] - valueInHz);
      const rightDiff = Math.abs(this.originalData[rightIndex][0] - valueInHz);

      // 返回差值较小的索引
      return leftDiff <= rightDiff ? leftIndex : rightIndex;
    },
    // 修改 toggleFrequencyUnit 方法
    toggleFrequencyUnit() {
      const fromUnit = this.currentUnit;
      let toUnit = 'Hz';

      // 循环切换: Hz -> RPM -> Orders -> Hz
      if (fromUnit === 'Hz') {
        toUnit = 'RPM';
      } else if (fromUnit === 'RPM') {
        toUnit = 'Orders';
        // 当切换到Orders单位时，显示参考转速设置对话框
        this.$nextTick(() => {
          this.showReferenceSpeedDialog = true;
        });
      } else if (fromUnit === 'Orders') {
        toUnit = 'Hz';
      }

      this.handleUnitConversion({ fromUnit, toUnit });
    },
    updateChart() {
      if (!this.chartInstance) {
        return; // 如果图表实例不存在，直接返回
      }
      
      // 获取数据中的最大 x 值
      const maxX = this.data.length > 0 ? this.data[this.data.length - 1][0] : 0;
      // 保存当前的标记线数据
      const currentMarkLines = this.chartInstance?.getOption()?.series?.[0]?.markLine?.data || [];

      // 如果有错误，则显示错误信息
      if (this.error) {
        this.chartInstance.clear();
        this.chartInstance.setOption({
          title: {
            show: true,
            text: this.error,
            textStyle: {
              color: '#999',
              fontSize: 14
            },
            left: 'center',
            top: 'center'
          },
          grid: { show: false },
          xAxis: { show: false },
          yAxis: { show: false },
          series: []
        }, true);
        return;
      }

      // 检查数据是否存在且有效
      console.log('检查数据状态:', {
        data: this.data,
        dataLength: this.data ? this.data.length : 0,
        error: this.error,
        errorType: typeof this.error
      });
      
      if (!this.data || this.data.length === 0) {
        console.log('数据为空，显示无数据提示');
        this.chartInstance.clear();
        this.chartInstance.setOption({
          title: {
            show: true,
            textStyle: {
              color: '#999',
              fontSize: 14
            },
            left: 'center',
            top: 'center'
          },
          grid: { show: false },
          xAxis: { show: false },
          yAxis: { show: false },
          series: []
        }, true);
        return;
      }

      console.log('数据存在，开始渲染图表');

      // 调整X轴标签显示
      let xAxisName = this.currentUnit;
      if (this.currentUnit === 'RPM') {
        // RPM和CPM是相同的单位，只是名称不同
        xAxisName = 'RPM (CPM)';
      } else if (this.currentUnit === 'Orders') {
        xAxisName = 'Orders';
      }

      const option = {
        animation: false,
        grid: {
          left: '0%',
          right: '3.5%',
          top: this.isExpanded ? '5%' : '14%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            /* restore: { title: '还原' }, */
          }
        },
        tooltip: {
          trigger: this.isHoverMode ? 'none' : 'axis', // 根据模式决定是否启用悬浮提示
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            },
            lineStyle: {
              color: '#6a7985',
              width: 1,
              type: 'dashed'
            }
          },
          backgroundColor: 'rgba(50,50,50,0.7)',
          borderColor: '#333',
          borderWidth: 0,
          padding: [5,10],
          textStyle: {
            color: '#fff'
          },
          formatter: (params) => {
              const data = params[0]
              if (!data) return ''
              this.currentXValue = data.value[0]
              this.currentYValue = data.value[1]
              if (this.keyboardNavigation) {
                this.keyboardNavigation.currentPointIndex = data.dataIndex;
              }
              return `频率: ${this.currentXValue} ${this.currentUnit}<br/>幅值: ${this.currentYValue} ${this.currentAmplitudeUnit}`
          }
        },
        xAxis: {
          type: 'value',
          name: xAxisName,
          show: true,
          min: this.axisRange && this.axisRange.xMin,
          max: this.axisRange && this.axisRange.xMax,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333'
            }
          },
          axisTick: {
            show: true
          },
          axisLabel: {
            show: true,
            color: '#333'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#eee'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: this.currentAmplitudeUnit,
          nameLocation: 'end',
          show: true,
          min: this.axisRange && this.axisRange.yMin,
          max: this.axisRange && this.axisRange.yMax,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333'
            }
          },
          axisTick: {
            show: true
          },
          axisLabel: {
            show: true,
            color: '#333',
            formatter: (value) => {
              return value.toFixed(4)
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#eee'
            }
          }
        },
        series: [
          {
            type: 'line',
            Smooth: true,
            animation: false,
            sampling: 'none',
            symbolSize: 3,
            showSymbol: false,
            data: this.data,
            itemStyle: {
              color: '#4985DF'
            },
            lineStyle: {
              color: '#4985DF'
            },
            emphasis: {
              scale: true,
              focus: 'series'
            },
            markLine: {
              symbol: ['none', 'arrow'],
              symbolSize: [5,5],
              animation: false,
              snap: true,
              lineStyle: {
                width: 1,
                type: 'solid'
              },
              data: [
                // 游标A
                ...(this.showCursor1 && this.cursor1 !== null ? [[
                  {
                    coord: [this.cursor1, this.getYAxisExtent()[1]],
                    lineStyle: {
                      color: 'red',
                      type: 'solid'
                    },
                    label: {
                      formatter: `A: ${this.cursor1.toFixed(4)}${this.currentUnit}`,
                      position: 'start'
                    }
                  },
                  {
                    coord: [this.cursor1, this.cursor1Y || this.getYAxisExtent()[0]],
                    lineStyle: {
                      color: 'red',
                      type: 'solid'
                    }
                  }
                ]] : []),
                // 谐波标记
                ...(this.showHarmonics ? this.harmonicMarkers.map(marker => [
                  {
                    coord: [marker.freq, this.getYAxisExtent()[1]],
                    lineStyle: {
                      color: '#ffa500',
                      type: 'dashed'
                    },
                    label: {
                      formatter: `${marker.order}X`,
                      position: 'start'
                    }
                  },
                  {
                    coord: [marker.freq, marker.amp],
                    lineStyle: {
                      color: '#ffa500',
                      type: 'dashed'
                    }
                  }
                ]) : []),
                // 修改边频标记的处理
                ...(this.showSidebands && this.cursor1 ? (() => {
                  // 清除旧的边频标记
                  const sidebands = this.calculateSidebands(this.cursor1);
                  return sidebands.map(sideband => [{
                    coord: [sideband.freq, this.getYAxisExtent()[1]],
                    lineStyle: {
                      color: '#9254de',
                      type: 'dashed'
                    },
                    label: {
                      formatter: `${sideband.freq.toFixed(2)}${this.currentUnit}`,
                      position: 'start'
                    }
                  },
                  {
                    coord: [sideband.freq, sideband.amp],
                    lineStyle: {
                      color: '#9254de',
                      type: 'dashed'
                    }
                  }])
                })() : []),
                // 保留轴承频率标记
                ...currentMarkLines.filter(mark =>
                  Array.isArray(mark) &&
                  mark[0]?.lineStyle?.color === '#9254de' &&
                  mark[0]?.label?.formatter?.includes('X——') // 只保留轴承特征频率标记
                )
              ]
            }
          }
        ]
      }

      console.log('设置ECharts配置:', option);
      
      try {
        this.chartInstance.setOption(option, {
          replaceMerge: ['series']
        });
        console.log('ECharts配置设置成功');
        
        // 检查图表容器大小
        const container = this.$refs.chart;
        console.log('图表容器信息:', {
          offsetWidth: container.offsetWidth,
          offsetHeight: container.offsetHeight,
          clientWidth: container.clientWidth,
          clientHeight: container.clientHeight,
          style: container.style.cssText
        });
        
        // 强制重新渲染
        this.chartInstance.resize();
        
        this.initKeyboardNavigation()
      } catch (error) {
        console.error('ECharts配置设置失败:', error);
      }

      // 尝试让图表容器获取焦点
      this.$nextTick(() => {
        if (this.$refs.chart) {
          this.$refs.chart.focus();
        }
      });
    },
    getYAxisExtent() {
      try {
        if (!this.chartInstance) return [0, 0];
        const yAxis = this.chartInstance.getModel().getComponent('yAxis', 0);
        if (!yAxis || !yAxis.axis) return [0, 0];
        return yAxis.axis.scale.getExtent();
      } catch (error) {
        console.warn('获取Y轴范围失败:', error);
        return [0, 0];
      }
    },
    // 处理图表点击事件
    handleChartClick(params) {
      if (!this.isHoverMode && params.componentType === 'series') {
        const xValue = params.value[0]
        const yValue = params.value[1]
        this.currentXValue = xValue
        this.currentYValue = yValue
        // 更新当前选中点
        this.currentSelectedPoint = { x: this.currentXValue, y: this.currentYValue }
        //打印下试试
        console.log(this.currentSelectedPoint);
      }else if(params.componentType === 'markPoint') {
        this.clearMark();
        return;
      }else if(params.componentType === 'markLine') {
        return;
      }
      // 更新标记
      this.updateMark()
    },
    // 更新标记
    updateMark(){
      if (!this.currentSelectedPoint) return

      if (!this.chartInstance) return;
      const { x, y } = this.currentSelectedPoint
      const yAxisRange = this.chartInstance.getModel().getComponent('yAxis', 0).axis.scale.getExtent()
      this.chartInstance.setOption({
          series: [{
            markLine: {
              silent: false,
              animation: false,
              symbol: ['none', 'none'],
              data: [[
                { xAxis: x, yAxis: yAxisRange[0] },
                {
                  xAxis: x,
                  yAxis: yAxisRange[1],
                  lineStyle: {
                    color: '#FF0000',
                    width: 1,
                    type: 'dashed'
                  }
                }
              ]]
            },
            markPoint: {
              animation: false,
              data: [{
                coord: [x, y],
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                  color: '#FF0000'
                },
                label: {
                  show: true,
                  formatter: `频率: ${x} ${this.currentUnit}\n幅度: ${y} ${this.currentAmplitudeUnit}`,
                  position: 'right',
                  backgroundColor: 'rgba(50, 50, 50, 0.9)',
                  padding: [5, 10],
                  borderRadius: 4,
                  color: '#fff'
                }
              }]
            }
          }]
      })
    },
    // 清除标记
    clearMark() {
      if (!this.chartInstance) return;
      this.chartInstance.setOption({
        series: [{
          markLine: {
            data: []
          },
          markPoint: {
            data: []
          }
        }]
      })
      this.currentSelectedPoint = null
    },
    // 处理数据缩放事件
    handleDataZoom(params) {
      // 更新游标位置
      if (this.showCursor1 || this.showCursor2) {
        this.calculateDiff();
        this.updateChart();
      }
    },

    async getSpectrumData() {
      try {
        const dataStore = this.$store.state.dataStore
        if (!dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
          this.error = '暂无数据'
          this.data = []
          this.origindata = []
          this.originalData = []
          return false
        }

        // 先检查趋势图数据
        let hasTrendData = false
        try {
          // 获取当前节点ID
          const nodeId = this.$store.state.tree.selectedTreeNode?.id
          if (nodeId) {
            // 使用数据管理器检查趋势数据，避免重复请求
            hasTrendData = await dataManager.hasTrendData(nodeId)
          }
        } catch (err) {
          console.error('检查趋势图数据失败:', err)
          hasTrendData = false
        }

        // 如果趋势图没有数据，则不获取频谱数据
        if (!hasTrendData) {
          this.error = '暂无数据'
          this.data = []
          this.origindata = []
          this.originalData = []
          return false
        }

        const params = await getParams(2) // 频谱图ftype为2

        // 针对包络测量类型进行特殊处理
        if (this.configNodeInfo?.measureDefinitions?.measureDefineName.includes('包络')) {
          params.band = this.configNodeInfo.measureDefinitions.envelopeFrequencyDetail.split('-').map(Number)
          params.ftype = 4
        }
        // 将本次请求的参数保存起来，供"临时修改"功能使用
        this.temporaryData = params;
        // 获取频谱图数据
        const result = await getWaveform(params)
        console.log('API返回结果:', result);
        
        if (!result || !result.data || !result.data.x || !result.data.y) {
          this.error = '暂无数据'
          this.data = []
          this.origindata = []
          this.originalData = []
          return false
        }

        // 合并频率和幅度数据为一个数组
        this.data = result.data.x.map((xValue, index) => [
          xValue,
          result.data.y[index]
        ]);
        this.origindata = [...this.data]; // 同时更新origindata
        this.originalData = [...this.data] // 保存原始数据用于单位转换

        console.log('数据加载成功:', {
          dataLength: this.data.length,
          dataSample: this.data.slice(0, 3),
          xRange: [result.data.x[0], result.data.x[result.data.x.length - 1]],
          yRange: [Math.min(...result.data.y), Math.max(...result.data.y)]
        });

        // 保存当前的幅值单位
        if (this.isSpeedMeasurement) {
          this.currentAmplitudeUnit = this.speedUnitOptions[this.speedUnitIndex];
        } else {
          this.currentAmplitudeUnit = this.amplitudeUnit;
        }

        // 初始化坐标轴范围
        this.initDefaultAxisRange();

        // 清除错误状态
        this.error = null
        console.log('错误状态已清除:', this.error);

        console.log('数据设置完成，准备更新图表');
        this.updateChart()
        return true
      } catch (error) {
        console.error('频谱图数据加载失败:', error);
        this.error = '暂无数据'
        // 清空数据
        this.data = []
        this.origindata = []
        this.originalData = []
        return false
      }
    },
    async checkDataStore() {
      const dataStore = this.$store.state.dataStore
      if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
        return new Promise(resolve => {
          const interval = setInterval(() => {
            if (this.$store.state.dataStore && this.$store.state.dataStore.device_id) {
              clearInterval(interval)
              resolve()
            }
          }, 100)
        })
      }
    },
    // 添加游标相关方法
    toggleCursor(cursorNum) {
      if (cursorNum === 1) {
        this.showCursor1 = !this.showCursor1;
        // 设置初始游标位置为当前数据范围的1/4处
        if (this.showCursor1 && this.cursor1 === null && this.data.length > 0) {
          this.cursor1 = this.data[Math.floor(this.data.length / 10)][0];

          // 当激活游标A时，让图表获取焦点
          this.$nextTick(() => {
            if (this.$refs.chart) {
              this.$refs.chart.focus();
            }
          });
        }
        // 关闭游标A时，同时关闭边频标记和谐波标记
        if (!this.showCursor1) {
          this.showSidebands = false;
          this.showHarmonics = false; // 关闭谐波标记
          this.harmonicMarkers = []; // 清空谐波标记数据
        }
      }
      this.updateChart();
      // this.calculateDiff(); // 不再需要计算差值
    },
    // 修改 calculateDiff 方法
    calculateDiff() {
      // 更新游标A的Y值
      if (this.showCursor1 && this.cursor1 !== null) {
        const point = this.findNearestDataPoint(this.cursor1);
        if (point) {
          this.cursor1Y = point[1];
        }
      }

      // 更新游标B的Y值
      if (this.showCursor2 && this.cursor2 !== null) {
        const point = this.findNearestDataPoint(this.cursor2);
        if (point) {
          this.cursor2Y = point[1];
        }
      }

      // 计算差值
      if (this.showCursor1 && this.showCursor2 && this.cursor1 !== null && this.cursor2 !== null) {
        this.freqDiff = Math.abs(this.cursor2 - this.cursor1).toFixed(2);
        if (this.cursor1Y !== null && this.cursor2Y !== null) {
          this.ampDiff = Math.abs(this.cursor2Y - this.cursor1Y).toFixed(3);
        }
      } else {
        this.freqDiff = 0;
        this.ampDiff = 0;
      }
    },
    // 判断鼠标是否在游标附近
    isNearCursor(mouseX, cursorX) {
      if (!this.chartInstance || cursorX === null) return false;
      // 获取游标位置的像素坐标
      if (!this.chartInstance) return false;
      const cursorPixel = this.chartInstance.convertToPixel({xAxisIndex: 0}, cursorX);
      return Math.abs(mouseX - cursorPixel) <= 5; // 5像素的判定范围
    },

    // 新增：判断鼠标是否在谐波线附近
    isNearHarmonic(mouseX, harmonicMarker) {
      if (!this.chartInstance || !harmonicMarker || harmonicMarker.freq === null) return false;
      const harmonicPixelX = this.chartInstance.convertToPixel({ xAxisIndex: 0 }, harmonicMarker.freq);
      if (harmonicPixelX === null || harmonicPixelX === undefined) return false;
      return Math.abs(mouseX - harmonicPixelX) <= 5; // 5像素的判定范围
    },

    // 处理鼠标按下事件
    handleMouseDown(e) {
      if (!this.chartInstance) return;

      // 优先检查是否点击在谐波线附近
      if (this.showHarmonics) {
        for (const marker of this.harmonicMarkers) {
          if (this.isNearHarmonic(e.offsetX, marker)) {
            this.isDraggingHarmonic = true;
            this.activeHarmonicOrder = marker.order;
            this.$refs.chart.style.cursor = 'ew-resize';
            // 阻止默认的图表点击行为，避免与图表自身的点击事件冲突
            if (e.event) {
              e.event.preventDefault();
              e.event.stopPropagation();
            }
            return;
          }
        }
      }

      // 检查是否点击在游标附近
      if (this.showCursor1 && this.isNearCursor(e.offsetX, this.cursor1)) {
        this.isDragging = true;
        this.activeCursor = 1;
        this.$refs.chart.style.cursor = 'ew-resize';
      } else if (this.showCursor2 && this.isNearCursor(e.offsetX, this.cursor2)) {
        this.isDragging = true;
        this.activeCursor = 2;
        this.$refs.chart.style.cursor = 'ew-resize';
      } else if (this.showSidebands) {
        // 检查是否点击在第一条边频附近
        const centerFreq = this.cursor1 || this.cursor2;
        if (centerFreq) {
          const upperSideband = centerFreq + this.sidebandSpacing;
          const lowerSideband = centerFreq - this.sidebandSpacing;

          if (this.isNearFrequency(e.offsetX, upperSideband) ||
              this.isNearFrequency(e.offsetX, lowerSideband)) {
            this.isDraggingSideband = true;
            this.$refs.chart.style.cursor = 'ew-resize';
          }
        }
      }
    },

    // 添加一个查找最近数据点的方法
    findNearestDataPoint(xValue) {
      if (!this.data || this.data.length === 0) return null;

      // 使用二分查找找到最近的点
      let left = 0;
      let right = this.data.length - 1;

      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const midX = this.data[mid][0];

        if (midX === xValue) {
          return this.data[mid];
        }

        if (midX < xValue) {
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }

      // 比较右两个点，找出最近的
      const leftPoint = this.data[Math.max(0, right)];
      const rightPoint = this.data[Math.min(this.data.length - 1, left)];

      if (!leftPoint) return rightPoint;
      if (!rightPoint) return leftPoint;

      return Math.abs(leftPoint[0] - xValue) <= Math.abs(rightPoint[0] - xValue)
        ? leftPoint
        : rightPoint;
    },

    // 修改 handleMouseMove 方法
    handleMouseMove(e) {
      if (!this.chartInstance) return;

      if (this.isDragging && this.activeCursor === 1 ) {
        // 获取鼠标位置对应的数据值
        if (!this.chartInstance) return;
        const xValue = this.chartInstance.convertFromPixel({xAxisIndex: 0}, e.offsetX);
        const step = this.fixedCursorStep;
        const precision = (step.toString().split('.')[1] || '').length;
        // 将鼠标位置吸附到最近的固定步长倍数
        let newCursorX = Math.round(xValue / step) * step;
        let minX = -Infinity, maxX = Infinity;
        if (this.axisRange.xMin !== null) minX = this.axisRange.xMin;
        else if (this.data && this.data.length > 0) minX = this.data[0][0];
        if (this.axisRange.xMax !== null) maxX = this.axisRange.xMax;
        else if (this.data && this.data.length > 0) maxX = this.data[this.data.length - 1][0];
        if (minX !== -Infinity && maxX !== Infinity) {
          newCursorX = Math.max(minX, Math.min(maxX, newCursorX));
        }
        this.cursor1 = parseFloat(newCursorX.toFixed(precision));
        this.cursor1Y = this.getInterpolatedYForCursor(this.cursor1);

        // Dispatch to Vuex store, ensuring value is in Hz
        if (this.cursor1 !== null) {
          let frequencyInHz = this.cursor1;
          if (this.currentUnit === 'RPM') {
            frequencyInHz = this.cursor1 / 60;
          } else if (this.currentUnit === 'Orders') {
            if (this.referenceSpeed && this.referenceSpeed !== 0) {
              frequencyInHz = (this.cursor1 * this.referenceSpeed) / 60;
            } else {
              console.error("SpectrumChart: Reference speed for Orders is not set or zero. Cannot dispatch to Vuex during drag.");
              frequencyInHz = undefined; // 避免 dispatch 错误的值
            }
          }
          if (frequencyInHz !== undefined) {
            this._isUpdatingFromSelf = true;
            this.$store.dispatch('chartLink/setBaseFrequency', frequencyInHz);
            this.$nextTick(() => {
                this._isUpdatingFromSelf = false;
            });
          }
        }

        // 如果谐波标记启用,更新谐波
        if (this.showHarmonics) {
            this.baseFrequency = this.cursor1;
            this.calculateHarmonics();
        }

        // 更新图表和差值
        this.updateChart();
        this.calculateDiff();
      } else if (this.isDraggingHarmonic && this.activeHarmonicOrder) {
        if (!this.chartInstance) return;
        let harmonicTargetFreqAtMouse = this.chartInstance.convertFromPixel({xAxisIndex: 0}, e.offsetX);
        if (harmonicTargetFreqAtMouse === null || harmonicTargetFreqAtMouse === undefined) return;

        let impliedBaseFrequency = harmonicTargetFreqAtMouse / this.activeHarmonicOrder;

        const step = this.fixedCursorStep;
        const precision = (step.toString().split('.')[1] || '').length;

        let newSnappedBaseFrequency = Math.round(impliedBaseFrequency / step) * step;

        let minXData = -Infinity, maxXData = Infinity;
        if (this.data && this.data.length > 0) {
            minXData = this.data[0][0];
            maxXData = this.data[this.data.length - 1][0];
        }

        let clampedMinBase = minXData;
        if (this.axisRange.xMin !== null) {
            clampedMinBase = Math.max(minXData, this.axisRange.xMin);
        }
         // 确保最小基频不为负
        clampedMinBase = Math.max(0, clampedMinBase);


        let clampedMaxDataRange = maxXData;
        if (this.axisRange.xMax !== null) {
            clampedMaxDataRange = Math.min(maxXData, this.axisRange.xMax);
        }

        let clampedMaxBase = clampedMaxDataRange;
        if (this.harmonicCount > 0 && clampedMaxDataRange !== Infinity) {
             // 最高次谐波的频率是 base * harmonicCount
             // 所以 base <= clampedMaxDataRange / harmonicCount
            clampedMaxBase = clampedMaxDataRange / this.harmonicCount;
        }


        newSnappedBaseFrequency = Math.max(clampedMinBase, Math.min(clampedMaxBase, newSnappedBaseFrequency));
        newSnappedBaseFrequency = parseFloat(newSnappedBaseFrequency.toFixed(precision));

        if (isNaN(newSnappedBaseFrequency)) return; // 防止无效值

        this.cursor1 = newSnappedBaseFrequency;
        this.baseFrequency = this.cursor1;

        const nearestBasePoint = this.findNearestDataPoint(this.cursor1);
        if (nearestBasePoint) {
            this.baseAmplitude = nearestBasePoint[1];
        } else {
            this.baseAmplitude = null;
        }

        if (this.cursor1 !== null) {
            let frequencyInHz = this.cursor1;
            if (this.currentUnit === 'RPM') {
                frequencyInHz = this.cursor1 / 60;
            } else if (this.currentUnit === 'Orders') {
                if (this.referenceSpeed && this.referenceSpeed !== 0) {
                    frequencyInHz = (this.cursor1 * this.referenceSpeed) / 60;
                } else {
                    frequencyInHz = undefined;
                }
            }
            if (frequencyInHz !== undefined) {
                this._isUpdatingFromSelf = true; // <-- 在 dispatch 前设置标志位
                this.$store.dispatch('chartLink/setBaseFrequency', frequencyInHz);
                this.$nextTick(() => {
                    this._isUpdatingFromSelf = false;
                });
            }
        }

        this.calculateHarmonics();
        this.updateChart();
        this.calculateDiff();

      } else if (this.isDraggingSideband) {
        if (!this.chartInstance) return;
        const xValue = this.chartInstance.convertFromPixel({xAxisIndex: 0}, e.offsetX);
        const centerFreq = this.cursor1 || this.cursor2;
        if (centerFreq) {
          // 计算新的边频间隔
          this.sidebandSpacing = Math.abs(xValue - centerFreq);
          // 直接更新图表，而不是累加边频线
          this.updateChart();
        }
      } else {
        // 检查鼠标是否悬停在游标或第一条边频附近
        const centerFreq = this.cursor1 || this.cursor2;
        let onElement = false;

        if (this.showHarmonics) {
          for (const marker of this.harmonicMarkers) {
            if (this.isNearHarmonic(e.offsetX, marker)) {
              this.$refs.chart.style.cursor = 'ew-resize';
              onElement = true;
              break;
            }
          }
        }

        if (!onElement && (this.showCursor1 && this.isNearCursor(e.offsetX, this.cursor1) ||
            this.showCursor2 && this.isNearCursor(e.offsetX, this.cursor2))) {
          this.$refs.chart.style.cursor = 'ew-resize';
          onElement = true;
        }

        if (!onElement && centerFreq && this.showSidebands && this.sidebandSpacing) {
          const upperSideband = centerFreq + this.sidebandSpacing;
          const lowerSideband = centerFreq - this.sidebandSpacing;

          if (this.isNearFrequency(e.offsetX, upperSideband) ||
              this.isNearFrequency(e.offsetX, lowerSideband)) {
            this.$refs.chart.style.cursor = 'ew-resize';
            onElement = true;
          }
        }

        if (!onElement) {
          this.$refs.chart.style.cursor = 'default';
        }
      }
    },
    getInterpolatedYForCursor(targetX) {
      // 检查输入和数据有效性
      if (targetX === null || targetX === undefined || !this.data || this.data.length === 0) {
        const yExtent = this.getYAxisExtent(); // 尝试获取Y轴的当前显示范围
        // 如果无法获取范围或数据无效，返回0或Y轴底部，或者null表示无法确定
        return yExtent && yExtent.length === 2 ? yExtent[0] : 0;
      }

      // 确保 this.data 中的数据点是 [x, y] 格式
      // 并且假定 this.data 是按 x 值 (this.data[i][0]) 升序排列的

      // 处理 targetX 超出数据范围的情况
      if (targetX < this.data[0][0]) {
        return this.data[0][1]; // 小于最小X值，返回第一个点的Y值
      }
      if (targetX > this.data[this.data.length - 1][0]) {
        return this.data[this.data.length - 1][1]; // 大于最大X值，返回最后一个点的Y值
      }

      // 查找 targetX 两侧的数据点 p1 (左侧或精确匹配) 和 p2 (右侧)
      let p1 = null;
      let p2 = null;

      for (let i = 0; i < this.data.length; i++) {
        const currentPoint = this.data[i];
        const currentX = currentPoint[0];
        const currentY = currentPoint[1];

        if (currentX === targetX) {
          // 精确匹配到数据点
          return currentY;
        }

        if (currentX < targetX) {
          p1 = currentPoint; // 更新左侧点
        } else { // currentX > targetX
          p2 = currentPoint; // 找到第一个右侧点
          break; // 因为数据已排序，后续的点X值更大，无需继续查找
        }
      }

      // 进行线性插值
      if (p1 && p2) {
        const x1 = p1[0];
        const y1 = p1[1];
        const x2 = p2[0];
        const y2 = p2[1];

        // 避免除以零，尽管在排序且X值不同的数据中不太可能发生
        if (x2 === x1) {
          return y1; // 或者 (y1 + y2) / 2
        }

        // 线性插值公式: y = y1 + (y2 - y1) * (targetX - x1) / (x2 - x1)
        const interpolatedY = y1 + (y2 - y1) * (targetX - x1) / (x2 - x1);
        return interpolatedY;
      } else if (p1) {
        // 理论上，如果 targetX 在数据范围内，且不精确匹配，p1 和 p2 都应该能找到。
        // 但作为防御，如果只有 p1 (意味着 targetX 可能等于最后一个点的X，但上面精确匹配已处理)
        // 或者某种原因 p2 没找到，就返回 p1 的Y值。
        return p1[1];
      } else if (p2) {
        // 只有p2 (意味着 targetX 可能等于第一个点的X，但上面精确匹配已处理)
        // 或者p1没找到，返回p2的Y值。
        return p2[1];
      }

      // 如果以上情况都未命中（理论上不应该发生，除非数据非常特殊或为空，但前面已检查）
      // 返回一个默认值
      const yExtentFallback = this.getYAxisExtent();
      return yExtentFallback && yExtentFallback.length === 2 ? yExtentFallback[0] : 0;
    },
    // 修改 handleMouseUp 方法
    handleMouseUp() {
      this.isDragging = false;
      this.isDraggingSideband = false;
      this.activeCursor = null;
      this.isDraggingHarmonic = false; // 新增：重置拖动谐波状态
      this.activeHarmonicOrder = null; // 新增：重置激活的谐波阶数
      this.$refs.chart.style.cursor = 'default';
    },

    // 切换谐波显示
    toggleHarmonics() {
      if (!this.showCursor1) {
        this.$message.warning('请先启用游标A');
        return;
      }

      this.showHarmonics = !this.showHarmonics;
      if (this.showHarmonics) {
        // 如果游标A启用,使用游标A的位置作为基频
        if (this.showCursor1) {
          this.baseFrequency = this.cursor1;
        }
        this.calculateHarmonics();
      } else {
        this.harmonicMarkers = [];
      }
      this.updateChart();
    },

    // 设置基频
    handleBaseFrequencyEnter() {
      if (!this.baseFrequency) return;

      // 找到最接近基频的数据点
      const nearestPoint = this.findNearestDataPoint(this.baseFrequency);
      if (nearestPoint) {
        this.baseAmplitude = nearestPoint[1];
        effectiveBaseFreq = nearestPoint[0]; // 使用实际吸附到的数据点的值作为有效基频

        if (this.showCursor1) {
          this.cursor1 = nearestPoint[0];
          this.cursor1Y = nearestPoint[1];
          // calculateDiff 和 updateChart 会在后面统一调用
        }
      } else {
        // 如果没有吸附到点（例如输入值超出范围），依然使用用户输入的值
        this.baseAmplitude = null; // 无法确定幅值
      }

      // Dispatch the effective base frequency to Vuex, ensuring it's in Hz.
      // effectiveBaseFreq 当前单位是 this.currentUnit
      let frequencyInHz = effectiveBaseFreq;
      if (this.currentUnit === 'RPM') {
        frequencyInHz = effectiveBaseFreq / 60;
      } else if (this.currentUnit === 'Orders') {
        if (this.referenceSpeed && this.referenceSpeed !== 0) {
          frequencyInHz = (effectiveBaseFreq * this.referenceSpeed) / 60;
        } else {
          console.error("SpectrumChart: Reference speed for Orders is not set or zero. Cannot dispatch from BaseFrequencyEnter.");
          frequencyInHz = undefined; // 阻止 dispatch
        }
      }

      if (frequencyInHz !== undefined) {
        this._isUpdatingFromSelf = true; // <-- 在 dispatch 前设置标志位
        this.$store.dispatch('chartLink/setBaseFrequency', frequencyInHz);
        this.$nextTick(() => {
            this._isUpdatingFromSelf = false;
        });
      }

      this.calculateHarmonics(); // 谐波计算应使用 this.baseFrequency (v-model绑定的值，单位是currentUnit)
      this.updateChart();
      // 如果游标A是激活的，并且其位置因基频输入而改变，重新计算差值
      if (this.showCursor1 && nearestPoint && this.cursor1 === nearestPoint[0]) {
        this.calculateDiff();
      }
    },

    // 计算谐波标记
    calculateHarmonics() {
      // 使用游标A的位置作为基频
      if (this.showCursor1) {
        this.baseFrequency = this.cursor1;
        // 更新基频幅值
        const nearestPoint = this.findNearestDataPoint(this.cursor1);
        if(nearestPoint) {
          this.baseAmplitude = nearestPoint[1];
        }
      }

      if (!this.baseFrequency) return;

      this.harmonicMarkers = [];
      const maxX = this.data[this.data.length - 1][0];

      // 使用设置的谐波数量
      for(let i = 2; i <= this.harmonicCount; i++) {
        const harmonicFreq = this.baseFrequency * i;
        if(harmonicFreq > maxX) break;

        // 找到最接近谐波频率的实际数据点
        const nearestPoint = this.findNearestDataPoint(harmonicFreq);
        if(nearestPoint) {
          this.harmonicMarkers.push({
            freq: nearestPoint[0],
            amp: nearestPoint[1],
            order: i
          });
        }
      }
    },
    // 切换边频显示
    toggleSidebands() {
      if (!this.showCursor1) {
        this.$message.warning('请先启用游标A');
        return;
      }
      this.showSidebands = !this.showSidebands;
      if (this.showSidebands && !this.sidebandSpacing) {
        // 默认边频间隔为数据范围的 5%
        const maxX = this.data[this.data.length - 1][0];
        this.sidebandSpacing = maxX * 0.05;
      }
      this.updateChart();
    },

    // 计算边频标记
    calculateSidebands(centerFreq) {
      if (!centerFreq || !this.sidebandSpacing) return [];

      const sidebands = [];
      for (let i = 1; i <= this.sidebandCount; i++) {
        // 上边频
        const upperFreq = centerFreq + (this.sidebandSpacing * i);
        const nearestUpperPoint = this.findNearestDataPoint(upperFreq);
        if (nearestUpperPoint) {
          sidebands.push({
            freq: nearestUpperPoint[0],
            amp: nearestUpperPoint[1],
            order: i,
            type: 'upper'
          });
        }

        // 下边频
        const lowerFreq = centerFreq - (this.sidebandSpacing * i);
        if (lowerFreq > 0) { // 确保频率为正
          const nearestLowerPoint = this.findNearestDataPoint(lowerFreq);
          if (nearestLowerPoint) {
            sidebands.push({
              freq: nearestLowerPoint[0],
              amp: nearestLowerPoint[1],
              order: i,
              type: 'lower'
            });
          }
        }
      }
      return sidebands;
    },

    // 添加判断是否接近某个频率点的方法
    isNearFrequency(mouseX, freq) {
      if (!this.chartInstance) return false;
      const freqPixel = this.chartInstance.convertToPixel({xAxisIndex: 0}, freq);
      return Math.abs(mouseX - freqPixel) <= 5;
    },
    handleHarmonicCountChange() {
      if (!this.harmonicCount || this.harmonicCount < 1) {
        this.harmonicCount = 1;
      } else if (this.harmonicCount > 20) {
        this.harmonicCount = 20;
      }

      if (this.showHarmonics) {
        this.calculateHarmonics();
        this.updateChart();
      }
    },
    handleSidebandCountChange() {
      if (!this.sidebandCount || this.sidebandCount < 1) {
        this.sidebandCount = 1;
      } else if (this.sidebandCount > 10) {
        this.sidebandCount = 10;
      }
      this.updateChart();
    },
    handleSidebandSpacingChange() {
      if (!this.sidebandSpacing || this.sidebandSpacing <= 0) {
        // 默认边频间隔为数据范围的 5%
        const maxX = this.data[this.data.length - 1][0];
        this.sidebandSpacing = maxX * 0.05;
      }
      this.updateChart();
    },
    // 添加鼠标进入和离开图表容器的处理方法
    handleChartMouseEnter() {
      this.isChartFocused = true;
    },

    handleChartMouseLeave() {
      this.isChartFocused = false;
    },
    // 添加轴承按钮点击处理方法
    handleBearingClick() {
      this.showBearingDialog = true;
    },

    handleGearAnalysisClick() {
      this.showGearAnalysisDialog = true;
    },

    startDragTempDialog(event) {
      const dialog = this.$refs.tempModifyDialogRef
      const startX = event.clientX - dialog.offsetLeft
      const startY = event.clientY - dialog.offsetTop

      const handleMouseMove = (e) => {
        dialog.style.left = `${e.clientX - startX}px`
        dialog.style.top = `${e.clientY - startY}px`
      }

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    },
    openTempModifyDialog() {
      // 1. 安全检查，确保有基础请求参数可用
      if (!this.temporaryData) {
        this.$message.error('无法打开临时修改，缺少基础请求参数。');
        return;
      }

      // 2. 从保存的请求参数中获取 fs_lines
      const fsLines = this.temporaryData.fs_lines;

      // 3. 检查范围数据是否有效，并填充表单
      if (Array.isArray(fsLines) && fsLines.length === 2) {
        const [maxVal, minVal] = fsLines;

        // 特殊处理 [0, 0] 的情况，这通常代表后端自动计算范围。
        // 在这种情况下，我们用当前图表的实际数据范围来填充。
        if (maxVal === 0 && minVal === 0 && this.data.length > 0) {
          this.tempModifyForm.rangeMin = this.data[0][0];
          this.tempModifyForm.rangeMax = this.data[this.data.length - 1][0];
        } else {
          // 正常情况，用参数里的值填充表单
          this.tempModifyForm.rangeMin = minVal;
          this.tempModifyForm.rangeMax = maxVal;
        }
      } else {
        // 如果 fs_lines 格式不正确，使用当前图表的实际范围作为备用方案
        if (this.data.length > 0) {
            this.tempModifyForm.rangeMin = this.data[0][0];
            this.tempModifyForm.rangeMax = this.data[this.data.length - 1][0];
        } else {
            // 如果没有任何可用的范围信息，则清空表单
            this.tempModifyForm.rangeMin = null;
            this.tempModifyForm.rangeMax = null;
        }
      }

      // 4. 最后，在数据准备好之后，才显示对话框
      this.showTempModifyDialog = true;
    },
    async handleTempModify() {
      try{
        // 在这里处理临时修改的逻辑
        console.log('临时数据:', this.temporaryData)
        // 将临时数据中的参数应用到图表配置中
        const params = { ...this.temporaryData };

        // 确保范围值在有效区间内
        const minValue = Number(this.tempModifyForm.rangeMin);
        const maxValue = Number(this.tempModifyForm.rangeMax);
        params.fs_lines = [maxValue, minValue]
        const res = await getWaveform(params)

        // 更新图表数据
        const measureName = this.configNodeInfo?.measureDefinitions?.measureDefineName;
        if (measureName === '速度') {
          this.originalData = res.data.x.map((xValue, index) => [
            xValue,
            res.data.y[index]
          ]);
        } else {
          this.originalData = res.data.x.map((xValue, index) => [
            xValue,
            res.data.y[index]
          ]);
        }

        // 更新当前显示数据
        this.data = [...this.originalData];

        // 重新初始化坐标轴范围
        this.initDefaultAxisRange();

        // 重新初始化图表
        this.initChart();

        console.log('应用修改成功:', params)
        this.showTempModifyDialog = false
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('数据更新失败：请确保输入的频率范围在有效区间内(0-2.5Hz)')
      }
    },

    handleBearingFrequencies(bearingFrequencies) {
      console.log('接收到轴承频率数据:', bearingFrequencies)

      // 清除现有的标记
      this.clearAllMarkers()

      // 为每个轴承添加频率标记
      bearingFrequencies.forEach(bearing => {
        bearing.frequencies.forEach(freq => {
          // 找到最接近的数据点
          /* const nearestPoint = this.findNearestDataPoint(freq.frequency)
          if (nearestPoint) {
            this.addFrequencyMarker({
              freq: nearestPoint[0],
              amp: nearestPoint[1],
              // label: `${freq.multiple}X——${freq.frequency.toFixed(2)}Hz`
              label: `${freq.multiple}X——`
            })
          } */
          // 直接使用计算的频率值
          this.addFrequencyMarker({
            freq: freq.frequency,
            amp: 0, // 或者设置一个固定值，比如 this.yAxis[0].max * 0.8
            label: `${freq.multiple}X——`,
            isFirstThree: freq.multiple <= 1 // 标记前三个倍频
          })
        })
      })

      if (this.chartInstance) {
        console.log('更新后的图表配置:', this.chartInstance.getOption())
      }
      // 更新图表
      this.updateChart()

    },


    handleGearAnalysis(bearingFrequencies) {
      console.log('接收到轴承频率数据:', bearingFrequencies)

      // 清除现有的标记
      this.clearAllMarkers()

      // 为每个轴承添加频率标记
      bearingFrequencies.forEach(bearing => {
        bearing.frequencies.forEach(freq => {
          // 找到最接近的数据点
          const nearestPoint = this.findNearestDataPoint(freq.frequency)
          console.log('找到的最近点:', nearestPoint, '对应频率:', freq.frequency)
          if (nearestPoint) {
            this.addFrequencyMarker({
              freq: nearestPoint[0],
              amp: nearestPoint[1],
              label: `${freq.multiple}X——`,
              isFirstThree: freq.multiple <= 1 // 标记前三个倍频
            })
          }
        })
      })

      if (this.chartInstance) {
        console.log('更新后的图表配置:', this.chartInstance.getOption())
      }
      // 更新图表
      this.updateChart()
    },


    // 清除所有标记
    clearAllMarkers() {
      // 清除现有的标记线
      if (!this.chartInstance) return;
      this.chartInstance.setOption({
        series: [{
          markLine: {
            data: []
          }
        }]
      })
    },
    // 添加频率标记
    addFrequencyMarker(marker) {
      if (!this.chartInstance) return;
      const option = this.chartInstance.getOption()
      const markLine = option.series[0].markLine || { data: [] }
      console.log('添加频率标记:', markLine)
      markLine.data.push([
        {
          coord: [marker.freq, this.getYAxisExtent()[1]],
          lineStyle: { color: '#9254de', type: 'dashed' },
          label: {
            formatter: function(params) {
              // 使用传入的isFirstThree标记来确定是否显示Hz值
              return marker.isFirstThree ?
                marker.label.replace('——', ` ${marker.freq.toFixed(2)}Hz `) :
                marker.label.replace('——', ' ');
            },
            position: 'start',
            distance: -1,
            padding: [8, 8],
            borderRadius: 4
          }
        },
        {
          coord: [marker.freq, marker.amp],
          lineStyle: { color: '#9254de', type: 'dashed' }
        }
      ])

      this.chartInstance.setOption({
        series: [{
          markLine: {
            silent: true,
            data: markLine.data
          }
        }]
      })
    },
    // 初始化默认范围
    initDefaultAxisRange() {
      if (this.data && this.data.length > 0) {
        // X轴默认范围
        this.defaultAxisRange.xMin = this.data[0][0];
        this.defaultAxisRange.xMax = this.data[this.data.length - 1][0];

        // Y轴默认范围
        const yValues = this.data.map(point => point[1]);
        this.defaultAxisRange.yMin = 0; // 从0开始
        this.defaultAxisRange.yMax = Math.max(...yValues) * 1.2; // 最大值的1.2倍

        // 初始化当前范围
        this.axisRange = JSON.parse(JSON.stringify(this.defaultAxisRange));
      }
    },

    // 重置范围
    resetAxisRange() {
      this.axisRange = JSON.parse(JSON.stringify(this.defaultAxisRange));
      this.applyAxisRange();
    },

    // 应用范围设置
    applyAxisRange() {
      if (this.chartInstance) {
        // 将可能为字符串的输入值转换为浮点数
        // parseFloat 对于空字符串或无效字符串会返回 NaN
        const xMin = parseFloat(this.axisRange.xMin);
        const xMax = parseFloat(this.axisRange.xMax);
        const yMin = parseFloat(this.axisRange.yMin);
        const yMax = parseFloat(this.axisRange.yMax);

        const option = {
          xAxis: {
            // 如果解析结果不是数字(NaN)，则视为 null，ECharts 会自动计算范围
            min: !isNaN(xMin) ? xMin : null,
            max: !isNaN(xMax) ? xMax : null
          },
          yAxis: {
            min: !isNaN(yMin) ? yMin : null,
            max: !isNaN(yMax) ? yMax : null
          }
        };

        if (!this.chartInstance) return;
        this.chartInstance.setOption(option);
        this.showAxisRangeDialog = false;
      }
    },
    // 添加清除轴承标记的方法
    clearBearingMarkers() {
      if (!this.chartInstance) return;
      const option = this.chartInstance.getOption();
      const markLine = option.series[0].markLine || { data: [] };

      console.log('当前 markLine 数据:', markLine.data);
      markLine.data = markLine.data.filter(mark =>
        /* !Array.isArray(mark) ||
        !mark[0]?.label?.formatter?.includes('X——') */
        Array.isArray(mark) && mark[0]?.lineStyle?.color !== '#9254de'
      );

      // 更新图表配置
      this.chartInstance.setOption({
        series: [{
          markLine: {
            data: markLine.data
          }
        }]
      });
    },
    // 处理左箭头键事件
    handleLeftArrow() {
      this.moveCursor(-1);
    },

    // 处理右箭头键事件
    handleRightArrow() {
      this.moveCursor(1);
    },

    // 移动游标的通用方法
    moveCursor(direction) {
      if (!this.showCursor1) return;

      let newCursorX;
      const step = this.fixedCursorStep;
      const precision = (step.toString().split('.')[1] || '').length; // 获取步长的小数位数，用于toFixed

      if (this.cursor1 === null) {
        // 如果游标初始为null，可以设置一个默认起始位置，例如图表数据的某个百分比处
        if (this.data && this.data.length > 0) {
          const initialChartX = this.data[Math.floor(this.data.length / 10)][0];
          newCursorX = Math.round(initialChartX / step) * step; // 吸附到最近的步长倍数
        } else {
          return; // 没有数据，无法确定初始位置
        }
      } else {
        newCursorX = this.cursor1 + (direction * step);
      }

      // 限制在图表X轴的有效范围内
      // 你可能需要根据 this.data 或 this.axisRange 来确定 minX 和 maxX
      let minX = -Infinity, maxX = Infinity;
      if (this.axisRange.xMin !== null) minX = this.axisRange.xMin;
      else if (this.data && this.data.length > 0) minX = this.data[0][0];

      if (this.axisRange.xMax !== null) maxX = this.axisRange.xMax;
      else if (this.data && this.data.length > 0) maxX = this.data[this.data.length - 1][0];

      if (minX !== -Infinity && maxX !== Infinity) {
          newCursorX = Math.max(minX, Math.min(maxX, newCursorX));
      }


      this.cursor1 = parseFloat(newCursorX.toFixed(precision));

      // 更新 cursor1Y (推荐使用插值)
      this.cursor1Y = this.getInterpolatedYForCursor(this.cursor1);
      // 更新后提交基频值到Vuex, 确保是Hz单位
      if (this.cursor1 !== null) {
        let frequencyInHz = this.cursor1;
        if (this.currentUnit === 'RPM') {
          frequencyInHz = this.cursor1 / 60;
        } else if (this.currentUnit === 'Orders') {
          if (this.referenceSpeed && this.referenceSpeed !== 0) {
            frequencyInHz = (this.cursor1 * this.referenceSpeed) / 60;
          } else {
            console.error("SpectrumChart: Reference speed for Orders is not set or zero. Cannot dispatch to Vuex from moveCursor.");
            frequencyInHz = undefined; // 阻止 dispatch错误的值
          }
        }
        if (frequencyInHz !== undefined) {
          this._isUpdatingFromSelf = true; // <-- 在 dispatch 前设置标志位
          this.$store.dispatch('chartLink/setBaseFrequency', frequencyInHz);
          this.$nextTick(() => {
              this._isUpdatingFromSelf = false;
          });
        }
      }

      if (this.showHarmonics) {
        this.baseFrequency = this.cursor1;
        this.calculateHarmonics();
      }
      this.calculateDiff(); // 如果你还需要计算差值
      this.updateChart();
    },
    // 添加获取健康等级文本的方法
    getHealthLevelText(level) {
      const levelTexts = {
        'excellent': '优秀',
        'good': '良好',
        'fair': '一般',
        'poor': '较差',
        'critical': '严重'
      };
      return levelTexts[level] || '未知';
    },
    onDragBearingHealth(x, y) {
      this.bearingHealthPosition = { x, y };
      // 保存位置到本地存储
      localStorage.setItem('bearingHealthPosition', JSON.stringify({ x, y }));
    },
    onResizeBearingHealth(x, y, width, height) {
      this.bearingHealthPosition = { x, y };
      this.bearingHealthSize = { width, height };
      // 保存位置和大小到本地存储
      localStorage.setItem('bearingHealthPosition', JSON.stringify({ x, y }));
      localStorage.setItem('bearingHealthSize', JSON.stringify({ width, height }));
    },
    toggleBearingHealthPanel() {
      this.showBearingHealthPanel = !this.showBearingHealthPanel;
      // 保存到本地存储，记住用户的选择
      localStorage.setItem('showBearingHealthPanel', this.showBearingHealthPanel.toString());
    },
    // 设置参考转速
    setReferenceSpeed(value) {
      if (value !== null && value !== undefined && value > 0) { // 确保 value 有效且大于0
        this.referenceSpeed = Number(value);

        // 如果选择保存设置，则保存到localStorage
        if (this.saveReferenceSpeed) {
          localStorage.setItem('spectrumChartReferenceSpeed', this.referenceSpeed.toString());
        }

        // 在这里，我们从当前单位（切换前一定是Hz或RPM）转换到Orders
        this.handleUnitConversion({ fromUnit: this.currentUnit, toUnit: 'Orders' });
      }
      this.showReferenceSpeedDialog = false;
    },
    // 添加单位直接切换方法
    changeFrequencyUnit(targetUnit) {
      if (targetUnit === this.currentUnit) return;

      // 如果切换到Orders单位，显示参考转速设置对话框
      if (targetUnit === 'Orders') {
        this.showReferenceSpeedDialog = true;
      }else {
        // 如果切换到其他单位，则直接切换
        this.handleUnitConversion({ fromUnit: this.currentUnit, toUnit: targetUnit });
      }

    },
  }
}
</script>

<style scoped lang="scss">
.sycontainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.spectrum-toolbar {
  padding: 0 10px;
  background: #f5f5f5;
  height: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;  // 添加相对定位

  .damage-info {
    position: absolute;
    right: 10px;
    padding: 6px 10px;
    font-size: 13px;
    color: #595959;
    font-weight: 500;
    background: #f5f7fa;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    user-select: none;
    -webkit-user-select: none;
    display: flex;
    flex-direction: column;
    gap: 3px;
    min-width: 120px;
    border-left: 3px solid #909399;

    &.excellent {
      border-left-color: #52c41a;
      .score-value, .score-level {
        color: #52c41a;
      }
    }

    &.good {
      border-left-color: #1890ff;
      .score-value, .score-level {
        color: #1890ff;
      }
    }

    &.fair {
      border-left-color: #faad14;
      .score-value, .score-level {
        color: #faad14;
      }
    }

    &.poor {
      border-left-color: #fa8c16;
      .score-value, .score-level {
        color: #fa8c16;
      }
    }

    &.critical {
      border-left-color: #f5222d;
      .score-value, .score-level {
        color: #f5222d;
      }
    }

    .health-score {
      display: flex;
      align-items: center;
      gap: 5px;

      .score-value {
        font-size: 16px;
        font-weight: bold;
      }

      .score-level {
        font-size: 13px;
      }
    }

    .damage-type, .damage-percentage {
      font-size: 12px;
      color: #595959;
    }

    .measure-type {
      font-size: 11px;
      color: #8c8c8c;
    }
  }

  .fuction {
    display: flex;
    gap: 8px;

    .cursor-btn {
      min-width: 32px;
      height: 24px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: white;
      color: #595959;
      font-size: 13px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      padding: 0 8px;

      &:hover {
        background: #f0f0f0;
        border-color: #40a9ff;
        color: #40a9ff;
      }

      &.active {
        background: #1890ff;
        border-color: #1890ff;
        color: white;
      }

      &:active {
        transform: scale(0.98);
      }

      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #00000040;
        cursor: not-allowed;

        &:hover {
          background: #f5f5f5;
          border-color: #d9d9d9;
          color: #00000040;
        }
      }
    }
  }

  .display-data {
    display: flex;
    gap: 16px;
    align-items: center;
    font-size: 13px;
    color: #595959;

    div {
      white-space: nowrap;
    }
  }
}

.chart {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  box-sizing: border-box;
  padding: 5px;
  background-color: #f5f5f5;
  position: relative;
  outline: none; /* 防止获取焦点时出现默认轮廓 */
}

.top-container {
  width: 100%;
  height: 30px;
  background-color: #f5f5f5;
  font-size: 14px;
  text-align: left;
  display: flex;
  padding: 3px;
  /* justify-content: center;
  align-items: center; */
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: red;
  font-size: 14px;
}
.btn{
  border: none;
}
.btn:active{
  transform:scale(0.9) ;
}

.harmonic-settings {
  display: flex;
  align-items: center;
  gap: 8px;

  .frequency-input {
    width: 100px;
    height: 24px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    &:focus {
      border-color: #40a9ff;
      outline: none;
    }
  }
}

.sideband-settings {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;

  .frequency-input {
    width: 80px;
    height: 24px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    -moz-appearance: textfield;

    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &:focus {
      border-color: #40a9ff;
      outline: none;
    }
  }
}

.temp-modify-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 300px;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f5f7fa;
    border-radius: 8px 8px 0 0;
    cursor: move;

    .close-btn {
      border: none;
      background: none;
      font-size: 20px;
      cursor: pointer;
      color: #909399;

      &:hover {
        color: #409EFF;
      }
    }
  }

  .dialog-content {
    padding: 20px;

    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      span {
        width: 80px;
        text-align: right;
        margin-right: 10px;
      }

      .el-input {
        flex: 1;
      }
    }
  }

  .dialog-footer {
    padding: 10px 20px;
    text-align: right;
    border-top: 1px solid #e4e7ed;

    .el-button + .el-button {
      margin-left: 10px;
    }
  }
}

.axis-range-dialog {
  .axis-range-form {
    padding: 10px;

    .axis-group {
      margin-bottom: 20px;

      .axis-label {
        margin-bottom: 10px;
        font-weight: bold;
        color: #606266;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

:deep(.el-input-group__prepend) {
  padding: 0 10px;
}

:deep(.el-input__inner) {
  text-align: center;
}

.loading-info {
  position: absolute;
  right: 10px;
  padding: 8px 12px;
  font-size: 13px;
  color: #595959;
  font-weight: 500;
  background: #f5f7fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  user-select: none;
  -webkit-user-select: none;
  min-width: 140px;
  max-width: 180px;
  border-left: 3px solid #909399;
}

.loading-text {
  color: #595959;
  display: flex;
  align-items: center;
  gap: 5px;
}

.loading-text:before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid #1890ff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  to {
    transform: rotate(360deg);
  }
}

.draggable-health-box {
  background: transparent;
  cursor: move;
  position: absolute;
  top: 0;
  left: 0;

  .vdr-handle {
    background: #1890ff;
    &:hover {
      background: #40a9ff;
    }
  }
}

.damage-info {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  pointer-events: auto;
  user-select: none;
  -webkit-user-select: none;
  display: flex;
  flex-direction: column;
  gap: 5px;
  border-left: 3px solid #52c41a;
  min-width: 200px;
  max-width: 300px;
  min-height: 80px;
  overflow: hidden;

  &.normal {
    border-left-color: #52c41a;
  }

  &.warning {
    border-left-color: #faad14;
  }

  &.danger {
    border-left-color: #f5222d;
  }

  .health-score {
    display: flex;
    align-items: baseline;
    margin-bottom: 5px;

    .score-value {
      font-size: 24px;
      font-weight: bold;
      color: #595959;
      margin-right: 8px;
    }

    .score-level {
      font-size: 14px;
      color: #8c8c8c;
    }
  }

  .damage-label {
    font-size: 13px;
    color: #8c8c8c;
    width: 55px;
    display: inline-block;
  }

  .damage-value {
    font-size: 13px;
    color: #595959;
    font-weight: 500;
  }

  .measure-type {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 5px;
  }
}

.reference-speed-dialog {
  .reference-speed-form {
    padding: 10px;

    .reference-speed-description {
      margin-bottom: 20px;
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
    }

    .reference-speed-input {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 16px;

      .reference-speed-label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
        font-size: 14px;
        color: #606266;
      }
    }

    .reference-speed-options {
      margin-left: 142px;
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon-question {
        color: #909399;
        cursor: pointer;

        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

.unit-dropdown {
  margin-left: 5px;

  .unit-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 2px;
    font-weight: 500;

    &:hover {
      background-color: #ecf5ff;
      color: #409EFF;
    }
  }

  .el-icon-arrow-down {
    font-size: 12px;
    margin-left: 4px;
  }
}

:deep(.unit-dropdown-menu) {
  .unit-name {
    font-weight: 500;
    margin-right: 8px;
  }

  .unit-desc {
    font-size: 12px;
    color: #909399;
  }

  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    line-height: 1.5;
    padding: 8px 16px;

    &.is-disabled {
      background-color: #f5f7fa;
      cursor: not-allowed;

      .unit-name {
        color: #409EFF;
      }
    }
  }
}
</style>

