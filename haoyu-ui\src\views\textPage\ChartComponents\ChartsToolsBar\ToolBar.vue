<!-- ToolBar.vue -->
<template>
  <div class="toolbar">
    <!-- 工具栏 -->
    <div class="toolbar__actions">
      <template v-for="toolId in selectedTools">
        <!-- 多点选择工具 -->
        <template v-if="toolId === 'calculate'">
          <el-popover
            :key="`popover-${toolId}`"
            placement="bottom"
            width="300"
            trigger="click"
            v-model="calcPopoverVisible"
          >
            <!-- 多点选择器内容 -->
            <el-checkbox-group v-model="selectedPoints">
              <el-checkbox v-for="point in points" :key="point.id" :label="point.id">
                {{ point.name }}
              </el-checkbox>
            </el-checkbox-group>

            <div style="margin-top: 10px; text-align: right;">
              <el-button size="mini" @click="confirmPointSelection">确认</el-button>
              <el-button size="mini" @click="cancelPointSelection">取消</el-button>
            </div>

            <!-- 触发按钮 -->
            <el-tooltip slot="reference" :content="getToolName(toolId)" effect="dark" placement="top">
              <el-button
                :title="getToolName(toolId)"
                class="toolbar__button"
                style="border: none;"
              >
                <component :is="getToolIcon(toolId)" size="16px"/>
              </el-button>
            </el-tooltip>
          </el-popover>
        </template>

        <!-- 上下限选择工具 -->
        <template v-else-if="toolId === 'limit'">
          <el-popover
            :key="`popover-${toolId}`"
            placement="bottom"
            width="300"
            trigger="click"
            v-model="limitPopoverVisible"
          >
            <div class="limit-inputs">
              <el-input
                v-model="yMin"
                placeholder="输入最小值"
                class="limit-input"
                type="number"
                clearable
              ></el-input>
              <el-input
                v-model="yMax"
                placeholder="输入最大值"
                class="limit-input"
                type="number"
                clearable
              ></el-input>
              <div style="margin-top: 10px; text-align: right;">
                <el-button size="mini" type="warning" @click="resetLimitSelection">重置</el-button>
                <el-button size="mini" type="primary" @click="confirmLimitSelection">确认</el-button>
                <el-button size="mini" @click="cancelLimitSelection">取消</el-button>
              </div>
            </div>

            <!-- 触发按钮 -->
            <el-tooltip slot="reference" :content="getToolName(toolId)" effect="dark" placement="top">
              <el-button
                :title="getToolName(toolId)"
                class="toolbar__button"
                style="border: none;"
              >
                <component :is="getToolIcon(toolId)" size="16px"/>
              </el-button>
            </el-tooltip>
          </el-popover>
        </template>
        <!-- 单位转换工具 -->
        <template v-else-if="toolId === 'conversion'">
          <el-popover
            :key="`popover-${toolId}`"
            placement="bottom"
            width="300"
            trigger="click"
            v-model="conversionPopoverVisible"
          >
            <div class="conversion-inputs">
              <!-- 显示当前单位 -->
              <div class="current-unit">
                <label>当前单位：</label>
                <span>{{ currentUnit }}</span>
              </div>

              <!-- 目标单位选择 -->
              <div class="target-unit">
                <label>目标单位：</label>
                <el-select
                  v-model="targetUnit"
                  placeholder="选择目标单位"
                  class="conversion-select"
                >
                  <el-option
                    v-for="unit in frequencyUnits"
                    :key="unit.value"
                    :label="unit.label"
                    :value="unit.value"
                  />
                </el-select>
              </div>

              <div style="margin-top: 10px; text-align: right;">
                <el-button size="mini" type="warning" @click="resetUnitConversion">重置</el-button>
                <el-button size="mini" type="primary" @click="confirmUnitConversion">确认</el-button>
                <el-button size="mini" @click="cancelUnitConversion">取消</el-button>
              </div>
            </div>

            <!-- 触发按钮 -->
            <el-tooltip slot="reference" :content="getToolName(toolId)" effect="dark" placement="top">
              <el-button
                :title="getToolName(toolId)"
                class="toolbar__button"
                style="border: none;"
              >
                <component :is="getToolIcon(toolId)" size="16px"/>
              </el-button>
            </el-tooltip>
          </el-popover>
        </template>

        <!-- 其他普通工具 -->
        <template v-else>
          <el-tooltip
            :key="`tooltip-${toolId}`"
            :content="getToolName(toolId)"
            effect="dark"
            placement="top"
          >
            <el-button
              :title="getToolName(toolId)"
              class="toolbar__button"
              @click="handleToolClick(toolId)"
              style="border: none;"
            >
              <component :is="getToolIcon(toolId)" size="16px"/>
            </el-button>
          </el-tooltip>
        </template>
      </template>

      <!-- 工具选择按钮 -->
      <el-tooltip content="工具选择" effect="dark" placement="top">
        <el-button
          class="toolbar__button toolbar__button--settings"
          @click="openToolDialog"
          style="border: none;"
        >
          <i class="el-icon-s-tools"></i>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 工具选择弹窗 -->
    <el-dialog
      title="选择工具"
      :visible.sync="dialogVisible"
      width="400px"
      :modal-append-to-body="false"
      :append-to-body="true"
      :center="true"
      custom-class="toolbar-dialog"
      :close-on-click-modal="false"
      @close="handleDialogCancel"
    >
      <div class="toolbar-dialog__content">
        <div
          v-for="tool in tools"
          :key="tool.id"
          class="toolbar-dialog__item"
        >
          <el-checkbox
            v-model="tempSelectedTools"
            :label="tool.id"
          >
            <div class="toolbar-dialog__label">
              <component :is="tool.icon" :size="tool.iconsize"/>
              <span>{{ tool.name }}</span>
            </div>
          </el-checkbox>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogCancel">取 消</el-button>
        <el-button type="primary" @click="handleDialogConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import calcICON from './ICON/calcICON.vue';
import download from './ICON/download.vue';
import limit from './ICON/limit.vue';
import conversion from './ICON/conversion.vue';
import audioICON from './ICON/audioICON.vue';
import toogledisplay from './ICON/toogledisplay.vue';
export default {
  name: 'ToolBar',
  components: {
    calcICON,
    download,
    limit,
    conversion,
    audioICON,
    toogledisplay
  },
  data() {
    return {
      dialogVisible: false,
      selectedTools: [],
      tempSelectedTools: [],
      // 多点选择相关数据
      calcPopoverVisible: false,
      selectedPoints: [],
      points: [
        { id: "A", name: "A" },
        { id: "B", name: "B" },
        { id: "C", name: "C" },
        { id: "D", name: "D" },
        { id: "E", name: "E" },
      ],
      tools: [
        {
          id: 'limit',
          name: '上下限选择',
          icon: 'limit',
          iconsize: '16px'
        },
        {
          id: 'conversion',
          name: '单位转换',
          icon: 'conversion',
          iconsize: '16px'
        },
        {
          id: 'download',
          name: '下载',
          icon: 'download',
          iconsize: '16px'
        },
        {
          id: 'calculate',
          name: '多点选择',
          icon: 'calcICON',
          iconsize: '16px'
        },
        {
          id: 'audioICON',
          name: '播放声波',
          icon: 'audioICON',
          iconsize: '16px'
        },
        {
          id: 'toogledisplay',
          name: '显示触发模式切换',
          icon: 'toogledisplay',
          iconsize: '16px'
        }
      ],
      limitPopoverVisible: false,
      yMin: null,
      yMax: null,
      conversionPopoverVisible: false,
      currentUnit: 'Hz',
      targetUnit: 'Hz',
      frequencyUnits: [
        { label: '赫兹 (Hz)', value: 'Hz' },
        { label: '千赫兹 (kHz)', value: 'kHz' },
        { label: '兆赫兹 (MHz)', value: 'MHz' }
      ],
    };
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    unit: {
      type: String,
      default: 'Hz'
    }
  },
  // 监听 unit 变化
  watch: {
    unit: {
      immediate: true,
      handler(newUnit) {
        this.currentUnit = newUnit;
      }
    }
  },
  methods: {
    getToolName(toolId) {
      const tool = this.tools.find(t => t.id === toolId);
      return tool ? tool.name : '';
    },

    getToolIcon(toolId) {
      const tool = this.tools.find(t => t.id === toolId);
      return tool ? tool.icon : '';
    },

    openToolDialog() {
      this.tempSelectedTools = [...this.selectedTools];
      this.dialogVisible = true;
    },

    handleToolClick(toolId) {
      if (toolId === 'toogledisplay') {
        // 切换显示模式时抛出事件
        this.$emit('display-mode-change');
      }
      this.$emit('tool-action', toolId);
    },

    handleDialogCancel() {
      this.dialogVisible = false;
      this.tempSelectedTools = [...this.selectedTools];
    },

    handleDialogConfirm() {
      this.selectedTools = [...this.tempSelectedTools];
      this.dialogVisible = false;
    },

    // 多点选择相关方法
    confirmPointSelection() {
      const selectedPointsData = this.selectedPoints.map((pointId, index) => {
        const point = this.points.find(p => p.id === pointId);
        return {
          id: pointId,
          name: point.name,
          index: index
        };
      });

      this.$emit('points-selected', selectedPointsData);
      this.$message.success("选择已确认：" + this.selectedPoints.join(", "));
      this.calcPopoverVisible = false;
    },

    cancelPointSelection() {
      this.selectedPoints = [];
      this.calcPopoverVisible = false;
    },

    confirmLimitSelection() {
      const min = this.yMin !== null && this.yMin !== '' ? parseFloat(this.yMin) : null;
      const max = this.yMax !== null && this.yMax !== '' ? parseFloat(this.yMax) : null;

      if (min !== null && max !== null && min >= max) {
        this.$message.error('最小值必须小于最大值');
        return;
      }

      this.$emit('limit-selected', { min, max });
      this.$message.success(`筛选范围：${min || '最小'} ~ ${max || '最大'}`);
      this.limitPopoverVisible = false;
    },

    resetLimitSelection() {
      this.yMin = null;
      this.yMax = null;
      this.$emit('limit-selected', { min: null, max: null });
      this.$message.success('已恢复显示全部数据');
      this.limitPopoverVisible = false;
    },

    cancelLimitSelection() {
      this.yMin = null;
      this.yMax = null;
      this.limitPopoverVisible = false;
    },

    confirmUnitConversion() {
      if (this.currentUnit === this.targetUnit) {
        this.$message.warning('当前单位与目标单位相同，无需转换');
        return;
      }

      this.$emit('unit-conversion', {
        fromUnit: this.currentUnit,
        toUnit: this.targetUnit
      });
      this.currentUnit = this.targetUnit;
      this.$message.success(`单位已转换: ${this.currentUnit}`);
      // 更新当前单位为目标单位，为下一次转换做准备
      this.conversionPopoverVisible = false;
    },

    resetUnitConversion() {
      this.currentUnit = 'Hz';
      this.targetUnit = 'Hz';
      this.$emit('unit-conversion', {
        fromUnit: 'Hz',
        toUnit: 'Hz'
      });
      this.$message.success('已重置为默认单位(Hz)');
      this.conversionPopoverVisible = false;
    },

    cancelUnitConversion() {
      this.currentUnit = 'Hz';
      this.targetUnit = this.currentUnit; // 取消时恢复目标单位为当前单位
      this.conversionPopoverVisible = false;
    }
  }
};
</script>

<style lang="scss">
// 变量定义
$primary-color: #409EFF;
$text-color: #303133;
$border-color: #DCDFE6;
$hover-color: #F5F7FA;
$border-radius: 4px;
$button-size: 28px;
$icon-size: 16px;
$spacing: 8px;

.toolbar {
  // 工具栏
  &__actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
  }

  &__button {
    width: $button-size;
    height: $button-size;
    padding: 0;
    margin-left: $spacing;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    background: transparent !important;
    color: $text-color;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    outline: none;

    &:hover {
      color: $primary-color;
    }

    &--settings {
      margin-left: $spacing * 2;
    }
  }
}

// 弹窗样式
.toolbar-dialog {
  &__content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing * 2;
    padding: $spacing 0;
  }

  &__item {
    display: flex;
    align-items: center;
    border-radius: $border-radius;

    .el-checkbox {
      width: auto;
      margin: 0;
      height: 100%;
      margin-right: $spacing;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &__label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;

    span {
      line-height: 1;
    }
  }
}

// element-ui样式覆写
.el-dialog__body {
  padding: 20px 20px;
}

.limit-inputs {
  padding: 10px;

  .limit-input {
    margin-bottom: 10px;

    &:last-of-type {
      margin-bottom: 15px;
    }

    .el-input__inner {
      text-align: left;
    }
  }

  .el-button + .el-button {
    margin-left: 8px;
  }
}
.conversion-inputs {
  padding: 10px;

  .current-unit {
    margin-bottom: 15px;
    label {
      display: inline-block;
      width: 70px;
      color: #606266;
    }
    span {
      color: #409EFF;
      font-weight: bold;
    }
  }

  .target-unit {
    margin-bottom: 15px;
    label {
      display: inline-block;
      width: 70px;
      color: #606266;
    }
  }

  .conversion-select {
    width: calc(100% - 75px); // 减去 label 的宽度
  }

  .el-button + .el-button {
    margin-left: 8px;
  }
}
</style>
