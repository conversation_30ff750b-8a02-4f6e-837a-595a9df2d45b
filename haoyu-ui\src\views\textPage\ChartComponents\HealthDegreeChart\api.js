import request from '@/utils/request'
/**
 *
 * @param {*} params
 * @returns
 * {
        "total": 49682,
        "rows": [
            {
                "id": 118437,
                "collectorTime": "2025-05-15 03:22:27",
                "collectorTimestamp": "**********",
                "measurementId": "3173",
                "deviceId": 2782,
                "devicePartId": 6128,
                "fileWavLocation": "CAAF1157199641E3BB192B540AAA88CF-7",
                "waveState": "1",
                "healthValue": -1.0000
            },
          ],
        "code": 200,
        "msg": "查询成功"
    }
 */
export const getHealthDegreeChartData = (params) => {
  return request({
    url: '/waveForm/waveFormDataInfo/list/desc',
    method: 'get',
    params:{
      pageNum: 1,
      pageSize: 10000,
      ...params
    }
  })
}
