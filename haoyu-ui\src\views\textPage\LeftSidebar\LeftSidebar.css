.sidebar {
  width: 100px; /* 调整宽度以适应按钮的内容 */
  height: 100%;
  background-color: #f0f0f0; /* 更柔和的背景颜色 */
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center; /* 居中对齐图标 */
  padding: 5px 0; /* 添加顶部和底部填充 */
  border: 2px solid #b0b0b0; /* 边框颜色 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  border-radius: 8px; /* 添加圆角 */
}

.sidebar-button {
  margin: 1px 0; /* 调整按钮之间的间隔 */
  width: 100%; /* 让按钮宽度撑满侧边栏 */
  display: flex;
  justify-content: center; /* 使按钮内容居中 */
  align-items: center;
  background-color: #e0e0e0; /* 按钮背景颜色 */
  border: none; /* 移除按钮边框 */
  border-radius: 4px; /* 添加按钮圆角 */
  transition: background-color 0.3s; /* 添加背景颜色过渡效果 */
}

.ant-btn:hover, .ant-btn:focus, .ant-btn:active, .ant-btn.active {
  background: transparent;
}
.sidebar-button:active {
  background-color: #c0c0c0; /* 点击时的按钮背景颜色 */
}
