<template>
  <div id="app">
    <div class="model-container">
      <objViewer_pj :data="selectedRowData" class="model-viewer" @Goback="goBack"/>
    </div>

    <div class="photo-section" v-if="showDevicePhotos">
      <uploadpng class="photo-uploader"/>
    </div>

    <!-- <tuolazhuai class="drag-component"/>

    <div class="photo-toggle">
      <el-switch
        v-model="showDevicePhotos"
        active-text="显示设备照片"
        inactive-text="隐藏设备照片"
      />
    </div> -->
  </div>
</template>

<script>
import objViewer_pj from './vue/objViewer_pj.vue'
import uploadpng from './vue/pt_2d.vue'
import tuolazhuai from './vue/template/tuolazhuai.vue'

export default {
name: 'web3d',
components: {
    objViewer_pj,
    uploadpng,
    tuolazhuai,
  },
  
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  
  mounted() {
    console.log('接收到的数据:props ==> ' + JSON.stringify(this.data))
    this.selectedRowData = this.data
  },
  
  data() {
    return {
      selectedRowData:this.data,
      showDevicePhotos: false // 默认显示设备现场照片
    };
  },

  methods: {
    goBack() {
      this.$emit('Goback', this.selectedRowData)
    }
  }

}
</script>

<style scoped>
.model-container {
  height: 80vh;
  width: 100%;
  position: relative;
}

.model-viewer {
  width: 100%;
  height: 100%;
}

.photo-section {
  margin-top: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.photo-toggle {
  position: fixed;
  right: 20px;
  top: 20px;
  z-index: 100;
}

.drag-component {
  margin-top: 20px;
}
</style>