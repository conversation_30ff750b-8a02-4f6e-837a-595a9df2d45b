<template>
  <el-dialog
    title="图片库"
    :visible.sync="dialogVisible"
    width="1300px"
    :before-close="handleClose"
    @close="handleClose"
    class="image-selector-dialog"
    top="5vh"
  >
    <div class="image-selector">
      <!-- 顶部搜索和操作区 -->
      <div class="header-section">
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索图片名称、分类..."
            size="medium"
            class="search-input"
            @input="handleSearchDebounced"
            prefix-icon="el-icon-search"
            clearable
          />
        </div>
        <div class="filter-section-header">
          <div class="filter-tags-compact">
            <el-tag
              :type="selectedCategory === '' ? 'primary' : 'info'"
              :effect="selectedCategory === '' ? 'dark' : 'plain'"
              @click="selectCategory('')"
              class="filter-tag"
            >
              全部 ({{ images.length }})
            </el-tag>
            <el-tag
              v-for="category in availableCategories"
              :key="category"
              :type="selectedCategory === category ? 'primary' : 'info'"
              :effect="selectedCategory === category ? 'dark' : 'plain'"
              @click="selectCategory(category)"
              class="filter-tag"
            >
              {{ category || '未分类' }} ({{ getCategoryCount(category) }})
            </el-tag>
          </div>
          <!-- 分页控件 -->
          <div class="pagination-header" v-if="pagination.total > pagination.pageSize">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pagination.currentPage"
              :page-sizes="[12, 24, 48, 96]"
              :page-size="pagination.pageSize"
              layout="sizes, prev, pager, next"
              :total="pagination.total"
              small
            />
          </div>
        </div>
        <div class="action-section">
          <span class="panel-title">管理工具</span>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content with-panel">
        <!-- 左侧图片内容 -->
        <div class="content-section">
          <!-- 图片网格 -->
          <div class="image-grid-container">
            <div class="image-grid" v-loading="loading">
              <div 
                v-for="image in filteredImages" 
                :key="image.id"
                class="image-card"
                :class="{ 'selected': selectedImage && selectedImage.id === image.id }"
                @click="selectImage(image)"
              >
                <div class="image-container">
                  <img :src="getImageUrl(image)" :alt="image.imageNote || image.imageUrl" />
                  <div class="image-mask">
                    <div class="image-actions">
                      <el-button type="primary" size="mini" circle icon="el-icon-check" @click.stop="selectImage(image)" />
                      <el-button type="info" size="mini" circle icon="el-icon-edit" @click.stop="editImage(image)" />
                      <el-button type="danger" size="mini" circle icon="el-icon-delete" @click.stop="deleteImage(image)" />
                    </div>
                  </div>
                </div>
                <div class="image-meta">
                  <div class="image-title" :title="image.imageNote || image.imageUrl">
                    {{ image.imageNote || '未命名图片' }}
                  </div>
                  <div class="image-tag" v-if="image.imageTypeName">
                    {{ image.imageTypeName }}
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="!loading && filteredImages.length === 0" class="empty-state">
                <div class="empty-icon">
                  <i class="el-icon-picture-outline"></i>
                </div>
                <div class="empty-text">
                  <p>{{ searchKeyword || selectedCategory ? '未找到匹配的图片' : '暂无图片' }}</p>
                  <p class="empty-hint">{{ searchKeyword || selectedCategory ? '尝试调整搜索条件' : '点击右侧面板上传新图片' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧管理面板 -->
        <div class="right-panel">
          <el-tabs v-model="activeTab" class="panel-tabs">
            <el-tab-pane label="上传图片" name="upload">
              <div class="upload-panel">
                <el-form :model="uploadForm" label-width="80px" class="upload-form" :rules="uploadRules" ref="uploadForm">
                  <el-form-item label="分类" prop="category">
                    <el-select 
                      v-model="uploadForm.category" 
                      placeholder="选择分类或输入新分类"
                      allow-create
                      filterable
                      default-first-option
                      style="width: 100%"
                      size="small"
                    >
                      <el-option 
                        v-for="category in availableCategories" 
                        :key="category" 
                        :label="category" 
                        :value="category" 
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="说明" prop="imageNote">
                    <el-input v-model="uploadForm.imageNote" placeholder="为图片添加说明" size="small" />
                  </el-form-item>
                  <el-form-item label="图片" prop="file">
                    <el-upload
                      class="upload-area-compact"
                      drag
                      :before-upload="beforeUpload"
                      :on-change="handleFileChange"
                      :file-list="uploadFileList"
                      :limit="1"
                      accept="image/*"
                      :on-remove="handleFileRemove"
                      :auto-upload="false"
                      action="#"
                    >
                      <div class="upload-content-compact">
                        <i class="el-icon-upload upload-icon-small"></i>
                        <div class="upload-text-small">拖拽图片到此处</div>
                        <div class="upload-tip-small">支持 JPG、PNG、GIF</div>
                      </div>
                    </el-upload>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="confirmUpload" :disabled="!selectedFile" style="width: 100%" size="small">
                      <i class="el-icon-upload"></i>
                      上传并使用
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="管理分类" name="category">
              <div class="category-panel">
                <!-- 添加新分类 -->
                <div class="add-category-compact">
                  <el-form :model="newCategoryForm" class="add-form-compact" :rules="categoryRules" ref="newCategoryForm">
                    <el-form-item prop="typeName">
                      <el-input 
                        v-model="newCategoryForm.typeName" 
                        placeholder="新分类名称"
                        style="width: 100%;"
                        size="small"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" @click="addCategory" style="width: 100%" size="small">
                        <i class="el-icon-plus"></i>
                        添加分类
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 分类列表 -->
                <div class="category-list-compact">
                  <div class="category-item" v-for="category in categoryList" :key="category.id">
                    <div class="category-info">
                      <div class="category-name" v-if="!category.editing">
                        {{ category.imageType }}
                        <span class="category-count">({{ getCategoryUsageCount(category.imageType) }})</span>
                      </div>
                      <el-input 
                        v-else 
                        v-model="category.editImageType" 
                        size="small"
                        @keyup.enter.native="saveCategory(category)"
                        @blur="cancelEdit(category)"
                      />
                    </div>
                    <div class="category-actions">
                      <div v-if="!category.editing">
                        <el-button 
                          type="text" 
                          size="mini" 
                          @click="editCategory(category)"
                          icon="el-icon-edit"
                        />
                        <el-button 
                          type="text" 
                          size="mini" 
                          @click="deleteCategory(category)"
                          style="color: #f56c6c;"
                          icon="el-icon-delete"
                          :disabled="getCategoryUsageCount(category.imageType) > 0"
                        />
                      </div>
                      <div v-else>
                        <el-button 
                          type="text" 
                          size="mini" 
                          @click="saveCategory(category)"
                          style="color: #67c23a;"
                          icon="el-icon-check"
                        />
                        <el-button 
                          type="text" 
                          size="mini" 
                          @click="cancelEdit(category)"
                          icon="el-icon-close"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <div class="footer-info">
        <span v-if="selectedImage" class="selected-info">
          已选择: {{ selectedImage.imageNote || '未命名图片' }}
        </span>
      </div>
      <div class="footer-actions">
        <el-button @click="handleClose" size="medium">取消</el-button>
        <el-button type="primary" @click="confirmSelection" :disabled="!selectedImage" size="medium">
          <i class="el-icon-check"></i>
          确定选择
        </el-button>
      </div>
    </div>

    <!-- 图片编辑对话框 -->
    <el-dialog
      title="编辑图片信息"
      :visible.sync="showEditImageDialog"
      width="500px"
      append-to-body
    >
      <el-form :model="editImageForm" label-width="80px" :rules="editImageRules" ref="editImageForm">
        <el-form-item label="当前图片">
          <div class="current-image-preview">
            <img :src="editImageForm.imageUrl" :alt="editImageForm.imageNote" style="max-width: 100%; max-height: 150px; border-radius: 8px; border: 1px solid #ddd;" />
          </div>
        </el-form-item>
        <el-form-item label="图片URL">
          <el-input v-model="editImageForm.imageUrl" placeholder="图片链接地址" />
        </el-form-item>
        <el-form-item label="重新上传">
          <el-upload
            class="edit-upload-area"
            drag
            :before-upload="beforeEditUpload"
            :on-change="handleEditFileChange"
            :file-list="editFileList"
            :limit="1"
            accept="image/*"
            :on-remove="handleEditFileRemove"
            :auto-upload="false"
            action="#"
          >
            <div class="edit-upload-content">
              <i class="el-icon-upload edit-upload-icon"></i>
              <div class="edit-upload-text">重新选择图片</div>
              <div class="edit-upload-tip">支持 JPG、PNG、GIF</div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="图片说明" prop="imageNote">
          <el-input v-model="editImageForm.imageNote" placeholder="请输入图片说明" />
        </el-form-item>
        <el-form-item label="分类" prop="imageTypeName">
          <el-select 
            v-model="editImageForm.imageTypeName" 
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option 
              v-for="category in availableCategories" 
              :key="category" 
              :label="category" 
              :value="category" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="cancelEditImage">取消</el-button>
        <el-button type="primary" @click="confirmEditImage">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { get2DImageList, delete2DImage, update2DImage, add2DImage, uploadImageToURL, getImageTypeList, addImageType, updateImageType, deleteImageType } from '../api/GeneralPictureApi'

export default {
  name: 'ImageSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      searchKeyword: '',
      selectedCategory: '',
      selectedImage: null,
      images: [],
      filteredImages: [],
      pagination: {
        currentPage: 1,
        pageSize: 12,
        total: 0
      },
      uploadForm: {
        category: '',
        imageNote: ''
      },
      uploadRules: {
        category: [
          { required: true, message: '请选择或输入图片分类', trigger: 'blur' }
        ],
        imageNote: [
          { required: true, message: '请输入图片说明', trigger: 'blur' }
        ]
      },
      uploadFileList: [],
      selectedFile: null,
      availableCategories: [], // 来自getImageTypeList API的分类列表
      searchTimer: null,
      activeTab: 'upload',
      newCategoryForm: {
        typeName: ''
      },
      categoryRules: {
        typeName: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ]
      },
      categoryList: [],
      // 图片编辑相关
      showEditImageDialog: false,
      editImageForm: {
        id: null,
        imageNote: '',
        imageTypeName: '',
        imageUrl: '',
        fileName: ''
      },
      editImageRules: {
        imageNote: [
          { required: true, message: '请输入图片说明', trigger: 'blur' }
        ],
        imageTypeName: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ]
      },
      editFileList: [],
      editSelectedFile: null
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.loadImages()
          this.loadAvailableCategories()
          this.loadCategoryList()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    activeTab(val) {
      if (val === 'category') {
        this.loadCategoryList()
      }
    }
  },
  mounted() {
    // 初始化时加载分类列表
    this.loadAvailableCategories()
    if (this.activeTab === 'category') {
      this.loadCategoryList()
    }
  },
  methods: {
    async loadAvailableCategories() {
      try {
        const response = await getImageTypeList()
        this.availableCategories = (response.rows || []).map(item => item.imageType)
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    },

    async loadImages() {
      this.loading = true
      try {
        const response = await get2DImageList({
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize
        })
        
        if (response.code === 200 || response.rows) {
          const allImages = response.rows || response.data || []
          this.pagination.total = response.total || allImages.length
          
          console.log("allImages", allImages)
          
          this.images = allImages
          this.filterImages()
        }
      } catch (error) {
        console.error('加载图片列表失败:', error)
        this.$message.error('加载图片列表失败')
      } finally {
        this.loading = false
      }
    },

    filterImages() {
      let filtered = [...this.images]
      
      // 分类筛选
      if (this.selectedCategory) {
        filtered = filtered.filter(img => img.imageTypeName === this.selectedCategory)
      }
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(img => 
          (img.imageNote && img.imageNote.toLowerCase().includes(keyword)) ||
          (img.imageTypeName && img.imageTypeName.toLowerCase().includes(keyword)) ||
          img.imageUrl.toLowerCase().includes(keyword)
        )
      }
      
      this.filteredImages = filtered
    },

    handleSearchDebounced() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      // 设置新的定时器
      this.searchTimer = setTimeout(() => {
        this.filterImages()
      }, 300)
    },

    handleCategoryChange() {
      this.filterImages()
    },

    selectImage(image) {
      this.selectedImage = image
    },

    getImageUrl(image) {
      // 根据实际情况构建图片URL
      if (image.imageUrl) {
        return image.imageUrl
      }
      // 默认占位图
      return require('@/assets/img/test.png')
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadImages()
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadImages()
    },

    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      return true
    },

    handleFileRemove(file) {
      this.selectedFile = null
      this.uploadFileList = []
    },

    handleFileChange(file) {
      // 存储选择的文件，但不立即上传
      this.selectedFile = file.raw
      console.log('选择的文件:', file)
    },

    async confirmUpload() {
      // 验证表单
      try {
        await this.$refs.uploadForm.validate()
      } catch (error) {
        return
      }

      if (!this.selectedFile) {
        this.$message.warning('请先选择图片')
        return
      }

      // 显示上传进度
      const loading = this.$loading({
        lock: true,
        text: '正在上传图片...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 第一步：上传图片到文件服务器获取URL
        console.log('开始上传图片文件...')
        const uploadResponse = await uploadImageToURL(this.selectedFile)
        console.log('图片上传响应:', uploadResponse)

        if (!uploadResponse.fileName) {
          throw new Error('上传失败：未获取到文件名')
        }

        // 第二步：保存图片信息到2D图片库
        const imageData = {
          fileName: uploadResponse.fileName,
          imageNote: this.uploadForm.imageNote,
          imageUrl: uploadResponse.url
        }

        // 检查是否是现有分类
        const isExistingCategory = this.availableCategories.includes(this.uploadForm.category)
        
        if (isExistingCategory) {
          // 如果是现有分类，发送imageType
          imageData.imageType = 2
          imageData.imageTypeName = this.uploadForm.category
        } else {
          // 如果是新分类，只发送分类名字
          imageData.imageTypeName = this.uploadForm.category
        }

        console.log('保存到图片库的数据:', imageData)

        const result = await add2DImage(imageData)
        console.log('保存到图片库的结果:', result)
        
        // 选择刚上传的图片
        this.selectedImage = {
          ...imageData,
          id: result.data?.id || Date.now()
        }
        
        loading.close()
        
        // 重置上传表单
        this.uploadForm = {
          category: '',
          imageNote: ''
        }
        this.uploadFileList = []
        this.selectedFile = null
        this.$refs.uploadForm?.resetFields()
        
        // 刷新图片列表
        await this.loadImages()
        
        // 确认选择
        this.confirmSelection()
        
        this.$message.success('图片上传并保存成功')
      } catch (error) {
        loading.close()
        console.error('上传失败:', error)
        this.$message.error('上传失败: ' + (error.message || '未知错误'))
      }
    },

    cancelUpload() {
      this.uploadForm = {
        category: '',
        imageNote: ''
      }
      this.uploadFileList = []
      this.selectedFile = null
      this.$refs.uploadForm?.resetFields()
    },

    confirmSelection() {
      if (!this.selectedImage) {
        this.$message.warning('请选择一张图片')
        return
      }
      
      this.$emit('select', this.selectedImage)
      this.handleClose()
    },

    handleClose() {
      this.dialogVisible = false
      this.selectedImage = null
      this.searchKeyword = ''
      this.selectedCategory = ''
    },

    selectCategory(category) {
      this.selectedCategory = category
      this.filterImages()
    },

    getCategoryCount(category) {
      return this.images.filter(img => img.imageTypeName === category).length
    },

    async addCategory() {
      try {
        await this.$refs.newCategoryForm.validate()
        const response = await addImageType({ imageType: this.newCategoryForm.typeName })
        this.newCategoryForm.typeName = ''
        this.$refs.newCategoryForm.resetFields()
        this.$message.success('分类添加成功')
        // 重新加载分类列表
        this.loadCategoryList()
        // 刷新可用分类列表
        this.loadAvailableCategories()
        this.loadImages()
      } catch (error) {
        console.error('添加分类失败:', error)
        this.$message.error('添加分类失败: ' + (error.message || '未知错误'))
      }
    },

    async editCategory(category) {
      this.$set(category, 'editing', true)
      this.$set(category, 'editImageType', category.imageType)
    },

    async saveCategory(category) {
      try {
        if (!category.editImageType || category.editImageType.trim() === '') {
          this.$message.warning('分类名称不能为空')
          return
        }
        
        const response = await updateImageType({ id: category.id, imageType: category.editImageType.trim() })
        this.$set(category, 'editing', false)
        this.$set(category, 'imageType', category.editImageType.trim())
        this.$message.success('分类更新成功')
        // 刷新可用分类列表
        this.loadAvailableCategories()
        this.loadImages()
      } catch (error) {
        console.error('更新分类失败:', error)
        this.$message.error('更新分类失败: ' + (error.message || '未知错误'))
      }
    },

    async cancelEdit(category) {
      this.$set(category, 'editing', false)
    },

    async deleteCategory(category) {
      try {
        await this.$confirm(`确定要删除分类"${category.imageType}"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await deleteImageType(category.id)
        this.categoryList = this.categoryList.filter(c => c.id !== category.id)
        this.$message.success('分类删除成功')
        // 刷新可用分类列表
        this.loadAvailableCategories()
        this.loadImages()
      } catch (error) {
        if (error === 'cancel') return
        console.error('删除分类失败:', error)
        this.$message.error('删除分类失败: ' + (error.message || '未知错误'))
      }
    },

    getCategoryUsageCount(imageType) {
      return this.images.filter(img => img.imageTypeName === imageType).length
    },

    async loadCategoryList() {
      try {
        const response = await getImageTypeList()
        this.categoryList = response.rows || []
      } catch (error) {
        console.error('加载分类列表失败:', error)
        this.$message.error('加载分类列表失败')
      }
    },

    editImage(image) {
      this.selectedImage = image
      this.showEditImageDialog = true
      this.editImageForm.id = image.id
      this.editImageForm.imageNote = image.imageNote
      this.editImageForm.imageTypeName = image.imageTypeName
      this.editImageForm.imageUrl = image.imageUrl
      this.editImageForm.fileName = image.fileName
    },

    async deleteImage(image) {
      try {
        await this.$confirm(`确定要删除图片"${image.imageNote || '未命名图片'}"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await delete2DImage(image.id)
        this.$message.success('图片删除成功')
        // 刷新图片列表
        this.loadImages()
        // 如果删除的是当前选中的图片，清除选中状态
        if (this.selectedImage && this.selectedImage.id === image.id) {
          this.selectedImage = null
        }
      } catch (error) {
        if (error === 'cancel') return
        console.error('删除图片失败:', error)
        this.$message.error('删除图片失败: ' + (error.message || '未知错误'))
      }
    },

    async confirmEditImage() {
      try {
        await this.$refs.editImageForm.validate()
        
        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在更新图片...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        let updateData = {
          id: this.editImageForm.id,
          imageNote: this.editImageForm.imageNote,
          imageTypeName: this.editImageForm.imageTypeName,
          imageUrl: this.editImageForm.imageUrl
        }

        // 如果有重新上传的文件，先上传文件获取新的URL
        if (this.editSelectedFile) {
          try {
            console.log('开始上传新图片文件...')
            const uploadResponse = await uploadImageToURL(this.editSelectedFile)
            console.log('新图片上传响应:', uploadResponse)

            if (!uploadResponse.fileName) {
              throw new Error('上传失败：未获取到文件名')
            }

            // 更新图片URL和文件名
            updateData.imageUrl = process.env.VUE_APP_BASE_API + uploadResponse.fileName
            updateData.fileName = uploadResponse.fileName
          } catch (uploadError) {
            loading.close()
            console.error('上传新图片失败:', uploadError)
            this.$message.error('上传新图片失败: ' + (uploadError.message || '未知错误'))
            return
          }
        }
        
        // 更新图片信息
        await update2DImage(updateData)
        
        loading.close()
        this.$message.success('图片更新成功')
        
        // 关闭对话框
        this.showEditImageDialog = false
        
        // 重置编辑相关数据
        this.editFileList = []
        this.editSelectedFile = null
        
        // 刷新图片列表
        this.loadImages()
        
        // 更新选中的图片信息
        if (this.selectedImage && this.selectedImage.id === this.editImageForm.id) {
          this.selectedImage.imageNote = updateData.imageNote
          this.selectedImage.imageTypeName = updateData.imageTypeName
          this.selectedImage.imageUrl = updateData.imageUrl
          if (updateData.fileName) {
            this.selectedImage.fileName = updateData.fileName
          }
        }
      } catch (error) {
        console.error('更新图片失败:', error)
        this.$message.error('更新图片失败: ' + (error.message || '未知错误'))
      }
    },

    cancelEditImage() {
      this.showEditImageDialog = false
      this.editImageForm = {
        id: null,
        imageNote: '',
        imageTypeName: '',
        imageUrl: '',
        fileName: ''
      }
      this.editFileList = []
      this.editSelectedFile = null
      this.$refs.editImageForm?.resetFields()
    },

    beforeEditUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      return true
    },

    handleEditFileChange(file) {
      // 存储选择的文件，但不立即上传
      this.editSelectedFile = file.raw
      console.log('选择的文件:', file)
    },

    handleEditFileRemove(file) {
      this.editSelectedFile = null
      this.editFileList = []
    }
  }
}
</script>

<style scoped>
/* 对话框整体样式 */
.image-selector-dialog {
  border-radius: 16px;
}

.image-selector-dialog ::v-deep .el-dialog {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.image-selector-dialog ::v-deep .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  border-bottom: none;
}

.image-selector-dialog ::v-deep .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.image-selector-dialog ::v-deep .el-dialog__close {
  color: rgba(255, 255, 255, 0.8);
  font-size: 20px;
  transition: color 0.3s;
}

.image-selector-dialog ::v-deep .el-dialog__close:hover {
  color: white;
}

.image-selector-dialog ::v-deep .el-dialog__body {
  padding: 0;
}

/* 主容器样式 */
.image-selector {
  min-height: 600px;
  background: #fafbfc;
}

/* 头部区域 */
.header-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.search-section {
  flex: 1;
}

.search-input {
  max-width: 400px;
}

.search-input ::v-deep .el-input__inner {
  border-radius: 12px;
  border: 2px solid #e9ecef;
  padding-left: 45px;
  height: 44px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input ::v-deep .el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input ::v-deep .el-input__prefix {
  left: 15px;
  color: #6c757d;
}

.action-section .el-button {
  height: 44px;
  padding: 0 24px;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.action-section .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.action-section .panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

/* 筛选区域 */
.filter-section {
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-title {
  font-weight: 600;
  color: #343a40;
  font-size: 15px;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  cursor: pointer;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.filter-tag:hover {
  transform: translateY(-1px);
}

.filter-tag.el-tag--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
  color: white;
}

/* 内容区域 */
.main-content {
  display: flex;
  height: calc(100vh - 200px);
  max-height: 600px;
}

.content-section {
  flex: 1;
  padding: 24px;
  padding-right: 0;
  min-height: 400px;
  overflow-y: auto;
}

.image-grid-container {
  position: relative;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* 图片卡片 */
.image-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 3px solid transparent;
}

.image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.image-card.selected {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

.image-container {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.image-card:hover .image-container img {
  transform: scale(1.1);
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card:hover .image-mask,
.image-card.selected .image-mask {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.image-actions .el-button {
  background: white;
  border: none;
  font-size: 14px;
  width: 32px;
  height: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.image-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.image-actions .el-button--primary {
  color: #1890ff;
}

.image-actions .el-button--info {
  color: #909399;
}

.image-actions .el-button--danger {
  color: #f56c6c;
}

.image-meta {
  padding: 16px;
}

.image-title {
  font-size: 14px;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.image-tag {
  display: inline-block;
  font-size: 11px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24px;
}

.empty-icon i {
  font-size: 64px;
  color: #dee2e6;
}

.empty-text p {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #6c757d;
  font-weight: 500;
}

.empty-hint {
  color: #adb5bd !important;
  font-size: 14px !important;
  font-weight: normal !important;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

/* 底部区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-info {
  flex: 1;
}

.selected-info {
  color: #28a745;
  font-weight: 500;
  font-size: 14px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.footer-actions .el-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  min-width: 100px;
}

/* 上传对话框样式 */
.upload-dialog ::v-deep .el-dialog__header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.upload-form {
  padding: 8px 0;
}

.upload-form ::v-deep .el-form-item__label {
  font-weight: 600;
  color: #495057;
}

.upload-area {
  width: 100%;
}

.upload-area ::v-deep .el-upload-dragger {
  width: 100%;
  height: 140px;
  border: 3px dashed #28a745;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  transition: all 0.3s ease;
}

.upload-area ::v-deep .el-upload-dragger:hover {
  border-color: #20c997;
  background: linear-gradient(135deg, #e8fdf1 0%, #d4f4dd 100%);
  transform: scale(1.02);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.upload-icon {
  font-size: 40px;
  color: #28a745;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  color: #495057;
  margin-bottom: 8px;
  font-weight: 600;
}

.upload-tip {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* Element UI 组件优化 */
::v-deep .el-input__inner {
  border-radius: 8px;
  transition: all 0.3s ease;
}

::v-deep .el-select .el-input__inner {
  border-radius: 8px;
}

::v-deep .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

::v-deep .el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

::v-deep .el-pagination {
  font-weight: 500;
}

::v-deep .el-pagination.is-background .el-pager li {
  border-radius: 8px;
  margin: 0 4px;
  font-weight: 500;
}

::v-deep .el-pagination.is-background .el-pager li.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next {
  border-radius: 8px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-input {
    max-width: none;
  }
  
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
  }
  
  .content-section {
    padding: 16px;
  }
}

/* 分类管理对话框样式 */
.category-manager-dialog ::v-deep .el-dialog__header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.category-manager {
  padding: 8px 0;
}

.add-category-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.add-form .el-form-item {
  margin-bottom: 0;
}

.category-list {
  min-height: 300px;
}

.category-list ::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.category-list ::v-deep .el-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.category-list ::v-deep .el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.category-list ::v-deep .el-table__row:hover {
  background: #f8f9fa;
}

/* 右侧面板样式 */
.right-panel {
  width: 300px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  overflow-y: auto;
}

.panel-tabs {
  height: 100%;
}

.panel-tabs ::v-deep .el-tabs__header {
  margin-bottom: 0;
  background: white;
  padding: 0 20px;
  border-bottom: 1px solid #e9ecef;
}

.panel-tabs ::v-deep .el-tabs__content {
  padding: 0;
  height: calc(100% - 40px);
  overflow-y: auto;
}

.upload-panel {
  padding: 16px;
}

.upload-form .el-form-item {
  margin-bottom: 16px;
}

.upload-area-compact {
  width: 100%;
}

.upload-area-compact ::v-deep .el-upload-dragger {
  width: 100%;
  height: 100px;
  border: 2px dashed #28a745;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  transition: all 0.3s ease;
}

.upload-content-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 10px;
}

.upload-icon-small {
  font-size: 24px;
  color: #28a745;
  margin-bottom: 8px;
}

.upload-text-small {
  font-size: 12px;
  color: #495057;
  margin-bottom: 4px;
  font-weight: 500;
}

.upload-tip-small {
  font-size: 10px;
  color: #6c757d;
  line-height: 1.2;
}

.category-panel {
  padding: 16px;
}

.add-category-compact {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.add-form-compact .el-form-item {
  margin-bottom: 12px;
}

.add-form-compact .el-form-item:last-child {
  margin-bottom: 0;
}

.category-list-compact {
  max-height: 250px;
  overflow-y: auto;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.category-item:hover {
  border-color: #d4edda;
  background: #f8fff9;
}

.category-info {
  flex: 1;
  margin-right: 8px;
}

.category-name {
  font-size: 13px;
  font-weight: 500;
  color: #343a40;
  line-height: 1.4;
}

.category-count {
  font-size: 11px;
  color: #6c757d;
  font-weight: normal;
  margin-left: 4px;
}

.category-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.filter-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin: 0 20px;
}

.filter-tags-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.pagination-header {
  margin-left: 20px;
}

.pagination-header ::v-deep .el-pagination {
  padding: 0;
}

/* 图片编辑对话框样式 */
.current-image-preview {
  margin-bottom: 16px;
}

.edit-upload-area {
  width: 100%;
}

.edit-upload-area ::v-deep .el-upload-dragger {
  width: 100%;
  height: 80px;
  border: 2px dashed #20c997;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  transition: all 0.3s ease;
}

.edit-upload-area ::v-deep .el-upload-dragger:hover {
  border-color: #28a745;
  background: linear-gradient(135deg, #e8fdf1 0%, #d4f4dd 100%);
}

.edit-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 10px;
}

.edit-upload-icon {
  font-size: 24px;
  color: #28a745;
  margin-bottom: 8px;
}

.edit-upload-text {
  font-size: 12px;
  color: #495057;
  margin-bottom: 4px;
  font-weight: 500;
}

.edit-upload-tip {
  font-size: 10px;
  color: #6c757d;
  line-height: 1.2;
}
</style> 