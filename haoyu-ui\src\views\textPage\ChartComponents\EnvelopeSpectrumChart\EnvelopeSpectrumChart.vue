<template>
  <div class="envelope">
    <div class="top-container">{{ treePathText }} 包络谱</div>
    <!-- 工具栏 -->
    <div class="trend-toolbar">
      <tool-bar
        @tool-action="handleToolAction"
      />
    </div>
    <!-- ECharts 图表容器 -->
    <div ref="chartContainer" class="chart" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import { getEnvelope } from './data'
import { getParams } from '@/utils/graph_Interface'
import ToolBar from '../ChartsToolsBar/ToolBar'
export default {
  components: {
    ToolBar
  },
  name: 'EnvelopeComponent',
  data() {
    return {
      chartInstance: null,
      data: [],
      loading: false,
      error: null,
      band: []
    }
  },
  computed: {
    ...mapState('tree', ['treePathText']),
    ...mapState('dataStore', ['device_id', 'point_id', 'time_point']),
    ...mapState('SettingConnect', ['configNodeInfo'])
  },
  watch: {
    '$store.state.tree.selectedTreeNode.id': {
      async handler(newNodeId) {
        console.log('节点ID变化:', newNodeId);
        if (!newNodeId) return
/*         await this.getEnvelopeData() */
        if (this.$store.state.dataStore && this.$store.state.dataStore.length > 0) {
/*           if (!this.chartInstance) {
            this.updateChart()
          } else { */
            await this.getEnvelopeData()
            this.updateChart()
/*           } */
        }
      }
    },
    'time_point': {
      async handler(newTimePoint) {
        if (!newTimePoint) return

        const { device_id, point_id } = this.$store.state.dataStore;
        if (!device_id || !point_id) return;
        await this.getEnvelopeData()
        this.updateChart()
      }
    }
  },
  async mounted() {
    await this.checkDataStore()
    await this.getEnvelopeData()
    this.$nextTick(() => {
      this.initChart()
    })
    const resizeObserver = new ResizeObserver(() => {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    });
    resizeObserver.observe(this.$refs.chartContainer);
  },
  beforeUnmount() {
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    async getEnvelopeData() {
      try {
        const dataStore = this.$store.state.dataStore
        if (!dataStore.device_id && !dataStore.point_id && !dataStore.time_point) {
          return
        }
        const params = await getParams(4) // 包络谱的 ftype 为 4
        this.band = this.$store.state.SettingConnect.configNodeInfo.measureDefinitions.envelopeFrequencyDetail.split('-').map(Number)
        console.log('band', this.band);
        // 修改 band 参数
        if (params.band) {
          // 这里可以根据需求设置具体的 band 值
          params.band = this.band  // 或其他所需的值
        }
        console.log('包络谱获取到的参数--->', params);
        const result = await getEnvelope(params)
        this.data = result.data.x.map((xValue, index) => [xValue, result.data.y[index]])
        this.loading = false
        this.updateChart();
      } catch (error) {
        this.error = '数据加载失败'
        this.loading = false
      }
    },
    async checkDataStore() {
      const dataStore = this.$store.state.dataStore
      if (!dataStore || !dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
        this.loading = true
        return new Promise(resolve => {
          const interval = setInterval(() => {
            if (this.$store.state.dataStore && this.$store.state.dataStore.device_id) {
              clearInterval(interval)
              this.loading = false
              resolve()
            }
          }, 100)
        })
      }
    },
    initChart() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
      this.chartInstance = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },
    updateChart() {
      if (!this.chartInstance) return
      // 获取数据中的最大 x 值
      const maxX = this.data.length > 0 ? this.data[this.data.length - 1][0] : 0;
      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '5%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none',
              title: {
                zoom: '区域缩放',
                back: '还原缩放'
              }
            },
            restore: { title: '还原' },
            saveAsImage: { title: '保存' }
          }
        },
        xAxis: {
          type: 'value',
          name: '频率(Hz)',
          max: maxX,
          axisLine: {
            symbol: 'none',
            lineStyle: {
              type: 'solid'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '幅值',
          axisLine: {
            symbol: 'none',
            lineStyle: {
              type: 'solid'
            }
          }
        },
        series: [
          {
            data: this.data,
            type: 'line',
            symbol: false,
            smooth: true,
            lineStyle: {
              color: '#4985DF',
              width: 2
            },
            showSymbol: false, // 确保所有状态下都不显示符号
            // 新增优化配置
            sampling: 'lttb',  // 采用 LTTB 降采样算法
            large: true,       // 开启大数据优化
            largeThreshold: 5000,  // 超过该数值开启优化
            progressive: 200,  // 分片渲染, 每片数据点的数量
            progressiveThreshold: 3000  // 超过该阈值后启用分片渲染
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          }
        ]
      }

      this.chartInstance.setOption(option)
    }
  }
}
</script>

<style scoped>
.top-container {
  background: #f0f0f0;
}
.trend-toolbar {
  background: #f0f0f0;
  height: 30px;
}
.chart {
  width: 100%;
  height: 95%;
  margin: 0 auto;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.chart:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
</style>
