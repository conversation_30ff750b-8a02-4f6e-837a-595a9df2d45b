<template>
  <div class="main-layout">
    <!-- 顶部svg图标-->
    <div class="header">
      <ChartSwitcher @chart-change="updateChart" />
    </div>
    <div class="layout-body">
      <!-- 左边导航 -->
      <div style="height: 100%">
        <LeftSidebar
          @company-added="handleCompanyAdded"
          @expand-all="handleExpandAll"
          @collapse-all="handleCollapseAll"
        />
      </div>
      <div ref="container" class="maincontainer">
        <!-- <div class="split-container"> -->
          <!-- 左 -->
          <div ref="leftContainer" class="left-container">
            <!-- 数据采集表格 -->
            <DataConfiguration ref="dataConfig" />
          </div>
          <!-- 分割线 -->
          <div class="resizer" @mousedown="initResize" />
           <!-- 右 -->
          <div ref="rightContainer" class="right-container">
            <!-- 默认视图 -->
            <!-- <card v-if="!showChart" /> -->
            <!-- 数据图表 -->
            <ChartComponents
              ref="chartComponent"
              :type="currentChart"
              style="height: 100%;"
              @show-tooltip="showTooltip"
              @contextmenu.prevent
              @dragstart.prevent
            />

          </div>
        <!-- </div> -->
      </div>
    </div>
    <!-- 底部容器 -->
    <!-- <div class="bottom-container">
      <el-button size="mini" type="text" @click="showDialog = true">
        <svg-icon icon-class="axis" />
      </el-button>
    </div> -->
    <!-- 弹窗 -->
    <!-- <el-dialog title="报警" :visible.sync="showDialog" width="70%">
      <AlarmDialogContent />
    </el-dialog> -->
    <div v-if="tooltip.show" :style="tooltipStyle" class="tooltip">{{ tooltip.content }}</div>
  </div>
</template>

<script>
import LeftSidebar from './LeftSidebar/LeftSidebar.vue'
import DataConfiguration from './DataConfiguration/index.vue'
import ChartComponents from './ChartComponents/ChartComponents.vue'
import ChartSwitcher from './ChartSwitcher/ChartSwitcher.vue'
import AlarmDialogContent from './AlarmDialogContent/AlarmDialogContent.vue'
import { mapState } from 'vuex'

// import card from './CardComponent/card.vue'
export default {
  name: 'textPage',
  components: {
    DataConfiguration,
    LeftSidebar,
    ChartComponents,
    AlarmDialogContent,
    ChartSwitcher,
    // card
  },
  data() {
    return {
      showDialog: false, // 弹窗是否显示
      currentChart: 'TrendChart', // 初始化图表
      startX: 0, // 初始X位置
      startWidth: 0, // 初始宽度
      isResizing: false, // 是否正在调整大小
      showChart: false, // 控制图表显示

      tooltip: { // tooltip 数据
        show: false,
        content: '',
        left: 0,
        top: 0
      }
    }
  },
  computed: {
    ...mapState('chartSwitcher', ['activeChart']),
    tooltipStyle() {
      return {
        position: 'absolute',
        backgroundColor: 'white',
        border: '1px solid black',
        padding: '5px',
        left: this.tooltip.left + 'px',
        top: this.tooltip.top + 'px',
        display: this.tooltip.show ? 'block' : 'none'
      }
    },
    activeChart() {
      return this.$store.state.chartSwitcher.activeChart
    }
  },

  watch: {
    activeChart: {
      immediate: true,
      handler(newChartType) {
        if (newChartType) {
          this.updateChart(newChartType)
        }
      }
    }
  },

  mounted(){

  },
  methods: {
    // 图表更新组件
    updateChart(chartType) {
      this.currentChart = chartType
      this.$store.dispatch('chartSwitcher/updateChart', chartType)
      console.log('Switching to chart:', chartType) // 添加调试日志
      
    },
    // 初始化调整大小
    initResize(e) {
      e.preventDefault()
      this.startX = e.clientX
      this.startWidth = this.$refs.leftContainer.offsetWidth
      this.isResizing = true
      document.addEventListener('mousemove', this.resize)
      document.addEventListener('mouseup', this.stopResize)
    },
    // 调整大小
    resize(e) {
      if (this.isResizing) {
        const newWidth = this.startWidth + (e.clientX - this.startX)
        const maxWidth = this.$refs.container.offsetWidth * 0.5 // 父级宽度的50%
        const minWidth = Math.max(this.startWidth * 0.5, 100) // 当前宽度的50%或最小100
        if (newWidth >= minWidth && newWidth <= maxWidth) {
          this.$refs.leftContainer.style.width = `${newWidth}px`
          this.$refs.rightContainer.style.width = `calc(100% - ${newWidth}px)`
          this.$refs.chartComponent.resizeChart()
        }
      }
    },
    // 停止调整大小
    stopResize() {
      this.isResizing = false
      document.removeEventListener('mousemove', this.resize)
      document.removeEventListener('mouseup', this.stopResize)
    },
    // 显示tooltip
    showTooltip(data) {
      this.tooltip.content = data.content
      this.tooltip.left = data.left
      this.tooltip.top = data.top - 20 // 使 tooltip 显示在点的上方
      this.tooltip.show = true
    },
    // 调用 DataConfiguration 组件中的 WorkTree 子组件的方法
    handleCompanyAdded() {
      console.log('接收到company-added事件')
      if (this.$refs.dataConfig && this.$refs.dataConfig.$refs.workTree) {
        // console.log('调用 getTreelist 方法')
        this.$refs.dataConfig.$refs.workTree.getTreelist()
      } else {
        // console.error('无法获取到 workTree 的引用')
      }
    },
    handleExpandAll() {
      // 调用 DataConfiguration 组件的展开方法
      if (this.$refs.dataConfig && this.$refs.dataConfig.$refs.workTree) {
        this.$refs.dataConfig.$refs.workTree.expandAll()
      }
    },

    handleCollapseAll() {
      // 调用 DataConfiguration 组件的收缩方法
      if (this.$refs.dataConfig && this.$refs.dataConfig.$refs.workTree) {
        this.$refs.dataConfig.$refs.workTree.collapseAll()
      }
    }
  }
}
</script>

<style scoped>
@import './index.css';
</style>
