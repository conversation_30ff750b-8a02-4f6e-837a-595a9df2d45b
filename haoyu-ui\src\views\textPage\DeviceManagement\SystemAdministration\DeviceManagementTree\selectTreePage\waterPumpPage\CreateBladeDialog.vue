<template>
  <el-dialog
    :visible.sync="localVisible"
    :title="title"
    :close-on-click-modal="false"
    width="50%"
    @close="handleCancel"
  >
    <el-form :model="form" label-width="120px">
      <el-form-item label="描述">
        <el-input v-model="form.description" size="mini" />
      </el-form-item>
      <el-form-item label="叶片数">
        <el-input v-model="form.bladeCount" size="mini" type="number" />
      </el-form-item>
      <el-form-item label="所在轴">
        <el-input v-model="form.axis" size="mini" disabled />
      </el-form-item>

      <el-form-item label="转速">
        <el-input v-model="form.inputSpeed" size="mini" disabled />
      </el-form-item>

    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleConfirm">确认</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    editMode: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新建叶片'
    },
    selectedNode: {
      type: Object,
      default: () => ({})
    },
    bladeData: {
      type: Object,
      default: () => ({
        description: '叶片',
        bladeCount: 10,
        axis: '',
        treeIcon: 'blade',
        inputSpeed: ''
      })
    }
  },
  data() {
    return {
      localVisible: this.visible,
      form: {
        description: '叶片',
        bladeCount: 10,
        axis: '',
        treeIcon: 'blade',
        inputSpeed: ''
      }
    }
  },
  watch: {
    visible(newVal) {
      if (!this.editMode) {
      // console.log('mouted')
        this.form.axis = this.selectedNode.title
      }
      this.form.inputSpeed = this.selectedNode.inputSpeed
      this.localVisible = newVal
    },
    bladeData(newVal) {
      this.form = newVal
      console.log(this.editMode)
      if (!this.editMode) {
        // console.log('更改')
        this.form.axis = this.selectedNode.title
      }
    },
    localVisible(newVal) {
      this.$emit('update:visible', newVal)
    }
  },
  mounted() {
    if (!this.editMode) {
      // console.log('mouted')
      this.form.axis = this.selectedNode.title
    }
    this.form.inputSpeed = this.selectedNode.inputSpeed
    // 打印接收到的树节点数据
    // console.log('Received selectedNode:', this.selectedNode)
  },
  methods: {
    Reset() {
      this.form = {
        description: '叶片',
        bladeCount: 10,
        axis: this.selectedNode.title,
        treeIcon: 'blade',
        inputSpeed: this.selectedNode.inputSpeed ? this.selectedNode.inputSpeed : ''
      }
    },
    handleConfirm() {
      const submitData = {
        id: null,
        title: this.form.description,
        treeIcon: this.form.treeIcon,
        table: this.form,
        inputSpeed: this.inputSpeed
      }
      this.$emit('confirm', submitData)
      this.Reset()
      this.localVisible = false
    },
    handleCancel() {
      this.Reset()
      this.$emit('cancel')
      this.localVisible = false
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
