<template>
  <div class="model-list-panel">
    <div class="panel-header">
      <h3>模型列表</h3>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          size="mini"
          placeholder="搜索模型"
          prefix-icon="el-icon-search"
          clearable
        ></el-input>
      </div>
    </div>
    <div class="model-list">
      <div v-for="(model, index) in filteredModels" 
           :key="index" 
           :class="['model-item', selectedModelIndices.includes(getOriginalIndex(index)) ? 'active' : '']"
           @click="toggleModelSelection(getOriginalIndex(index))"
      >
        <div class="model-checkbox">
          <el-checkbox 
            :value="selectedModelIndices.includes(getOriginalIndex(index))"
            @click.native.stop
            @change="handleCheckboxChange($event, getOriginalIndex(index))"
          ></el-checkbox>
        </div>
        <div class="model-info">
          <span v-if="!isEditing(getOriginalIndex(index))" class="model-name" @dblclick="startEditName(getOriginalIndex(index))">
            {{ model.name || `未命名模型${getOriginalIndex(index) + 1}` }}
          </span>
          <el-input
            v-else
            v-model="models[getOriginalIndex(index)].name"
            size="mini"
            @blur="finishEditName(getOriginalIndex(index))"
            @keyup.enter.native="finishEditName(getOriginalIndex(index))"
            ref="nameInput"
          ></el-input>
        </div>
        <div class="model-actions">
          <el-button size="mini" type="text" @click.stop="copyModel(getOriginalIndex(index))">复制</el-button>
          <el-button size="mini" type="text" @click.stop="deleteModel(getOriginalIndex(index))">删除</el-button>
        </div>
      </div>
      <div v-if="filteredModels.length === 0" class="no-models">
        {{ models.length === 0 ? '暂无模型，请添加模型' : '没有找到匹配的模型' }}
      </div>
    </div>
    <div class="model-list-footer" v-if="models.length > 0">
      <div class="footer-actions">
        <el-button size="mini" @click="selectAllModels" type="text">全选</el-button>
        <el-button size="mini" @click="deselectAllModels" type="text">取消选择</el-button>
        <el-button 
          size="mini" 
          type="text" 
          @click="deleteSelectedModels" 
          :disabled="selectedModelIndices.length === 0 || isLoading"
          class="delete-selected-btn">批量删除</el-button>
      </div>
      <div class="save-action">
        <el-button 
          size="mini" 
          type="primary" 
          @click="saveChanges" 
          :loading="saving">
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModelList',
  
  props: {
    models: {
      type: Array,
      required: true
    },
    selectedModelIndices: {
      type: Array,
      default: () => []
    },
    hasChanges: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      searchQuery: '',
      editingIndex: null,
      saving: false
    };
  },
  
  computed: {
    filteredModels() {
      if (!this.searchQuery) {
        return this.models;
      }
      
      // 获取过滤后的模型和索引映射
      const result = this.models.filter((model, index) => {
        const modelName = model.name || `未命名模型${index + 1}`;
        return modelName.toLowerCase().includes(this.searchQuery.toLowerCase());
      });
      
      return result;
    }
  },
  
  methods: {
    // 获取过滤列表中项目的原始索引
    getOriginalIndex(filteredIndex) {
      if (!this.searchQuery) {
        // 没有搜索时，索引相同
        return filteredIndex;
      }
      
      // 有搜索时，需要找到原始索引
      const modelAtFilteredIndex = this.filteredModels[filteredIndex];
      return this.models.findIndex(model => model === modelAtFilteredIndex);
    },
    
    // 检查模型是否处于编辑状态
    isEditing(index) {
      return this.editingIndex === index;
    },
    
    // 简化的选择模型方法，点击直接选中或取消选中
    toggleModelSelection(index) {
      const isSelected = this.selectedModelIndices.includes(index);
      
      if (isSelected) {
        // 如果已选中，则取消选中
        const newIndices = this.selectedModelIndices.filter(i => i !== index);
        this.$emit('update:selectedModelIndices', newIndices);
      } else {
        // 如果未选中，则添加到选中列表中
        const newIndices = [...this.selectedModelIndices, index];
        this.$emit('update:selectedModelIndices', newIndices);
      }
    },
    
    // 复选框的变更处理
    handleCheckboxChange(isChecked, index) {
      if (isChecked) {
        // 添加到选中列表
        if (!this.selectedModelIndices.includes(index)) {
          const newIndices = [...this.selectedModelIndices, index];
          this.$emit('update:selectedModelIndices', newIndices);
        }
      } else {
        // 从选中列表移除
        const newIndices = this.selectedModelIndices.filter(i => i !== index);
        this.$emit('update:selectedModelIndices', newIndices);
      }
    },
    
    // 全选模型
    selectAllModels() {
      const allIndices = this.models.map((_, index) => index);
      this.$emit('update:selectedModelIndices', allIndices);
    },
    
    // 取消所有选择
    deselectAllModels() {
      this.$emit('update:selectedModelIndices', []);
    },
    
    // 批量删除选中的模型
    deleteSelectedModels() {
      if (this.selectedModelIndices.length === 0) return;
      
      this.$confirm('确定要删除选中的所有模型吗？此操作不可撤销', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 将selectedModelIndices排序，从大到小排列，防止删除时索引变化
        const sortedIndices = [...this.selectedModelIndices].sort((a, b) => b - a);
        
        // 依次删除所有选中的模型
        sortedIndices.forEach(index => {
          this.$emit('delete-model', index);
        });
        
        // 清空选择
        this.$emit('update:selectedModelIndices', []);
      }).catch(() => {
        // 取消删除
      });
    },
    
    // 保存更改
    saveChanges() {
      this.saving = true;
      this.$emit('save-changes');
      
      // 模拟保存完成后的状态恢复
      setTimeout(() => {
        this.saving = false;
      }, 500);
    },
    
    startEditName(index) {
      this.editingIndex = index;
      this.$nextTick(() => {
        if (this.$refs.nameInput && this.$refs.nameInput[0]) {
          this.$refs.nameInput[0].focus();
        }
      });
    },
    
    finishEditName(index) {
      const model = this.models[index];
      if (!model.name || model.name.trim() === '') {
        model.name = `未命名模型${index + 1}`;
      }
      const newName = model.name.trim();
      
      this.editingIndex = null;
      this.$emit('name-updated', { index, name: newName });
    },
    
    copyModel(index) {
      this.$emit('copy-model', index);
    },
    
    deleteModel(index) {
      this.$emit('delete-model', index);
    }
  }
};
</script>

<style scoped>
.model-list-panel {
  background: white;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.search-box {
  width: 150px;
}

.model-list {
  max-height: 200px;
  overflow-y: auto;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
  cursor: pointer;
}

.model-item:hover {
  background: #f5f7fa;
}

.model-item.active {
  background: #ecf5ff;
}

.model-checkbox {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.model-info {
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
}

.model-name {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.model-name:hover {
  color: #409EFF;
}

.model-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.no-models {
  color: #909399;
  text-align: center;
  padding: 20px 0;
}

.model-list-footer {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.footer-actions {
  display: flex;
  gap: 10px;
}

.delete-selected-btn {
  color: #F56C6C;
}

.delete-selected-btn.is-disabled {
  color: #C0C4CC;
  cursor: not-allowed;
}

.save-action {
  transition: all 0.3s;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}
</style> 