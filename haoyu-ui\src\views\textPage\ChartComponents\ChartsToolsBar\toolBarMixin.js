export default {
    data() {
      return {
        dialogVisible: false,
        selectedTools: [],
        tempSelectedTools: [],
        // 多点选择相关数据
        calcPopoverVisible: false,
        selectedPoints: [],
        points: [
          { id: "A", name: "A" },
          { id: "B", name: "B" },
          { id: "C", name: "C" },
          { id: "D", name: "D" },
          { id: "E", name: "E" },
        ],
        tools: [
          {
            id: 'limit',
            name: '上下限选择',
            icon: 'limit',
            iconsize: '16px'
          },
          {
            id: 'conversion',
            name: '单位转换',
            icon: 'conversion',
            iconsize: '16px'
          },
          {
            id: 'download',
            name: '下载',
            icon: 'download',
            iconsize: '16px'
          },
          {
            id: 'calculate',
            name: '多点选择',
            icon: 'calcICON',
            iconsize: '16px'
          },
          {
            id: 'audioICON',
            name: '播放声波',
            icon: 'audioICON',
            iconsize: '16px'
          },
          {
            id: 'toogledisplay',
            name: '显示触发模式切换',
            icon: 'toogledisplay',
            iconsize: '16px'
          }
        ],
        limitPopoverVisible: false,
        yMin: null,
        yMax: null,
        conversionPopoverVisible: false,
        currentUnit: 'Hz',
        targetUnit: 'Hz',
        frequencyUnits: [
          { label: '赫兹 (Hz)', value: 'Hz' },
          { label: '千赫兹 (kHz)', value: 'kHz' },
          { label: '兆赫兹 (MHz)', value: 'MHz' }
        ],
      }
    },
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      unit: {
        type: String,
        default: 'Hz'
      }
    },
    watch: {
      unit: {
        immediate: true,
        handler(newUnit) {
          this.currentUnit = newUnit;
        }
      }
    },
    methods: {
      getToolName(toolId) {
        const tool = this.tools.find(t => t.id === toolId);
        return tool ? tool.name : '';
      },
  
      getToolIcon(toolId) {
        const tool = this.tools.find(t => t.id === toolId);
        return tool ? tool.icon : '';
      },
  
      openToolDialog() {
        this.tempSelectedTools = [...this.selectedTools];
        this.dialogVisible = true;
      },
  
      handleToolClick(toolId) {
        if (toolId === 'toogledisplay') {
          this.$emit('display-mode-change');
        }
        this.$emit('tool-action', toolId);
      },
  
      handleDialogCancel() {
        this.dialogVisible = false;
        this.tempSelectedTools = [...this.selectedTools];
      },
  
      handleDialogConfirm() {
        this.selectedTools = [...this.tempSelectedTools];
        this.dialogVisible = false;
      },
  
      // 多点选择相关方法
      confirmPointSelection() {
        const selectedPointsData = this.selectedPoints.map((pointId, index) => {
          const point = this.points.find(p => p.id === pointId);
          return {
            id: pointId,
            name: point.name,
            index: index
          };
        });
  
        this.$emit('points-selected', selectedPointsData);
        this.$message.success("选择已确认：" + this.selectedPoints.join(", "));
        this.calcPopoverVisible = false;
      },
  
      cancelPointSelection() {
        this.selectedPoints = [];
        this.calcPopoverVisible = false;
      },
  
      confirmLimitSelection() {
        const min = this.yMin !== null && this.yMin !== '' ? parseFloat(this.yMin) : null;
        const max = this.yMax !== null && this.yMax !== '' ? parseFloat(this.yMax) : null;
  
        if (min !== null && max !== null && min >= max) {
          this.$message.error('最小值必须小于最大值');
          return;
        }
  
        this.$emit('limit-selected', { min, max });
        this.$message.success(`筛选范围：${min || '最小'} ~ ${max || '最大'}`);
        this.limitPopoverVisible = false;
      },
  
      resetLimitSelection() {
        this.yMin = null;
        this.yMax = null;
        this.$emit('limit-selected', { min: null, max: null });
        this.$message.success('已恢复显示全部数据');
        this.limitPopoverVisible = false;
      },
  
      cancelLimitSelection() {
        this.yMin = null;
        this.yMax = null;
        this.limitPopoverVisible = false;
      },
  
      confirmUnitConversion() {
        if (this.currentUnit === this.targetUnit) {
          this.$message.warning('当前单位与目标单位相同，无需转换');
          return;
        }
  
        this.$emit('unit-conversion', {
          fromUnit: this.currentUnit,
          toUnit: this.targetUnit
        });
        this.currentUnit = this.targetUnit;
        this.$message.success(`单位已转换：${this.currentUnit}`);
        this.conversionPopoverVisible = false;
      },
  
      resetUnitConversion() {
        this.targetUnit = 'Hz';
        this.$emit('unit-conversion', {
          fromUnit: this.currentUnit,
          toUnit: this.targetUnit
        });
        this.currentUnit = this.targetUnit;
        this.$message.success('已恢复默认单位');
        this.conversionPopoverVisible = false;
      },
  
      cancelUnitConversion() {
        this.targetUnit = this.currentUnit;
        this.conversionPopoverVisible = false;
      }
    }
  }