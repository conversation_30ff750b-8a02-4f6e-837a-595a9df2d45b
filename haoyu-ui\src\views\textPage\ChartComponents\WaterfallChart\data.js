// import axios from "axios"
import request from '@/utils/request'
// 构造默认请求数据
export const req = {
  "device_id": "3995",
  "point_id": "2657",
  "time_point": "2024-11-05 12:08:12",
  "ftype": "8",   //瀑布图
  "input_type": 0,
  "outer_type": 0,
  "cf": 2,
  "fth": 0.02,
  "band": [
      2
  ],
  "win": "hanning",
  "ytype": 0,
  "fs_lines": [
      2000,
      2000
  ],
  "fband": [],
  "nperseg": "",
  "noverlap": ""
}

export async function getWaterfallData(data){
  /* return axios.post('http://112.14.133.41:8090/waveform_graph', data)
  .then(response => {
    return response.data // 返回响应数据
  })
  .catch(error => {
    console.error('请求错误:', error);
    throw error; // 继续抛出错误以便在调用处处理
  }); */
  const param1 = 'waveform_graph'
  return request({
    url: `/python/render/post/${param1}`,
    method: 'post',
    data: data
  })
}
