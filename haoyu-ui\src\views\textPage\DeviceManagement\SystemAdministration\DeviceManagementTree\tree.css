.a-input {
  width: auto
}
.tree-container {
  border-radius: 4px;
  padding: 10px;
}

.custom-tree .ant-tree {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
}

.custom-tree .ant-tree-treenode {
  padding-left: 16px; /* 一级和二级节点之间的间隙 */
}

.custom-tree {
  max-width: 100%; /* 设置最大宽度 */
  max-height: 100%; /* 设置最大高度 */
}


.custom-tree .ant-tree-node-content-wrapper {
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.custom-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f5f5f5;
}
.form-container {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 设置每个表单项之间的间距 */
}

.form-item {
  display: flex;
  align-items: center;
}

.form-item label {
  width: 150px; /* 设置标签的宽度 */
  margin-right: 10px; /* 设置标签和输入框之间的间距 */
  text-align: right; /* 使标签右对齐 */
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px; /* 设置底部按钮和表单之间的间距 */
}
/* 自定义滚动条样式 */
.ant-tree::-webkit-scrollbar {
  width: 8px; /* 设置滚动条宽度 */
  height: 8px; /* 设置滚动条高度 */
}

.ant-treet::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 设置滚动条颜色 */
  border-radius: 4px; /* 设置滚动条圆角 */
}

.ant-tree::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1); /* 设置滚动条轨道颜色 */
}

.tree-container .ant-tree{
  height: 100%;
}

.tree-container {
  display: flex;
  align-items: flex-start;
  height: 100%;
  width: 100%;
}

.tree-icon {
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 300px;

}

.search-container .ant-input {
  margin-right: 10px; /* 搜索框与按钮之间的间距 */
}

.search-container .ant-btn {
  margin-left: 10px; /* 按钮与输入框之间的间距 */
}


.ant-tree-show-line .ant-tree-switcher.ant-tree-switcher-noop:before {
  content: " ";
  width: 16px;
  border-bottom: 1px solid #d9d9d9;
  height: 50%;
  position: absolute;
  left: 12px;
}

.ant-tree-show-line .ant-tree-child-tree>li:last-child>.ant-tree-switcher.ant-tree-switcher-noop:before, .ant-tree-show-line>li:last-child>.ant-tree-switcher.ant-tree-switcher-noop:before {
  display: block;
  content: " " !important;
  width: 16px !important;
  border-left: 1px solid #d9d9d9 !important;
  border-bottom: 1px solid #d9d9d9 !important;
  height: 50% !important;
  position: absolute !important;
  left: 12px !important;
}

.ant-tree-show-line .ant-tree-switcher.ant-tree-switcher-noop:after {
  content: " " !important;
  width: 1px !important;
  height: 100% !important;
  position: absolute !important;
  left: 12px !important;
  background: linear-gradient(to bottom, #d9d9d9 50%, transparent 50%) !important;
}

.ant-tree-show-line .anticon-file {
  display: none !important;
}

.Point-container {
  display: flex;
  height: 70vh;
  padding: 20px;
  background-color: #eceff1; /* 更加工业风的浅灰色背景 */
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* 增加阴影以强调工业化感 */
  border: 1px solid #b0bec5; /* 工业风的边框颜色 */
}

.Point-left {
  display: flex;
  flex-direction: column;
  width: 30%; /* 90% */
  height: 100%;
  background-color: #f5f5f5; /* 淡灰色背景 */
  border-radius: 8px; /* 圆角边框 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  padding: 10px; /* 内边距 */
}

.Point-right {
  width: 90%; /* 90% */
  margin-left: 15px;
  background-color: #fafafa;
  padding: 20px;
  border-radius: 4px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #cfd8dc;
}

.Point-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700; /* 加粗字体 */
  color: #37474f; /* 更深的字体颜色 */
  text-align: center;
  border-bottom: 3px solid #90a4ae; /* 更深的边框颜色 */
  padding-bottom: 10px;
  text-transform: uppercase; /* 全部大写，增加工业感 */
}


.Point-left-top {
  flex: 1;
  margin-bottom: 10px; /* 底部间距 */
  background-color: #ffffff; /* 白色背景 */
  border-radius: 4px; /* 圆角边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  overflow: auto; /* 如果树内容超出高度，将出现滚动条 */
  padding: 10px; /* 内边距 */
}

.Point-left-bottom {
  flex: 1;
  text-align: left;
  background-color: #ffffff; /* 白色背景 */
  border-radius: 4px; /* 圆角边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  padding: 10px; /* 内边距 */
  overflow: auto; /* 如果表单内容超出高度，将出现滚动条 */
}

.trigger-container {
  display: flex;
  flex-direction: column;
}

.trigger-item {
  display: block;
  font-size: 12px;
  color: #131414; /* 设置字体颜色 */
  border: none; /* 去除按钮边框 */
  text-align: left; /* 左对齐文本 */
  border-radius: 4px; /* 圆角边框 */
  background-color: #f5f5f5; /* 背景色 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
}

.trigger-item:hover {
  background-color: #e0e0e0; /* 鼠标悬停时的背景色 */
}

.dialog-footer {
  text-align: right;
}
