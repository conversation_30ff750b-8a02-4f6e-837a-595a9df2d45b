<!-- 行星轴系组件 -->
<template>
  <div class="planetary-section">
    <!-- 齿轮箱参数概览 -->
    <div class="gearbox-overview">
      <div class="overview-item">
        <span class="label">总传动比:</span>
        <span class="value">{{ calculateTotalRatio }}</span>
      </div>
      <div class="overview-item">
        <span class="label">输入转速:</span>
        <el-input-number
          v-model="inputSpeed"
          :min="0"
          :max="100000"
          :step="1"
          size="mini"
          @change="handleInputSpeedChange"
          style="width: 120px"
        >
          <template slot="suffix">rpm</template>
        </el-input-number>
      </div>
      <div class="overview-item">
        <span class="label">输出转速:</span>
        <span class="value">{{ getOutputSpeed }} rpm</span>
      </div>
      <div class="overview-item">
        <span class="label">行星轮数量:</span>
        <el-input-number
          v-model="planetaryCount"
          :min="3"
          :max="12"
          :step="1"
          size="mini"
          @change="handlePlanetaryCountChange"
          style="width: 120px"
        />
      </div>
    </div>

    <!-- 齿轮箱类型说明 -->
    <div class="type-description">
      <el-alert
        :title="getTypeDescription"
        type="info"
        :description="getTypeDetailedDescription"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 齿轮详细信息表格 -->
    <el-table
      :data="gearbox.children"
      border
      class="gear-detail-table"
      size="mini"
    >
      <!-- 级数列 -->
      <el-table-column
        prop="level"
        label="级数"
        width="80"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag size="mini" type="info">{{ scope.row.level }}</el-tag>
        </template>
      </el-table-column>

      <!-- 齿轮类型和齿数列 -->
      <el-table-column
        label="齿轮类型"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <div class="gear-info">
            <span class="gear-type">{{ scope.row.type }}</span>
            <div class="editable-cell">
              <template v-if="scope.row.editableTeethCount">
                <el-input
                  v-model.number="scope.row.teethCount"
                  size="mini"
                  @blur="handleBlur(scope.row, 'teethCount')"
                  @keyup.enter.native="handleBlur(scope.row, 'teethCount')"
                >
                  <template slot="append">齿</template>
                </el-input>
              </template>
              <div
                v-else
                class="cell-value"
                @dblclick="handleEdit(scope.row, 'teethCount')"
              >
                {{ scope.row.teethCount }} 齿
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 转速列 -->
      <el-table-column
        prop="inputSpeed"
        label="转速"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          <div class="editable-cell">
            <template v-if="scope.row.editableInputSpeed">
              <el-input
                v-model.number="scope.row.inputSpeed"
                size="mini"
                :disabled="!isInputShaft(scope.row)"
                @blur="handleBlur(scope.row, 'inputSpeed')"
                @keyup.enter.native="handleBlur(scope.row, 'inputSpeed')"
              >
                <template slot="append">rpm</template>
              </el-input>
            </template>
            <div
              v-else
              class="cell-value"
              @dblclick="handleEdit(scope.row, 'inputSpeed')"
            >
              {{ scope.row.inputSpeed }} rpm
              <!-- 输入轴标识 -->
              <el-tag v-if="isInputShaft(scope.row)" size="mini" type="success" style="margin-left: 4px;">输入轴</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 啮合频率列 -->
      <el-table-column
        label="啮合频率"
        align="center"
      >
        <template slot-scope="scope">
          <div class="frequency-info">
            <div class="frequency-section">
              <div class="frequency-header">
                <!-- <span class="frequency-title">{{ scope.row.type }}相关频率</span> -->
                <el-select 
                  v-model="scope.row.selectedFrequencyType" 
                  size="mini" 
                  style="width: 280px;"
                  @change="handleFrequencyTypeChange(scope.row)"
                  popper-class="frequency-select-dropdown"
                >
                  <el-option
                    v-for="item in getFrequencyOptions(scope.row)"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <div class="frequency-option">
                      <div class="frequency-option-header">
                        {{ item.label }}
                        <span class="frequency-option-value">{{ getFrequencyValue(scope.row, item.value) }} Hz</span>
                      </div>
                      <div class="frequency-option-formula">
                        <span class="formula-label">公式：</span>
                        <span class="formula-text">{{ item.formula }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </div>
              
              <div class="frequency-detail">
                <div class="frequency-value">
                  {{ getSelectedFrequencyValue(scope.row) }} Hz
                </div>
                <div class="formula-container">
                  <span class="formula-label">公式：</span>
                  <span class="formula-text">{{ getSelectedFormula(scope.row) }}</span>
                </div>
              </div>
            </div>

          </div>
        </template>
      </el-table-column>

      <!-- 倍频选择列 -->
      <el-table-column
        label="倍频选择"
        width="240"
        align="center"
      >
        <template slot="header">
          <span>倍频选择</span>
          <el-tooltip
            content="输入数字N，将自动选择1~N倍频"
            placement="top"
            effect="light"
          >
            <i class="el-icon-question" style="color:#409EFF;cursor:pointer;margin-left:5px;"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div class="multiple-selection">
            <template >
              <div class="mesh-description">
                {{ getSelectedFrequencyLabel(scope.row) }}
              </div>
              <div class="multiple-row">
                <el-input
                  v-model.number="scope.row.multipleInput"
                  size="mini"
                  :max="500"
                  placeholder="输入倍频数"
                  @input="handleMultipleInput(scope.row)"
                >
                  <template slot="append">倍</template>
                </el-input>
              </div>
            </template>

          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 选中倍频的频率显示 -->
    <div v-if="hasSelectedMultiples" class="selected-frequencies">
      <div class="frequency-title">选中的频率点：</div>
      <el-tag
        v-for="freq in selectedFrequencies"
        :key="freq.id"
        size="mini"
        type="info"
        class="frequency-tag"
      >
        {{ freq.value.toFixed(2) }} Hz ({{ freq.multiple }}倍频)
      </el-tag>
    </div>
  </div>
</template>

<script>
  import { 
    planetGearRPMFromRing, 
    absoluteGearRPMFromRing, 
    sunGearRPMFromSun, 
    planetCarrierRPMFromSun, 
    ringFailureFrequency, 
    planetGearRPM3, 
    callingFrequency 
  } from './calculate.js';

export default {
  name: 'PlanetaryAxisSystem',
  props: {
    gearbox: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      planetaryType: '', // 行星轴系类型
      planetaryCount: 4, // 默认值为4
      typeDescriptions: {
        '行星架输入': {
          title: '行星架输入型 - 行星架输入，太阳轮输出',
          detail: '这种类型的行星齿轮减速器以行星架为输入轴，太阳轮为输出轴，内齿圈固定。适用于需要大减速比的场合。'
        },
        '太阳轮输入': {
          title: '太阳轮输入型 - 太阳轮输入，行星架输出',
          detail: '这种类型的行星齿轮减速器以太阳轮为输入轴，行星架为输出轴，内齿圈固定。适用于需要中等减速比的场合。'
        }
      },
      meshFrequencyFormulas: {
        '行星架输入': {
          // 太阳轮转频
          sunPlanet: 'N3 = (1 + Zr/Zs) × N1',
          // 行星轮转频
          ringPlanet: 'N2 = (Zr/Zp) × N1',
          // 行星轮绝对转频
          absolutePlanet: 'N2,abs = (1 + Zr/Zp) × N1',
          // 单个行星轮与太阳轮的啮合频率
          singleSunPlanetMesh: 'fp_s_single = n_c × Z_s / (60 × N)',
          // 单个行星轮与内齿圈的啮合频率
          singleRingPlanetMesh: 'fp_r_single = n_c / 60',
          // 太阳轮上某一点通过的行星轮的频率
          sunPassFreq: 'f_s_pass = n_c × N / 60',
          // 内齿圈某一点通过的行星轮的频率
          ringPassFreq: 'f_fault = p × N1',
          // 行星架的转动频率
          carrierRotateFreq: 'f_c = n_c / 60',
          // 啮合频率
          meshFreq: 'fmesh = Zr × N1'
        },
        '太阳轮输入': {
          // 太阳轮转频
          sunPlanet: 'N3 = N3',
          // 行星架转频
          carrierFreq: 'N1 = Zs/(Zs+Zr) × N3',
          // 行星轮转频
          ringPlanet: 'N2 = (Zr/Zp) × N1 = (Zr/Zp) × [Zs/(Zs+Zr)] × N3',
          // 行星轮绝对转频
          absolutePlanet: 'N2,abs = (1 + Zr/Zp) × N1 = (1 + Zr/Zp) × [Zs/(Zs+Zr)] × N3',
          // 单个行星轮与太阳轮的啮合频率
          singleSunPlanetMesh: 'fp_s_single = n_s / 60',
          // 单个行星轮与内齿圈的啮合频率
          singleRingPlanetMesh: 'fp_r_single = n_s / (60 × Z_s)',
          // 太阳轮上某一点通过的行星轮的频率
          sunPassFreq: 'f_s_pass = n_s × N / 60',
          // 内齿圈某一点通过的行星轮的频率
          ringPassFreq: 'f_fault = p × N1 = p × [Zs/(Zs+Zr)] × N3',
          // 行星架的转动频率
          carrierRotateFreq: 'f_c = n_s / (60 × (1 + Z_r/Z_s))',
          // 啮合频率
          meshFreq: 'fmesh = Zr × N1 = Zr × [Zs/(Zs+Zr)] × N3'
        }
      },
      formulaDescriptions: {
        '行星架输入': {
          sunPlanet: {
            title: '太阳轮-行星轮啮合频率',
            desc: '其中：fp_s - 太阳轮-行星轮啮合频率，n_c - 行星架转速，Z_s - 太阳轮齿数'
          },
          ringPlanet: {
            title: '内齿圈-行星轮啮合频率',
            desc: '其中：fp_r - 内齿圈-行星轮啮合频率，n_c - 行星架转速，Z_r - 内齿圈齿数'
          }
        },
        '太阳轮输入': {
          sunPlanet: {
            title: '太阳轮-行星轮啮合频率',
            desc: '其中：fp_s - 太阳轮-行星轮啮合频率，n_s - 太阳轮转速，Z_s - 太阳轮齿数'
          },
          ringPlanet: {
            title: '内齿圈-行星轮啮合频率',
            desc: '其中：fp_r - 内齿圈-行星轮啮合频率，n_s - 太阳轮转速，Z_r - 内齿圈齿数'
          }
        }
      },
      inputSpeed: 0
    }
  },
  computed: {
    // 获取行星轮数量
    getPlanetaryCount() {
      return this.planetaryCount;
    },

    // 获取类型说明标题
    getTypeDescription() {
      const type = this.getPlanetaryType();
      return this.typeDescriptions[type]?.title || '未知类型';
    },

    // 获取类型详细说明
    getTypeDetailedDescription() {
      const type = this.getPlanetaryType();
      return this.typeDescriptions[type]?.detail || '';
    },

    // 计算总传动比
    calculateTotalRatio() {
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetGear = planetaryGears.find(g => g.type === '行星轮');

      if (!sunGear || !ringGear || !planetGear) return '0.00';

      // 根据不同类型计算传动比
      const sunTeeth = Number(sunGear.teethCount);
      const ringTeeth = Number(ringGear.teethCount);

      let ratio;
      switch (this.getPlanetaryType()) {
        case '行星架输入':
          ratio = (1 + ringTeeth / sunTeeth);
          break;
        case '太阳轮输入':
          ratio = Math.abs(ringTeeth / sunTeeth); // 使用绝对值
          break;
        default:
          ratio = 1;
      }
      return ratio.toFixed(2);
    },

    // 获取输入转速
    getInputSpeed() {
      // 直接使用组件的 inputSpeed
      return Number(this.inputSpeed).toFixed(2);
    },

    // 获取输出转速
    getOutputSpeed() {
      const sunGear = this.gearbox.children.find(g => g.type === '太阳轮');
      if (!sunGear) return '0.00';

      const inputSpeed = Number(this.getInputSpeed);
      const ratio = Number(this.calculateTotalRatio);
      return (inputSpeed / ratio).toFixed(2);
    },

    // 是否有选中的倍频
    hasSelectedMultiples() {
      return this.gearbox.children.some(gear => 
        gear.selectedMultiples && gear.selectedMultiples.length > 0
      );
    },

    // 获取选中的频率点
    selectedFrequencies() {
      const frequencies = [];
      this.gearbox.children.forEach(gear => {
        if (gear.selectedMultiples && gear.selectedMultiples.length > 0) {
          const baseFrequency = this.calculateMeshFrequency(gear);
          gear.selectedMultiples.forEach(multiple => {
            frequencies.push({
              id: `${gear.id}_${multiple}`,
              value: Number(baseFrequency) * multiple,
              multiple,
              type: gear.type
            });
          });
        }
      });
      console.log("frequencies",frequencies)
      return frequencies;
    }
  },
  created() {
    // 初始化输入转速，根据模式从 children 中获取
    const type = this.getPlanetaryType();
    if (type === '太阳轮输入') {
      const sunGear = this.gearbox.children.find(g => g.type === '太阳轮');
      this.inputSpeed = sunGear ? Number(sunGear.inputSpeed) : 0;
    } else {
      const planetGear = this.gearbox.children.find(g => g.type === '行星轮');
      this.inputSpeed = planetGear ? Number(planetGear.inputSpeed) : 0;
    }
    // 初始化后续逻辑
    this.updateAllSpeeds();
    this.initDefaultFrequencyOptions();
    this.initPlanetaryCount();
    this.logAllFormulasAndResults();
  },
  methods: {
    // 新增一个方法，用于记录所有公式和结果
    logAllFormulasAndResults() {
      console.log("=========== 行星轴系公式和结果记录 ===========");
      const type = this.getPlanetaryType();
      console.log("行星轴系类型:", type);
      
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetGear = planetaryGears.find(g => g.type === '行星轮');
      
      console.log("齿轮参数:", {
        太阳轮齿数: sunGear.teethCount,
        内齿圈齿数: ringGear.teethCount,
        行星轮齿数: planetGear.teethCount,
        行星轮数量: this.planetaryCount
      });
      
      console.log("转速信息:", {
        输入转速: this.inputSpeed,
        太阳轮转速: sunGear.inputSpeed,
        内齿圈转速: ringGear.inputSpeed,
        行星架转速: planetGear.inputSpeed
      });
      
      const sunTeeth = Number(sunGear.teethCount);
      const ringTeeth = Number(ringGear.teethCount);
      const planetTeeth = Number(planetGear.teethCount);
      
      if (type === '行星架输入') {
        const carrierRPM = Number(planetGear.inputSpeed);
        const carrierFreq = carrierRPM / 60;
        
        // 太阳轮转频
        const sunRatio = 1 + ringTeeth / sunTeeth;
        const sunFreq = sunRatio * carrierFreq;
        console.log("太阳轮转频计算:", {
          公式: "N3 = (1 + Zr/Zs) * N1",
          计算过程: `${carrierFreq} * (1 + ${ringTeeth}/${sunTeeth}) = ${sunFreq}`,
          结果Hz: sunFreq,
          结果RPM: sunFreq * 60
        });
        
        // 行星轮转频
        const planetRatio = ringTeeth / planetTeeth;
        const planetFreq = planetRatio * carrierFreq;
        console.log("行星轮转频计算:", {
          公式: "N2 = (Zr/Zp) * N1",
          计算过程: `${carrierFreq} * (${ringTeeth}/${planetTeeth}) = ${planetFreq}`,
          结果Hz: planetFreq,
          结果RPM: planetFreq * 60
        });
        
        // 行星轮绝对转频
        const planetAbsRatio = 1 + ringTeeth / planetTeeth;
        const planetAbsFreq = planetAbsRatio * carrierFreq;
        console.log("行星轮绝对转频计算:", {
          公式: "N2,abs = (1 + Zr/Zp) * N1",
          计算过程: `${carrierFreq} * (1 + ${ringTeeth}/${planetTeeth}) = ${planetAbsFreq}`,
          结果Hz: planetAbsFreq,
          结果RPM: planetAbsFreq * 60
        });
        
        // 齿圈故障通过频率
        const faultFreq = this.planetaryCount * carrierFreq;
        console.log("齿圈故障通过频率计算:", {
          公式: "f_fault = p * N1",
          计算过程: `${this.planetaryCount} * ${carrierFreq} = ${faultFreq}`,
          结果Hz: faultFreq,
          结果RPM: faultFreq * 60
        });
        
        // 啮合频率
        const meshFreq = ringTeeth * carrierFreq;
        console.log("啮合频率计算:", {
          公式: "fmesh = Zr * N1",
          计算过程: `${ringTeeth} * ${carrierFreq} = ${meshFreq}`,
          结果Hz: meshFreq
        });
      } else {
        const sunRPM = Number(sunGear.inputSpeed);
        const sunFreq = sunRPM / 60;
        
        // 行星架转频
        const carrierRatio = sunTeeth / (sunTeeth + ringTeeth);
        const carrierFreq = carrierRatio * sunFreq;
        console.log("行星架转频计算:", {
          公式: "N1 = Zs/(Zs+Zr) * N3",
          计算过程: `${sunFreq} * (${sunTeeth}/(${sunTeeth}+${ringTeeth})) = ${carrierFreq}`,
          结果Hz: carrierFreq,
          结果RPM: carrierFreq * 60
        });
        
        // 其他频率计算...
      }
      
      console.log("=========== 行星轴系公式和结果记录结束 ===========");
    },
    
    // 获取行星轴系类型
    getPlanetaryType() {
      const deviceName = this.gearbox.deviceName;
      let currentType;
      
      if (deviceName.includes('行星架输入')) {
        currentType = '行星架输入';
      } else if (deviceName.includes('太阳轮输入')) {
        currentType = '太阳轮输入';
      } else {
        currentType = '行星架输入'; // 默认类型
      }
      
      // 检查类型是否发生变化
      if (this.planetaryType !== currentType) {
        this.planetaryType = currentType;
        
        // 在下一个事件循环中重新初始化默认频率选项
        // 这里使用 nextTick 是为了确保所有计算属性和数据更新后再执行
        this.$nextTick(() => {
          this.initDefaultFrequencyOptions();
        });
      }
      
      return currentType;
    },

    // 计算啮合频率
    calculateMeshFrequency(gear) {
      if (!gear.teethCount || !gear.inputSpeed) return '0.00';

      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetGear = planetaryGears.find(g => g.type === '行星轮');

      if (!sunGear || !ringGear || !planetGear) return '0.00';

      const inputSpeed = Number(gear.inputSpeed);
      const planetaryType = this.getPlanetaryType();
      let frequency = 0;

      switch (gear.type) {
        case '太阳轮':
          switch (planetaryType) {
            case '行星架输入':
              frequency = (Number(sunGear.teethCount) * inputSpeed) / 60;
              break;
            case '太阳轮输入':
              frequency = (Number(sunGear.teethCount) * Math.abs(inputSpeed)) / 60;
              break;
          }
          break;
        case '行星轮':
          switch (planetaryType) {
            case '行星架输入':
              frequency = (Number(planetGear.teethCount) * inputSpeed) / 60;
              break;
            case '太阳轮输入':
              frequency = (Number(planetGear.teethCount) * Math.abs(inputSpeed)) / 60;
              break;
          }
          break;
        case '内齿圈':
          switch (planetaryType) {
            case '行星架输入':
              frequency = (Number(ringGear.teethCount) * inputSpeed) / 60;
              break;
            case '太阳轮输入':
              frequency = (Number(ringGear.teethCount) * Math.abs(inputSpeed)) / 60;
              break;
          }
          break;
      }

      return frequency.toFixed(2);
    },

    // 处理编辑状态
    handleEdit(row, field) {
      this.$set(row, `editable${this.capitalizeFirstLetter(field)}`, true);
      this.$nextTick(() => {
        const input = this.$refs[`input${this.capitalizeFirstLetter(field)}_${row.index}`];
        if (input && input.focus) {
          input.focus();
        }
      });
    },

    // 判断是否为输入轴
    isInputShaft(gear) {
      const type = this.getPlanetaryType();
      switch (type) {
        case '行星架输入':
          return gear.type === '行星轮';
        case '太阳轮输入':
          return gear.type === '太阳轮';
        default:
          return false;
      }
    },

    // 更新所有转速
    updateAllSpeeds() {
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetGear = planetaryGears.find(g => g.type === '行星轮');

      if (!sunGear || !ringGear || !planetGear) {
        console.error("缺少必要的齿轮组件");
        return;
      }

      // 确保齿数为数字
      const sunTeeth = Number(sunGear.teethCount) || 1;
      const ringTeeth = Number(ringGear.teethCount) || 1;
      const planetTeeth = Number(planetGear.teethCount) || 1;
      
      const type = this.getPlanetaryType();
      console.log(`更新所有转速 - ${type}模式:`, {
        当前输入转速: this.inputSpeed,
        太阳轮齿数: sunTeeth,
        内齿圈齿数: ringTeeth,
        行星轮齿数: planetTeeth
      });

      // 无论输入转速是否为0，都需要更新其他转速
      if (type === '太阳轮输入') {
        // 太阳轮输入模式
        // 设置太阳轮转速为输入转速
        this.$set(sunGear, 'inputSpeed', this.inputSpeed);
        
        // 计算行星架转速
        const carrierFreq = planetCarrierRPMFromSun(this.inputSpeed, sunTeeth, ringTeeth, false);
        const carrierRPM = carrierFreq * 60; // 转换回RPM
        this.$set(planetGear, 'inputSpeed', carrierRPM);
        
        // 内齿圈固定
        this.$set(ringGear, 'inputSpeed', 0);
        
        console.log("太阳轮输入模式 - 所有转速:", {
          太阳轮: sunGear.inputSpeed,
          行星架: planetGear.inputSpeed,
          内齿圈: ringGear.inputSpeed
        });
      } else {
        // 行星架输入模式
        // 设置行星架转速为输入转速
        this.$set(planetGear, 'inputSpeed', this.inputSpeed);
        
        // 内齿圈固定
        this.$set(ringGear, 'inputSpeed', 0);
        
        // 计算太阳轮转速
        const sunFreq = sunGearRPMFromSun(this.inputSpeed, sunTeeth, ringTeeth, true);
        const sunRPM = sunFreq * 60; // 转换回RPM
        this.$set(sunGear, 'inputSpeed', sunRPM);
        
        console.log("行星架输入模式 - 所有转速:", {
          行星架: planetGear.inputSpeed,
          太阳轮: sunGear.inputSpeed,
          内齿圈: ringGear.inputSpeed
        });
      }

      // 更新tableData中的转速信息
      if (this.gearbox.table && this.gearbox.table.tableData) {
        if (type === '太阳轮输入') {
          // 太阳轮输入模式
          this.gearbox.table.tableData.speed = sunGear.inputSpeed;
          this.gearbox.table.tableData.sunSpeed = sunGear.inputSpeed;
          this.gearbox.table.tableData.carrierSpeed = planetGear.inputSpeed;
          this.gearbox.table.tableData.ringSpeed = ringGear.inputSpeed;
          this.gearbox.table.tableData.inputShaftSpeed = this.inputSpeed; // 确保设置输入轴转速
        } else {
          // 行星架输入模式
          this.gearbox.table.tableData.speed = planetGear.inputSpeed;
          this.gearbox.table.tableData.carrierSpeed = planetGear.inputSpeed;
          this.gearbox.table.tableData.sunSpeed = sunGear.inputSpeed;
          this.gearbox.table.tableData.ringSpeed = ringGear.inputSpeed;
          this.gearbox.table.tableData.inputShaftSpeed = this.inputSpeed; // 确保设置输入轴转速
        }
      }
    },

    // 处理输入转速变化
    handleInputSpeedChange() {
      const type = this.getPlanetaryType();
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetGear = planetaryGears.find(g => g.type === '行星轮');

      if (!sunGear || !ringGear || !planetGear) return;

      const sunTeeth = Number(sunGear.teethCount) || 1;
      const ringTeeth = Number(ringGear.teethCount) || 1;
      const planetTeeth = Number(planetGear.teethCount) || 1;

      console.log(`处理输入转速变化 - ${type}模式:`, {
        输入转速: this.inputSpeed,
        太阳轮齿数: sunTeeth,
        内齿圈齿数: ringTeeth,
        行星轮齿数: planetTeeth
      });

      if (type === '太阳轮输入') {
        // 太阳轮输入模式
        // 设置太阳轮转速为输入转速
        this.$set(sunGear, 'inputSpeed', this.inputSpeed);
        
        // 计算行星架转速
        const carrierFreq = planetCarrierRPMFromSun(this.inputSpeed, sunTeeth, ringTeeth, false);
        const carrierRPM = carrierFreq * 60; // 转换回RPM
        this.$set(planetGear, 'inputSpeed', carrierRPM);
        
        // 内齿圈固定
        this.$set(ringGear, 'inputSpeed', 0);
        
        console.log("太阳轮输入模式 - 更新后的转速:", {
          太阳轮: sunGear.inputSpeed,
          行星架: planetGear.inputSpeed,
          内齿圈: ringGear.inputSpeed
        });
      } else {
        // 行星架输入模式
        // 设置行星架转速为输入转速
        this.$set(planetGear, 'inputSpeed', this.inputSpeed);
        
        // 内齿圈固定
        this.$set(ringGear, 'inputSpeed', 0);
        
        // 计算太阳轮转速
        const sunFreq = sunGearRPMFromSun(this.inputSpeed, sunTeeth, ringTeeth, true);
        const sunRPM = sunFreq * 60; // 转换回RPM
        this.$set(sunGear, 'inputSpeed', sunRPM);
        
        console.log("行星架输入模式 - 更新后的转速:", {
          行星架: planetGear.inputSpeed,
          太阳轮: sunGear.inputSpeed,
          内齿圈: ringGear.inputSpeed
        });
      }
      
      // 确保更新到tableData中
      if (this.gearbox.table && this.gearbox.table.tableData) {
        if (type === '太阳轮输入') {
          this.gearbox.table.tableData.speed = this.inputSpeed;
          this.gearbox.table.tableData.sunSpeed = this.inputSpeed;
          this.gearbox.table.tableData.inputShaftSpeed = this.inputSpeed;
        } else {
          this.gearbox.table.tableData.speed = this.inputSpeed;
          this.gearbox.table.tableData.carrierSpeed = this.inputSpeed;
          this.gearbox.table.tableData.inputShaftSpeed = this.inputSpeed;
        }
      }
      
      // 触发更新事件，确保数据被保存
      this.$emit('update', JSON.parse(JSON.stringify(this.gearbox)));
    },

    // 修改原有的handleBlur方法
    handleBlur(row, field) {
      this.$set(row, `editable${this.capitalizeFirstLetter(field)}`, false);
      
      if (field === 'teethCount') {
        const value = Number(row.teethCount);
        if (isNaN(value) || value <= 0) {
          this.$message.warning('齿数必须为大于0的数字');
          row.teethCount = 0;
          return;
        }
      }

      if (field === 'inputSpeed') {
        const value = Number(row.inputSpeed);
        if (isNaN(value) || value < 0) {
          this.$message.warning('转速必须为大于等于0的数字');
          row.inputSpeed = 0;
          return;
        }

        const type = this.getPlanetaryType();
        if (type === '太阳轮输入') {
          // 对于太阳轮输入型，如果修改的是太阳轮转速，需要更新其他齿轮的转速
          if (row.type === '太阳轮') {
            row.inputSpeed = value; // 确保值被正确设置
            this.inputSpeed = value; // 更新组件的输入转速
            this.updateAllSpeeds();
          }
        }
      }

      // 构建要保存的数据
      const updatedGearbox = {
        ...this.gearbox,
        inputSpeed: this.inputSpeed,
        children: this.gearbox.children.map(gear => ({
          ...gear,
          inputSpeed: gear.inputSpeed
        }))
      };

      // 触发更新事件
      this.$emit('update', updatedGearbox);
    },

    // 处理倍频输入
    handleMultipleInput(row) {
      const input = row.multipleInput;
      if (!input || isNaN(input) || input <= 0) {
        this.$set(row, 'selectedMultiples', []);
        return;
      }

      const maxMultiple = parseInt(input);
      const multiples = Array.from(
        { length: maxMultiple },
        (_, i) => i + 1
      );

      // 使用 $set 确保数组变更能被 Vue 检测到
      this.$set(row, 'selectedMultiples', multiples);

      // 计算基频
      const baseFrequency = this.getFrequencyValue(row, row.selectedFrequencyType);
      
      // 构建更新数据
      const updatedGear = {
        ...row,
        baseFrequency: Number(baseFrequency),
        frequencyType: row.selectedFrequencyType,
        description: `${this.gearbox.deviceName}-${row.type}`,
        selectedMultiples: multiples
      };

      // 触发更新事件
      this.$emit('frequency-change', updatedGear);
    },

    // 首字母大写
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    // 显示啮合频率公式
    showMeshFrequencyFormula(gear) {
      return true; // 始终显示公式
    },

    // 获取啮合频率公式
    getMeshFrequencyFormula(gear) {
      const type = this.getPlanetaryType();
      const formulas = this.meshFrequencyFormulas[type];
      if (!formulas) return '';

      if (gear.type === '太阳轮') {
        return formulas.sunPlanet;
      } else if (gear.type === '内齿圈') {
        return formulas.ringPlanet;
      }
      return '';
    },

    // 获取啮合标题
    getMeshTitle(gear) {
      const type = this.getPlanetaryType();
      const formulas = this.formulaDescriptions[type];
      if (!formulas) return '';

      if (gear.type === '太阳轮') {
        return formulas.sunPlanet.title;
      } else if (gear.type === '内齿圈') {
        return formulas.ringPlanet.title;
      }
      return '';
    },

    // 获取公式说明
    getFormulaDescription(gear) {
      const type = this.getPlanetaryType();
      const formulas = this.formulaDescriptions[type];
      if (!formulas) return '';

      if (gear.type === '太阳轮') {
        return formulas.sunPlanet.desc;
      } else if (gear.type === '内齿圈') {
        return formulas.ringPlanet.desc;
      }
      return '';
    },

    // 计算单个行星轮啮合频率
    calculateSinglePlanetMeshFrequency(gear) {
      if (!gear.teethCount || !gear.inputSpeed) return '0.00';

      const planetaryType = this.getPlanetaryType();
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetCount = Number(this.getPlanetaryCount);

      let frequency = 0;
      const inputSpeed = Number(gear.inputSpeed);

      switch (planetaryType) {
        case '行星架输入':
          if (gear.type === '太阳轮') {
            frequency = (Number(sunGear.teethCount) * inputSpeed) / (60 * planetCount);
          } else if (gear.type === '内齿圈') {
            frequency = inputSpeed / 60;
          }
          break;
        case '太阳轮输入':
          if (gear.type === '太阳轮') {
            frequency = (Number(sunGear.teethCount) * inputSpeed) / (60 * planetCount);
          } else if (gear.type === '内齿圈') {
            frequency = (Math.abs(inputSpeed) - Number(planetaryGears.find(g => g.type === '行星轮').inputSpeed)) / 60;
          }
          break;
      }

      return frequency.toFixed(2);
    },

    // 计算通过频率
    calculatePassFrequency(gear) {
      if (!gear.inputSpeed) return '0.00';

      const planetaryType = this.getPlanetaryType();
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetCount = Number(this.getPlanetaryCount);
      const inputSpeed = Number(gear.inputSpeed);

      let frequency = 0;

      switch (planetaryType) {
        case '行星架输入':
          frequency = (inputSpeed * planetCount) / 60;
          break;
        case '太阳轮输入':
          frequency = (inputSpeed * planetCount) / (60 * (1 + Number(sunGear.teethCount) / Number(ringGear.teethCount)));
          break;
      }

      return frequency.toFixed(2);
    },

    // 计算行星架转动频率
    calculateCarrierRotateFrequency(gear) {
      if (!gear.inputSpeed) return '0.00';

      const planetaryType = this.getPlanetaryType();
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const inputSpeed = Number(gear.inputSpeed);

      let frequency = 0;

      switch (planetaryType) {
        case '行星架输入':
          frequency = inputSpeed / 60;
          break;
        case '太阳轮输入':
          frequency = inputSpeed / (60 * (1 + Number(ringGear.teethCount) / Number(sunGear.teethCount)));
          break;
      }

      return frequency.toFixed(2);
    },

    // 获取单个行星轮啮合频率公式
    getSinglePlanetMeshFormula(gear) {
      const type = this.getPlanetaryType();
      const formulas = this.meshFrequencyFormulas[type];
      if (!formulas) return '';

      if (gear.type === '太阳轮') {
        return formulas.singleSunPlanetMesh;
      } else if (gear.type === '内齿圈') {
        return formulas.singleRingPlanetMesh;
      }
      return '';
    },

    // 获取通过频率公式
    getPassFrequencyFormula(gear) {
      const type = this.getPlanetaryType();
      const formulas = this.meshFrequencyFormulas[type];
      if (!formulas) return '';

      if (gear.type === '太阳轮') {
        return formulas.sunPassFreq;
      } else if (gear.type === '内齿圈') {
        return formulas.ringPassFreq;
      }
      return '';
    },

    // 获取行星架转动频率公式
    getCarrierRotateFormula() {
      const type = this.getPlanetaryType();
      const formulas = this.meshFrequencyFormulas[type];
      return formulas ? formulas.carrierRotateFreq : '';
    },

    // 获取频率选项 label
    getFrequencyOptions(gear) {
      const type = this.getPlanetaryType();
      const formulas = this.meshFrequencyFormulas[type];
      const options = [];

      if(this.getPlanetaryType() === "行星架输入"){
        if (gear.type === '太阳轮') {
          options.push(
            { 
              label: '太阳轮转频N3', 
              value: 'fp_s_xingxing',
              formula: formulas.sunPlanet
            },
          );
        } else if (gear.type === '内齿圈') {
          options.push(
            { 
              label: '齿圈故障通过频率', 
              value: 'f_r_pass_xingxing',
              formula: formulas.ringPassFreq
            },
            { 
              label: '啮合频率', 
              value: 'fp_r_xingxing',
              formula: formulas.meshFreq
            },
          );
        } else if (gear.type === '行星轮') {
          options.push(
            { 
              label: '行星轮转频N2', 
              value: 'fp_r_single_xingxing',
              formula: formulas.ringPlanet
            },
            { 
              label: '行星轮绝对转频', 
              value: 'f_r_pass_xingxing',
              formula: formulas.absolutePlanet
            }
          );
        }

    }else if(this.getPlanetaryType() === "太阳轮输入"){
      if (gear.type === '太阳轮') {
          options.push(
            { 
              label: '啮合频率', 
              value: 'fp_s_taiyanglun',
              formula: formulas.meshFreq
            },
          );
        } else if (gear.type === '内齿圈') {
          options.push(
            { 
              label: '齿圈故障通过频率', 
              value: 'fp_r_taiyanglun',
              formula: formulas.ringPassFreq
            },
          );
        } else if (gear.type === '行星轮') {
          options.push(
            { 
              label: '行星轮转频N2', 
              value: 'fp_r_taiyanglun',
              formula: formulas.ringPlanet
            },
            { 
              label: '行星轮绝对转频', 
              value: 'fp_r_single_taiyanglun',
              formula: formulas.absolutePlanet
            },
            { 
              label: '行星架转频N1', 
              value: 'f_r_pass_taiyanglun',
              formula: formulas.carrierFreq
            }
          );
        }

    }
      return options;
    },

    // 获取频率值
    getFrequencyValue(gear, type) {
      const planetaryGears = this.gearbox.children;
      const sunGear = planetaryGears.find(g => g.type === '太阳轮');
      const ringGear = planetaryGears.find(g => g.type === '内齿圈');
      const planetGear = planetaryGears.find(g => g.type === '行星轮');
      const planetCount = Number(planetGear.planetaryCount || 4);

      if (!sunGear || !ringGear || !planetGear) return '0.00';

      // 确保所有输入值都是有效数字
      const sunGearRPM = Number(sunGear.inputSpeed) || 0;
      const ringGearTeeth = Number(ringGear.teethCount) || 1;
      const planetGearTeeth = Number(planetGear.teethCount) || 1;
      const sunGearTeeth = Number(sunGear.teethCount) || 1;
      const carrierRPM = Number(planetGear.inputSpeed) || 0;

      let frequency = 0;
      let formulaName = '';
      let formulaText = '';
      let calculationProcess = '';

      if (this.getPlanetaryType() === '行星架输入') {
        switch (type) {
          case 'fp_s_xingxing': // 太阳轮转频N3
            formulaName = '太阳轮转频N3';
            formulaText = 'N3 = (1 + Zr/Zs) * N1';
            const carrierFreq = carrierRPM / 60;
            const ratio = 1 + ringGearTeeth / sunGearTeeth;
            frequency = ratio * carrierFreq;
            calculationProcess = `${carrierFreq} * (1 + ${ringGearTeeth}/${sunGearTeeth}) = ${frequency}`;
            break;
          case 'fp_r_xingxing': // 啮合频率
            formulaName = '啮合频率';
            formulaText = 'fmesh = Zr * N1';
            const carrierFreqMesh = carrierRPM / 60;
            frequency = ringGearTeeth * carrierFreqMesh;
            calculationProcess = `${ringGearTeeth} * ${carrierFreqMesh} = ${frequency}`;
            break;
          case 'f_r_pass_xingxing': // 齿圈故障通过频率或行星轮绝对转频
            if (gear.type === '内齿圈') {
              formulaName = '齿圈故障通过频率';
              formulaText = 'f_fault = p * N1';
              const carrierFreqFault = carrierRPM / 60;
              frequency = planetCount * carrierFreqFault;
              calculationProcess = `${planetCount} * ${carrierFreqFault} = ${frequency}`;
            } else if (gear.type === '行星轮') {
              formulaName = '行星轮绝对转频';
              formulaText = 'N2,abs = (1 + Zr/Zp) * N1';
              const carrierFreqAbs = carrierRPM / 60;
              const ratioAbs = 1 + ringGearTeeth / planetGearTeeth;
              frequency = ratioAbs * carrierFreqAbs;
              calculationProcess = `${carrierFreqAbs} * (1 + ${ringGearTeeth}/${planetGearTeeth}) = ${frequency}`;
            }
            break;
          case 'fp_r_single_xingxing': // 行星轮转频N2
            formulaName = '行星轮转频N2';
            formulaText = 'N2 = (Zr/Zp) * N1';
            const carrierFreqPlanet = carrierRPM / 60;
            const ratioPlanet = ringGearTeeth / planetGearTeeth;
            frequency = ratioPlanet * carrierFreqPlanet;
            calculationProcess = `${carrierFreqPlanet} * (${ringGearTeeth}/${planetGearTeeth}) = ${frequency}`;
            break;
        }
      } else if (this.getPlanetaryType() === '太阳轮输入') {
        // 太阳轮输入模式的计算
        const sunFreq = sunGearRPM / 60; // 太阳轮转频
        
        // 行星架转频计算 - 太阳轮输入模式下的基础计算
        const carrierRatio = sunGearTeeth / (sunGearTeeth + ringGearTeeth);
        const carrierFreq = carrierRatio * sunFreq;
        
        console.log("太阳轮输入模式计算基础数据:", {
          太阳轮转速RPM: sunGearRPM,
          太阳轮转频Hz: sunFreq,
          太阳轮齿数: sunGearTeeth,
          内齿圈齿数: ringGearTeeth,
          行星轮齿数: planetGearTeeth,
          行星轮数量: planetCount,
          行星架转频Hz: carrierFreq,
          行星架转速RPM: carrierFreq * 60,
          选择的频率类型: type
        });
        
        switch (type) {
          case 'fp_s_taiyanglun': // 啮合频率
            formulaName = '啮合频率';
            formulaText = 'fmesh = Zr × N1 = Zr × [Zs/(Zs+Zr)] × N3';
            // 啮合频率计算
            frequency = ringGearTeeth * carrierFreq;
            calculationProcess = `${ringGearTeeth} * (${sunGearTeeth}/(${sunGearTeeth}+${ringGearTeeth})) * ${sunFreq} = ${frequency}`;
            break;
          
          case 'fp_r_taiyanglun': // 齿圈故障通过频率或行星轮转频N2
            if (gear.type === '内齿圈') {
              formulaName = '齿圈故障通过频率';
              formulaText = 'f_fault = p × N1 = p × [Zs/(Zs+Zr)] × N3';
              // 故障频率计算
              frequency = planetCount * carrierFreq;
              calculationProcess = `${planetCount} * (${sunGearTeeth}/(${sunGearTeeth}+${ringGearTeeth})) * ${sunFreq} = ${frequency}`;
            } else if (gear.type === '行星轮') {
              formulaName = '行星轮转频N2';
              formulaText = 'N2 = (Zr/Zp) × N1 = (Zr/Zp) × [Zs/(Zs+Zr)] × N3';
              // 行星轮转频计算
              const ratioPlanet = ringGearTeeth / planetGearTeeth;
              frequency = ratioPlanet * carrierFreq;
              calculationProcess = `(${ringGearTeeth}/${planetGearTeeth}) * (${sunGearTeeth}/(${sunGearTeeth}+${ringGearTeeth})) * ${sunFreq} = ${frequency}`;
            }
            break;
            
          case 'fp_r_single_taiyanglun': // 行星轮绝对转频
            formulaName = '行星轮绝对转频';
            formulaText = 'N2,abs = (1 + Zr/Zp) × N1 = (1 + Zr/Zp) × [Zs/(Zs+Zr)] × N3';
            // 行星轮绝对转频计算
            const ratioAbs = 1 + ringGearTeeth / planetGearTeeth;
            frequency = ratioAbs * carrierFreq;
            calculationProcess = `(1 + ${ringGearTeeth}/${planetGearTeeth}) * (${sunGearTeeth}/(${sunGearTeeth}+${ringGearTeeth})) * ${sunFreq} = ${frequency}`;
            break;
            
          case 'f_r_pass_taiyanglun': // 行星架转频N1
            formulaName = '行星架转频N1';
            formulaText = 'N1 = Zs/(Zs+Zr) × N3';
            // 行星架转频就是之前计算的carrierFreq
            frequency = carrierFreq;
            calculationProcess = `(${sunGearTeeth}/(${sunGearTeeth}+${ringGearTeeth})) * ${sunFreq} = ${frequency}`;
            break;
        }
      }

      // 输出计算过程的日志
      console.log(`${gear.type} ${formulaName} 计算:`, {
        公式: formulaText,
        计算过程: calculationProcess,
        结果Hz: frequency,
        结果RPM: frequency * 60
      });

      return frequency.toFixed(2);
    },

    // 获取选中频率的标签
    getSelectedFrequencyLabel(gear) {
      if (!gear.selectedFrequencyType) return '';
      
      const options = this.getFrequencyOptions(gear);
      const selectedOption = options.find(opt => opt.value === gear.selectedFrequencyType);
      return selectedOption ? selectedOption.label : '';
    },

    // 获取选中频率的值
    getSelectedFrequencyValue(gear) {
      if (!gear.selectedFrequencyType) {
        // 设置默认选中的频率类型
        this.$set(gear, 'selectedFrequencyType', gear.type === '太阳轮' ? 'fp_s' : 'fp_r');
      }
      return this.getFrequencyValue(gear, gear.selectedFrequencyType);
    },

    // 获取选中频率的公式说明
    getSelectedFormulaDescription(gear) {
      if (!gear.selectedFrequencyType) return '';

      const type = this.getPlanetaryType();
      const formulas = this.meshFrequencyFormulas[type];
      if (!formulas) return '';

      switch (gear.selectedFrequencyType) {
        case 'fp_s':
          return '太阳轮-行星轮啮合频率公式说明';
        case 'fp_r':
          return '内齿圈-行星轮啮合频率公式说明';
        case 'fp_s_single':
          return '单个行星轮与太阳轮的啮合频率公式说明';
        case 'fp_r_single':
          return '单个行星轮与内齿圈的啮合频率公式说明';
        case 'f_s_pass':
          return '太阳轮上某一点通过的行星轮的频率公式说明';
        case 'f_r_pass':
          return '内齿圈某一点通过的行星轮的频率公式说明';
        default:
          return '';
      }
    },

    // 获取选中频率的公式
    getSelectedFormula(gear) {
      if (!gear.selectedFrequencyType) return '';

      const type = this.getPlanetaryType();
      const options = this.getFrequencyOptions(gear);
      const selectedOption = options.find(opt => opt.value === gear.selectedFrequencyType);
      
      return selectedOption ? selectedOption.formula : '';
    },

    // 处理频率类型变化
    handleFrequencyTypeChange(gear) {
      // 计算基频
      const baseFrequency = this.getFrequencyValue(gear, gear.selectedFrequencyType);
      
      // 确保选中的类型是有效的
      const validOptions = this.getFrequencyOptions(gear);
      if (!validOptions.find(opt => opt.value === gear.selectedFrequencyType)) {
        // 如果选中的类型无效，设置为第一个有效选项
        gear.selectedFrequencyType = validOptions[0]?.value;
      }
      
      // 构建更新数据
      const updatedGear = {
        ...gear,
        baseFrequency: Number(baseFrequency),
        frequencyType: gear.selectedFrequencyType,
        description: `${this.gearbox.deviceName}-${gear.type}`,
        selectedMultiples: gear.selectedMultiples || []
      };

      // 使用 Vue 的响应式更新
      this.$set(gear, 'selectedFrequencyType', gear.selectedFrequencyType);
      
      // 触发更新事件
      this.$emit('frequency-change', updatedGear);
    },

    // 处理行星轮数量变化
    handlePlanetaryCountChange(value) {
      // 更新行星轮数量
      this.planetaryCount = value;

      // 更新到行星轮对象中
      const planetGear = this.gearbox.children.find(g => g.type === '行星轮');
      if (planetGear) {
        this.$set(planetGear, 'planetaryCount', value);
      }

      // 更新到 tableData 中
      if (this.gearbox.table && this.gearbox.table.tableData) {
        this.gearbox.table.tableData.planetaryGearCount = value;
      }

      // 触发更新事件，保持原有转速不变
      const type = this.getPlanetaryType();
      const updatedGearbox = {
        ...this.gearbox,
        children: this.gearbox.children.map(gear => {
          const updatedGear = { ...gear };
          // 在太阳轮输入模式下，保持太阳轮（输入轴）的转速不变
          if (type === '太阳轮输入' && gear.type === '太阳轮') {
            updatedGear.inputSpeed = gear.inputSpeed;
          }
          // 在行星架输入模式下，保持行星轮（输入轴）的转速不变
          if (type === '行星架输入' && gear.type === '行星轮') {
            updatedGear.inputSpeed = gear.inputSpeed;
          }
          return updatedGear;
        })
      };

      this.$emit('update', updatedGearbox);
    },

    // 在保存数据的方法中
    saveData() {
      const dataToSave = {
        ...this.gearbox,
        children: this.gearbox.children.map(gear => ({
          ...gear,
          selectedFrequencyType: gear.selectedFrequencyType, // 确保这个值被保存
          // ... 其他属性
        }))
      };
      // ... 保存逻辑
    },

    // 初始化行星轮数量
    initPlanetaryCount() {
      const planetaryGears = this.gearbox.children;
      const planetGear = planetaryGears.find(g => g.type === '行星轮');
      if (planetGear && planetGear.planetaryCount) {
        // 优先从行星轮对象中获取数量
        this.planetaryCount = planetGear.planetaryCount;
      } else if (this.gearbox.table && this.gearbox.table.tableData && this.gearbox.table.tableData.planetaryGearCount) {
        // 其次从 tableData 中获取
        this.planetaryCount = this.gearbox.table.tableData.planetaryGearCount;
        // 同步到行星轮对象中
        if (planetGear) {
          this.$set(planetGear, 'planetaryCount', this.planetaryCount);
        }
      } else {
        // 如果都没有，则使用默认值 4
        this.planetaryCount = 4;
        // 同步到行星轮对象和 tableData 中
        if (planetGear) {
          this.$set(planetGear, 'planetaryCount', this.planetaryCount);
        }
        if (this.gearbox.table && this.gearbox.table.tableData) {
          this.gearbox.table.tableData.planetaryGearCount = this.planetaryCount;
        }
      }
    },

    // 初始化默认频率选项
    initDefaultFrequencyOptions() {
      this.gearbox.children.forEach(gear => {
        const options = this.getFrequencyOptions(gear);
        if (options && options.length > 0) {
          // 自动选择第一个选项
          this.$set(gear, 'selectedFrequencyType', options[0].value);
          
          // 计算基频
          const baseFrequency = this.getFrequencyValue(gear, options[0].value);
          
          // 更新基频和频率描述
          this.$set(gear, 'baseFrequency', Number(baseFrequency));
          this.$set(gear, 'frequencyTypeDescription', options[0].label);
          
          console.log(`为 ${gear.type} 自动选择频率选项:`, {
            选择的频率类型: options[0].value,
            选择的频率标签: options[0].label,
            基频: baseFrequency
          });
        }
      });
    }
  },
  watch: {
    'gearbox.children': {
      handler(newValue) {
        // 监听数据变化，确保 selectedFrequencyType 始终有效
        newValue.forEach(gear => {
          const validOptions = this.getFrequencyOptions(gear);
          if (!validOptions.find(opt => opt.value === gear.selectedFrequencyType)) {
            this.$set(gear, 'selectedFrequencyType', validOptions[0]?.value);
          }
        });
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
@import './PlanetaryAxisSystem.css';
</style> 