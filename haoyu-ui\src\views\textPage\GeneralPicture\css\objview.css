.obj-viewer {
    width: 100%;
    height: 100%;
    min-height: 500px;
    background: #ffffff;
    position: relative;
    /* 添加相对定位 */
    overflow: hidden;
}

.model-tooltip {
    position: absolute;
    z-index: 10;
    pointer-events: auto;
    cursor: move;
    user-select: none;
    transition: transform 0.3s ease;
}

.tooltip-content {
    background: linear-gradient(135deg, rgba(18, 30, 52, 0.98), rgba(5, 15, 35, 0.9));
    border-radius: 8px;
    padding: 12px;
    color: white;
    font-size: 12px;
    min-width: 150px;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 20px rgba(0, 140, 255, 0.15), inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 140, 255, 0.2);
}

.tooltip-header {
    font-weight: bold;
    margin-bottom: 6px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(0, 140, 255, 0.3);
    color: #1890ff;
    text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.tooltip-body {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.tooltip-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-value {
    color: #67C23A;
}

.health-value {
    color: #409EFF;
}

.model-point {
    position: absolute;
    width: 16px !important;
    height: 16px !important;
    background: radial-gradient(circle at center, #1890ff 30%, rgba(24, 144, 255, 0.8) 70%);
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.5), 0 0 5px rgba(24, 144, 255, 0.8);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.8);
    pointer-events: none;
    transition: none;
}

.connection-lines {
    pointer-events: none;
    z-index: 1;
}

.connection-path {
    fill: none;
    stroke: rgba(24, 144, 255, 0.7);
    stroke-width: 3;
    /* 增加线条粗细 */
    stroke-dasharray: 6 6;
    /* 调整虚线间距 */
    filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.3));
    transition: stroke-width 0.3s ease;
}

.connection-path:hover {
    stroke-width: 5;
    stroke: rgba(24, 144, 255, 0.9);
}

.measure-definitions {
    margin-top: 10px;
    background: rgba(0, 140, 255, 0.1);
    border-radius: 4px;
    padding: 4px;
}

.definition-item {
    padding: 6px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    pointer-events: auto;
    border-radius: 4px;
    margin: 2px 0;
}

.definition-item:hover {
    background: rgba(24, 144, 255, 0.2);
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.1);
    transform: translateX(2px);
}

.model-tooltip {
    cursor: move;
    user-select: none;
}

.model-tooltip:hover {
    transform: translate(-50%, 0) scale(1.05);
}

.operation-guide {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.guide-button {
    background: rgba(24, 144, 255, 0.1);
    border-radius: 4px;
    padding: 8px 16px;
    color: #1890ff;
    transition: all 0.3s;
}

.guide-button:hover {
    background: rgba(24, 144, 255, 0.2);
}

.operation-tips {
    font-size: 14px;
    color: #333;
}

.operation-tips h4 {
    margin: 0 0 16px 0;
    color: #1890ff;
    font-size: 16px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.tips-section {
    margin-bottom: 16px;
}

.tips-section h5 {
    margin: 0 0 8px 0;
    color: #1890ff;
    font-size: 14px;
}

.tips-section ul {
    margin: 0;
    padding-left: 20px;
}

.tips-section li {
    margin: 4px 0;
    color: #666;
    line-height: 1.5;
}

.tips-section li:hover {
    color: #1890ff;
}