<template>
  <el-dialog
    title="模型与测点匹配"
    :visible.sync="dialogVisible"
    width="60%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="false"
    v-loading="loading"
    custom-class="model-point-match-dialog"
  >
    <el-table :data="matchData" style="width: 100%" v-loading="tableLoading">
      <el-table-column prop="modelName" label="模型名称" width="180">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.modelName" 
            placeholder="请选择模型"
            filterable
            default-first-option
          >
            <el-option
              v-for="model in modelList"
              :key="model.id"
              :label="model.name"
              :value="model.name"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="pointName" label="测点名称" width="180">
        <template slot-scope="scope">
          <el-select 
            v-model="scope.row.pointName" 
            placeholder="请选择测点"
            filterable
            default-first-option
          >
            <el-option
              v-for="point in pointList"
              :key="point.id"
              :label="point.title"
              :value="point.title"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="isShow" label="是否显示" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.isShow" @change="handleShowChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.$index, scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px">
      <el-button type="primary" @click="handleAdd">添加匹配</el-button>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getDeviceModelIdAndName, getDeviceMeasurePoint, getDeviceWithPoint, CreateDeviceWithPoint, UpdateDeviceWithPoint, deleteDeviceWithPoint } from '../api/Dalog.js';

export default {
  name: 'ModelPointMatch',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    deviceId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      matchData: [],
      modelList: [],
      pointList: [],
      loading: false,
      tableLoading: false
    }
  },
  watch: {
    async visible(val) {
      if (val) {
        this.dialogVisible = true
        if (!this.modelList.length || !this.pointList.length) {
          await this.initData()
        }
      } else {
        this.dialogVisible = false
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    async initData() {
      if (this.loading) return
      this.loading = true
      this.tableLoading = true
      try {
        // 添加延迟确保DOM已经渲染
        await this.$nextTick();
        
        const [modelResponse, pointResponse, matchResponse] = await Promise.all([
          getDeviceModelIdAndName(this.deviceId),
          getDeviceMeasurePoint(this.deviceId),
          getDeviceWithPoint(this.deviceId)
        ])

        // 等待所有图像加载完成
        await new Promise(resolve => setTimeout(resolve, 100));

        if (modelResponse.data) {
          this.modelList = Object.entries(modelResponse.data).map(([name, id]) => ({
            name,
            id
          }))
        }

        if (pointResponse.data) {
          this.pointList = Object.entries(pointResponse.data).map(([title, id]) => ({
            title,
            id
          }))
        }

        if (matchResponse && matchResponse.rows) {
          this.matchData = matchResponse.rows.map(match => ({
            modelName: match.modelName,
            pointName: match.measurementName,
            isShow: match.isShow === "1",
            id: match.id,
            modelId: match.modelId,
            measurementId: match.measurementId,
            location: match.location || null
          }))
        }

        // 确保所有图像和资源都已加载完成
        await this.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
        this.tableLoading = false
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleAdd() {
      try {
        // 确保DOM已更新
        await this.$nextTick();
        
        // 先在本地添加一个空的匹配项
        const newMatch = {
          modelName: '',
          pointName: '',
          isShow: true,
          location: null
        };
        this.matchData.push(newMatch);
        
        // 等待DOM更新
        await this.$nextTick();
        
        // 获取新添加行的索引
        const newIndex = this.matchData.length - 1;
        
        // 等待用户选择模型和测点
        await new Promise(resolve => {
          const unwatch = this.$watch(
            () => [this.matchData[newIndex].modelName, this.matchData[newIndex].pointName],
            async ([modelName, pointName]) => {
              if (modelName && pointName) {
                try {
                  await this.$nextTick();
                  // 找到对应的模型和测点ID
                  const model = this.modelList.find(m => m.name === modelName);
                  const point = this.pointList.find(p => p.title === pointName);
                  
                  if (model && point) {
                    // 创建新的匹配关系
                    const response = await CreateDeviceWithPoint({
                      deviceId: Number(this.deviceId),
                      modelId: Number(model.id),
                      measurementId: Number(point.id),
                      isShow: "1",
                      location: null
                    });
                    
                    // 更新本地数据，添加返回的id
                    if (response.code === 200) {
                      await this.$nextTick();
                      this.$set(this.matchData[newIndex], 'id', response.data);
                      this.$set(this.matchData[newIndex], 'modelId', model.id);
                      this.$set(this.matchData[newIndex], 'measurementId', point.id);
                      this.$message.success('添加匹配成功');
                      // 实时通知父组件更新
                      this.$emit('confirm', this.matchData.filter(match => match.modelName && match.pointName));
                    }
                  }
                } catch (error) {
                  console.error('创建匹配关系失败:', error);
                  this.$message.error('创建匹配关系失败');
                  // 如果创建失败，从本地数据中移除
                  this.matchData.splice(newIndex, 1);
                }
                unwatch(); // 取消监听
                resolve();
              }
            },
            { deep: true }
          );
        });
      } catch (error) {
        console.error('添加匹配失败:', error);
        this.$message.error('添加匹配失败');
      }
    },
    async handleDelete(index, row) {
      try {
        if (row.id) {
          // 如果有id，调用删除API
          await deleteDeviceWithPoint(row.id);
          this.$message.success('删除成功');
        }
        // 从本地数据中移除
        this.matchData.splice(index, 1);
        // 实时通知父组件更新
        this.$emit('confirm', this.matchData.filter(match => match.modelName && match.pointName));
      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
      }
    },
    async handleConfirm() {
      console.log("sure")
      try {
        // 只处理更新操作
        const validMatches = this.matchData.filter(match => 
          match.id && match.modelName && match.pointName
        );
        
        // 更新匹配关系
        for (const match of validMatches) {
          const model = this.modelList.find(m => m.name === match.modelName);
          const point = this.pointList.find(p => p.title === match.pointName);
          
          await UpdateDeviceWithPoint({
            id: match.id,
            deviceId: Number(this.deviceId),
            modelId: Number(model.id),
            measurementId: Number(point.id),
            isShow: match.isShow ? "1" : "0",
            location: match.location
          });
          
          // 每次更新一条记录后就通知父组件
          this.$emit('confirm', this.matchData.filter(match => match.modelName && match.pointName));
        }

        this.$message.success('匹配关系更新成功');
        this.handleClose();
      } catch (error) {
        console.error('更新匹配关系失败:', error);
        this.$message.error('更新失败');
      }
    },
    async handleShowChange(row) {
      try {
        if (row.id) {
          const model = this.modelList.find(m => m.name === row.modelName);
          const point = this.pointList.find(p => p.title === row.pointName);
          
          await UpdateDeviceWithPoint({
            id: row.id,
            deviceId: Number(this.deviceId),
            modelId: Number(model.id),
            measurementId: Number(point.id),
            isShow: row.isShow ? "1" : "0",
            location: row.location
          });
          
          // 实时通知父组件更新
          this.$emit('confirm', this.matchData.filter(match => match.modelName && match.pointName));
        }
      } catch (error) {
        console.error('更新显示状态失败:', error);
        this.$message.error('更新显示状态失败');
        // 如果更新失败，恢复原来的状态
        row.isShow = !row.isShow;
      }
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 100%;
}

.model-point-match-dialog {
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.model-point-match-dialog .el-dialog__body {
  padding: 20px;
}

.model-point-match-dialog .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
}
</style> 