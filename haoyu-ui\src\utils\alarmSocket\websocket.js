class WebSocketService {
  constructor() {
    this.ws = null;
    this.messageCallback = null;
    this.heartbeatTimer = null;
    this.currentAlarmInstance = null; // 添加变量来跟踪当前弹窗实例
    this.resolvedAlarms = new Set(); // 存储已解决的报警ID
    this.alarmNotificationCallback = null; // 添加报警通知回调
  }

  // 连接WebSocket
  connect(url) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接');
      return;
    }

    console.log('正在连接WebSocket...');
    this.ws = new WebSocket(url);

    // 连接成功
    this.ws.onopen = () => {
      console.log('WebSocket连接成功');
      this.startHeartbeat();
    };

    // 接收消息
    this.ws.onmessage = (event) => {
      console.log('收到消息:', event.data);
      if (event.data.includes('测点')) {
        console.log('检测到测点信息');
      }

      // 处理报警消息并显示通知
      this.handleAlarmMessage(event.data);

      if (this.messageCallback) {
        this.messageCallback(event.data);
      }
    };

    // 连接关闭
    this.ws.onclose = () => {
      console.log('WebSocket连接关闭');
      this.stopHeartbeat();
      // 3秒后重连
      setTimeout(() => this.connect(url), 3000);
    };

    // 连接错误
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }

  // 发送消息
  send(data) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接');
      return;
    }

    const message = typeof data === 'string' ? data : JSON.stringify(data);
    this.ws.send(message);
  }

  // 设置消息处理回调
  onMessage(callback) {
    this.messageCallback = callback;
  }

  // 设置报警通知回调
  onAlarmNotification(callback) {
    this.alarmNotificationCallback = callback;
  }

  // 处理报警消息
  handleAlarmMessage(data) {
    try {
      // 直接检查字符串是否包含"测点"
      if (typeof data === 'string' && data.includes('测点')) {
        console.log('检测到测点信息，准备处理报警消息');

        // 格式化报警消息
        const alarmData = {
          id: Date.now() + Math.random().toString(36).substring(2, 7),
          timestamp: new Date(),
          type: '设备报警',
          level: 'critical',
          message: data
        };

        // 如果有自定义回调，则调用
        if (this.alarmNotificationCallback) {
          try {
            this.alarmNotificationCallback(alarmData);
          } catch (callbackError) {
            console.error('报警通知回调执行出错:', callbackError);
            this.fallbackHandleAlarm(alarmData);
          }
        }
      }
    } catch (error) {
      console.error('处理报警消息出错:', error);
    }
  }

  // 备用报警处理方法
  fallbackHandleAlarm(data) {
    console.log('使用备用方法处理报警:', data);
    // 这里可以实现备用的报警处理逻辑
    // 比如直接显示一个消息通知等
  }

  // 开始心跳
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.send('heartbeat');
    }, 30000);
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 关闭连接
  close() {
    if (this.ws) {
      this.ws.close();
      this.stopHeartbeat();
    }
  }
}

// 创建单例
const wsService = new WebSocketService();
export default wsService;
